using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Plugins;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("AwakenC4Limiter", "Abtral", "1.1.1")]
    [Description("Limits the amount of C4 a player can place in a certain area within a time window.")]
    public class AwakenC4Limiter : RustPlugin
    {
        private readonly Dictionary<ulong, PlayerData> _playerData = new Dictionary<ulong, PlayerData>();

        #region Player Data Classes

        private class PlayerData
        {
            public List<PlacementInfo> Placements { get; set; } = new List<PlacementInfo>();
            public float CooldownEndTime { get; set; }
        }

        private class PlacementInfo
        {
            public Vector3 Position { get; set; }
            public float Timestamp { get; set; }
        }

        #endregion

        #region Configuration

        private new Configuration Config;

        private class Configuration
        {
            [JsonProperty("C4 Limit Per Area")]
            [DefaultValue(20)]
            public int C4Limit { get; set; }

            [JsonProperty("Area Radius (Meters)")]
            [DefaultValue(50f)]
            public float AreaRadius { get; set; }

            [JsonProperty("Cooldown After Reaching Limit (Seconds)")]
            [DefaultValue(60)]
            public int CooldownDurationSeconds { get; set; }

            [JsonProperty("Time Window to Remember Placements (Seconds)")]
            [DefaultValue(60)]
            public int PlacementRefreshSeconds { get; set; }

            [JsonProperty("Cooldown Message")]
            [DefaultValue("<color=#ff4747>You cannot place C4 right now! Please wait {cooldown} seconds.</color>")]
            public string CooldownMessage { get; set; }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                Config = Config.ReadObject<Configuration>();
                if (Config == null) throw new System.Exception();
                SaveConfig();
            }
            catch
            {
                PrintError("Your configuration file contains an error. Using default configuration values.");
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig() => Config.WriteObject(Config);

        protected override void LoadDefaultConfig() => Config = new Configuration();
        
        #endregion

        #region Hooks

        private object OnEntitySpawned(BaseEntity entity)
        {
            if (entity == null || entity.ShortPrefabName != "timed.explosive")
            {
                return null;
            }

            if (entity.OwnerID == 0)
            {
                return null;
            }

            var player = BasePlayer.FindByID(entity.OwnerID);
            if (player == null)
            {
                return null;
            }

            if (!_playerData.TryGetValue(player.userID, out var data))
            {
                data = new PlayerData();
                _playerData[player.userID] = data;
            }

            var currentTime = Time.realtimeSinceStartup;

            if (data.CooldownEndTime > currentTime)
            {
                var remainingCooldown = Mathf.CeilToInt(data.CooldownEndTime - currentTime);
                var message = Config.CooldownMessage.Replace("{cooldown}", remainingCooldown.ToString());
                player.ChatMessage(message);
                entity.Kill();
                return false;
            }

            data.Placements.RemoveAll(p => currentTime - p.Timestamp > Config.PlacementRefreshSeconds);

            var newPosition = entity.transform.position;
            var nearbyPlacements = data.Placements.Count(p => Vector3.Distance(p.Position, newPosition) <= Config.AreaRadius);

            if (nearbyPlacements >= Config.C4Limit)
            {
                var message = Config.CooldownMessage.Replace("{cooldown}", Config.CooldownDurationSeconds.ToString());
                player.ChatMessage(message);
                
                data.CooldownEndTime = currentTime + Config.CooldownDurationSeconds;
                data.Placements.Clear(); 
                entity.Kill();
                return false; 
            }

            data.Placements.Add(new PlacementInfo { Position = newPosition, Timestamp = currentTime });

            return null;
        }

        #endregion
    }
}