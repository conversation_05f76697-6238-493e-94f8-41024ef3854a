using HarmonyLib;
using Newtonsoft.Json;
using Oxide.Core.Plugins;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using System.Text;
using UnityEngine;
using Network;
using ConVar;

namespace Oxide.Plugins
{
    [Info("Awaken Queue Manager", "Skelee", "1.1.11")]
    [Description("Handles the server queue for Awaken Servers.")]
    public class AwakenQueueManager : CovalencePlugin
    {
        #region Config
        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("VIP Priority Queue")] bool VipPriorityQueue = false,
            [property: JsonProperty("Reconnect Time (Minutes)")] int ReconnectTime = 5);

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    config = LoadDefaultConfig();
                }
            }
            catch (Exception e)
            {
                Puts($"Failed to load config: {e.Message}");
                config = LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig, true);
            return defaultConfig;
        }

        protected override void SaveConfig()
        {
            if (config != null)
            {
                Config.WriteObject(config, true);
            }
        }
        #endregion

        #region Defines
        [PluginReference] private Plugin? AwakenAdminMenu;
        private static HarmonyLib.Harmony _harmony;
        private static AwakenQueueManager? Instance;
        private readonly Dictionary<string, DisconnectInfo> disconnectedPlayers = new();
        private ConnectionQueue? queue;
        private Coroutine? queueManagerCoroutine;
        private Coroutine? fallbackQueueCheckCoroutine;
        #endregion

        #region Classes
        private record DisconnectInfo(bool InQueue = false, int QueuePosition = -1, float LeaveTime = 0f);
        #endregion

        #region Hooks
        private void Loaded()
        {
            Instance = this;
            try
            {
                _harmony = new HarmonyLib.Harmony("com.Skelee.AwakenQueueManager");
                _harmony.Patch(AccessTools.Method(typeof(ConnectionQueue), "Cycle"), transpiler: new HarmonyMethod(typeof(ManageQueue), nameof(ManageQueue.Transpiler)));
                _harmony.Patch(AccessTools.Method(typeof(ConnectionQueue), "RemoveConnection"), prefix: new HarmonyMethod(typeof(OnQueueLeave), nameof(OnQueueLeave.Prefix)));
                _harmony.Patch(AccessTools.Method(typeof(ConnectionQueue), "Join"), prefix: new HarmonyMethod(typeof(OnQueueJoin), nameof(OnQueueJoin.Prefix)));
            }
            catch (Exception e)
            {
                Puts($"Failed to apply Harmony patches: {e.Message}");
            }

            permission.RegisterPermission("awakenqueuemanager.bypass", this);
            permission.RegisterPermission("awakenqueuemanager.priority", this);
        }

        private void Unload()
        {
            if (_harmony != null)
            {
                _harmony.UnpatchAll("com.Skelee.AwakenQueueManager");
            }
            if (queueManagerCoroutine != null && ServerMgr.Instance != null)
            {
                ServerMgr.Instance.StopCoroutine(queueManagerCoroutine);
            }
            if (fallbackQueueCheckCoroutine != null && ServerMgr.Instance != null)
            {
                ServerMgr.Instance.StopCoroutine(fallbackQueueCheckCoroutine);
            }
            config = null;
            Instance = null;
        }

        private void OnServerInitialized(bool initial)
        {
            queue = ServerMgr.Instance?.connectionQueue;
            if (queue == null)
            {
                Puts("Could not find connection queue instance. Queue management disabled.");
            }
            else if (ServerMgr.Instance != null)
            {
                fallbackQueueCheckCoroutine = ServerMgr.Instance.StartCoroutine(FallbackQueueCheck());
            }
        }

        private void OnPlayerConnected(BasePlayer player)
        {
            if (player == null) return;
            disconnectedPlayers.Remove(player.UserIDString);
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            if (player == null) return;
            disconnectedPlayers[player.UserIDString] = new DisconnectInfo { LeaveTime = UnityEngine.Time.time };
            if (queueManagerCoroutine == null && ServerMgr.Instance != null)
            {
                queueManagerCoroutine = ServerMgr.Instance.StartCoroutine(QueueManager());
            }
        }

        private IEnumerator QueueManager()
        {
            if (config == null)
            {
                Puts("Configuration is null, cannot manage queue.");
                yield break;
            }

            int reconnectTime = Math.Max(1, config.ReconnectTime);
            while (disconnectedPlayers.Count > 0)
            {
                foreach (var (userId, info) in disconnectedPlayers.ToArray())
                {
                    if (UnityEngine.Time.time - info.LeaveTime > reconnectTime * 60)
                    {
                        disconnectedPlayers.Remove(userId);
                    }
                }
                yield return CoroutineEx.waitForSeconds(60f);
            }
            queueManagerCoroutine = null;
        }

        private IEnumerator FallbackQueueCheck()
        {
            while (queue != null)
            {
                try
                {
                    int availableSlots = ManageQueue.CalculateAvailableSlots();
                    if (Instance != null && queue != null && availableSlots > queue.Joining && disconnectedPlayers.Count > 0)
                    {
                        var connection = queue.joining.FirstOrDefault();
                        if (connection != null)
                        {
                            queue.SkipQueue(connection.userid);
                        }
                    }
                }
                catch (Exception e)
                {
                    Puts($"Fallback queue check failed: {e.Message}");
                }
                yield return CoroutineEx.waitForSeconds(1f);
            }
            fallbackQueueCheckCoroutine = null;
        }
        #endregion

        #region Harmony
        private static class ManageQueue
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator)
            {
                var list = instructions.ToList();
                int joinLine = list.FindIndex(i => i.opcode == OpCodes.Call && i.operand is MethodBase mb && mb.Name == "JoinGame" && mb.DeclaringType == typeof(ConnectionQueue));
                if (joinLine == -1)
                {
                    Instance?.Puts("Failed to find JoinGame call in ConnectionQueue.Cycle. Queue management relies on fallback coroutine.");
                    LogILInstructions(list);
                    return list;
                }

                list.InsertRange(joinLine, new[]
                {
                    new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(ManageQueue), nameof(CalculateAvailableSlots))),
                    new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(ManageQueue), nameof(CheckQueue)))
                });

                return list;
            }

            internal static void CheckQueue(int availableSlots)
            {
                if (Instance == null || Instance.queue == null || availableSlots - Instance.queue.Joining <= 0 || Instance.disconnectedPlayers.Count <= 0)
                    return;

                var connection = Instance.queue.joining.FirstOrDefault();
                if (connection != null)
                {
                    Instance.queue.SkipQueue(connection.userid);
                }
            }

            internal static int CalculateAvailableSlots()
            {
                if (BasePlayer.activePlayerList == null)
                {
                    Instance?.Puts("Cannot calculate available slots: activePlayerList is null.");
                    return 0;
                }
                int maxPlayers = ConVar.Server.maxplayers;
                int activePlayers = BasePlayer.activePlayerList.Count;
                int slots = maxPlayers - activePlayers;
                return Math.Max(0, slots);
            }

            private static void LogILInstructions(List<CodeInstruction> instructions)
            {
                var sb = new StringBuilder("[Awaken Queue Manager] ConnectionQueue.Cycle IL Instructions:\n");
                for (int i = 0; i < instructions.Count; i++)
                {
                    var instr = instructions[i];
                    var operand = instr.operand switch
                    {
                        MethodBase mb => $"{mb.DeclaringType?.Name}.{mb.Name}",
                        FieldInfo fi => $"{fi.DeclaringType?.Name}.{fi.Name}",
                        _ => instr.operand?.ToString() ?? "null"
                    };
                    sb.AppendLine($"IL_{i:D4}: {instr.opcode} {operand}");
                }
            }
        }

        private static class OnQueueJoin
        {
            internal static bool Prefix(Network.Connection connection, ConnectionQueue __instance)
            {
                if (connection == null || Instance == null || Instance.queue == null || config == null)
                {
                    Instance?.Puts("Invalid connection or instance during queue join.");
                    return true;
                }

                connection.state = Connection.State.InQueue;
                InsertToQueue(connection, __instance);
                Instance.queue.nextMessageTime = 0f;
                if (CanJumpQueue(connection))
                {
                    Instance.queue.SkipQueue(connection.userid);
                }
                return false;
            }

            internal static void InsertToQueue(Network.Connection connection, ConnectionQueue queue)
            {
                if (connection == null || Instance == null || config == null)
                {
                    Instance?.Puts("Failed to insert connection to queue: null connection or instance.");
                    return;
                }

                string userId = connection.userid.ToString();
                if (Instance.disconnectedPlayers.TryGetValue(userId, out var info) && info.InQueue)
                {
                    int position = info.QueuePosition > queue.Queued ? queue.Queued : info.QueuePosition;
                    queue.queue.Insert(position, connection);
                    Instance.disconnectedPlayers.Remove(userId);
                }
                else if (config.VipPriorityQueue && Instance.permission.UserHasPermission(userId, "awakenqueuemanager.priority"))
                {
                    int prioritySpot = GetPrioritySpot(queue);
                    queue.queue.Insert(prioritySpot, connection);
                }
                else
                {
                    queue.queue.Add(connection);
                }
            }

            internal static int GetPrioritySpot(ConnectionQueue queue)
            {
                if (Instance == null)
                {
                    return queue.Queued;
                }

                int index = 0;
                foreach (var player in queue.queue)
                {
                    if (!Instance.permission.UserHasPermission(player.userid.ToString(), "awakenqueuemanager.priority"))
                        return index;
                    index++;
                }
                return index;
            }

            internal static bool CanJumpQueue(Network.Connection connection)
            {
                if (connection == null || Instance == null)
                {
                    Instance?.Puts("Invalid connection or instance in CanJumpQueue.");
                    return false;
                }
                return Instance.permission.UserHasPermission(connection.userid.ToString(), "awakenqueuemanager.bypass");
            }
        }

        private static class OnQueueLeave
        {
            internal static bool Prefix(ConnectionQueue __instance, Network.Connection connection)
            {
                if (connection == null || Instance == null)
                {
                    Instance?.Puts("Invalid connection or instance during queue leave.");
                    return false;
                }

                if (connection.state != Connection.State.InQueue)
                {
                    return true;
                }

                int index = __instance.queue.IndexOf(connection);
                if (index != -1)
                {
                    Instance.disconnectedPlayers[connection.userid.ToString()] = new DisconnectInfo
                    {
                        InQueue = true,
                        QueuePosition = index,
                        LeaveTime = UnityEngine.Time.time
                    };
                }

                __instance.queue.Remove(connection);
                __instance.joining.Remove(connection);
                connection.state = Connection.State.Disconnected;
                return false;
            }
        }
        #endregion
    }
}









