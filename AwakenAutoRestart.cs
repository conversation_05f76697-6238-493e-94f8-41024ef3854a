using Oxide.Core;
using Oxide.Core.Configuration;
using Oxide.Core.Plugins;
using System;
using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("Awaken Auto Restart", "Skelee", "1.0.0")]
    [Description("Automatically restarts the server at 6 AM every day with configurable warnings")]
    public class AwakenAutoRestart : RustPlugin
    {
        #region Configuration
        private ConfigData config;

        private class ConfigData
        {
            [JsonProperty("Restart Hour (24-hour format, 0-23)")]
            public int RestartHour { get; set; } = 6;

            [JsonProperty("Restart Minute")]
            public int RestartMinute { get; set; } = 0;

            [JsonProperty("Warning Times (minutes before restart)")]
            public List<int> WarningTimes { get; set; } = new List<int> { 60, 30, 15, 10, 5, 3, 2, 1 };

            [JsonProperty("Restart Command")]
            public string RestartCommand { get; set; } = "restart";

            [JsonProperty("Time Zone Offset (hours from UTC, e.g., -8 for PST, +1 for CET)")]
            public int TimeZoneOffset { get; set; } = 0;
        }

        protected override void LoadDefaultConfig()
        {
            config = new ConfigData();
            SaveConfig();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<ConfigData>();
                if (config == null)
                    LoadDefaultConfig();
            }
            catch (Exception ex)
            {
                PrintError("Error loading config: " + ex.Message);
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Variables
        private Timer restartTimer;
        private Timer warningTimer;
        private DateTime nextRestartTime;
        private List<int> pendingWarnings = new List<int>();
        #endregion

        #region Hooks
        private void Init()
        {
            CalculateNextRestartTime();
            ScheduleRestart();
            Puts($"[Auto Restart] Next restart scheduled for: {nextRestartTime:yyyy-MM-dd HH:mm:ss}");
        }

        private void Unload()
        {
            restartTimer?.Destroy();
            warningTimer?.Destroy();
        }
        #endregion

        #region Core Logic
        private void CalculateNextRestartTime()
        {
            DateTime now = DateTime.UtcNow.AddHours(config.TimeZoneOffset);
            DateTime todayRestart = new DateTime(now.Year, now.Month, now.Day, config.RestartHour, config.RestartMinute, 0);

            if (now >= todayRestart)
            {
                // If we've passed today's restart time, schedule for tomorrow
                nextRestartTime = todayRestart.AddDays(1);
            }
            else
            {
                // Schedule for today
                nextRestartTime = todayRestart;
            }

            // Convert back to UTC for timer calculations
            nextRestartTime = nextRestartTime.AddHours(-config.TimeZoneOffset);
        }

        private void ScheduleRestart()
        {
            restartTimer?.Destroy();
            warningTimer?.Destroy();

            TimeSpan timeUntilRestart = nextRestartTime - DateTime.UtcNow;

            if (timeUntilRestart.TotalSeconds <= 0)
            {
                // If somehow we're past the restart time, calculate next one
                CalculateNextRestartTime();
                timeUntilRestart = nextRestartTime - DateTime.UtcNow;
            }

            // Schedule the actual restart
            restartTimer = timer.Once((float)timeUntilRestart.TotalSeconds, () =>
            {
                ExecuteRestart();
            });

            // Schedule warnings
            ScheduleWarnings();

            Puts($"[Auto Restart] Restart scheduled in {timeUntilRestart.Days} days, {timeUntilRestart.Hours} hours, {timeUntilRestart.Minutes} minutes");
        }

        private void ScheduleWarnings()
        {
            pendingWarnings.Clear();
            pendingWarnings.AddRange(config.WarningTimes);
            pendingWarnings.Sort((a, b) => b.CompareTo(a)); // Sort descending

            ScheduleNextWarning();
        }

        private void ScheduleNextWarning()
        {
            if (pendingWarnings.Count == 0) return;

            TimeSpan timeUntilRestart = nextRestartTime - DateTime.UtcNow;
            int nextWarningMinutes = pendingWarnings[0];

            double warningTimeSeconds = timeUntilRestart.TotalSeconds - (nextWarningMinutes * 60);

            if (warningTimeSeconds <= 0)
            {
                // This warning time has passed, remove it and try the next one
                pendingWarnings.RemoveAt(0);
                ScheduleNextWarning();
                return;
            }

            warningTimer = timer.Once((float)warningTimeSeconds, () =>
            {
                SendRestartWarning(nextWarningMinutes);
                pendingWarnings.RemoveAt(0);
                ScheduleNextWarning();
            });
        }

        private void SendRestartWarning(int minutesUntilRestart)
        {
            string message;
            if (minutesUntilRestart >= 60)
            {
                int hours = minutesUntilRestart / 60;
                message = $"<color=#ff6b6b>Server restart in {hours} hour{(hours > 1 ? "s" : "")}!</color>";
            }
            else
            {
                message = $"<color=#ff6b6b>Server restart in {minutesUntilRestart} minute{(minutesUntilRestart > 1 ? "s" : "")}!</color>";
            }

            PrintToChat(message);
            Puts($"[Auto Restart] Warning sent: {minutesUntilRestart} minutes until restart");
        }

        private void ExecuteRestart()
        {
            PrintToChat("<color=#ff6b6b><size=16>SERVER RESTARTING NOW!</size></color>");
            Puts("[Auto Restart] Executing server restart...");

            // Wait a moment for the message to be sent, then restart
            timer.Once(2f, () =>
            {
                rust.RunServerCommand(config.RestartCommand);
            });
        }
        #endregion

        #region Commands
        [ConsoleCommand("autorestart.info")]
        private void InfoCommand(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;

            TimeSpan timeUntilRestart = nextRestartTime - DateTime.UtcNow;
            DateTime localRestartTime = nextRestartTime.AddHours(config.TimeZoneOffset);

            arg.ReplyWith($"[Auto Restart] Next restart: {localRestartTime:yyyy-MM-dd HH:mm:ss} (in {timeUntilRestart.Days}d {timeUntilRestart.Hours}h {timeUntilRestart.Minutes}m)");
        }

        [ConsoleCommand("autorestart.reschedule")]
        private void RescheduleCommand(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;

            CalculateNextRestartTime();
            ScheduleRestart();
            
            TimeSpan timeUntilRestart = nextRestartTime - DateTime.UtcNow;
            DateTime localRestartTime = nextRestartTime.AddHours(config.TimeZoneOffset);

            arg.ReplyWith($"[Auto Restart] Rescheduled! Next restart: {localRestartTime:yyyy-MM-dd HH:mm:ss} (in {timeUntilRestart.Days}d {timeUntilRestart.Hours}h {timeUntilRestart.Minutes}m)");
        }

        [ConsoleCommand("autorestart.test")]
        private void TestCommand(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;

            SendRestartWarning(5);
            arg.ReplyWith("[Auto Restart] Test warning sent!");
        }
        #endregion
    }
}










