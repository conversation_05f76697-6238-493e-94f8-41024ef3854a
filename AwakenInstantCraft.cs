using Newtonsoft.Json;
using System.Collections.Generic;
using Carbon.Core;
using Carbon.Extensions;
using System;
using UnityEngine;

namespace Carbon.Plugins
{
    [Info("Instant Craft", "Abtral", "1.0.0")]
    [Description("Instantly crafts all items for players with permission.")]
    public class InstantCraft : CarbonPlugin
    {
        #region Fields
        private const string PermUse = "instantcraft.use";
        private ConfigData _config;
        private bool _debug = false;
        #endregion

        #region Configuration
        private class ConfigData
        {
            [JsonProperty(PropertyName = "Use Skin ID? (If available)")]
            public bool UseSkinID { get; set; } = true;

            [JsonProperty(PropertyName = "Enable Debug Logging")]
            public bool DebugLogging { get; set; } = false;
        }
        #endregion

        #region Hooks
        protected override void LoadConfig()
        {
            base.LoadConfig();
            _config = Config.ReadObject<ConfigData>();
            
            if (_config == null)
            {
                _config = new ConfigData();
                SaveConfig();
            }
            
            _debug = _config.DebugLogging;
        }

        protected override void SaveConfig() => Config.WriteObject(_config, true);

        private void OnServerInitialized()
        {
            permission.RegisterPermission(PermUse, this);
            
            if (_debug) Log("InstantCraft plugin initialized");
        }

        private object OnItemCraft(ItemCraftTask task, BasePlayer player)
        {
            if (task == null || player == null || task.cancelled)
                return null;

            if (!permission.UserHasPermission(player.UserIDString, PermUse))
                return null;

            if (_debug) Log($"Instant Crafting {task.blueprint.targetItem.shortname} x {task.amount} for {player.displayName}");

            GiveCraftedItems(task, player);
            task.cancelled = true;

            return true;
        }
        #endregion

        #region Methods
        private void GiveCraftedItems(ItemCraftTask task, BasePlayer player)
        {
            int amountToCreate = task.amount * task.blueprint.amountToCreate;
            ulong skinID = _config.UseSkinID ? (ulong)task.skinID : 0;

            Item item = null;
            try
            {
                item = ItemManager.CreateByItemID(task.blueprint.targetItem.itemid, amountToCreate, skinID);
            }
            catch (Exception e)
            {
                LogError($"Exception creating item! ItemID: {task.blueprint.targetItem.itemid}, Amount: {amountToCreate}, SkinID: {skinID}; Exception: {e}");
                return;
            }

            if (item == null)
            {
                LogError($"Failed to create item! ItemID: {task.blueprint.targetItem.itemid}, Amount: {amountToCreate}, SkinID: {skinID}");
                return;
            }

            if (item.hasCondition && task.conditionScale != 1f)
            {
                item.maxCondition *= task.conditionScale;
                item.condition = item.maxCondition;
            }

            if (!player.inventory.GiveItem(item))
            {
                item.Drop(player.transform.position, Vector3.up * 2f);
                if (_debug) LogWarning($"Inventory full. Dropped {task.blueprint.targetItem.shortname} at {player.displayName}'s feet.");
            }
            else
            {
                player.Command("note.inv", item.info.itemid, amountToCreate);
            }
        }

        private void Log(string message)
        {
            if (_debug) Puts(message);
        }
        #endregion
    }
} 