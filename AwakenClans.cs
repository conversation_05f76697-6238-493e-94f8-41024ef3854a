using Facepunch;
using Facepunch.Extend;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Clans", "Skelee", "2.1.0")]
    [Description("Clan plugin for Awaken Servers with ally FF and chat")]
    public class AwakenClans : CovalencePlugin
    {
        #region Defines
        [PluginReference] private Plugin? AwakenClansShare, AwakenActionBlocker;
        public static AwakenClans? Instance;
        private readonly Regex filter = new(@"[^a-zA-Z0-9]");
        private static readonly object False = false;
        private readonly Dictionary<string, BasePlayer> _ids = new Dictionary<string, BasePlayer>();
        private readonly Dictionary<BasePlayer, string> _players = new Dictionary<BasePlayer, string>();
        private readonly Dictionary<string, LeadershipVote> activeLeadershipVotes = new Dictionary<string, LeadershipVote>();
        private readonly Dictionary<string, Timer> voteTimers = new Dictionary<string, Timer>();
        #endregion

        #region ENUMs & Classes
        private enum TeamAction { Create, Join, Leave, Disbanded }

        private class LeadershipVote
        {
            public string ClanName { get; set; } = string.Empty;
            public string Claimant { get; set; } = string.Empty;
            public HashSet<string> VotesFor { get; set; } = new HashSet<string>();
            public HashSet<string> VotesAgainst { get; set; } = new HashSet<string>();
            public DateTime StartTime { get; set; } = DateTime.UtcNow;

            public bool HasVoted(string playerId) => VotesFor.Contains(playerId) || VotesAgainst.Contains(playerId);

            public int TotalVotes => VotesFor.Count + VotesAgainst.Count;

            public bool HasMajority(int totalMembers) => VotesFor.Count > totalMembers / 2;
        }

        public class ClanInfo
        {
            public ulong ClanTeamId { get; set; } = 0;
            public string ClanName { get; set; } = "";
            public string ClanTag { get; set; } = "";
            public string ClanOwner { get; set; } = "";
            public int MaxClanMembers { get; set; } = 16;
            public bool ClanFF { get; set; } = false;
            public List<string> ClanOfficers { get; set; } = new List<string>();
            public Dictionary<string, string> ClanMemebers { get; set; } = new Dictionary<string, string>();
            public List<string> ClanInvites { get; set; } = new List<string>();
            public List<string> ClanAllys { get; set; } = new List<string>();
            public List<string> ClanAllyInvites { get; set; } = new List<string>();
            public Dictionary<string, bool> ClanAllyFF { get; set; } = new Dictionary<string, bool>();
            public List<string> ClanMergeAllys { get; set; } = new List<string>();
            public List<string> ClanMergeAllyInvites { get; set; } = new List<string>();
        }

        private readonly HashSet<ClanInfo> clanCache = new HashSet<ClanInfo>();
        private readonly Dictionary<string, string> playerClansCache = new Dictionary<string, string>();
        #endregion

        #region Config
        private record GeneralSettings(int MaxMemebers = 16, bool EnableAllyCommand = true);
        private record ClanName(int MinChars = 2, int MaxChars = 10, List<string>? BlockedWords = null)
        {
            public ClanName() : this(2, 10, GetDefaultBlockedWords()) { }
        }
        private record ClanTag(int MinChars = 2, int MaxChars = 10, List<string>? BlockedWords = null)
        {
            public ClanTag() : this(2, 10, GetDefaultBlockedWords()) { }
        }

        private static List<string> GetDefaultBlockedWords()
        {
            return new List<string>
            {
                // Racial slurs and terms
                "nigger", "nigga", "nig", "negro", "coon", "spook", "jigaboo", "pickaninny", "sambo", "uncle tom",
                "chink", "gook", "slant", "zipperhead", "nip", "jap", "chinaman", "oriental",
                "spic", "wetback", "beaner", "greaser", "taco", "border bunny", "fence jumper",
                "towelhead", "raghead", "sand nigger", "camel jockey", "terrorist", "bomber", "jihad",
                "kike", "hymie", "sheeny", "yid", "hebe", "jew boy", "oven dodger",
                "cracker", "honkey", "whitey", "peckerwood", "redneck", "hillbilly", "trailer trash",
                "redskin", "injun", "savage", "squaw", "chief", "brave", "firewater",
                "gypsy", "pikey", "tinker", "roma scum",

                // Homophobic slurs
                "faggot", "fag", "fagget", "fagg", "queer", "homo", "dyke", "lesbo", "tranny", "shemale",
                "cocksucker", "buttfucker", "pillow biter", "fudge packer", "fairy", "pansy", "sissy",
                "sodomite", "fruit", "flamer", "poof", "nancy boy", "light in the loafers",

                // General offensive terms
                "retard", "retarded", "mongoloid", "spastic", "cripple", "gimp",
                "whore", "slut", "bitch", "cunt", "pussy", "dick", "cock", "penis", "vagina",
                "fuck", "shit", "damn", "hell", "ass", "asshole", "bastard", "prick",

                // Variations and common misspellings
                "n1gger", "n1gga", "f4ggot", "f4g", "h0mo", "qu33r", "b1tch", "fuk", "fck",
                "sh1t", "a55", "a55hole", "d1ck", "c0ck", "pu55y", "wh0re", "5lut",

                // Numbers/symbols variations
                "n!gger", "f@ggot", "h0m0", "b!tch", "sh!t", "a$$", "d!ck", "c0ck",

                // Admin/server related
                "admin", "moderator", "owner", "staff", "official", "server", "rust", "oxide"
            };
        }

        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("General Clan Settings")] GeneralSettings? ClanSettings = null,
            [property: JsonProperty("Clan Name Settings")] ClanName? ClanName = null,
            [property: JsonProperty("Clan Tag Settings")] ClanTag? ClanTag = null)
        {
            public Configuration() : this(new GeneralSettings(), new ClanName(), new ClanTag()) { }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Language
        protected override void LoadDefaultMessages() => lang.RegisterMessages(new Dictionary<string, string>
        {
            ["HelpText"] = "<size=14><color=orange>Awaken Clans Commands:</color></size>\n<color=#00FFFF>/clan</color> - Displays all your clan information.\n<color=#00FFFF>/clan create <clan name></color> - Creates a clan with specified name\n<color=#00FFFF>/clan join <clan name></color> - Attempts to join a clan by name, requires an invite.\n<color=#00FFFF>/clan leave</color> - Leaves your current clan.\n<color=#00FFFF>/a <message></color> - Sends a message to allied clans.",
            ["ModeratorHelpText"] = "\n\n<size=14><color=#ffa500>Officer</color> commands:</size>\n<color=#00FFFF>/clan invite <playername|playerid></color> - Invites a player to the clan by name or ID.\n<color=#00FFFF>/clan kick <playername|playerid></color> - Kicks a player from the clan.\n<color=#00FFFF>/clan ally invite <clanname></color> - Invites another clan to ally.\n<color=#00FFFF>/clan ally accept <clanname></color> - Accepts an ally invite.\n<color=#00FFFF>/clan ally reject <clanname></color> - Rejects an ally invite.\n<color=#00FFFF>/clan ally remove <clanname></color> - Removes an ally.\n<color=#00FFFF>/clan ally ff <clanname></color> - Toggles friendly fire with an allied clan.\n<color=#00FFFF>/clan ally merge <clanname></color> - Invites another clan to merge ally (for raiding together).\n<color=#00FFFF>/clan ally mergeaccept <clanname></color> - Accepts a merge ally invite.\n<color=#00FFFF>/clan ally mergereject <clanname></color> - Rejects a merge ally invite.\n<color=#00FFFF>/clan ally mergeremove <clanname></color> - Removes a merge ally.",
            ["OwnerHelpText"] = "\n\n<size=14><color=#ffa500>Owner</color> commands:</size>\n<color=#00FFFF>/clan tag <tag></color> - Changes the clan tag.\n<color=#00FFFF>/clan promote <playername></color> - Promotes a player to officer.\n<color=#00FFFF>/clan demote <playername></color> - Demotes an officer.\n<color=#00FFFF>/clan disband</color> - Deletes the clan permanently.\n<color=#00FFFF>/clan ff</color> - Toggles friendly fire within the clan.",
            ["ClanInfo"] = "<size=14><color=orange>{3} Info:</color></size>\n<color=#e0e0e0>Clan Name and Count:</color> <color=#aaff55>{0}</color> ({1}/{2})\n<color=#e0e0e0>Clan Tag:</color> {3}\n<color=#e0e0e0>Clan Owner:</color> {4}\n<color=#e0e0e0>Clan Officers:</color> {5}\n<color=#e0e0e0>Clan Members:</color> {6}\n<color=#e0e0e0>Clan FF:</color> {7}\n<color=#e0e0e0>Clan Allies:</color> {8}\n<color=#e0e0e0>Clan Ally Invites:</color> {9}\n<color=#e0e0e0>Merge Allies:</color> {10}\n<color=#e0e0e0>Merge Ally Invites:</color> {11}\n<color=#e0e0e0>Use /clan for commands.</color>",
            ["ClanTagTooShort"] = "<color=#ffa500>Awaken Clans</color> Clan tag is too short, minimum {0} characters.",
            ["ClanTagTooLong"] = "<color=#ffa500>Awaken Clans</color> Clan tag is too long, maximum {0} characters.",
            ["AlreadyInClan"] = "<color=#ffa500>Awaken Clans</color> You cannot create a clan while already in one.",
            ["NotInClan"] = "<color=#ffa500>Awaken Clans</color> You are not a member of a clan.",
            ["NotClanOwner"] = "<color=#ffa500>Awaken Clans</color> This action requires clan owner privileges.",
            ["CouldNotPerformAction"] = "<color=#ffa500>Awaken Clans</color> Failed to update in-game team!",
            ["ErrorJoiningClan"] = "<color=#ffa500>Awaken Clans</color> Error joining the clan, please try again!",
            ["JoinedClan"] = "<color=#ffa500>Awaken Clans</color> You have joined {0}.",
            ["PlayerJoinedClan"] = "<color=#ffa500>Awaken Clans</color> {0} has joined the clan, welcome them!",
            ["LeftClan"] = "<color=#ffa500>Awaken Clans</color> You have left {0}.",
            ["PlayerLeftClan"] = "<color=#ffa500>Awaken Clans</color> {0} has left the clan.",
            ["CreatedClan"] = "<color=#ffa500>Awaken Clans</color> You have created clan '{0}'.",
            ["ClanNameTaken"] = "<color=#ffa500>Awaken Clans</color> A clan with that name already exists!",
            ["ClanTagTaken"] = "<color=#ffa500>Awaken Clans</color> A clan with that tag already exists!",
            ["ClanNameMinMax"] = "<color=#ffa500>Awaken Clans</color> Clan name must be {0}-{1} characters long.",
            ["ClanTagMinMax"] = "<color=#ffa500>Awaken Clans</color> Clan tag must be {0}-{1} characters long.",
            ["BlockedWord"] = "<color=#ffa500>Awaken Clans</color> You used a blocked word, try again.",
            ["DisbandedClan"] = "<color=#ffa500>Awaken Clans</color> Clan leader has disbanded the clan.",
            ["CantKickOwner"] = "<color=#ffa500>Awaken Clans</color> You cannot kick the clan owner.",
            ["KickedFromClan"] = "<color=#ffa500>Awaken Clans</color> You have been kicked from the clan.",
            ["PlayerKickedFromClan"] = "<color=#ffa500>Awaken Clans</color> {0} has been kicked from the clan.",
            ["InvitedToClan"] = "<color=#ffa500>Awaken Clans</color> You have been invited to {0}. Join with /clan join '{0}'.",
            ["PlayerInvitedToClan"] = "<color=#ffa500>Awaken Clans</color> {0} has been invited to the clan.",
            ["SetClanTag"] = "<color=#ffa500>Awaken Clans</color> Clan tag set to {0}.",
            ["MustDisband"] = "<color=#ffa500>Awaken Clans</color> As clan owner, you must disband with /clan disband to leave.",
            ["PleaseUseCommand"] = "<color=#ffa500>Awaken Clans</color> Create a clan with /clan create <clan name>",
            ["NotEnoughArgs"] = "<color=#ffa500>Awaken Clans</color> Missing arguments, use /clan help",
            ["PlayerOnline"] = "<color=#ffa500>Awaken Clans</color> {0} is now online.",
            ["PlayerOffline"] = "<color=#ffa500>Awaken Clans</color> {0} has gone offline.",
            ["NotInvited"] = "<color=#ffa500>Awaken Clans</color> You have not been invited to this clan yet.",
            ["ClanFull"] = "<color=#ffa500>Awaken Clans</color> The clan is currently full.",
            ["ClanDontExist"] = "<color=#ffa500>Awaken Clans</color> That clan does not exist, use the clan name, not tag.",
            ["PlayerAlreadyInvited"] = "<color=#ffa500>Awaken Clans</color> Player is already invited.",
            ["PlayerAlreadyInAClan"] = "<color=#ffa500>Awaken Clans</color> The invited player is already in a clan.",
            ["CantPromoteOwner"] = "<color=#ffa500>Awaken Clans</color> {0} is the owner and cannot be promoted.",
            ["AlreadyAOfficer"] = "<color=#ffa500>Awaken Clans</color> {0} is already an officer.",
            ["PromotedToOfficer"] = "<color=#ffa500>Awaken Clans</color> {0} has been promoted to officer.",
            ["NotAnOfficer"] = "<color=#ffa500>Awaken Clans</color> {0} is not an officer.",
            ["RemovedAsOfficer"] = "<color=#ffa500>Awaken Clans</color> {0} has been demoted from officer.",
            ["FriendlyFireOn"] = "<color=#ffa500>Awaken Clans</color> Clan leader has turned <color=green>on</color> friendly fire.",
            ["FriendlyFireOff"] = "<color=#ffa500>Awaken Clans</color> Clan leader has turned <color=red>off</color> friendly fire.",
            ["NoPermission"] = "<color=#ffa500>Awaken Clans</color> You lack permission for this command.",
            ["CantFindClan"] = "<color=#ffa500>Awaken Clans</color> Could not find a clan with that tag.",
            ["AllyInviteAlreadySent"] = "<color=#ffa500>Awaken Clans</color> Your clan already sent an ally invite to {0}.",
            ["AllyInviteAlreadyReceived"] = "<color=#ffa500>Awaken Clans</color> Your clan already has an invite from {0}.",
            ["AllyInviteSent"] = "<color=#ffa500>Awaken Clans</color> {0} has sent {1} an ally invite.",
            ["AllyInviteRecieved"] = "<color=#ffa500>Awaken Clans</color> You received an ally invite from {0}.",
            ["AllyAccepted"] = "<color=#ffa500>Awaken Clans</color> You are now allies with {0}.",
            ["AllyRejected"] = "<color=#ffa500>Awaken Clans</color> {0} has rejected {1}'s ally request.",
            ["AllyRejectedTo"] = "<color=#ffa500>Awaken Clans</color> {0} has rejected your ally request.",
            ["NoAllyInvite"] = "<color=#ffa500>Awaken Clans</color> No ally invite from {0}.",
            ["NotAlly"] = "<color=#ffa500>Awaken Clans</color> {0} is not your ally.",
            ["AllyRemoved"] = "<color=#ffa500>Awaken Clans</color> {0} is no longer an ally.",
            ["AllyPlayer"] = "<color=#ffa500>Awaken Clans</color> You shot an ally player; no damage dealt.",
            ["ClanPlayer"] = "<color=#ffa500>Awaken Clans</color> You shot a clan member; no damage dealt.",
            ["CantInviteYourself"] = "<color=#ffa500>Awaken Clans</color> You cannot invite yourself.",
            ["CantAllySelf"] = "<color=#ffa500>Awaken Clans</color> You cannot ally your own clan.",
            ["InvalidCharacters"] = "<color=#ffa500>Awaken Clans</color> Contains illegal characters, try again.",
            ["MaximumMembers"] = "<color=#ffa500>Awaken Clans</color> Clan is at maximum members; cannot invite more.",
            ["CouldNotFindPlayer"] = "<color=#ffa500>Awaken Clans</color> Could not find player with that name.",
            ["AllyFriendlyFireOn"] = "<color=#ffa500>Awaken Clans</color> Friendly fire with ally {0} is now <color=green>enabled</color>.",
            ["AllyFriendlyFireOff"] = "<color=#ffa500>Awaken Clans</color> Friendly fire with ally {0} is now <color=red>disabled</color>.",
            ["AllyChatMessage"] = "<color=#ffa500>[Ally Chat] {0} ({1})</color>: {2}",
            ["NoAlliesForChat"] = "<color=#ffa500>Awaken Clans</color> Your clan has no allies to chat with.",
            ["InvalidAlly"] = "<color=#ffa500>Awaken Clans</color> {0} is not an allied clan.",
            ["AllyFFUsage"] = "<color=#ffa500>Awaken Clans</color> Usage: /clan ally ff <clanname>",
            ["LeadershipClaimStarted"] = "<color=#ffa500>Awaken Clans</color> {0} has started a vote to claim leadership of the clan. Use /clan vote yes or /clan vote no to vote. Voting ends in 10 minutes.",
            ["LeadershipClaimAlreadyActive"] = "<color=#ffa500>Awaken Clans</color> There is already an active leadership vote in progress.",
            ["LeadershipClaimNotActive"] = "<color=#ffa500>Awaken Clans</color> There is no active leadership vote in progress.",
            ["LeadershipClaimVoted"] = "<color=#ffa500>Awaken Clans</color> You have voted {0} on the leadership claim.",
            ["LeadershipClaimAlreadyVoted"] = "<color=#ffa500>Awaken Clans</color> You have already voted on this leadership claim.",
            ["LeadershipClaimSuccess"] = "<color=#ffa500>Awaken Clans</color> The vote has passed! {0} is now the leader of the clan.",
            ["LeadershipClaimFailed"] = "<color=#ffa500>Awaken Clans</color> The leadership vote has failed. Not enough votes in favor.",
            ["LeadershipClaimCancelled"] = "<color=#ffa500>Awaken Clans</color> The leadership vote has been cancelled.",
            ["LeadershipClaimStatus"] = "<color=#ffa500>Awaken Clans</color> Leadership vote status: {0} votes for, {1} votes against. Need {2} votes to pass.",
            ["LeadershipClaimTimeRemaining"] = "<color=#ffa500>Awaken Clans</color> Time remaining: {0} minutes.",
            ["CannotClaimLeadership"] = "<color=#ffa500>Awaken Clans</color> You cannot claim leadership as you are already the leader.",
            ["OwnerOnlineCannotClaim"] = "<color=#ffa500>Awaken Clans</color> The current clan leader is online. You cannot claim leadership at this time.",
            ["MergeAllyInviteSent"] = "<color=#ffa500>Awaken Clans</color> {0} has sent {1} a merge ally invite.",
            ["MergeAllyInviteReceived"] = "<color=#ffa500>Awaken Clans</color> You received a merge ally invite from {0}.",
            ["MergeAllyAccepted"] = "<color=#ffa500>Awaken Clans</color> You are now merge allies with {0}.",
            ["MergeAllyRejected"] = "<color=#ffa500>Awaken Clans</color> {0} has rejected {1}'s merge ally request.",
            ["MergeAllyRejectedTo"] = "<color=#ffa500>Awaken Clans</color> {0} has rejected your merge ally request.",
            ["MergeAllyRemoved"] = "<color=#ffa500>Awaken Clans</color> {0} is no longer a merge ally.",
            ["NoMergeAllyInvite"] = "<color=#ffa500>Awaken Clans</color> No merge ally invite from {0}.",
            ["NotMergeAlly"] = "<color=#ffa500>Awaken Clans</color> {0} is not your merge ally.",
            ["MergeAllyInviteAlreadySent"] = "<color=#ffa500>Awaken Clans</color> Your clan already sent a merge ally invite to {0}.",
            ["MergeAllyInviteAlreadyReceived"] = "<color=#ffa500>Awaken Clans</color> Your clan already has a merge ally invite from {0}.",
            ["AlreadyMergeAlly"] = "<color=#ffa500>Awaken Clans</color> {0} is already a merge ally.",
            ["MergeAllyPlayer"] = "<color=#ffa500>Awaken Clans</color> You shot a merge ally player; no damage dealt.",
            ["AllyCommandDisabled"] = "<color=#ffa500>Awaken Clans</color> Regular ally commands are disabled. Use merge ally commands instead.",
            ["AllyCommandDisabledHelp"] = "<color=#ffa500>Awaken Clans</color> Regular ally commands are disabled. Available commands: merge, mergeaccept, mergereject, mergeremove.",
            ["ClanNamesCleanupComplete"] = "<color=#ffa500>Awaken Clans</color> Cleanup complete! Processed {0} clans and updated {1} member names to remove Steam IDs."
        }, this);

        public void SendMessage(BasePlayer? player, string langKey, params object[] args)
        {
            if (player?.IsConnected == true)
            {
                player.ChatMessage(string.Format(lang.GetMessage(langKey, this, player.UserIDString), args));
            }
        }

        private void SendHelpMessage(BasePlayer player)
        {
            if (player.IsConnected)
            {
                var sb = new StringBuilder(lang.GetMessage("HelpText", this, player.UserIDString));
                var clan = GetClan(player.userID);
                if (clan != null)
                {
                    sb.Append("\n\n<size=14><color=#ffa500>Member</color> commands:</size>\n");
                    sb.Append("<color=#00FFFF>/clan claim</color> - Start a vote to claim leadership of the clan.\n");
                    sb.Append("<color=#00FFFF>/clan vote yes|no</color> - Vote on an active leadership claim.\n");
                    sb.Append("<color=#00FFFF>/clan votestatus</color> - Check the status of an active leadership vote.\n");

                    if (IsOfficer(player.UserIDString) || clan.ClanOwner == player.UserIDString)
                    {
                        var moderatorHelp = lang.GetMessage("ModeratorHelpText", this, player.UserIDString);

                        // Add ally command info based on config
                        bool allyCommandEnabled = config?.ClanSettings?.EnableAllyCommand ?? true;
                        if (!allyCommandEnabled)
                        {
                            moderatorHelp += "\n<color=#ff6b6b>Note:</color> Regular ally commands are disabled. Only merge ally commands are available.";
                        }

                        sb.Append(moderatorHelp);
                    }

                    if (clan.ClanOwner == player.UserIDString)
                        sb.Append(lang.GetMessage("OwnerHelpText", this, player.UserIDString));
                }
                player.ChatMessage(sb.ToString());
            }
        }

        private void SendClanInfoMessage(BasePlayer player, ClanInfo? clan, bool tp = false) =>
            player.ChatMessage(clan == null ? lang.GetMessage("NotInClan", this, player.UserIDString) :
                string.Format(lang.GetMessage("ClanInfo", this, player.UserIDString), clan.ClanName, clan.ClanMemebers.Count, clan.MaxClanMembers, $"<color=#a1ff46>{clan.ClanTag}</color>",
                    $"<color=#b573ff>{(covalence.Players.FindPlayer(clan.ClanOwner)?.Name ?? clan.ClanOwner)}</color>", GetClanOfficers(clan), GetClanMembers(clan),
                    clan.ClanFF ? "<color=#aaff55>Enabled</color>" : "<color=#ce422b>Disabled</color>", GetClanAllys(clan), tp ? "<color=#fcf5cb>Not Available</color>" : GetClanAllyInvites(player),
                    GetClanMergeAllys(clan), tp ? "<color=#fcf5cb>Not Available</color>" : GetClanMergeAllyInvites(player)));

        private void SendMessageToClan(Dictionary<string, string> members, string langKey, params object[] args) =>
            members.Keys.Select(BasePlayer.Find).Where(p => p != null).ToList().ForEach(p => SendMessage(p, langKey, args));

        private string GetClanOfficers(ClanInfo clan) => clan.ClanOfficers.Count == 0 ? "<color=#ce422b>None</color>" :
            string.Join(", ", clan.ClanOfficers.Select(id => covalence.Players.FindPlayer(id)?.Name ?? id));

        private string GetClanMembers(ClanInfo clan) => clan.ClanMemebers.Count == 0 ? "<color=#ce422b>None</color>" :
            string.Join(", ", clan.ClanMemebers.Values);

        private string GetClanAllys(ClanInfo clan) => clan.ClanAllys.Count == 0 ? "<color=#ce422b>None</color>" :
            string.Join(", ", clan.ClanAllys);

        private string GetClanAllyInvites(BasePlayer player)
        {
            var clan = GetClan(player.userID);
            if (clan == null) return "<color=#ce422b>None</color>";
            return clan.ClanAllyInvites.Count == 0 ? "<color=#ce422b>None</color>" :
                string.Join(", ", clan.ClanAllyInvites);
        }

        private string GetClanMergeAllys(ClanInfo clan) => clan.ClanMergeAllys.Count == 0 ? "<color=#ce422b>None</color>" :
            string.Join(", ", clan.ClanMergeAllys.Select(name => $"<color=#ff9500>{name}</color>"));

        private string GetClanMergeAllyInvites(BasePlayer player)
        {
            var clan = GetClan(player.userID);
            if (clan == null) return "<color=#ce422b>None</color>";
            return clan.ClanMergeAllyInvites.Count == 0 ? "<color=#ce422b>None</color>" :
                string.Join(", ", clan.ClanMergeAllyInvites.Select(name => $"<color=#ff9500>{name}</color>"));
        }
        #endregion

        #region Data
        private ClanInfo? GetClanData(string clanName) => clanCache.FirstOrDefault(c => c?.ClanName.Equals(clanName, StringComparison.OrdinalIgnoreCase) == true);

        private void DeleteClan(string clanName)
        {
            var path = Path.Combine(Interface.Oxide.DataDirectory, $"Clans/{clanName}.json");
            if (File.Exists(path)) File.Delete(path);

            // Also remove from cache and clear player cache entries
            var clanToRemove = clanCache.FirstOrDefault(c => c.ClanName.Equals(clanName, StringComparison.OrdinalIgnoreCase));
            if (clanToRemove != null)
            {
                // Clear player cache entries for all members
                foreach (var memberId in clanToRemove.ClanMemebers.Keys)
                {
                    playerClansCache.Remove(memberId);
                }
                clanCache.Remove(clanToRemove);
            }
        }

        private void GetClans()
        {
            var dir = Path.Combine(Interface.Oxide.DataDirectory, "Clans");
            if (!Directory.Exists(dir)) return;

            foreach (var file in Directory.GetFiles(dir, "*.json"))
            {
                var clanName = Path.GetFileNameWithoutExtension(file);
                if (Interface.Oxide.DataFileSystem.ReadObject<ClanInfo>($"Clans/{clanName}") is { } clanObj)
                    clanCache.Add(clanObj);
            }
        }

        private void SaveClanData(ClanInfo? clan)
        {
            if (clan != null)
            {
                Interface.Oxide.DataFileSystem.WriteObject($"Clans/{clan.ClanName}", clan);

                // Invalidate cache for all clan members to ensure consistency
                if (clan.ClanMemebers != null)
                {
                    foreach (var memberId in clan.ClanMemebers.Keys)
                    {
                        playerClansCache.Remove(memberId);
                    }
                }
            }
        }
        #endregion

        #region Word Filtering
        private bool ContainsBlockedWord(string input, List<string>? blockedWords)
        {
            if (blockedWords == null || blockedWords.Count == 0 || string.IsNullOrEmpty(input))
                return false;

            // Normalize the input text for checking
            string normalizedInput = NormalizeText(input);

            foreach (string blockedWord in blockedWords)
            {
                string normalizedBlockedWord = NormalizeText(blockedWord);

                // Check if the normalized input contains the normalized blocked word
                if (normalizedInput.Contains(normalizedBlockedWord, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        private string NormalizeText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Convert to lowercase
            text = text.ToLower();

            // Remove spaces and common separators
            text = text.Replace(" ", "").Replace("-", "").Replace("_", "").Replace(".", "");

            // Replace common character substitutions
            var substitutions = new Dictionary<char, char>
            {
                {'0', 'o'}, {'1', 'i'}, {'3', 'e'}, {'4', 'a'}, {'5', 's'}, {'7', 't'}, {'8', 'b'},
                {'@', 'a'}, {'!', 'i'}, {'$', 's'}, {'+', 't'}, {'(', 'c'}, {')', 'c'},
                {'[', 'c'}, {']', 'c'}, {'{', 'c'}, {'}', 'c'}, {'<', 'c'}, {'>', 'c'}
            };

            foreach (var substitution in substitutions)
            {
                text = text.Replace(substitution.Key, substitution.Value);
            }

            return text;
        }
        #endregion

        #region Hooks
        private void Loaded()
        {
            Instance = this;
            GetClans();
            AddCovalenceCommand("clan", nameof(ClanCMD));
            AddCovalenceCommand("a", nameof(AllyChatCMD));

            // Admin command to clean clan names
            AddCovalenceCommand("clan.cleannames", nameof(CleanClanNamesCMD));

            // Clean existing clan data on load
            timer.Once(2f, () => {
                try
                {
                    CleanAllClanMemberNamesInternal();
                    Puts("[Awaken Clans] Cleaned existing clan member names on plugin load.");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Clans] Error cleaning clan names on load: {ex.Message}");
                }
            });

            // Start a timer to clean display names - but only for clan members
            timer.Every(5f, CleanClanMemberDisplayNames);
        }

        private void Unload()
        {
            config = null;
            foreach (var p in BasePlayer.activePlayerList)
                OnPlayerDisconnected(p, "");
        }

        private void OnServerInitialized()
        {
            try
            {
                // Clear existing teams safely
                foreach (var player in BasePlayer.allPlayerList.Where(p => p != null && p.IsValid()))
                {
                    try
                    {
                        player.ClearTeam();
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[Awaken Clans] Error clearing team for player {player.displayName}: {ex.Message}");
                    }
                }

                // Reset relationship manager safely
                try
                {
                    RelationshipManager.ServerInstance.playerToTeam.Clear();
                    RelationshipManager.ServerInstance.teams.Clear();
                    RelationshipManager.ServerInstance.lastTeamIndex = 1;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Clans] Error resetting teams: {ex.Message}");
                }

                // Recreate teams for existing clans with proper error handling
                foreach (var cInfo in clanCache)
                {
                    try
                    {
                        CreateTeamForClan(cInfo);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[Awaken Clans] Error creating team for clan {cInfo.ClanName}: {ex.Message}");
                    }
                }

                // Process connected players
                foreach (var player in BasePlayer.activePlayerList.Where(p => p != null && p.IsValid()))
                {
                    try
                    {
                        OnPlayerConnected(player);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[Awaken Clans] Error processing connected player {player.displayName}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Critical error during initialization: {ex.Message}\n{ex.StackTrace}");
            }
        }

        private void OnPlayerConnected(BasePlayer player)
        {
            SetPlayerID(player);
            if (GetClan(player.userID) is { } clan)
            {
                // Update player display name with clan tag for game UI (team UI, player list, etc.)
                RenamePlayer(player, clan);
                ManageIngameTeam(player, TeamAction.Join);

                // Removed online message to reduce chat spam
            }
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            if (GetClan(player.userID) is { } clan)
            {
                playerClansCache.Remove(player.UserIDString);
                // Removed offline message to reduce chat spam
            }
        }

        private object? OnTeamCreate(BasePlayer player)
        {
            SendMessage(player, "PleaseUseCommand");
            return false;
        }

        private object? OnTeamLeave(RelationshipManager.PlayerTeam team, BasePlayer player)
        {
            LeaveClan(player);
            return false;
        }

        private object? OnTeamKick(RelationshipManager.PlayerTeam team, BasePlayer player, ulong target)
        {
            KickPlayer(player, target.ToString());
            return false;
        }

        private object? OnTeamInvite(BasePlayer inviter, BasePlayer target)
        {
            // Use clean name instead of potentially contaminated displayName
            var cleanTargetName = !string.IsNullOrEmpty(target._name) ? target._name : "Unknown";
            InvitePlayer(inviter, cleanTargetName);
            return false;
        }

        private object? OnTeamAcceptInvite(RelationshipManager.PlayerTeam team, BasePlayer player)
        {
            JoinClan(player, team.teamLeader);
            return false;
        }

        private object? OnTeamRejectInvite(BasePlayer rejector, RelationshipManager.PlayerTeam team) => RejectInvite(rejector, team.teamLeader.ToString());

        // TEMPORARILY DISABLED - Testing if this hook is causing issues
        /*
        // Prevent team system from automatically adding Steam IDs to player names (but allow clan tags)
        private object? OnPlayerDisplayNameUpdate(BasePlayer player, string newName)
        {
            // If the team system is trying to modify the display name, only prevent Steam IDs
            if (player != null && !string.IsNullOrEmpty(player._name))
            {
                // Check if Steam ID was added to the name
                if (System.Text.RegularExpressions.Regex.IsMatch(newName, @"76561198\d{9}"))
                {
                    // Steam ID detected, clean it but preserve clan tags
                    var cleanedName = RemoveSteamIdFromName(newName);

                    // Ensure clan tag is present for team UI
                    var clan = GetClan(player.userID);
                    if (clan != null && !string.IsNullOrEmpty(clan.ClanTag))
                    {
                        var baseName = RemoveAllTagsFromName(cleanedName);
                        cleanedName = $"[{clan.ClanTag}] {baseName}";
                    }

                    player.displayName = cleanedName;
                    return false;
                }
            }
            return null;
        }
        */

        private void OnNewSave(string filename) => Directory.Delete(Path.Combine(Interface.Oxide.DataDirectory, "Clans"), true);

        private object? OnEntityTakeDamage(BasePlayer victim, HitInfo info)
        {
            try
            {
                if (victim == null || info?.InitiatorPlayer == null) return null;

                var attacker = info.InitiatorPlayer;
                if (attacker == victim) return null; // Self damage

                var attackerClan = GetClan(attacker.userID);
                var victimClan = GetClan(victim.userID);

                if (attackerClan == null || victimClan == null) return null;

                // Same clan check
                if (attackerClan.ClanName == victimClan.ClanName)
                {
                    if (!attackerClan.ClanFF)
                    {
                        SendMessage(attacker, "ClanPlayer");
                        return true; // Block damage
                    }
                    return null; // Allow damage (FF enabled)
                }

                // Regular ally check
                if (attackerClan.ClanAllys.Contains(victimClan.ClanName))
                {
                    bool allyFF = attackerClan.ClanAllyFF.TryGetValue(victimClan.ClanName, out var ff) && ff;
                    if (!allyFF)
                    {
                        SendMessage(attacker, "AllyPlayer");
                        return true; // Block damage
                    }
                    return null; // Allow damage (ally FF enabled)
                }

                // Merge ally check - always block damage
                if (attackerClan.ClanMergeAllys.Contains(victimClan.ClanName))
                {
                    SendMessage(attacker, "MergeAllyPlayer");
                    return true; // Always block damage for merge allies
                }

                return null; // Allow damage
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in OnEntityTakeDamage: {ex.Message}");
                return null;
            }
        }

        private void OnTeamUpdate(RelationshipManager.PlayerTeam team)
        {
            try
            {
                var clan = clanCache.FirstOrDefault(c => c.ClanTeamId == team.teamID);
                if (clan == null) return;

                SyncTeamWithClan(team, clan);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in OnTeamUpdate: {ex.Message}");
            }
        }

        private void SyncTeamWithClan(RelationshipManager.PlayerTeam team, ClanInfo clan)
        {
            bool changed = false;

            foreach (var memberId in team.members)
            {
                string memberIdStr = memberId.ToString();
                if (!clan.ClanMemebers.ContainsKey(memberIdStr))
                {
                    // Try to get the clean player name without Steam ID
                    string memberName = GetCleanPlayerNameById(memberIdStr);
                    clan.ClanMemebers[memberIdStr] = memberName;
                    changed = true;
                }
            }

            foreach (var memberIdStr in clan.ClanMemebers.Keys.ToList())
            {
                if (ulong.TryParse(memberIdStr, out var memberId) && !team.members.Contains(memberId))
                {
                    clan.ClanMemebers.Remove(memberIdStr);
                    changed = true;
                }
            }

            if (changed)
            {
                SaveClanData(clan);
            }
        }
        #endregion

        #region API Methods
        /// <summary>
        /// Gets the clan information for a player. This is the primary API method.
        /// </summary>
        /// <param name="playerId">The player's Steam ID</param>
        /// <returns>ClanInfo object if player is in a clan, null otherwise</returns>
        public ClanInfo? GetClan(ulong playerId)
        {
            try
            {
                if (playerId == 0) return null;

                string playerIdStr = playerId.ToString();

                // First check cache for performance
                if (playerClansCache.TryGetValue(playerIdStr, out var cachedClanName))
                {
                    var cachedClan = GetClanData(cachedClanName);
                    if (cachedClan != null && cachedClan.ClanMemebers.ContainsKey(playerIdStr))
                    {
                        return cachedClan;
                    }
                    else
                    {
                        // Cache is stale, remove it
                        playerClansCache.Remove(playerIdStr);
                    }
                }

                // Search through all clans if not in cache or cache was stale
                foreach (var clan in clanCache.ToList()) // ToList() to avoid modification during iteration
                {
                    if (clan?.ClanMemebers?.ContainsKey(playerIdStr) == true)
                    {
                        // Update cache
                        playerClansCache[playerIdStr] = clan.ClanName;
                        return clan;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClan API: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Checks if a player is in any clan
        /// </summary>
        /// <param name="playerId">The player's Steam ID</param>
        /// <returns>True if player is in a clan, false otherwise</returns>
        public bool IsInClan(ulong playerId)
        {
            try
            {
                return GetClan(playerId) != null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in IsInClan API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if two players are in the same clan
        /// </summary>
        /// <param name="playerId1">First player's Steam ID</param>
        /// <param name="playerId2">Second player's Steam ID</param>
        /// <returns>True if both players are in the same clan, false otherwise</returns>
        public bool SameClan(ulong playerId1, ulong playerId2)
        {
            try
            {
                if (playerId1 == 0 || playerId2 == 0 || playerId1 == playerId2) return false;

                var clan1 = GetClan(playerId1);
                var clan2 = GetClan(playerId2);

                return clan1 != null && clan2 != null &&
                       !string.IsNullOrEmpty(clan1.ClanName) &&
                       clan1.ClanName.Equals(clan2.ClanName, StringComparison.OrdinalIgnoreCase);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in SameClan API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets all members of a player's clan
        /// </summary>
        /// <param name="playerId">The player's Steam ID</param>
        /// <returns>Dictionary of member IDs and names, empty if not in clan</returns>
        public Dictionary<string, string> GetClanMemebers(ulong playerId)
        {
            try
            {
                var clan = GetClan(playerId);
                return clan?.ClanMemebers != null ?
                    new Dictionary<string, string>(clan.ClanMemebers) :
                    new Dictionary<string, string>();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanMemebers API: {ex.Message}");
                return new Dictionary<string, string>();
            }
        }

        /// <summary>
        /// Gets the clan tag for a player
        /// </summary>
        /// <param name="playerId">The player's Steam ID</param>
        /// <returns>Clan tag string, empty if not in clan</returns>
        public string GetClanTag(ulong playerId)
        {
            try
            {
                var clan = GetClan(playerId);
                return clan?.ClanTag ?? string.Empty;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanTag API: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the clan name from a clan object
        /// </summary>
        /// <param name="clan">ClanInfo object or compatible object</param>
        /// <returns>Clan name string, empty if invalid</returns>
        public string GetClanName(object clan)
        {
            try
            {
                return (clan as ClanInfo)?.ClanName ?? string.Empty;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanName API: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the clan name for a player
        /// </summary>
        /// <param name="playerId">The player's Steam ID</param>
        /// <returns>Clan name string, empty if not in clan</returns>
        public string GetClanName(ulong playerId)
        {
            try
            {
                var clan = GetClan(playerId);
                return clan?.ClanName ?? string.Empty;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanName API: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// Checks if a player is the owner of their clan
        /// </summary>
        /// <param name="playerId">The player's Steam ID as string</param>
        /// <returns>True if player is clan owner, false otherwise</returns>
        public bool IsClanOwner(string playerId)
        {
            try
            {
                if (string.IsNullOrEmpty(playerId)) return false;

                // Use GetClan for better performance and cache utilization
                if (ulong.TryParse(playerId, out var playerIdUlong))
                {
                    var clan = GetClan(playerIdUlong);
                    return clan?.ClanOwner == playerId;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in IsClanOwner API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if a player is an officer or owner of their clan
        /// </summary>
        /// <param name="playerId">The player's Steam ID as string</param>
        /// <returns>True if player is officer or owner, false otherwise</returns>
        public bool IsOfficer(string playerId)
        {
            try
            {
                if (string.IsNullOrEmpty(playerId)) return false;

                // Use GetClan for better performance and cache utilization
                if (ulong.TryParse(playerId, out var playerIdUlong))
                {
                    var clan = GetClan(playerIdUlong);
                    return clan != null && (clan.ClanOfficers?.Contains(playerId) == true || clan.ClanOwner == playerId);
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in IsOfficer API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if two players are regular allies (not merge allies)
        /// </summary>
        /// <param name="playerId1">First player's Steam ID</param>
        /// <param name="playerId2">Second player's Steam ID</param>
        /// <returns>True if players are regular allies, false otherwise</returns>
        public bool AreRegularAllies(ulong playerId1, ulong playerId2)
        {
            try
            {
                if (playerId1 == 0 || playerId2 == 0 || playerId1 == playerId2) return false;

                var clan1 = GetClan(playerId1);
                var clan2 = GetClan(playerId2);

                if (clan1 == null || clan2 == null || string.IsNullOrEmpty(clan1.ClanName) || string.IsNullOrEmpty(clan2.ClanName))
                    return false;

                // Check if clan1 has clan2 as a regular ally (but not merge ally)
                bool isRegularAlly = clan1.ClanAllys?.Contains(clan2.ClanName) == true &&
                                   clan1.ClanMergeAllys?.Contains(clan2.ClanName) != true;

                return isRegularAlly;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in AreRegularAllies API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if two players are merge allies
        /// </summary>
        /// <param name="playerId1">First player's Steam ID</param>
        /// <param name="playerId2">Second player's Steam ID</param>
        /// <returns>True if players are merge allies, false otherwise</returns>
        public bool AreMergeAllies(ulong playerId1, ulong playerId2)
        {
            try
            {
                if (playerId1 == 0 || playerId2 == 0 || playerId1 == playerId2) return false;

                var clan1 = GetClan(playerId1);
                var clan2 = GetClan(playerId2);

                if (clan1 == null || clan2 == null || string.IsNullOrEmpty(clan2.ClanName))
                    return false;

                return clan1.ClanMergeAllys?.Contains(clan2.ClanName) == true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in AreMergeAllies API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if two players are any type of allies (regular or merge)
        /// </summary>
        /// <param name="playerId1">First player's Steam ID</param>
        /// <param name="playerId2">Second player's Steam ID</param>
        /// <returns>True if players are allies of any type, false otherwise</returns>
        public bool AreAllies(ulong playerId1, ulong playerId2)
        {
            try
            {
                return AreRegularAllies(playerId1, playerId2) || AreMergeAllies(playerId1, playerId2);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in AreAllies API: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets all members of allied clans (regular allies only, not merge allies)
        /// </summary>
        /// <param name="playerId">The player's Steam ID</param>
        /// <returns>Dictionary of all allied clan member IDs and names</returns>
        public Dictionary<string, string> GetAlliedClanMembers(ulong playerId)
        {
            try
            {
                var result = new Dictionary<string, string>();
                var playerClan = GetClan(playerId);

                if (playerClan == null) return result;

                // Add clan members first
                if (playerClan.ClanMemebers != null)
                {
                    foreach (var member in playerClan.ClanMemebers)
                        result[member.Key] = member.Value;
                }

                // Add regular ally members (but not merge allies)
                if (playerClan.ClanAllys != null)
                {
                    foreach (var allyName in playerClan.ClanAllys)
                    {
                        // Skip if this ally is also a merge ally
                        if (playerClan.ClanMergeAllys?.Contains(allyName) == true)
                            continue;

                        // Find the ally clan and add its members
                        var allyClan = GetClanData(allyName);
                        if (allyClan?.ClanMemebers != null)
                        {
                            foreach (var allyMember in allyClan.ClanMemebers)
                                result[allyMember.Key] = allyMember.Value;
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetAlliedClanMembers API: {ex.Message}");
                return new Dictionary<string, string>();
            }
        }

        /// <summary>
        /// Invalidates the player clan cache for a specific player
        /// </summary>
        /// <param name="playerId">The player's Steam ID</param>
        public void InvalidatePlayerCache(ulong playerId)
        {
            try
            {
                string playerIdStr = playerId.ToString();
                playerClansCache.Remove(playerIdStr);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in InvalidatePlayerCache API: {ex.Message}");
            }
        }

        /// <summary>
        /// Clears all player clan cache entries
        /// </summary>
        public void ClearPlayerCache()
        {
            try
            {
                playerClansCache.Clear();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in ClearPlayerCache API: {ex.Message}");
            }
        }

        // API method to get all clans
        public List<object> GetAllClans()
        {
            try
            {
                var result = new List<object>();
                foreach (var clan in clanCache)
                {
                    if (clan != null)
                    {
                        result.Add(new
                        {
                            ClanName = clan.ClanName,
                            ClanTag = clan.ClanTag,
                            ClanOwner = clan.ClanOwner,
                            ClanMembers = clan.ClanMemebers,
                            MemberCount = clan.ClanMemebers.Count,
                            MaxMembers = clan.MaxClanMembers
                        });
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetAllClans API: {ex.Message}");
                return new List<object>();
            }
        }

        // API method to get all clan tags
        public List<string> GetAllClanTags()
        {
            try
            {
                return clanCache.Where(c => c != null && !string.IsNullOrEmpty(c.ClanTag))
                                .Select(c => c.ClanTag)
                                .ToList();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetAllClanTags API: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Gets the clan tag of a player - Clans.cs compatible API
        /// </summary>
        /// <param name="playerId">The player's Steam ID as string</param>
        /// <returns>Clan tag string, null if not in clan</returns>
        public string GetClanOf(string playerId)
        {
            try
            {
                if (string.IsNullOrEmpty(playerId)) return null;

                if (ulong.TryParse(playerId, out ulong id))
                {
                    var clan = GetClan(id);
                    return clan?.ClanTag;
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanOf API: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the clan tag of a player - Clans.cs compatible API
        /// </summary>
        /// <param name="playerId">The player's Steam ID as ulong</param>
        /// <returns>Clan tag string, null if not in clan</returns>
        public string GetClanOf(ulong playerId)
        {
            try
            {
                var clan = GetClan(playerId);
                return clan?.ClanTag;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanOf API: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the clan tag of a player - Clans.cs compatible API
        /// </summary>
        /// <param name="player">The BasePlayer object</param>
        /// <returns>Clan tag string, null if not in clan</returns>
        public string GetClanOf(BasePlayer player)
        {
            try
            {
                if (player == null) return null;
                return GetClanOf(player.userID);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanOf API: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets clan members as List of strings - Clans.cs compatible API
        /// </summary>
        /// <param name="playerId">The player's Steam ID as string</param>
        /// <returns>List of member Steam IDs</returns>
        public List<string> GetClanMembers(string playerId)
        {
            try
            {
                if (string.IsNullOrEmpty(playerId)) return new List<string>();

                if (ulong.TryParse(playerId, out ulong id))
                {
                    var clan = GetClan(id);
                    return clan?.ClanMemebers?.Keys.ToList() ?? new List<string>();
                }
                return new List<string>();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanMembers API: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// Gets clan members as List of strings - Clans.cs compatible API
        /// </summary>
        /// <param name="playerId">The player's Steam ID as ulong</param>
        /// <returns>List of member Steam IDs</returns>
        public List<string> GetClanMembers(ulong playerId)
        {
            try
            {
                var clan = GetClan(playerId);
                return clan?.ClanMemebers?.Keys.ToList() ?? new List<string>();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in GetClanMembers API: {ex.Message}");
                return new List<string>();
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// Cleans all clan member names to remove Steam IDs that may have been appended
        /// </summary>
        /// <param name="player">Admin player executing the command</param>
        private void CleanAllClanMemberNames(BasePlayer player)
        {
            try
            {
                var result = CleanAllClanMemberNamesInternal();
                SendMessage(player, "ClanNamesCleanupComplete", result.totalClans, result.totalMembersUpdated);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error cleaning clan member names: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        /// <summary>
        /// Internal method to clean clan member names without requiring a player
        /// </summary>
        /// <returns>Tuple with total clans processed and members updated</returns>
        private (int totalClans, int totalMembersUpdated) CleanAllClanMemberNamesInternal()
        {
            int totalClans = 0;
            int totalMembersUpdated = 0;

            foreach (var clan in clanCache.ToList())
            {
                if (clan?.ClanMemebers == null) continue;

                totalClans++;
                bool clanChanged = false;
                var membersToUpdate = new Dictionary<string, string>();

                foreach (var member in clan.ClanMemebers.ToList())
                {
                    string memberId = member.Key;
                    string currentName = member.Value;

                    // Try to get the clean player name
                    string cleanName = GetCleanPlayerNameById(memberId);

                    // Check if the name contains what looks like a Steam ID (long number in parentheses)
                    if (currentName != cleanName || (currentName.Contains("(") && currentName.Contains(")")))
                    {
                        // Remove anything that looks like a Steam ID in parentheses
                        string processedName = System.Text.RegularExpressions.Regex.Replace(cleanName, @"\s*\(\d{17}\)\s*$", "").Trim();

                        if (processedName != currentName)
                        {
                            membersToUpdate[memberId] = processedName;
                            totalMembersUpdated++;
                            clanChanged = true;
                        }
                    }
                }

                // Update the clan if any members were changed
                if (clanChanged)
                {
                    foreach (var update in membersToUpdate)
                    {
                        clan.ClanMemebers[update.Key] = update.Value;
                    }
                    SaveClanData(clan);
                }
            }

            return (totalClans, totalMembersUpdated);
        }

        /// <summary>
        /// Continuously cleans all online player display names to prevent Steam ID additions
        /// </summary>
        /// <summary>
        /// Clean display names ONLY for clan members to ensure proper clan tags in team UI
        /// </summary>
        private void CleanClanMemberDisplayNames()
        {
            try
            {
                foreach (var player in BasePlayer.activePlayerList)
                {
                    if (player == null || !player.IsConnected || string.IsNullOrEmpty(player._name))
                        continue;

                    // ONLY process players who are in clans
                    var clan = GetClan(player.userID);
                    if (clan != null && !string.IsNullOrEmpty(clan.ClanTag))
                    {
                        var currentDisplayName = player.displayName;
                        var cleanedName = RemoveSteamIdFromName(currentDisplayName);
                        var baseName = RemoveAllTagsFromName(cleanedName);
                        var expectedName = $"[{clan.ClanTag}] {baseName}";

                        if (player.displayName != expectedName)
                        {
                            player.displayName = expectedName;
                            player.SendNetworkUpdate();
                        }
                    }
                    // For non-clan members, do NOT modify their display names at all
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in CleanClanMemberDisplayNames: {ex.Message}");
            }
        }

        /// <summary>
        /// Removes Steam IDs from a name string
        /// </summary>
        private string RemoveSteamIdFromName(string name)
        {
            if (string.IsNullOrEmpty(name)) return "Unknown";

            // Remove Steam ID patterns in various formats
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\(76561198\d{9}\)", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\[76561198\d{9}\]", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"76561198\d{9}", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\(\d{17}\)", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\[\d{17}\]", "").Trim();

            // Clean up any remaining empty brackets or parentheses
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*\(\s*\)\s*", " ").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*\[\s*\]\s*", " ").Trim();

            // Clean up multiple spaces
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s+", " ").Trim();

            return string.IsNullOrEmpty(name) ? "Unknown" : name;
        }

        /// <summary>
        /// Removes all tags (clan tags and Steam IDs) from a name to get the base name
        /// </summary>
        private string RemoveAllTagsFromName(string name)
        {
            if (string.IsNullOrEmpty(name)) return "Unknown";

            // Remove Steam IDs first
            name = RemoveSteamIdFromName(name);

            // Remove clan tags [TAG]
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\[[A-Z0-9]{2,6}\]\s*", "").Trim();

            // Remove any remaining brackets or parentheses
            name = System.Text.RegularExpressions.Regex.Replace(name, @"^\s*[\[\(]\s*", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*[\]\)]\s*$", "").Trim();

            return string.IsNullOrEmpty(name) ? "Unknown" : name;
        }
        #endregion

        #region Commands
        private void ClanCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            if (iPlayer.Object is not BasePlayer player) return;

            if (args.Length == 0)
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                    SendHelpMessage(player);
                else
                    SendClanInfoMessage(player, clan);
                return;
            }

            switch (args[0].ToLower())
            {
                case "help":
                    SendHelpMessage(player);
                    break;

                case "cleannames":
                    if (!player.IsAdmin)
                    {
                        SendMessage(player, "NoPermission");
                        return;
                    }
                    CleanAllClanMemberNames(player);
                    break;

                case "create":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    CreateClan(player, args[1]);
                    break;

                case "join":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    JoinClanByName(player, args[1]);
                    break;

                case "leave":
                    LeaveClan(player);
                    break;

                case "invite":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    InvitePlayer(player, args[1]);
                    break;

                case "kick":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    KickPlayer(player, args[1]);
                    break;

                case "tag":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    SetClanTag(player, args[1]);
                    break;

                case "promote":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    PromotePlayer(player, args[1]);
                    break;

                case "demote":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    DemotePlayer(player, args[1]);
                    break;

                case "disband":
                    DisbandClan(player);
                    break;

                case "ff":
                    ToggleFriendlyFire(player);
                    break;

                case "ally":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }
                    HandleAllyCommand(player, args);
                    break;

                case "claim":
                    StartLeadershipClaim(player);
                    break;

                case "vote":
                    if (args.Length < 2)
                    {
                        SendMessage(player, "NotEnoughArgs");
                        return;
                    }

                    if (args[1].ToLower() == "yes")
                        VoteOnLeadershipClaim(player, true);
                    else if (args[1].ToLower() == "no")
                        VoteOnLeadershipClaim(player, false);
                    else
                        SendMessage(player, "NotEnoughArgs");
                    break;

                case "votestatus":
                    ShowVoteStatus(player);
                    break;

                default:
                    SendHelpMessage(player);
                    break;
            }
        }

        private void CleanClanNamesCMD(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin)
            {
                player.Reply("You need admin privileges to use this command.");
                return;
            }

            try
            {
                int processedClans = 0;
                int updatedMembers = 0;

                foreach (var clan in clanCache.ToList())
                {
                    processedClans++;
                    bool clanChanged = false;
                    var membersToUpdate = new Dictionary<string, string>();

                    foreach (var member in clan.ClanMemebers.ToList())
                    {
                        string memberId = member.Key;
                        string currentName = member.Value;
                        string cleanName = GetCleanPlayerNameById(memberId);

                        if (currentName != cleanName)
                        {
                            membersToUpdate[memberId] = cleanName;
                            clanChanged = true;
                            updatedMembers++;
                        }
                    }

                    if (clanChanged)
                    {
                        foreach (var update in membersToUpdate)
                        {
                            clan.ClanMemebers[update.Key] = update.Value;
                        }
                        SaveClanData(clan);
                    }
                }

                string message = string.Format(lang.GetMessage("ClanNamesCleanupComplete", this), processedClans, updatedMembers);
                player.Reply(message);
                Puts($"[Awaken Clans] Manual cleanup complete: {processedClans} clans processed, {updatedMembers} member names updated.");
            }
            catch (Exception ex)
            {
                player.Reply($"Error during cleanup: {ex.Message}");
                Debug.LogError($"[Awaken Clans] Error in manual cleanup: {ex.Message}");
            }
        }

        private void AllyChatCMD(IPlayer iPlayer, string command, string[] args)
        {
            try
            {
                if (iPlayer.IsServer) return;
                if (iPlayer.Object is not BasePlayer player) return;

                if (args.Length == 0)
                {
                    SendMessage(player, "NotEnoughArgs");
                    return;
                }

                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanAllys.Count == 0)
                {
                    SendMessage(player, "NoAlliesForChat");
                    return;
                }

                string message = string.Join(" ", args);
                string formattedMessage = string.Format(lang.GetMessage("AllyChatMessage", this, player.UserIDString),
                    player.displayName, clan.ClanTag, message);

                foreach (var allyClanName in clan.ClanAllys)
                {
                    var allyClan = GetClanData(allyClanName);
                    if (allyClan != null)
                    {
                        foreach (var memberId in allyClan.ClanMemebers.Keys)
                        {
                            var member = BasePlayer.Find(memberId);
                            if (member != null && member.IsConnected)
                            {
                                member.ChatMessage(formattedMessage);
                            }
                        }
                    }
                }

                foreach (var memberId in clan.ClanMemebers.Keys)
                {
                    var member = BasePlayer.Find(memberId);
                    if (member != null && member.IsConnected)
                    {
                        member.ChatMessage(formattedMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in ally chat command: {ex.Message}");
                SendMessage(iPlayer.Object as BasePlayer, "CouldNotPerformAction");
            }
        }

        private void CreateClan(BasePlayer player, string clanName)
        {
            try
            {
                if (GetClan(player.userID) != null)
                {
                    SendMessage(player, "AlreadyInClan");
                    return;
                }

                if (clanName.Length < config!.ClanName.MinChars || clanName.Length > config.ClanName.MaxChars)
                {
                    SendMessage(player, "ClanNameMinMax", config.ClanName.MinChars, config.ClanName.MaxChars);
                    return;
                }

                if (filter.IsMatch(clanName))
                {
                    SendMessage(player, "InvalidCharacters");
                    return;
                }

                if (ContainsBlockedWord(clanName, config.ClanName.BlockedWords))
                {
                    SendMessage(player, "BlockedWord");
                    return;
                }

                if (GetClanData(clanName) != null)
                {
                    SendMessage(player, "ClanNameTaken");
                    return;
                }

                var playerTeam = CreateTeamSafely();
                if (playerTeam != null)
                {
                    AddPlayerToTeamSafely(playerTeam, player);
                    SetTeamLeaderSafely(playerTeam, player.userID);
                }

                var clan = new ClanInfo
                {
                    ClanTeamId = playerTeam?.teamID ?? 0,
                    ClanName = clanName,
                    ClanTag = clanName.Length > 4 ? clanName.Substring(0, 4).ToUpper() : clanName.ToUpper(),
                    ClanOwner = player.UserIDString,
                    MaxClanMembers = config.ClanSettings.MaxMemebers
                };

                clan.ClanMemebers[player.UserIDString] = GetCleanPlayerName(player);
                clanCache.Add(clan);
                playerClansCache[player.UserIDString] = clan.ClanName;
                SaveClanData(clan);

                // Update player display name with clan tag
                RenamePlayer(player, clan);

                // Force team UI update safely
                if (playerTeam != null)
                {
                    MarkTeamDirtySafely(playerTeam);
                    SendNetworkUpdateSafely(player);
                    RefreshTeamUI(clan);
                }

                SendMessage(player, "CreatedClan", clanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error creating clan: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void JoinClanByName(BasePlayer player, string clanName)
        {
            try
            {
                if (GetClan(player.userID) != null)
                {
                    SendMessage(player, "AlreadyInClan");
                    return;
                }

                var clan = GetClanData(clanName);
                if (clan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanInvites.Contains(player.UserIDString))
                {
                    SendMessage(player, "NotInvited");
                    return;
                }

                if (clan.ClanMemebers.Count >= clan.MaxClanMembers)
                {
                    SendMessage(player, "ClanFull");
                    return;
                }

                clan.ClanInvites.Remove(player.UserIDString);
                clan.ClanMemebers[player.UserIDString] = GetCleanPlayerName(player);
                playerClansCache[player.UserIDString] = clan.ClanName;

                var team = FindTeamSafely(clan.ClanTeamId);
                if (team != null)
                {
                    AddPlayerToTeamSafely(team, player);
                    MarkTeamDirtySafely(team);

                    // Force team UI update
                    SendNetworkUpdateSafely(player);

                    // Update all team members' UI
                    foreach (var memberId in team.members)
                    {
                        var member = BasePlayer.FindAwakeOrSleeping(memberId.ToString());
                        if (member != null && member.IsConnected)
                        {
                            SendNetworkUpdateSafely(member);
                        }
                    }
                }

                // RenamePlayer removed - clan tags handled by OasisChat plugin

                SaveClanData(clan);

                // Refresh team UI for all members
                RefreshTeamUI(clan);

                SendMessage(player, "JoinedClan", clan.ClanName);
                SendMessageToClan(clan.ClanMemebers, "PlayerJoinedClan", player.displayName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error joining clan: {ex.Message}");
                SendMessage(player, "ErrorJoiningClan");
            }
        }

        private void LeaveClan(BasePlayer player)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanOwner == player.UserIDString)
                {
                    SendMessage(player, "MustDisband");
                    return;
                }

                clan.ClanMemebers.Remove(player.UserIDString);
                clan.ClanOfficers.Remove(player.UserIDString);
                playerClansCache.Remove(player.UserIDString);

                var team = FindTeamSafely(clan.ClanTeamId);
                if (team != null)
                {
                    RemovePlayerFromTeamSafely(team, player.userID);
                    MarkTeamDirtySafely(team);
                }

                // RenamePlayer removed - clan tags handled by OasisChat plugin

                SaveClanData(clan);
                SendMessage(player, "LeftClan", clan.ClanName);
                SendMessageToClan(clan.ClanMemebers, "PlayerLeftClan", player.displayName);

                if (clan.ClanMemebers.Count == 0)
                {
                    DisbandClanInternal(clan);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error leaving clan: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void InvitePlayer(BasePlayer player, string targetName)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (!IsOfficer(player.UserIDString) && clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NoPermission");
                    return;
                }

                if (clan.ClanMemebers.Count >= clan.MaxClanMembers)
                {
                    SendMessage(player, "MaximumMembers");
                    return;
                }

                var target = covalence.Players.FindPlayer(targetName);
                if (target == null)
                {
                    SendMessage(player, "CouldNotFindPlayer");
                    return;
                }

                if (target.Id == player.UserIDString)
                {
                    SendMessage(player, "CantInviteYourself");
                    return;
                }

                if (clan.ClanInvites.Contains(target.Id))
                {
                    SendMessage(player, "PlayerAlreadyInvited");
                    return;
                }

                if (GetClan(Convert.ToUInt64(target.Id)) != null)
                {
                    SendMessage(player, "PlayerAlreadyInAClan");
                    return;
                }

                clan.ClanInvites.Add(target.Id);
                SaveClanData(clan);

                // Create actual team invitation in Rust's team system
                var team = FindTeamSafely(clan.ClanTeamId);
                var targetPlayer = BasePlayer.Find(target.Id);
                if (team != null && targetPlayer != null)
                {
                    SendTeamInviteSafely(team, targetPlayer);
                }

                SendMessage(BasePlayer.Find(target.Id), "InvitedToClan", clan.ClanName);
                SendMessage(player, "PlayerInvitedToClan", target.Name);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error inviting player: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void KickPlayer(BasePlayer player, string targetName)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (!IsOfficer(player.UserIDString) && clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NoPermission");
                    return;
                }

                var target = covalence.Players.FindPlayer(targetName);
                if (target == null || !clan.ClanMemebers.ContainsKey(target.Id))
                {
                    SendMessage(player, "CouldNotFindPlayer");
                    return;
                }

                if (target.Id == clan.ClanOwner)
                {
                    SendMessage(player, "CantKickOwner");
                    return;
                }

                clan.ClanMemebers.Remove(target.Id);
                clan.ClanOfficers.Remove(target.Id);
                playerClansCache.Remove(target.Id);

                var team = FindTeamSafely(clan.ClanTeamId);
                if (team != null)
                {
                    RemovePlayerFromTeamSafely(team, Convert.ToUInt64(target.Id));
                    MarkTeamDirtySafely(team);
                }

                // RenamePlayer removed - clan tags handled by OasisChat plugin

                SaveClanData(clan);

                // Refresh team UI for remaining members
                RefreshTeamUI(clan);

                // Send message to kicked player if they're online
                var kickedPlayer = BasePlayer.Find(target.Id);
                if (kickedPlayer != null)
                {
                    SendMessage(kickedPlayer, "KickedFromClan");
                }
                SendMessageToClan(clan.ClanMemebers, "PlayerKickedFromClan", target.Name);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error kicking player: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void SetClanTag(BasePlayer player, string tag)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NotClanOwner");
                    return;
                }

                if (tag.Length < config!.ClanTag.MinChars)
                {
                    SendMessage(player, "ClanTagTooShort", config.ClanTag.MinChars);
                    return;
                }

                if (tag.Length > config.ClanTag.MaxChars)
                {
                    SendMessage(player, "ClanTagTooLong", config.ClanTag.MaxChars);
                    return;
                }

                if (filter.IsMatch(tag))
                {
                    SendMessage(player, "InvalidCharacters");
                    return;
                }

                if (ContainsBlockedWord(tag, config.ClanTag.BlockedWords))
                {
                    SendMessage(player, "BlockedWord");
                    return;
                }

                if (clanCache.Any(c => c.ClanTag.Equals(tag, StringComparison.OrdinalIgnoreCase) && c.ClanName != clan.ClanName))
                {
                    SendMessage(player, "ClanTagTaken");
                    return;
                }

                clan.ClanTag = tag.ToUpper();
                SaveClanData(clan);

                // RenamePlayer removed - clan tags handled by OasisChat plugin

                SendMessage(player, "SetClanTag", tag);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error setting clan tag: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void PromotePlayer(BasePlayer player, string targetName)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NotClanOwner");
                    return;
                }

                var target = covalence.Players.FindPlayer(targetName);
                if (target == null || !clan.ClanMemebers.ContainsKey(target.Id))
                {
                    SendMessage(player, "CouldNotFindPlayer");
                    return;
                }

                if (target.Id == clan.ClanOwner)
                {
                    SendMessage(player, "CantPromoteOwner", target.Name);
                    return;
                }

                if (clan.ClanOfficers.Contains(target.Id))
                {
                    SendMessage(player, "AlreadyAOfficer", target.Name);
                    return;
                }

                clan.ClanOfficers.Add(target.Id);
                SaveClanData(clan);
                SendMessageToClan(clan.ClanMemebers, "PromotedToOfficer", target.Name);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error promoting player: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void DemotePlayer(BasePlayer player, string targetName)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NotClanOwner");
                    return;
                }

                var target = covalence.Players.FindPlayer(targetName);
                if (target == null || !clan.ClanOfficers.Contains(target.Id))
                {
                    SendMessage(player, "NotAnOfficer", target.Name);
                    return;
                }

                clan.ClanOfficers.Remove(target.Id);
                SaveClanData(clan);
                SendMessageToClan(clan.ClanMemebers, "RemovedAsOfficer", target.Name);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error demoting player: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void DisbandClan(BasePlayer player)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NotClanOwner");
                    return;
                }

                foreach (var allyClanName in clan.ClanAllys.ToList())
                {
                    var allyClan = GetClanData(allyClanName);
                    if (allyClan != null)
                    {
                        allyClan.ClanAllys.Remove(clan.ClanName);
                        allyClan.ClanAllyFF.Remove(clan.ClanName);
                        SaveClanData(allyClan);
                        SendMessageToClan(allyClan.ClanMemebers, "AllyRemoved", clan.ClanName);
                    }
                }

                SendMessageToClan(clan.ClanMemebers, "DisbandedClan");

                var team = FindTeamSafely(clan.ClanTeamId);
                if (team != null)
                {
                    DisbandTeamSafely(team);
                }

                foreach (var memberId in clan.ClanMemebers.Keys)
                {
                    playerClansCache.Remove(memberId);
                    var member = BasePlayer.Find(memberId);
                    if (member != null)
                    {
                        ClearPlayerTeamSafely(member);
                        // RenamePlayer removed - clan tags handled by OasisChat plugin
                    }
                }

                DeleteClan(clan.ClanName); // This now handles cache removal too
                CancelLeadershipVote(clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error disbanding clan: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void DisbandClanInternal(ClanInfo clan)
        {
            try
            {
                foreach (var allyClanName in clan.ClanAllys.ToList())
                {
                    var allyClan = GetClanData(allyClanName);
                    if (allyClan != null)
                    {
                        allyClan.ClanAllys.Remove(clan.ClanName);
                        allyClan.ClanAllyFF.Remove(clan.ClanName);
                        SaveClanData(allyClan);
                        SendMessageToClan(allyClan.ClanMemebers, "AllyRemoved", clan.ClanName);
                    }
                }

                var team = FindTeamSafely(clan.ClanTeamId);
                if (team != null)
                {
                    DisbandTeamSafely(team);
                }

                foreach (var memberId in clan.ClanMemebers.Keys)
                {
                    playerClansCache.Remove(memberId);
                    var member = BasePlayer.Find(memberId);
                    if (member != null)
                    {
                        ClearPlayerTeamSafely(member);
                        // RenamePlayer removed - clan tags handled by OasisChat plugin
                    }
                }

                DeleteClan(clan.ClanName); // This now handles cache removal too
                CancelLeadershipVote(clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error in internal clan disband: {ex.Message}");
            }
        }

        private void ToggleFriendlyFire(BasePlayer player)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NotClanOwner");
                    return;
                }

                clan.ClanFF = !clan.ClanFF;
                SaveClanData(clan);
                SendMessageToClan(clan.ClanMemebers, clan.ClanFF ? "FriendlyFireOn" : "FriendlyFireOff");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error toggling friendly fire: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void HandleAllyCommand(BasePlayer player, string[] args)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (!IsOfficer(player.UserIDString) && clan.ClanOwner != player.UserIDString)
                {
                    SendMessage(player, "NoPermission");
                    return;
                }

                bool allyCommandEnabled = config?.ClanSettings?.EnableAllyCommand ?? true;

                switch (args[1].ToLower())
                {
                    case "invite":
                        if (!allyCommandEnabled)
                        {
                            SendMessage(player, "AllyCommandDisabled");
                            return;
                        }
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        InviteAlly(player, clan, args[2]);
                        break;

                    case "accept":
                        if (!allyCommandEnabled)
                        {
                            SendMessage(player, "AllyCommandDisabled");
                            return;
                        }
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        AcceptAlly(player, clan, args[2]);
                        break;

                    case "reject":
                        if (!allyCommandEnabled)
                        {
                            SendMessage(player, "AllyCommandDisabled");
                            return;
                        }
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        RejectAlly(player, clan, args[2]);
                        break;

                    case "remove":
                        if (!allyCommandEnabled)
                        {
                            SendMessage(player, "AllyCommandDisabled");
                            return;
                        }
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        RemoveAlly(player, clan, args[2]);
                        break;

                    case "ff":
                        if (!allyCommandEnabled)
                        {
                            SendMessage(player, "AllyCommandDisabled");
                            return;
                        }
                        if (args.Length < 3)
                        {
                            SendMessage(player, "AllyFFUsage");
                            return;
                        }
                        ToggleAllyFriendlyFire(player, clan, args[2]);
                        break;

                    // Merge ally commands are always available regardless of ally command setting
                    case "merge":
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        InviteMergeAlly(player, clan, args[2]);
                        break;

                    case "mergeaccept":
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        AcceptMergeAlly(player, clan, args[2]);
                        break;

                    case "mergereject":
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        RejectMergeAlly(player, clan, args[2]);
                        break;

                    case "mergeremove":
                        if (args.Length < 3)
                        {
                            SendMessage(player, "NotEnoughArgs");
                            return;
                        }
                        RemoveMergeAlly(player, clan, args[2]);
                        break;

                    default:
                        if (!allyCommandEnabled)
                        {
                            SendMessage(player, "AllyCommandDisabledHelp");
                        }
                        else
                        {
                            SendMessage(player, "NotEnoughArgs");
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error handling ally command: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void InviteAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                if (clan.ClanName.Equals(allyClanName, StringComparison.OrdinalIgnoreCase))
                {
                    SendMessage(player, "CantAllySelf");
                    return;
                }

                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (clan.ClanAllys.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "AllyInviteAlreadySent");
                    return;
                }

                if (clan.ClanAllyInvites.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "AllyInviteAlreadyReceived");
                    return;
                }

                allyClan.ClanAllyInvites.Add(clan.ClanName);
                SaveClanData(allyClan);

                SendMessageToClan(allyClan.ClanMemebers, "AllyInviteRecieved", clan.ClanName);
                SendMessage(player, "AllyInviteSent", clan.ClanName, allyClan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error inviting ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void AcceptAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanAllyInvites.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "NoAllyInvite", allyClan.ClanName);
                    return;
                }

                clan.ClanAllys.Add(allyClan.ClanName);
                clan.ClanAllyInvites.Remove(allyClan.ClanName);
                clan.ClanAllyFF[allyClan.ClanName] = false;

                allyClan.ClanAllys.Add(clan.ClanName);
                allyClan.ClanAllyInvites.Remove(clan.ClanName);
                allyClan.ClanAllyFF[clan.ClanName] = false;

                SaveClanData(clan);
                SaveClanData(allyClan);

                SendMessageToClan(clan.ClanMemebers, "AllyAccepted", allyClan.ClanName);
                SendMessageToClan(allyClan.ClanMemebers, "AllyAccepted", clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error accepting ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void RejectAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanAllyInvites.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "NoAllyInvite", allyClan.ClanName);
                    return;
                }

                clan.ClanAllyInvites.Remove(allyClan.ClanName);
                SaveClanData(clan);

                SendMessageToClan(clan.ClanMemebers, "AllyRejected", clan.ClanName, allyClan.ClanName);
                SendMessageToClan(allyClan.ClanMemebers, "AllyRejectedTo", clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error rejecting ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void RemoveAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanAllys.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "NotAlly", allyClan.ClanName);
                    return;
                }

                clan.ClanAllys.Remove(allyClan.ClanName);
                clan.ClanAllyFF.Remove(allyClan.ClanName);

                allyClan.ClanAllys.Remove(clan.ClanName);
                allyClan.ClanAllyFF.Remove(clan.ClanName);

                SaveClanData(clan);
                SaveClanData(allyClan);

                SendMessageToClan(clan.ClanMemebers, "AllyRemoved", allyClan.ClanName);
                SendMessageToClan(allyClan.ClanMemebers, "AllyRemoved", clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error removing ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void ToggleAllyFriendlyFire(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanAllys.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "InvalidAlly", allyClan.ClanName);
                    return;
                }

                bool currentFF = clan.ClanAllyFF.TryGetValue(allyClan.ClanName, out var ff) && ff;
                clan.ClanAllyFF[allyClan.ClanName] = !currentFF;

                SaveClanData(clan);

                SendMessageToClan(clan.ClanMemebers, !currentFF ? "AllyFriendlyFireOn" : "AllyFriendlyFireOff", allyClan.ClanName);
                SendMessageToClan(allyClan.ClanMemebers, !currentFF ? "AllyFriendlyFireOn" : "AllyFriendlyFireOff", clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error toggling ally friendly fire: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void InviteMergeAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                if (clan.ClanName.Equals(allyClanName, StringComparison.OrdinalIgnoreCase))
                {
                    SendMessage(player, "CantAllySelf");
                    return;
                }

                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (clan.ClanMergeAllys.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "AlreadyMergeAlly", allyClan.ClanName);
                    return;
                }

                if (clan.ClanMergeAllyInvites.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "MergeAllyInviteAlreadySent", allyClan.ClanName);
                    return;
                }

                if (allyClan.ClanMergeAllyInvites.Contains(clan.ClanName))
                {
                    SendMessage(player, "MergeAllyInviteAlreadyReceived", allyClan.ClanName);
                    return;
                }

                allyClan.ClanMergeAllyInvites.Add(clan.ClanName);
                SaveClanData(allyClan);

                SendMessageToClan(allyClan.ClanMemebers, "MergeAllyInviteReceived", clan.ClanName);
                SendMessage(player, "MergeAllyInviteSent", clan.ClanName, allyClan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error inviting merge ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void AcceptMergeAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanMergeAllyInvites.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "NoMergeAllyInvite", allyClan.ClanName);
                    return;
                }

                clan.ClanMergeAllys.Add(allyClan.ClanName);
                clan.ClanMergeAllyInvites.Remove(allyClan.ClanName);

                allyClan.ClanMergeAllys.Add(clan.ClanName);
                allyClan.ClanMergeAllyInvites.Remove(clan.ClanName);

                SaveClanData(clan);
                SaveClanData(allyClan);

                SendMessageToClan(clan.ClanMemebers, "MergeAllyAccepted", allyClan.ClanName);
                SendMessageToClan(allyClan.ClanMemebers, "MergeAllyAccepted", clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error accepting merge ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void RejectMergeAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanMergeAllyInvites.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "NoMergeAllyInvite", allyClan.ClanName);
                    return;
                }

                clan.ClanMergeAllyInvites.Remove(allyClan.ClanName);
                SaveClanData(clan);

                SendMessageToClan(clan.ClanMemebers, "MergeAllyRejected", clan.ClanName, allyClan.ClanName);
                SendMessageToClan(allyClan.ClanMemebers, "MergeAllyRejectedTo", clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error rejecting merge ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void RemoveMergeAlly(BasePlayer player, ClanInfo clan, string allyClanName)
        {
            try
            {
                var allyClan = GetClanData(allyClanName);
                if (allyClan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return;
                }

                if (!clan.ClanMergeAllys.Contains(allyClan.ClanName))
                {
                    SendMessage(player, "NotMergeAlly", allyClan.ClanName);
                    return;
                }

                clan.ClanMergeAllys.Remove(allyClan.ClanName);
                allyClan.ClanMergeAllys.Remove(clan.ClanName);

                SaveClanData(clan);
                SaveClanData(allyClan);

                SendMessageToClan(clan.ClanMemebers, "MergeAllyRemoved", allyClan.ClanName);
                SendMessageToClan(allyClan.ClanMemebers, "MergeAllyRemoved", clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error removing merge ally: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private object? JoinClan(BasePlayer player, ulong teamLeader)
        {
            try
            {
                var clan = GetClan(teamLeader);
                if (clan == null)
                {
                    SendMessage(player, "ClanDontExist");
                    return null;
                }

                if (!clan.ClanInvites.Contains(player.UserIDString))
                {
                    SendMessage(player, "NotInvited");
                    return null;
                }

                JoinClanByName(player, clan.ClanName);
                return null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error joining clan via team: {ex.Message}");
                SendMessage(player, "ErrorJoiningClan");
                return null;
            }
        }

        private object? RejectInvite(BasePlayer rejector, string teamLeader)
        {
            try
            {
                var clan = GetClan(Convert.ToUInt64(teamLeader));
                if (clan == null)
                {
                    SendMessage(rejector, "ClanDontExist");
                    return null;
                }

                if (!clan.ClanInvites.Contains(rejector.UserIDString))
                {
                    SendMessage(rejector, "NotInvited");
                    return null;
                }

                clan.ClanInvites.Remove(rejector.UserIDString);
                SaveClanData(clan);
                SendMessage(rejector, "NotInvited");
                return null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error rejecting invite: {ex.Message}");
                SendMessage(rejector, "CouldNotPerformAction");
                return null;
            }
        }
        #endregion

        #region Leadership Claim System
        private void StartLeadershipClaim(BasePlayer player)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (clan.ClanOwner == player.UserIDString)
                {
                    SendMessage(player, "CannotClaimLeadership");
                    return;
                }

                if (activeLeadershipVotes.ContainsKey(clan.ClanName))
                {
                    SendMessage(player, "LeadershipClaimAlreadyActive");
                    return;
                }

                var currentLeader = BasePlayer.Find(clan.ClanOwner);
                if (currentLeader != null && currentLeader.IsConnected)
                {
                    SendMessage(player, "OwnerOnlineCannotClaim");
                    return;
                }

                var vote = new LeadershipVote
                {
                    ClanName = clan.ClanName,
                    Claimant = player.UserIDString,
                    StartTime = DateTime.UtcNow
                };

                vote.VotesFor.Add(player.UserIDString);
                activeLeadershipVotes[clan.ClanName] = vote;

                voteTimers[clan.ClanName] = timer.Once(600f, () => EndLeadershipVote(clan.ClanName, false));

                SendMessageToClan(clan.ClanMemebers, "LeadershipClaimStarted", player.displayName);
                CheckVoteStatus(clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error starting leadership claim: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void VoteOnLeadershipClaim(BasePlayer player, bool voteYes)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (!activeLeadershipVotes.TryGetValue(clan.ClanName, out var vote))
                {
                    SendMessage(player, "LeadershipClaimNotActive");
                    return;
                }

                if (vote.HasVoted(player.UserIDString))
                {
                    SendMessage(player, "LeadershipClaimAlreadyVoted");
                    return;
                }

                if (voteYes)
                {
                    vote.VotesFor.Add(player.UserIDString);
                    SendMessage(player, "LeadershipClaimVoted", "yes");
                }
                else
                {
                    vote.VotesAgainst.Add(player.UserIDString);
                    SendMessage(player, "LeadershipClaimVoted", "no");
                }

                CheckVoteStatus(clan.ClanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error voting on leadership claim: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void CheckVoteStatus(string clanName)
        {
            try
            {
                if (!activeLeadershipVotes.TryGetValue(clanName, out var vote))
                    return;

                var clan = GetClanData(clanName);
                if (clan == null)
                    return;

                int totalMembers = clan.ClanMemebers.Count;
                int votesNeeded = (totalMembers / 2) + 1;

                if (vote.VotesFor.Count >= votesNeeded)
                {
                    EndLeadershipVote(clanName, true);
                    return;
                }

                int remainingVotes = totalMembers - vote.TotalVotes;
                if (vote.VotesFor.Count + remainingVotes < votesNeeded)
                {
                    EndLeadershipVote(clanName, false);
                    return;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error checking vote status: {ex.Message}");
            }
        }

        private void ShowVoteStatus(BasePlayer player)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null)
                {
                    SendMessage(player, "NotInClan");
                    return;
                }

                if (!activeLeadershipVotes.TryGetValue(clan.ClanName, out var vote))
                {
                    SendMessage(player, "LeadershipClaimNotActive");
                    return;
                }

                int totalMembers = clan.ClanMemebers.Count;
                int votesNeeded = (totalMembers / 2) + 1;

                TimeSpan timeElapsed = DateTime.UtcNow - vote.StartTime;
                int minutesRemaining = Math.Max(0, 10 - (int)timeElapsed.TotalMinutes);

                SendMessage(player, "LeadershipClaimStatus", vote.VotesFor.Count, vote.VotesAgainst.Count, votesNeeded);
                SendMessage(player, "LeadershipClaimTimeRemaining", minutesRemaining);

                var claimant = BasePlayer.Find(vote.Claimant);
                string claimantName = claimant?.displayName ?? vote.Claimant;

                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"<color=#74c6ff>[Clans]</color> Leadership claim by: {claimantName}");

                sb.AppendLine("<color=green>Votes For:</color>");
                foreach (var voterId in vote.VotesFor)
                {
                    var voter = BasePlayer.Find(voterId);
                    string voterName = voter?.displayName ?? voterId;
                    sb.AppendLine($"- {voterName}");
                }

                sb.AppendLine("<color=red>Votes Against:</color>");
                foreach (var voterId in vote.VotesAgainst)
                {
                    var voter = BasePlayer.Find(voterId);
                    string voterName = voter?.displayName ?? voterId;
                    sb.AppendLine($"- {voterName}");
                }

                player.ChatMessage(sb.ToString());
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error showing vote status: {ex.Message}");
                SendMessage(player, "CouldNotPerformAction");
            }
        }

        private void EndLeadershipVote(string clanName, bool success)
        {
            try
            {
                if (!activeLeadershipVotes.TryGetValue(clanName, out var vote))
                    return;

                var clan = GetClanData(clanName);
                if (clan == null)
                    return;

                if (voteTimers.TryGetValue(clanName, out var timer) && timer != null)
                {
                    timer.Destroy();
                    voteTimers.Remove(clanName);
                }

                if (success)
                {
                    string oldLeader = clan.ClanOwner;
                    clan.ClanOwner = vote.Claimant;

                    if (!clan.ClanOfficers.Contains(oldLeader))
                        clan.ClanOfficers.Add(oldLeader);

                    SaveClanData(clan);
                    UpdateTeamLeader(clan);

                    var claimant = BasePlayer.Find(vote.Claimant);
                    string claimantName = claimant?.displayName ?? vote.Claimant;
                    SendMessageToClan(clan.ClanMemebers, "LeadershipClaimSuccess", claimantName);
                }
                else
                {
                    SendMessageToClan(clan.ClanMemebers, "LeadershipClaimFailed");
                }

                activeLeadershipVotes.Remove(clanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error ending leadership vote: {ex.Message}");
            }
        }

        private void UpdateTeamLeader(ClanInfo clan)
        {
            try
            {
                var team = RelationshipManager.ServerInstance.teams.FirstOrDefault(t => t.Value.teamID == clan.ClanTeamId).Value;
                if (team == null)
                    return;

                ulong newLeaderId = Convert.ToUInt64(clan.ClanOwner);
                team.SetTeamLeader(newLeaderId);

                try
                {
                    var method = team.GetType().GetMethod("MarkDirtySafe");
                    if (method != null)
                        method.Invoke(team, null);
                    else
                        team.MarkDirty();
                }
                catch
                {
                    team.MarkDirty();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error updating team leader: {ex.Message}");
            }
        }

        private void CancelLeadershipVote(string clanName)
        {
            try
            {
                var clan = GetClanData(clanName);
                if (clan == null)
                    return;

                if (voteTimers.TryGetValue(clanName, out var timer) && timer != null)
                {
                    timer.Destroy();
                    voteTimers.Remove(clanName);
                }

                activeLeadershipVotes.Remove(clanName);

                SendMessageToClan(clan.ClanMemebers, "LeadershipClaimCancelled");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error cancelling leadership vote: {ex.Message}");
            }
        }
        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets a clean player name without Steam IDs but preserves clan tags for team UI
        /// </summary>
        private string GetCleanPlayerName(BasePlayer player)
        {
            if (player == null) return "Unknown";

            // Prefer _name (original name) over displayName (which might have modifications)
            string name = player._name ?? player.displayName ?? "Unknown";

            // ONLY remove Steam ID patterns, preserve clan tags for team UI
            name = System.Text.RegularExpressions.Regex.Replace(name, @"[\[\(]76561198\d{9}[\]\)]", "").Trim();

            // Remove any remaining empty brackets or parentheses (but not clan tags)
            name = System.Text.RegularExpressions.Regex.Replace(name, @"^\s*[\[\(]\s*[\]\)]\s*", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*[\[\(]\s*[\]\)]\s*$", "").Trim();

            // If name is empty after cleaning, return "Unknown"
            return string.IsNullOrEmpty(name) ? "Unknown" : name;
        }

        /// <summary>
        /// Gets a clean player name by Steam ID and adds clan tag for team UI
        /// </summary>
        private string GetCleanPlayerNameById(string steamId)
        {
            var player = BasePlayer.FindAwakeOrSleeping(steamId);
            if (player != null)
            {
                var cleanName = GetCleanPlayerName(player);

                // Add clan tag for team UI if player is in a clan
                var clan = GetClan(player.userID);
                if (clan != null && !string.IsNullOrEmpty(clan.ClanTag))
                {
                    // Check if clan tag is already in the name
                    if (!cleanName.Contains($"[{clan.ClanTag}]"))
                    {
                        cleanName = $"[{clan.ClanTag}] {cleanName}";
                    }
                }

                return cleanName;
            }

            // Fallback to covalence player data
            var covalencePlayer = covalence.Players.FindPlayerById(steamId);
            if (covalencePlayer != null)
            {
                string name = covalencePlayer.Name ?? "Unknown";
                // ONLY remove Steam IDs, not clan tags
                name = System.Text.RegularExpressions.Regex.Replace(name, @"[\[\(]76561198\d{9}[\]\)]", "").Trim();
                name = System.Text.RegularExpressions.Regex.Replace(name, @"^\s*[\[\(]\s*[\]\)]\s*", "").Trim();
                name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*[\[\(]\s*[\]\)]\s*$", "").Trim();

                // Add clan tag if needed
                if (ulong.TryParse(steamId, out ulong userId))
                {
                    var clan = GetClan(userId);
                    if (clan != null && !string.IsNullOrEmpty(clan.ClanTag))
                    {
                        if (!name.Contains($"[{clan.ClanTag}]"))
                        {
                            name = $"[{clan.ClanTag}] {name}";
                        }
                    }
                }

                return string.IsNullOrEmpty(name) ? "Unknown" : name;
            }

            return "Unknown"; // Never return Steam ID as name
        }

        private void SetPlayerID(BasePlayer player)
        {
            if (!_ids.ContainsKey(player.UserIDString))
                _ids[player.UserIDString] = player;

            if (!_players.ContainsKey(player))
                _players[player] = player.UserIDString;
        }

        /// <summary>
        /// Updates player display name with clan tag for game UI (team UI, player list, etc.)
        /// OasisChat handles chat formatting separately
        /// </summary>
        private void RenamePlayer(BasePlayer player, ClanInfo clan)
        {
            try
            {
                if (player == null || clan == null || string.IsNullOrEmpty(clan.ClanTag))
                    return;

                // Get clean base name without any existing tags or Steam IDs
                var baseName = GetCleanPlayerName(player);

                // Create display name with clan tag for game UI
                var newDisplayName = $"[{clan.ClanTag}] {baseName}";

                // Only update if it's different to avoid unnecessary network updates
                if (player.displayName != newDisplayName)
                {
                    player.displayName = newDisplayName;
                    player.SendNetworkUpdate();

                    if (Debug.isDebugBuild)
                        Puts($"[Awaken Clans] Updated display name: {baseName} -> {newDisplayName}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error updating player display name: {ex.Message}");
            }
        }

        #region Safe Team Management Functions

        private RelationshipManager.PlayerTeam? CreateTeamSafely()
        {
            try
            {
                return RelationshipManager.ServerInstance.CreateTeam();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error creating team: {ex.Message}");
                return null;
            }
        }

        private RelationshipManager.PlayerTeam? FindTeamSafely(ulong teamId)
        {
            try
            {
                return RelationshipManager.ServerInstance.FindTeam(teamId);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error finding team {teamId}: {ex.Message}");
                return null;
            }
        }

        private void AddPlayerToTeamSafely(RelationshipManager.PlayerTeam team, BasePlayer player)
        {
            try
            {
                if (team != null && player != null && !team.members.Contains(player.userID))
                {
                    team.AddPlayer(player);

                    // TEMPORARILY DISABLED - Testing if this is causing Steam ID issues
                    /*
                    // Clean Steam IDs and ensure proper clan tag after team addition
                    NextTick(() => {
                        if (player != null && player.IsConnected)
                        {
                            var cleanedName = RemoveSteamIdFromName(player.displayName);

                            // Ensure clan tag is present for team UI
                            var clan = GetClan(player.userID);
                            if (clan != null && !string.IsNullOrEmpty(clan.ClanTag))
                            {
                                var baseName = RemoveAllTagsFromName(cleanedName);
                                cleanedName = $"[{clan.ClanTag}] {baseName}";
                            }

                            if (player.displayName != cleanedName)
                            {
                                player.displayName = cleanedName;
                                player.SendNetworkUpdate();
                            }
                        }
                    });
                    */
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error adding player {player?.displayName} to team: {ex.Message}");
            }
        }

        private void RemovePlayerFromTeamSafely(RelationshipManager.PlayerTeam team, ulong playerId)
        {
            try
            {
                if (team != null && team.members.Contains(playerId))
                {
                    team.RemovePlayer(playerId);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error removing player {playerId} from team: {ex.Message}");
            }
        }

        private void SetTeamLeaderSafely(RelationshipManager.PlayerTeam team, ulong playerId)
        {
            try
            {
                if (team != null)
                {
                    team.SetTeamLeader(playerId);

                    // TEMPORARILY DISABLED - Testing if this is causing Steam ID issues
                    /*
                    // Clean Steam IDs and ensure proper clan tags for all team members
                    NextTick(() => {
                        foreach (var memberId in team.members)
                        {
                            var member = BasePlayer.FindAwakeOrSleeping(memberId.ToString());
                            if (member != null && member.IsConnected)
                            {
                                var cleanedName = RemoveSteamIdFromName(member.displayName);

                                // Ensure clan tag is present for team UI
                                var clan = GetClan(member.userID);
                                if (clan != null && !string.IsNullOrEmpty(clan.ClanTag))
                                {
                                    var baseName = RemoveAllTagsFromName(cleanedName);
                                    cleanedName = $"[{clan.ClanTag}] {baseName}";
                                }

                                if (member.displayName != cleanedName)
                                {
                                    member.displayName = cleanedName;
                                    member.SendNetworkUpdate();
                                }
                            }
                        }
                    });
                    */
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error setting team leader {playerId}: {ex.Message}");
            }
        }

        private void MarkTeamDirtySafely(RelationshipManager.PlayerTeam team)
        {
            try
            {
                if (team != null)
                {
                    // Try to use MarkDirtySafe method first (assembly-level safe method)
                    var method = team.GetType().GetMethod("MarkDirtySafe");
                    if (method != null)
                    {
                        method.Invoke(team, null);
                    }
                    else
                    {
                        // Fallback to regular MarkDirty
                        team.MarkDirty();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error marking team dirty: {ex.Message}");
                // Try one more time with basic MarkDirty
                try
                {
                    team?.MarkDirty();
                }
                catch (Exception ex2)
                {
                    Debug.LogError($"[Awaken Clans] Secondary error marking team dirty: {ex2.Message}");
                }
            }
        }

        private void SendNetworkUpdateSafely(BasePlayer player)
        {
            try
            {
                if (player != null && player.IsConnected)
                {
                    player.SendNetworkUpdate();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error sending network update for {player?.displayName}: {ex.Message}");
            }
        }

        private void SendTeamInviteSafely(RelationshipManager.PlayerTeam team, BasePlayer player)
        {
            try
            {
                if (team != null && player != null)
                {
                    team.SendInvite(player);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error sending team invite to {player?.displayName}: {ex.Message}");
            }
        }

        private void DisbandTeamSafely(RelationshipManager.PlayerTeam team)
        {
            try
            {
                if (team != null)
                {
                    team.Disband();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error disbanding team: {ex.Message}");
            }
        }

        private void ClearPlayerTeamSafely(BasePlayer player)
        {
            try
            {
                if (player != null)
                {
                    player.ClearTeam();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error clearing team for {player?.displayName}: {ex.Message}");
            }
        }

        private void CreateTeamForClan(ClanInfo clan)
        {
            try
            {
                var playerTeam = CreateTeamSafely();
                if (playerTeam == null) return;

                foreach (var userId in clan.ClanMemebers.Keys)
                {
                    if (!ulong.TryParse(userId, out var userID)) continue;

                    var player = BasePlayer.FindAwakeOrSleeping(userId);
                    if (player != null && player.IsValid())
                    {
                        AddPlayerToTeamSafely(playerTeam, player);
                    }
                    else
                    {
                        // Add offline players manually
                        if (!playerTeam.members.Contains(userID))
                            playerTeam.members.Add(userID);

                        if (!RelationshipManager.ServerInstance.playerToTeam.ContainsKey(userID))
                            RelationshipManager.ServerInstance.playerToTeam[userID] = playerTeam;
                    }
                }

                ulong leaderID = Convert.ToUInt64(clan.ClanOwner);
                SetTeamLeaderSafely(playerTeam, leaderID);
                MarkTeamDirtySafely(playerTeam);

                clan.ClanTeamId = playerTeam.teamID;
                SaveClanData(clan);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error creating team for clan {clan.ClanName}: {ex.Message}");
            }
        }

        private void ManageIngameTeam(BasePlayer player, TeamAction action)
        {
            try
            {
                var clan = GetClan(player.userID);
                if (clan == null) return;

                var team = FindTeamSafely(clan.ClanTeamId);
                if (team == null && action != TeamAction.Leave && action != TeamAction.Disbanded)
                {
                    team = CreateTeamSafely();
                    if (team != null)
                    {
                        clan.ClanTeamId = team.teamID;
                        SaveClanData(clan);
                    }
                }

                if (team == null) return;

                switch (action)
                {
                    case TeamAction.Create:
                    case TeamAction.Join:
                        AddPlayerToTeamSafely(team, player);

                        // Ensure the clan owner is always the team leader
                        var ownerUserId = Convert.ToUInt64(clan.ClanOwner);
                        if (team.teamLeader != ownerUserId)
                        {
                            SetTeamLeaderSafely(team, ownerUserId);
                        }
                        break;

                    case TeamAction.Leave:
                        RemovePlayerFromTeamSafely(team, player.userID);
                        break;

                    case TeamAction.Disbanded:
                        DisbandTeamSafely(team);
                        break;
                }

                MarkTeamDirtySafely(team);

                // Force network update to sync team changes for all team members
                NextTick(() => {
                    SendNetworkUpdateSafely(player);

                    // Update all team members' UI
                    foreach (var memberId in team.members)
                    {
                        var member = BasePlayer.FindAwakeOrSleeping(memberId.ToString());
                        if (member != null && member.IsConnected && member != player)
                        {
                            SendNetworkUpdateSafely(member);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error managing in-game team: {ex.Message}");
            }
        }

        private void RefreshTeamUI(ClanInfo clan)
        {
            try
            {
                var team = FindTeamSafely(clan.ClanTeamId);
                if (team == null) return;

                MarkTeamDirtySafely(team);

                // Force UI refresh for all online clan members
                foreach (var memberId in clan.ClanMemebers.Keys)
                {
                    var member = BasePlayer.Find(memberId);
                    if (member != null && member.IsConnected)
                    {
                        NextTick(() => {
                            if (member != null && member.IsConnected)
                            {
                                SendNetworkUpdateSafely(member);
                            }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans] Error refreshing team UI: {ex.Message}");
            }
        }

        #endregion
        #endregion
    }
}










