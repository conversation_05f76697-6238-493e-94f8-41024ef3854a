// Reference: 0Harmony
using Facepunch;
using HarmonyLib;
using Network;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using ProtoBuf;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using System.Text;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Admin Menu", "Skelee", "1.0.0")]
    [Description("A full blown admin menu for Awaken.")]

    public class AwakenAdminMenu : CovalencePlugin
    {
        #region Classes
        public class UIInfo
        {
            public BasePlayer selectedPlayer;
            public int playerListPage = 1;
        }

        public class PlayerInfo
        {
            public string adminTag = string.Empty;
            public string adminColor = string.Empty;
            public bool adminMode = false;
            public bool godmode = false;
            public bool noclip = false;
            public bool freeze = false;
            public bool vanish = false;
            public bool mapTeleport = false;
        }
        #endregion

        #region Defines
        [PluginReference] private Plugin ImageLibrary;
        private static Harmony _harmony;
        private static AwakenAdminMenu Instance;
        PlayerInfo pInfo;
        Dictionary<string, PlayerInfo> playerData = new Dictionary<string, PlayerInfo>();
        Dictionary<string, UIInfo> uiData = new Dictionary<string, UIInfo>();
        List<string> disabledCMDS = new List<string>();
        private Dictionary<string, string> playerSearch = new Dictionary<string, string>();
        private readonly Dictionary<string, string> playerToggles = new Dictionary<string, string>()
        {
            { "Godmode", "god" },
            { "Vanish", "vanish" },
            { "Noclip", "noclip" },
            { "Freeze", "freeze" },
            { "Map_Teleport", "mapteleport" }
        };
        private readonly Dictionary<string, string> playerActions = new Dictionary<string, string>()
        {
            { "Spectate Player", "spectateplayer" },
            { "Kick Player", "kickplayer" },
            { "Ban Player", "banplayer" },
            { "Heal Player", "healplayer" },
            { "Kill Player", "killplayer" },
            { "Strip Player", "stripplayer" },
            { "Teleport To Player", "teleporttoplayer" },
            { "Teleport Player To Me", "teleportplayertome" },
            { "Teleport Player To Player", "teleportplayertoplayer" },
            { "Manage Blueprints", "manageblueprints" },
            { "Manage Inventory", "manageinventory" },
            { "View Group", "viewgroup" }
        };
        public List<string> groupTags = new List<string>();
        private readonly object False = false;
        #endregion

        #region Language
        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["NoVanish"] = "Vanish is not running on this server.",
                ["NoPerms"] = "You do not have the permission for this command.",
                ["Frozen"] = "You have been <color=red>frozen</color> by {0}.",
                ["Unfrozen"] = "You have been <color=green>un-frozen</color> by {0}.",
                ["GodmodeOn"] = "You have been <color=green>granted</color> godmode by {0}.",
                ["GodmodeOff"] = "You have been <color=red>revoked</color> godmode by {0}.",
                ["VanishOn"] = "You have been <color=green>vanished</color> by {0}.",
                ["VanishOff"] = "You have been <color=red>unvanished</color> by {0}.",
                ["MapTeleportOn"] = "You have been <color=green>granted</color> map teleport by {0}.",
                ["MapTeleportOff"] = "You have been <color=red>revoked</color> map teleport by {0}."
            }, this);
        }
        #endregion

        #region Config
        static Configuration config;
        public class Logging
        {
            public bool noPermissionLogging = true;
            public string noPermissionWebhook = "";
            public bool serverCMDLogging = true;
            public string serverCMDWebhook = "";
            public bool adminMenuLogging = true;
            public string adminMenuWebhook = "";
        }

        public class Configuration
        {
            [JsonProperty(PropertyName = "Whitelited Commands")] public List<string> whitelistedCMDs;
            [JsonProperty(PropertyName = "Disabled Commands")] public List<string> disabledCMDs;
            [JsonProperty(PropertyName = "Use Discord Logging")] public bool useDiscordLogging;
            [JsonProperty(PropertyName = "Use File Logging")] public bool useFileLogging;
            [JsonProperty(PropertyName = "Logging")] public Logging Logging;
            public static Configuration DefaultConfig()
            {
                return new Configuration
                {
                    whitelistedCMDs = new List<string>(),
                    disabledCMDs = new List<string>()
                    {
                        "vanish",
                        "viewinv",
                        "tp",
                        "mute",
                        "unmute",
                        "god"
                    },
                    useDiscordLogging = false,
                    useFileLogging = true,
                    Logging = new Logging
                    {
                        noPermissionLogging = true,
                        noPermissionWebhook = "",
                        serverCMDLogging = true,
                        serverCMDWebhook = "",
                        adminMenuLogging = true,
                        adminMenuWebhook = ""
                    }
                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch
            {
                PrintWarning($"Creating new config file.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = Configuration.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Data
        private Save _save;
        public partial class Save
        {
            public BaseAdminGroup[] AdminGroups { get; set; } = new BaseAdminGroup[] { };
        }

        public partial class BaseAdminGroup
        {
            public string GroupName { get; set; }
            public string GroupTag { get; set; }
            public string GroupHexColor { get; set; }
            public BasePermission[] Permissions { get; set; }
            public Dictionary<string, string> Admins { get; set; }
        }

        public partial class BasePermission
        {
            public string CommandName { get; set; }
            public bool UseArgs { get; set; }
            public long ArgsCount { get; set; }
        }

        private void LoadDataFile()
        {
            try
            {
                _save = Interface.Oxide.DataFileSystem.ReadObject<Save>(Name);
                if (_save == null)
                {
                    Puts("Creating new admin data file with default structure.");
                    _save = CreateDefaultSave();
                    SaveData();
                }

                groupTags.Clear();
                if (_save.AdminGroups != null)
                {
                    foreach (var group in _save.AdminGroups)
                    {
                        if (!string.IsNullOrEmpty(group.GroupTag))
                            groupTags.Add(group.GroupTag);
                    }
                }

                uiData.Clear();
                foreach (BasePlayer player in BasePlayer.activePlayerList)
                    OnPlayerConnected(player, false);

                Puts($"Loaded admin data file with {_save.AdminGroups?.Length ?? 0} admin groups.");
            }
            catch (Exception ex)
            {
                PrintError($"Error loading admin data file: {ex.Message}");
                _save = CreateDefaultSave();
                SaveData();
            }
        }

        private Save CreateDefaultSave()
        {
            return new Save
            {
                AdminGroups = new BaseAdminGroup[]
                {
                    new BaseAdminGroup
                    {
                        GroupName = "Owner",
                        GroupTag = "[Owner]",
                        GroupHexColor = "#FF0000",
                        Permissions = new BasePermission[]
                        {
                            new BasePermission { CommandName = "*", UseArgs = false, ArgsCount = 0 }
                        },
                        Admins = new Dictionary<string, string>()
                    },
                    new BaseAdminGroup
                    {
                        GroupName = "Admin",
                        GroupTag = "[Admin]",
                        GroupHexColor = "#00FF00",
                        Permissions = new BasePermission[]
                        {
                            new BasePermission { CommandName = "kick", UseArgs = false, ArgsCount = 0 },
                            new BasePermission { CommandName = "ban", UseArgs = false, ArgsCount = 0 },
                            new BasePermission { CommandName = "heal", UseArgs = false, ArgsCount = 0 },
                            new BasePermission { CommandName = "tp", UseArgs = false, ArgsCount = 0 }
                        },
                        Admins = new Dictionary<string, string>()
                    }
                }
            };
        }
        private void SaveData()
        {
            Interface.Oxide.DataFileSystem.WriteObject(Name, _save);
        }

        [Command("reloaddata")]
        private void ReloadDataCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (!iPlayer.IsServer) return;
            LoadDataFile();
            Puts("Admin data file reloaded.");
        }

        [Command("addadmin")]
        private void AddAdminCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (!iPlayer.IsServer) return;

            if (args.Length < 2)
            {
                Puts("Usage: addadmin <steamid> <groupname>");
                Puts("Available groups: " + string.Join(", ", _save.AdminGroups.Select(g => g.GroupName)));
                return;
            }

            string steamId = args[0];
            string groupName = args[1];

            var group = _save.AdminGroups.FirstOrDefault(g => g.GroupName.Equals(groupName, StringComparison.OrdinalIgnoreCase));
            if (group == null)
            {
                Puts($"Group '{groupName}' not found. Available groups: " + string.Join(", ", _save.AdminGroups.Select(g => g.GroupName)));
                return;
            }

            if (group.Admins.ContainsKey(steamId))
            {
                Puts($"Player {steamId} is already in group {groupName}");
                return;
            }

            // Try to get player name (clean, without Steam IDs)
            string playerName = "Unknown";
            var player = BasePlayer.FindByID(ulong.Parse(steamId));
            if (player != null)
            {
                // Use clean player name without Steam IDs
                playerName = GetCleanPlayerName(player);
            }

            group.Admins[steamId] = playerName;
            SaveData();

            Puts($"Added {playerName} ({steamId}) to admin group '{groupName}'");

            // If player is online, initialize their data
            if (player != null)
                OnPlayerConnected(player, false);
        }

        [Command("removeadmin")]
        private void RemoveAdminCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (!iPlayer.IsServer) return;

            if (args.Length < 1)
            {
                Puts("Usage: removeadmin <steamid>");
                return;
            }

            string steamId = args[0];
            bool removed = false;
            string groupName = "";

            foreach (var group in _save.AdminGroups)
            {
                if (group.Admins.ContainsKey(steamId))
                {
                    group.Admins.Remove(steamId);
                    groupName = group.GroupName;
                    removed = true;
                    break;
                }
            }

            if (removed)
            {
                SaveData();
                Puts($"Removed {steamId} from admin group '{groupName}'");
            }
            else
            {
                Puts($"Player {steamId} is not in any admin group");
            }
        }

        [Command("listadmins")]
        private void ListAdminsCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (!iPlayer.IsServer) return;

            Puts("Current admin groups and members:");
            foreach (var group in _save.AdminGroups)
            {
                Puts($"Group: {group.GroupName} ({group.GroupTag})");
                if (group.Admins.Count == 0)
                {
                    Puts("  No members");
                }
                else
                {
                    foreach (var admin in group.Admins)
                    {
                        Puts($"  {admin.Value} ({admin.Key})");
                    }
                }
                Puts("");
            }
        }
        #endregion

        #region Functions
        void RenamePlayer(BasePlayer player, bool adminMode = false)
        {
            if (playerData.TryGetValue(player.UserIDString, out pInfo))
            {
                if (adminMode)
                {
                    if (string.IsNullOrEmpty(pInfo.adminTag)) return;

                    // Get clean player name without Steam IDs
                    var cleanName = GetCleanPlayerName(player);

                    // Only add admin tag if it's not already present
                    if (!cleanName.Contains(pInfo.adminTag))
                        player.displayName = $"{pInfo.adminTag} {cleanName}";
                    else
                        player.displayName = cleanName; // Ensure no Steam IDs
                }
                else
                {
                    // Remove admin tag and ensure clean name
                    var cleanName = GetCleanPlayerName(player);
                    player.displayName = cleanName.Replace(pInfo.adminTag + " ", string.Empty).Trim();
                }
            }
        }
        private bool HasPermission(BasePlayer player, string command)
        {
            if (!playerData[player.UserIDString].adminMode) return false;

            BaseAdminGroup group = FindPlayersGroup(player.UserIDString);

            // If player has Oxide admin permission but no custom group, give full access
            if (group == null && permission.UserHasPermission(player.UserIDString, "awakenadminmenu.admin"))
                return true;

            if (group == null) return false;

            command = command.Replace("\"", "");
            string[] commands = command.Split(' ');
            string finalCmd;

            foreach (BasePermission perm in group.Permissions)
            {
                if (perm.CommandName == "*") return true;
                if (perm.UseArgs)
                {
                    finalCmd = commands[0];
                    for (int i = 1; i < commands.Length; i++)
                    {
                        if (i > perm.ArgsCount) break;
                        finalCmd += " " + commands[i];
                    }
                }
                else
                    finalCmd = command;

                if (perm.CommandName.Equals(finalCmd)) return true;
            }
            return false;
        }
        private bool IsAdmin(string id)
        {
            // Check custom admin groups first
            foreach (BaseAdminGroup group in _save.AdminGroups)
                if (group.Admins.ContainsKey(id))
                    return true;

            // Fallback to Oxide permissions
            return permission.UserHasPermission(id, "awakenadminmenu.admin");
        }

        private bool CanUseBasicUI(string id)
        {
            // Allow basic UI access with either use permission or admin permission
            return permission.UserHasPermission(id, "awakenadminmenu.use") || IsAdmin(id);
        }

        private bool CanAccessServerTab(string id)
        {
            // Server tab requires special permission
            return permission.UserHasPermission(id, "awakenadminmenu.server") || IsAdmin(id);
        }
        private BaseAdminGroup FindPlayersGroup(string id)
        {
            foreach (BaseAdminGroup group in _save.AdminGroups)
                if (group.Admins.ContainsKey(id))
                    return group;

            return null;
        }
        private static int GetPageCount(BasePlayer player, int count, int pageSize)
        {
            double result = ((count - 1) / pageSize) + 1;
            bool isInt = result % 1 == 0;

            if (isInt)
                return Convert.ToInt32(result);
            else
            {
                double firstnumber = Math.Truncate(result);
                int returnnum = Convert.ToInt32(firstnumber);
                return returnnum;
            }
        }
        private static string GetGrid(Vector3 pos)
        {
            const float scale = 150f;
            float x = pos.x + World.Size / 2f;
            float z = pos.z + World.Size / 2f;
            int lat = (int)(x / scale);
            char latChar = (char)('A' + lat);
            int lon = (int)(World.Size / scale - z / scale);
            return latChar + lon.ToString();
        }
        private void SendDiscordMessage(string webhook, string embedName, int embedColor, Dictionary<string, string> values, string content = null)
        {
            if (string.IsNullOrEmpty(webhook))
            {
                Debug.LogWarning("Discord webhook URL is empty.");
                return;
            }

            try
            {
                var fields = new List<object>();
                foreach (var kvp in values)
                {
                    fields.Add(new
                    {
                        name = kvp.Key,
                        value = kvp.Value,
                        inline = true
                    });
                }

                var embed = new
                {
                    title = embedName,
                    color = embedColor,
                    fields = fields,
                    timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                };

                var payload = new
                {
                    content = content,
                    embeds = new[] { embed }
                };

                string json = JsonConvert.SerializeObject(payload);
                webrequest.Enqueue(webhook, json, (code, response) =>
                {
                    if (code != 200 && code != 204)
                    {
                        Debug.LogWarning($"Failed to send Discord webhook. Response code: {code}");
                    }
                }, this, RequestMethod.POST, new Dictionary<string, string>
                {
                    ["Content-Type"] = "application/json"
                });
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error sending Discord webhook: {ex.Message}");
            }
        }

        /* Credit to nivex */
        private void TeleportPlayer(BasePlayer player, Vector3 location)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;
            try
            {
                player.UpdateActiveItem(default(ItemId));
                player.EnsureDismounted();

                if (player.HasParent())
                    player.SetParent(null, true, true);

                if (player.IsConnected)
                {
                    player.EndLooting();
                    if (!player.IsSleeping())
                    {
                        player.SetPlayerFlag(BasePlayer.PlayerFlags.Sleeping, true);
                        player.sleepStartTime = UnityEngine.Time.time;
                        BasePlayer.sleepingPlayerList.Add(player);
                        player.CancelInvoke("InventoryUpdate");
                        player.CancelInvoke("TeamUpdate");
                    }
                }

                player.RemoveFromTriggers();
                player.Teleport(location);

                if (player.IsConnected && !Net.sv.visibility.IsInside(player.net.group, location))
                {
                    player.SetPlayerFlag(BasePlayer.PlayerFlags.ReceivingSnapshot, true);
                    player.ClientRPCPlayer(null, player, "StartLoading");
                    player.SendEntityUpdate();

                    if (!pInfo.vanish)
                    {
                        player.UpdateNetworkGroup();
                        player.SendNetworkUpdateImmediate(false);
                    }
                }
            }
            finally
            {
                if (!pInfo.vanish)
                    player.ForceUpdateTriggers();
            }
        }
        private bool InAdmInMode(BasePlayer player)
        {
            if (!playerData.ContainsKey(player.UserIDString)) return false;
            return playerData[player.UserIDString].adminMode;
        }
        private string GetColor(BasePlayer player)
        {
            return playerData[player.UserIDString].adminColor;
        }
        private void ToggleIcon(BasePlayer player, string icon)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;
            switch (icon)
            {
                case "All":
                    CuiHelper.DestroyUi(player, "AdminMode");
                    CuiHelper.DestroyUi(player, "Vanish");
                    CuiHelper.DestroyUi(player, "Godmode");
                    var container = new CuiElementContainer();

                    if (pInfo.adminMode)
                        container.Add(new CuiElement
                        {
                            Name = "AdminMode",
                            Parent = "Hud",
                            Components = {
                                new CuiRawImageComponent { Color = "1 1 1 0.8", Png = (string)ImageLibrary?.Call("GetImage", "AdminModeIcon"), Sprite = "assets/content/textures/generic/fulltransparent.tga" },
                                new CuiRectTransformComponent { AnchorMin = "0 1", AnchorMax = "0 1", OffsetMin = "10.543 -64.557", OffsetMax = "39.857 -35.243" }
                            }
                        });

                    if (pInfo.vanish)
                        container.Add(new CuiElement
                        {
                            Name = "Vanish",
                            Parent = "Hud",
                            Components = {
                                new CuiRawImageComponent { Color = "1 1 1 0.8", Png = (string)ImageLibrary?.Call("GetImage", "VanishIcon"), Sprite = "assets/content/textures/generic/fulltransparent.tga" },
                                new CuiRectTransformComponent { AnchorMin = "0 1", AnchorMax = "0 1", OffsetMin = "10.543 -138.797", OffsetMax = "39.857 -109.483" }
                            }
                        });

                    if (pInfo.godmode)
                        container.Add(new CuiElement
                        {
                            Name = "Godmode",
                            Parent = "Hud",
                            Components = {
                                new CuiRawImageComponent { Color = "1 1 1 0.8", Png = (string)ImageLibrary?.Call("GetImage", "GodmodeIcon"),Sprite = "assets/content/textures/generic/fulltransparent.tga" },
                                new CuiRectTransformComponent { AnchorMin = "0 1", AnchorMax = "0 1", OffsetMin = "10.543 -101.367", OffsetMax = "39.857 -72.053" }
                            }
                        });

                    if (container.Count == 0) return;
                    CuiHelper.AddUi(player, container);
                    break;

                case "AdminMode":
                    if (!pInfo.adminMode) { CuiHelper.DestroyUi(player, "AdminMode"); return; }
                    var adminContainer = new CuiElementContainer();
                    adminContainer.Add(new CuiElement
                    {
                        Name = "AdminMode",
                        Parent = "Hud",
                        Components = {
                            new CuiRawImageComponent { Color = "1 1 1 0.8", Png = (string)ImageLibrary?.Call("GetImage", "AdminModeIcon"), Sprite = "assets/content/textures/generic/fulltransparent.tga" },
                            new CuiRectTransformComponent { AnchorMin = "0 1", AnchorMax = "0 1", OffsetMin = "10.543 -64.557", OffsetMax = "39.857 -35.243" }
                        }
                    });
                    CuiHelper.AddUi(player, adminContainer);
                    break;
                case "Vanish":
                    if (!pInfo.vanish) { CuiHelper.DestroyUi(player, "Vanish"); return; }
                    var vanishContainer = new CuiElementContainer();
                    vanishContainer.Add(new CuiElement
                    {
                        Name = "Vanish",
                        Parent = "Hud",
                        Components = {
                            new CuiRawImageComponent { Color = "1 1 1 0.8", Png = (string)ImageLibrary?.Call("GetImage", "VanishIcon"), Sprite = "assets/content/textures/generic/fulltransparent.tga" },
                            new CuiRectTransformComponent { AnchorMin = "0 1", AnchorMax = "0 1", OffsetMin = "10.543 -138.797", OffsetMax = "39.857 -109.483" }
                        }
                    });
                    CuiHelper.AddUi(player, vanishContainer);
                    break;
                case "Godmode":
                    if (!pInfo.godmode) { CuiHelper.DestroyUi(player, "Godmode"); return; }
                    var godmodeContainer = new CuiElementContainer();
                    godmodeContainer.Add(new CuiElement
                    {
                        Name = "Godmode",
                        Parent = "Hud",
                        Components = {
                            new CuiRawImageComponent { Color = "1 1 1 0.8", Png = (string)ImageLibrary?.Call("GetImage", "GodmodeIcon"), Sprite = "assets/content/textures/generic/fulltransparent.tga" },
                            new CuiRectTransformComponent { AnchorMin = "0 1", AnchorMax = "0 1", OffsetMin = "10.543 -101.367", OffsetMax = "39.857 -72.053" }
                        }
                    });
                    CuiHelper.AddUi(player, godmodeContainer);
                    break;
            }
        }
        private void VanishManager(BasePlayer player, bool vanish = false)
        {
            player.metabolism.radiation_poison.max = vanish ? 0 : 500;
            player.metabolism.isDirty = true;
            player.metabolism.SendChangesToClient();
        }

        /// <summary>
        /// Gets a clean player name without Steam IDs or unwanted additions
        /// </summary>
        private string GetCleanPlayerName(BasePlayer player)
        {
            if (player == null) return "Unknown";

            // Prefer _name (original name) over displayName (which might have modifications)
            string name = player._name ?? player.displayName ?? "Unknown";

            // Remove Steam ID patterns: (76561198XXXXXXXXX) or [76561198XXXXXXXXX]
            name = System.Text.RegularExpressions.Regex.Replace(name, @"[\[\(]76561198\d{9}[\]\)]", "").Trim();

            // Remove any 17-digit numbers in brackets/parentheses
            name = System.Text.RegularExpressions.Regex.Replace(name, @"[\[\(]\d{17}[\]\)]", "").Trim();

            // Remove clan tag patterns: [TAG] or (TAG) - but only if they look like clan tags (2-6 uppercase chars)
            name = System.Text.RegularExpressions.Regex.Replace(name, @"[\[\(][A-Z0-9]{2,6}[\]\)]", "").Trim();

            // Remove any remaining empty brackets or parentheses
            name = System.Text.RegularExpressions.Regex.Replace(name, @"^\s*[\[\(]\s*[\]\)]\s*", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*[\[\(]\s*[\]\)]\s*$", "").Trim();

            // Clean up multiple spaces
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s+", " ").Trim();

            // If name is empty after cleaning, return "Unknown"
            return string.IsNullOrEmpty(name) ? "Unknown" : name;
        }
        #endregion

        #region Hooks
        private void Init()
        {
            Instance = this;
            _harmony = new Harmony("com.Skelee.AwakenAdminMenu");

            // Register permissions
            permission.RegisterPermission("awakenadminmenu.use", this);
            permission.RegisterPermission("awakenadminmenu.admin", this);
            permission.RegisterPermission("awakenadminmenu.server", this);
        }

        private void Loaded()
        {
            LoadDataFile();

            // Apply Harmony patches
            try
            {
                _harmony.PatchAll();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to apply Harmony patches: {ex.Message}");
            }
        }

        private void Unload()
        {
            // Cleanup UI for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "AdminMenu");
                CuiHelper.DestroyUi(player, "AdminMode");
                CuiHelper.DestroyUi(player, "Vanish");
                CuiHelper.DestroyUi(player, "Godmode");
            }

            _harmony?.UnpatchAll("com.Skelee.AwakenAdminMenu");
            Instance = null;
        }

        private void OnPlayerConnected(BasePlayer player, bool initial = true)
        {
            if (player == null) return;

            if (!playerData.ContainsKey(player.UserIDString))
            {
                playerData[player.UserIDString] = new PlayerInfo();
            }

            if (!uiData.ContainsKey(player.UserIDString))
            {
                uiData[player.UserIDString] = new UIInfo();
            }

            // Check if player is admin and set up their data
            if (IsAdmin(player.UserIDString))
            {
                var group = FindPlayersGroup(player.UserIDString);
                if (group != null)
                {
                    playerData[player.UserIDString].adminTag = group.GroupTag;
                    playerData[player.UserIDString].adminColor = group.GroupHexColor;
                }
            }
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            if (player == null) return;

            // Clean up UI data but keep player data for reconnection
            if (uiData.ContainsKey(player.UserIDString))
            {
                uiData.Remove(player.UserIDString);
            }
        }

        private object OnServerCommand(ConsoleSystem.Arg arg)
        {
            if (arg?.Player() == null) return null;

            BasePlayer player = arg.Player();
            string command = arg.cmd?.FullName;

            if (string.IsNullOrEmpty(command)) return null;

            // Check if command is disabled
            if (config.disabledCMDs.Contains(command.Split('.').LastOrDefault()))
            {
                return false;
            }

            // Only check permissions for admin commands when player is in admin mode
            // Don't block regular commands or commands from non-admins
            if (IsAdmin(player.UserIDString) && InAdmInMode(player))
            {
                // Only check permissions for server admin commands (not regular game commands)
                if (IsServerAdminCommand(command))
                {
                    if (!HasPermission(player, command))
                    {
                        player.ChatMessage("You don't have permission to use this admin command.");

                        // Log permission denial
                        if (config.useDiscordLogging && !string.IsNullOrEmpty(config.Logging.noPermissionWebhook))
                        {
                            var values = new Dictionary<string, string>
                            {
                                ["Player"] = $"{player.displayName} ({player.UserIDString})",
                                ["Command"] = command,
                                ["Time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                            };
                            SendDiscordMessage(config.Logging.noPermissionWebhook, "Permission Denied", 16711680, values);
                        }

                        return false;
                    }

                    // Log admin command usage
                    if (config.useDiscordLogging && !string.IsNullOrEmpty(config.Logging.serverCMDWebhook))
                    {
                        var values = new Dictionary<string, string>
                        {
                            ["Admin"] = $"{player.displayName} ({player.UserIDString})",
                            ["Command"] = command,
                            ["Arguments"] = string.Join(" ", arg.Args ?? new string[0]),
                            ["Time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        };
                        SendDiscordMessage(config.Logging.serverCMDWebhook, "Admin Command Used", 65280, values);
                    }
                }
            }

            return null;
        }

        private bool IsServerAdminCommand(string command)
        {
            // Define which commands should be checked for permissions
            // Only server admin commands, not regular game commands
            var adminCommands = new HashSet<string>
            {
                "kick", "ban", "unban", "banlist", "banlistex", "listid", "users", "status",
                "say", "notice", "players", "find", "teleport", "teleportany", "teleportpos",
                "spawn", "spawnitem", "give", "givearm", "giveid", "inventory.give",
                "god", "noclip", "debugcamera", "spectate", "weather.cloud_coverage",
                "weather.fog", "weather.rain", "weather.wind", "env.time", "server.save",
                "server.writecfg", "server.stop", "server.quit", "oxide.reload", "oxide.load",
                "oxide.unload", "rcon.login", "rcon.web", "global.restart", "global.quit"
            };

            string baseCommand = command.Split('.').FirstOrDefault()?.ToLower();
            return adminCommands.Contains(baseCommand) || adminCommands.Contains(command.ToLower());
        }
        #endregion

        #region Commands
        [Command("adminmenu", "am")]
        private void AdminMenuCommand(IPlayer iPlayer, string command, string[] args)
        {
            var player = iPlayer.Object as BasePlayer;
            if (player == null) return;

            if (!CanUseBasicUI(player.UserIDString))
            {
                player.ChatMessage("You don't have permission to use the admin menu.");
                return;
            }

            if (args.Length == 0)
            {
                ShowMainMenu(player);
                return;
            }

            switch (args[0].ToLower())
            {
                case "toggle":
                    if (IsAdmin(player.UserIDString))
                        ToggleAdminMode(player);
                    else
                        player.ChatMessage("You don't have permission to toggle admin mode.");
                    break;
                case "close":
                    CuiHelper.DestroyUi(player, "AdminMenu");
                    break;
                default:
                    ShowMainMenu(player);
                    break;
            }
        }

        [Command("adminmode")]
        private void AdminModeCommand(IPlayer iPlayer, string command, string[] args)
        {
            var player = iPlayer.Object as BasePlayer;
            if (player == null) return;

            if (!IsAdmin(player.UserIDString))
            {
                player.ChatMessage("You don't have permission to use admin mode.");
                return;
            }

            ToggleAdminMode(player);
        }

        private void ToggleAdminMode(BasePlayer player)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;

            pInfo.adminMode = !pInfo.adminMode;

            if (pInfo.adminMode)
            {
                player.ChatMessage("<color=green>Admin mode enabled</color>");
                RenamePlayer(player, true);
            }
            else
            {
                player.ChatMessage("<color=red>Admin mode disabled</color>");
                RenamePlayer(player, false);

                // Disable all admin features when leaving admin mode
                if (pInfo.godmode) ToggleGodmode(player);
                if (pInfo.vanish) ToggleVanish(player);
                if (pInfo.noclip) ToggleNoclip(player);
                if (pInfo.freeze) ToggleFreeze(player);
            }

            ToggleIcon(player, "All");
        }
        #endregion

        #region UI
        private void ShowMainMenu(BasePlayer player)
        {
            if (!CanUseBasicUI(player.UserIDString)) return;

            CuiHelper.DestroyUi(player, "AdminMenu");

            var container = new CuiElementContainer();
            bool isAdmin = IsAdmin(player.UserIDString);
            bool canAccessServer = CanAccessServerTab(player.UserIDString);

            // Main panel
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.2 0.2", AnchorMax = "0.8 0.8" },
                CursorEnabled = true
            }, "Overlay", "AdminMenu");

            // Title
            string titleText = isAdmin ? "Awaken ADMIN MENU" : "Awaken SERVER MENU";
            container.Add(new CuiLabel
            {
                Text = { Text = titleText, FontSize = 24, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0 0.9", AnchorMax = "1 1" }
            }, "AdminMenu");

            // Close button
            container.Add(new CuiButton
            {
                Button = { Command = "adminmenu close", Color = "0.8 0.2 0.2 0.8" },
                RectTransform = { AnchorMin = "0.92 0.92", AnchorMax = "0.98 0.98" },
                Text = { Text = "X", FontSize = 16, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "AdminMenu");

            float yPos = 0.8f;

            // Admin mode toggle (only for admins)
            if (isAdmin)
            {
                var adminModeColor = playerData[player.UserIDString].adminMode ? "0.2 0.8 0.2 0.8" : "0.8 0.2 0.2 0.8";
                var adminModeText = playerData[player.UserIDString].adminMode ? "Admin Mode: ON" : "Admin Mode: OFF";

                container.Add(new CuiButton
                {
                    Button = { Command = "adminmenu toggle", Color = adminModeColor },
                    RectTransform = { AnchorMin = "0.05 0.8", AnchorMax = "0.45 0.85" },
                    Text = { Text = adminModeText, FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, "AdminMenu");

                // Player list button (only for admins)
                container.Add(new CuiButton
                {
                    Button = { Command = "adminmenu.playerlist", Color = "0.2 0.4 0.8 0.8" },
                    RectTransform = { AnchorMin = "0.55 0.8", AnchorMax = "0.95 0.85" },
                    Text = { Text = "Player Management", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, "AdminMenu");

                yPos = 0.7f;

                // Toggle buttons (only for admins)
                foreach (var toggle in playerToggles)
                {
                    bool isActive = GetToggleState(player, toggle.Value);
                    var color = isActive ? "0.2 0.8 0.2 0.8" : "0.4 0.4 0.4 0.8";

                    container.Add(new CuiButton
                    {
                        Button = { Command = $"adminmenu.toggle {toggle.Value}", Color = color },
                        RectTransform = { AnchorMin = $"0.05 {yPos - 0.05}", AnchorMax = $"0.45 {yPos}" },
                        Text = { Text = toggle.Key, FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                    }, "AdminMenu");

                    yPos -= 0.08f;
                }
            }

            // Server tab button (for users with server permission)
            if (canAccessServer)
            {
                container.Add(new CuiButton
                {
                    Button = { Command = "adminmenu.servertab", Color = "0.4 0.6 0.2 0.8" },
                    RectTransform = { AnchorMin = $"0.55 {yPos - 0.05}", AnchorMax = $"0.95 {yPos}" },
                    Text = { Text = "Server Information", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, "AdminMenu");
            }
            else if (!isAdmin)
            {
                // Show message for users without any special permissions
                container.Add(new CuiLabel
                {
                    Text = { Text = "You have basic access to the server menu.\nContact an admin for additional permissions.", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.8 0.8 0.8 1" },
                    RectTransform = { AnchorMin = "0.1 0.4", AnchorMax = "0.9 0.6" }
                }, "AdminMenu");
            }

            CuiHelper.AddUi(player, container);
        }

        private void ShowPlayerList(BasePlayer player, int page = 1)
        {
            if (!IsAdmin(player.UserIDString)) return;

            CuiHelper.DestroyUi(player, "AdminMenu");

            var container = new CuiElementContainer();
            var players = BasePlayer.activePlayerList.ToList();
            int pageSize = 10;
            int totalPages = Mathf.CeilToInt((float)players.Count / pageSize);
            page = Mathf.Clamp(page, 1, totalPages);

            // Main panel
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.1 0.1", AnchorMax = "0.9 0.9" },
                CursorEnabled = true
            }, "Overlay", "AdminMenu");

            // Title
            container.Add(new CuiLabel
            {
                Text = { Text = $"PLAYER LIST - Page {page}/{totalPages}", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0 0.92", AnchorMax = "1 1" }
            }, "AdminMenu");

            // Back button
            container.Add(new CuiButton
            {
                Button = { Command = "adminmenu", Color = "0.4 0.4 0.4 0.8" },
                RectTransform = { AnchorMin = "0.02 0.92", AnchorMax = "0.1 0.98" },
                Text = { Text = "Back", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "AdminMenu");

            // Player entries
            float yPos = 0.85f;
            int startIndex = (page - 1) * pageSize;
            int endIndex = Mathf.Min(startIndex + pageSize, players.Count);

            for (int i = startIndex; i < endIndex; i++)
            {
                var targetPlayer = players[i];
                if (targetPlayer == null) continue;

                // Player info panel
                container.Add(new CuiPanel
                {
                    Image = { Color = "0.2 0.2 0.2 0.8" },
                    RectTransform = { AnchorMin = $"0.02 {yPos - 0.06}", AnchorMax = $"0.98 {yPos}" }
                }, "AdminMenu", $"PlayerEntry_{i}");

                // Player name and info
                var playerInfo = $"{targetPlayer.displayName} ({targetPlayer.UserIDString}) - {GetGrid(targetPlayer.transform.position)}";
                container.Add(new CuiLabel
                {
                    Text = { Text = playerInfo, FontSize = 10, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    RectTransform = { AnchorMin = "0.02 0", AnchorMax = "0.6 1" }
                }, $"PlayerEntry_{i}");

                // Action buttons
                container.Add(new CuiButton
                {
                    Button = { Command = $"adminmenu.selectplayer {targetPlayer.UserIDString}", Color = "0.2 0.6 0.8 0.8" },
                    RectTransform = { AnchorMin = "0.62 0.1", AnchorMax = "0.75 0.9" },
                    Text = { Text = "Select", FontSize = 10, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, $"PlayerEntry_{i}");

                container.Add(new CuiButton
                {
                    Button = { Command = $"adminmenu.quickaction kick {targetPlayer.UserIDString}", Color = "0.8 0.4 0.2 0.8" },
                    RectTransform = { AnchorMin = "0.77 0.1", AnchorMax = "0.85 0.9" },
                    Text = { Text = "Kick", FontSize = 10, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, $"PlayerEntry_{i}");

                container.Add(new CuiButton
                {
                    Button = { Command = $"adminmenu.quickaction ban {targetPlayer.UserIDString}", Color = "0.8 0.2 0.2 0.8" },
                    RectTransform = { AnchorMin = "0.87 0.1", AnchorMax = "0.95 0.9" },
                    Text = { Text = "Ban", FontSize = 10, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, $"PlayerEntry_{i}");

                yPos -= 0.08f;
            }

            // Pagination
            if (totalPages > 1)
            {
                if (page > 1)
                {
                    container.Add(new CuiButton
                    {
                        Button = { Command = $"adminmenu.playerlist {page - 1}", Color = "0.4 0.4 0.4 0.8" },
                        RectTransform = { AnchorMin = "0.4 0.02", AnchorMax = "0.48 0.08" },
                        Text = { Text = "Previous", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                    }, "AdminMenu");
                }

                if (page < totalPages)
                {
                    container.Add(new CuiButton
                    {
                        Button = { Command = $"adminmenu.playerlist {page + 1}", Color = "0.4 0.4 0.4 0.8" },
                        RectTransform = { AnchorMin = "0.52 0.02", AnchorMax = "0.6 0.08" },
                        Text = { Text = "Next", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                    }, "AdminMenu");
                }
            }

            CuiHelper.AddUi(player, container);
        }

        private bool GetToggleState(BasePlayer player, string toggleName)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return false;

            return toggleName switch
            {
                "god" => pInfo.godmode,
                "vanish" => pInfo.vanish,
                "noclip" => pInfo.noclip,
                "freeze" => pInfo.freeze,
                "mapteleport" => pInfo.mapTeleport,
                _ => false
            };
        }

        private void ShowServerTab(BasePlayer player)
        {
            if (!CanAccessServerTab(player.UserIDString)) return;

            CuiHelper.DestroyUi(player, "AdminMenu");

            var container = new CuiElementContainer();

            // Main panel
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.2 0.2", AnchorMax = "0.8 0.8" },
                CursorEnabled = true
            }, "Overlay", "AdminMenu");

            // Title
            container.Add(new CuiLabel
            {
                Text = { Text = "SERVER INFORMATION", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0 0.9", AnchorMax = "1 1" }
            }, "AdminMenu");

            // Back button
            container.Add(new CuiButton
            {
                Button = { Command = "adminmenu", Color = "0.4 0.4 0.4 0.8" },
                RectTransform = { AnchorMin = "0.02 0.92", AnchorMax = "0.1 0.98" },
                Text = { Text = "Back", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "AdminMenu");

            // Server information
            var serverInfo = $"Server Name: {ConVar.Server.hostname}\n" +
                           $"Players Online: {BasePlayer.activePlayerList.Count}/{ConVar.Server.maxplayers}\n" +
                           $"Server FPS: {Performance.current.frameRate:F0}\n" +
                           $"Uptime: {TimeSpan.FromSeconds(UnityEngine.Time.realtimeSinceStartup):dd\\:hh\\:mm\\:ss}\n" +
                           $"Map: {ConVar.Server.level} ({World.Size})\n" +
                           $"Seed: {World.Seed}";

            container.Add(new CuiLabel
            {
                Text = { Text = serverInfo, FontSize = 14, Align = TextAnchor.UpperLeft, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.05 0.4", AnchorMax = "0.95 0.85" }
            }, "AdminMenu");

            CuiHelper.AddUi(player, container);
        }
        #endregion

        #region Console Commands
        [ConsoleCommand("adminmenu.playerlist")]
        private void PlayerListConsoleCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !IsAdmin(player.UserIDString)) return;

            int page = 1;
            if (arg.Args != null && arg.Args.Length > 0)
                int.TryParse(arg.Args[0], out page);

            ShowPlayerList(player, page);
        }

        [ConsoleCommand("adminmenu.servertab")]
        private void ServerTabConsoleCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !CanAccessServerTab(player.UserIDString)) return;

            ShowServerTab(player);
        }

        [ConsoleCommand("adminmenu.toggle")]
        private void ToggleConsoleCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !IsAdmin(player.UserIDString) || !InAdmInMode(player)) return;

            if (arg.Args == null || arg.Args.Length == 0) return;

            string toggleType = arg.Args[0].ToLower();

            switch (toggleType)
            {
                case "god":
                    ToggleGodmode(player);
                    break;
                case "vanish":
                    ToggleVanish(player);
                    break;
                case "noclip":
                    ToggleNoclip(player);
                    break;
                case "freeze":
                    ToggleFreeze(player);
                    break;
                case "mapteleport":
                    ToggleMapTeleport(player);
                    break;
            }

            // Refresh the main menu to show updated states
            ShowMainMenu(player);
        }

        [ConsoleCommand("adminmenu.selectplayer")]
        private void SelectPlayerConsoleCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !IsAdmin(player.UserIDString)) return;

            if (arg.Args == null || arg.Args.Length == 0) return;

            string targetId = arg.Args[0];
            var targetPlayer = BasePlayer.FindByID(ulong.Parse(targetId));

            if (targetPlayer == null)
            {
                player.ChatMessage("Target player not found.");
                return;
            }

            if (!uiData.TryGetValue(player.UserIDString, out var ui)) return;
            ui.selectedPlayer = targetPlayer;

            ShowPlayerActions(player, targetPlayer);
        }

        [ConsoleCommand("adminmenu.quickaction")]
        private void QuickActionConsoleCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !IsAdmin(player.UserIDString) || !InAdmInMode(player)) return;

            if (arg.Args == null || arg.Args.Length < 2) return;

            string action = arg.Args[0].ToLower();
            string targetId = arg.Args[1];
            var targetPlayer = BasePlayer.FindByID(ulong.Parse(targetId));

            if (targetPlayer == null)
            {
                player.ChatMessage("Target player not found.");
                return;
            }

            switch (action)
            {
                case "kick":
                    targetPlayer.Kick("Kicked by admin");
                    LogAdminAction(player, $"Kicked {targetPlayer.displayName}");
                    break;
                case "ban":
                    Server.Command($"ban {targetPlayer.UserIDString} \"Banned by admin\"");
                    LogAdminAction(player, $"Banned {targetPlayer.displayName}");
                    break;
            }

            ShowPlayerList(player);
        }

        private void ShowPlayerActions(BasePlayer player, BasePlayer targetPlayer)
        {
            CuiHelper.DestroyUi(player, "AdminMenu");

            var container = new CuiElementContainer();

            // Main panel
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.2 0.2", AnchorMax = "0.8 0.8" },
                CursorEnabled = true
            }, "Overlay", "AdminMenu");

            // Title
            container.Add(new CuiLabel
            {
                Text = { Text = $"PLAYER ACTIONS - {targetPlayer.displayName}", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0 0.9", AnchorMax = "1 1" }
            }, "AdminMenu");

            // Back button
            container.Add(new CuiButton
            {
                Button = { Command = "adminmenu.playerlist", Color = "0.4 0.4 0.4 0.8" },
                RectTransform = { AnchorMin = "0.02 0.92", AnchorMax = "0.1 0.98" },
                Text = { Text = "Back", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "AdminMenu");

            // Player info
            var playerInfo = $"Name: {targetPlayer.displayName}\nSteam ID: {targetPlayer.UserIDString}\nPosition: {GetGrid(targetPlayer.transform.position)}\nHealth: {targetPlayer.health:F0}/{targetPlayer.MaxHealth():F0}";
            container.Add(new CuiLabel
            {
                Text = { Text = playerInfo, FontSize = 12, Align = TextAnchor.UpperLeft, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.05 0.7", AnchorMax = "0.95 0.85" }
            }, "AdminMenu");

            // Action buttons
            float yPos = 0.6f;
            foreach (var action in playerActions)
            {
                container.Add(new CuiButton
                {
                    Button = { Command = $"adminmenu.playeraction {action.Value} {targetPlayer.UserIDString}", Color = "0.2 0.4 0.8 0.8" },
                    RectTransform = { AnchorMin = $"0.05 {yPos - 0.05}", AnchorMax = $"0.95 {yPos}" },
                    Text = { Text = action.Key, FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, "AdminMenu");

                yPos -= 0.08f;
            }

            CuiHelper.AddUi(player, container);
        }

        [ConsoleCommand("adminmenu.playeraction")]
        private void PlayerActionConsoleCommand(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !IsAdmin(player.UserIDString) || !InAdmInMode(player)) return;

            if (arg.Args == null || arg.Args.Length < 2) return;

            string action = arg.Args[0];
            string targetId = arg.Args[1];
            var targetPlayer = BasePlayer.FindByID(ulong.Parse(targetId));

            if (targetPlayer == null)
            {
                player.ChatMessage("Target player not found.");
                return;
            }

            ExecutePlayerAction(player, targetPlayer, action);
        }

        private void ExecutePlayerAction(BasePlayer admin, BasePlayer target, string action)
        {
            switch (action)
            {
                case "spectateplayer":
                    admin.StartSpectating();
                    admin.SendConsoleCommand("spectate", target.UserIDString);
                    LogAdminAction(admin, $"Started spectating {target.displayName}");
                    break;

                case "kickplayer":
                    target.Kick("Kicked by admin");
                    LogAdminAction(admin, $"Kicked {target.displayName}");
                    break;

                case "banplayer":
                    Server.Command($"ban {target.UserIDString} \"Banned by admin\"");
                    LogAdminAction(admin, $"Banned {target.displayName}");
                    break;

                case "healplayer":
                    target.Heal(target.MaxHealth());
                    target.ChatMessage($"You have been healed by {admin.displayName}");
                    LogAdminAction(admin, $"Healed {target.displayName}");
                    break;

                case "killplayer":
                    target.Die();
                    LogAdminAction(admin, $"Killed {target.displayName}");
                    break;

                case "stripplayer":
                    target.inventory.Strip();
                    target.ChatMessage($"Your inventory has been stripped by {admin.displayName}");
                    LogAdminAction(admin, $"Stripped {target.displayName}");
                    break;

                case "teleporttoplayer":
                    TeleportPlayer(admin, target.transform.position);
                    LogAdminAction(admin, $"Teleported to {target.displayName}");
                    break;

                case "teleportplayertome":
                    TeleportPlayer(target, admin.transform.position);
                    target.ChatMessage($"You have been teleported by {admin.displayName}");
                    LogAdminAction(admin, $"Teleported {target.displayName} to self");
                    break;
            }

            ShowPlayerActions(admin, target);
        }

        private void LogAdminAction(BasePlayer admin, string action)
        {
            if (config.useDiscordLogging && !string.IsNullOrEmpty(config.Logging.adminMenuWebhook))
            {
                var values = new Dictionary<string, string>
                {
                    ["Admin"] = $"{admin.displayName} ({admin.UserIDString})",
                    ["Action"] = action,
                    ["Time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                SendDiscordMessage(config.Logging.adminMenuWebhook, "Admin Action", 255, values);
            }

            if (config.useFileLogging)
            {
                LogToFile("AdminActions", $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {admin.displayName} ({admin.UserIDString}): {action}", this);
            }
        }
        #endregion

        #region Toggle Functions
        private void ToggleGodmode(BasePlayer player)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;

            pInfo.godmode = !pInfo.godmode;
            player.metabolism.bleeding.max = pInfo.godmode ? 0 : 1;
            player.metabolism.radiation_poison.max = pInfo.godmode ? 0 : 100;
            player.metabolism.oxygen.min = pInfo.godmode ? 1 : 0;
            player.metabolism.calories.min = pInfo.godmode ? 500 : 0;
            player.metabolism.hydration.min = pInfo.godmode ? 250 : 0;

            string message = pInfo.godmode ? lang.GetMessage("GodmodeOn", this, player.UserIDString) : lang.GetMessage("GodmodeOff", this, player.UserIDString);
            player.ChatMessage(string.Format(message, "Admin"));

            ToggleIcon(player, "Godmode");
        }

        private void ToggleVanish(BasePlayer player)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;

            pInfo.vanish = !pInfo.vanish;
            VanishManager(player, pInfo.vanish);

            if (pInfo.vanish)
            {
                player.SetPlayerFlag(BasePlayer.PlayerFlags.Spectating, true);
                player.gameObject.SetLayerRecursive(10);
                player.CancelInvoke("MetabolismUpdate");
                player.CancelInvoke("InventoryUpdate");
            }
            else
            {
                player.SetPlayerFlag(BasePlayer.PlayerFlags.Spectating, false);
                player.gameObject.SetLayerRecursive(17);
                player.InvokeRepeating("MetabolismUpdate", 1f, 1f);
                player.InvokeRepeating("InventoryUpdate", 1f, 0.1f * UnityEngine.Random.Range(0.99f, 1.01f));
            }

            string message = pInfo.vanish ? lang.GetMessage("VanishOn", this, player.UserIDString) : lang.GetMessage("VanishOff", this, player.UserIDString);
            player.ChatMessage(string.Format(message, "Admin"));

            ToggleIcon(player, "Vanish");
        }

        private void ToggleNoclip(BasePlayer player)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;

            pInfo.noclip = !pInfo.noclip;

            if (pInfo.noclip)
            {
                player.SetPlayerFlag(BasePlayer.PlayerFlags.Spectating, true);
                player.gameObject.SetLayerRecursive(10);
            }
            else
            {
                player.SetPlayerFlag(BasePlayer.PlayerFlags.Spectating, false);
                player.gameObject.SetLayerRecursive(17);
            }

            player.ChatMessage(pInfo.noclip ? "<color=green>Noclip enabled</color>" : "<color=red>Noclip disabled</color>");
        }

        private void ToggleFreeze(BasePlayer player)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;

            pInfo.freeze = !pInfo.freeze;

            if (pInfo.freeze)
            {
                player.SetPlayerFlag(BasePlayer.PlayerFlags.Sleeping, true);
            }
            else
            {
                player.SetPlayerFlag(BasePlayer.PlayerFlags.Sleeping, false);
            }

            string message = pInfo.freeze ? lang.GetMessage("Frozen", this, player.UserIDString) : lang.GetMessage("Unfrozen", this, player.UserIDString);
            player.ChatMessage(string.Format(message, "Admin"));
        }

        private void ToggleMapTeleport(BasePlayer player)
        {
            if (!playerData.TryGetValue(player.UserIDString, out pInfo)) return;

            pInfo.mapTeleport = !pInfo.mapTeleport;

            string message = pInfo.mapTeleport ? lang.GetMessage("MapTeleportOn", this, player.UserIDString) : lang.GetMessage("MapTeleportOff", this, player.UserIDString);
            player.ChatMessage(string.Format(message, "Admin"));
        }
        #endregion

        #region API
        public bool InAdminMode(BasePlayer player) => InAdmInMode(player);
        public string GetAdminColor(BasePlayer player) => GetColor(player);
        public List<string> GetGroupTags() => groupTags;
        #endregion
    }
}









