using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("RemoveAllPuzzleDoors", "FuZZED", "0.0.1")]
    [Description("Removes All Puzzle Doors")]

    public class RemoveAllPuzzleDoors : RustPlugin
    {
        #region [Hooks]
            private void OnServerInitialized()
            {
                using (var enumerator = BaseNetworkable.serverEntities.GetEnumerator())
                {
                    while (enumerator.MoveNext())
                    {
                        var door = enumerator.Current;
                        
                        if (door == null) return;

                        if (door.ShortPrefabName.Contains("door.hinged.security"))
                        {
                            door.Kill();
                        }
                    }
                }
            }
        #endregion
    }
} 