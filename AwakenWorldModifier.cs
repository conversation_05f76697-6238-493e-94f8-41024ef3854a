using Newtonsoft.Json;
using Oxide.Core.Libraries.Covalence;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken World Modifier", "Skelee", "3.1.0")]
    [Description("Safe world modifier - removes entities in batches to prevent FPS drops and crashes")]
    public class AwakenWorldModifier : CovalencePlugin
    {
        #region Config
        private static Configuration? config;

        private class Configuration
        {
            [JsonProperty("Enable Entity Cleanup")]
            public bool EnableCleanup { get; set; } = true;

            [JsonProperty("Batch Size (entities per cycle)")]
            public int BatchSize { get; set; } = 10;

            [JsonProperty("Cleanup Delay (seconds)")]
            public float CleanupDelay { get; set; } = 30f;

            [JsonProperty("Process Interval (seconds)")]
            public float ProcessInterval { get; set; } = 1f;

            [JsonProperty("Max FPS Threshold")]
            public float MaxFpsThreshold { get; set; } = 20f;

            [JsonProperty("Enable FPS Monitoring")]
            public bool EnableFpsMonitoring { get; set; } = true;
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                config = LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Defines
        private static AwakenWorldModifier? Instance;
        private Timer? cleanupTimer;
        private int totalEntitiesProcessed = 0;
        private int totalEntitiesRemoved = 0;
        private int currentEntityIndex = 0;
        private List<BaseNetworkable>? entitySnapshot = null;
        private float lastFpsCheck = 0f;

        private static readonly HashSet<string> BlockedPrefabs = new HashSet<string> {
            "assets/bundled/prefabs/autospawn/resource/logs_wet/dead_log_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_wet/dead_log_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_wet/dead_log_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_1.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_2.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_3.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_4.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_5.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_set_1.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_set_2.prefab",
            "assets/bundled/prefabs/autospawn/resource/driftwood/driftwood_set_3.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_snow/dead_log_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_snow/dead_log_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_snow/dead_log_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_dry/dead_log_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_dry/dead_log_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/logs_dry/dead_log_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/swamp-trees/swamp_tree_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/swamp-trees/swamp_tree_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/swamp-trees/swamp_tree_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/swamp-trees/swamp_tree_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/swamp-trees/swamp_tree_e.prefab",
            "assets/bundled/prefabs/autospawn/resource/swamp-trees/swamp_tree_f.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_cactus/cactus-1.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_cactus/cactus-2.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_cactus/cactus-3.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_cactus/cactus-4.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_cactus/cactus-5.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_cactus/cactus-6.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_cactus/cactus-7.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic/bush_willow_snow_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic/bush_willow_snow_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic/bush_willow_snow_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic/bush_willow_snow_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic/bush_willow_snow_small_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic/bush_willow_snow_small_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic_forest/bush_spicebush_a_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic_forest/bush_spicebush_c_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic_forest/bush_willow_snow_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic_forest/bush_willow_snow_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic_forest/bush_willow_snow_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arctic_forest/bush_willow_snow_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_desert/creosote_bush_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_desert/creosote_bush_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_desert/mormon_tea_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_desert/mormon_tea_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_desert/mormon_tea_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_desert/mormon_tea_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_dry/creosote_bush_dry_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_dry/creosote_bush_dry_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forest_saplings/pine_sapling_d_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_dry_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_dry_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_dry_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_ocotillo/ocotillo_dry_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_temp/bush_spicebush_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_temp/bush_spicebush_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_temp/bush_spicebush_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_temp/bush_spicebush_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forest_saplings/pine_sapling_c_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forest_saplings/pine_sapling_a_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forest_saplings/pine_sapling_b_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forest_saplings/pine_sapling_d_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forest_saplings/pine_sapling_e_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forestside/pine_sapling_d_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arctic_forestside/pine_sapling_e_snow.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_field_pines/pine_sapling_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_field_pines/pine_sapling_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_field_pines/pine_sapling_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_field_pines/pine_sapling_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_field_pines/pine_sapling_e.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_forest_saplings/pine_sapling_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_forest_saplings/pine_sapling_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_forest_saplings/pine_sapling_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_forest_saplings/pine_sapling_d.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_tundra_forest_saplings/pine_sapling_e.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_forest/palm_tree_short_c_entity.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_arid_beachside/palm_tree_short_c_entity.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_grass/creosote_bush_a.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_grass/creosote_bush_b.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_grass/creosote_bush_c.prefab",
            "assets/bundled/prefabs/autospawn/resource/v3_bushes_arid_grass/creosote_bush_d.prefab"
        };
        #endregion

        #region Hooks
        private void Loaded()
        {
            Instance = this;
            if (config?.EnableCleanup == true)
            {
                Puts("[World Modifier] Plugin loaded - using safe entity cleanup approach");
            }
            else
            {
                Puts("[World Modifier] Plugin loaded - entity cleanup is DISABLED in config");
            }
        }

        private void OnServerInitialized()
        {
            try
            {
                if (config?.EnableCleanup != true)
                {
                    Puts("[World Modifier] Entity cleanup is disabled in config - no cleanup will be performed");
                    return;
                }

                // Start cleanup after server is fully initialized
                float delay = config?.CleanupDelay ?? 10f;
                timer.Once(delay, () => StartSafeCleanup());
                Puts($"[World Modifier] Starting safe entity cleanup in {delay} seconds...");
            }
            catch (Exception ex)
            {
                PrintError($"[World Modifier] Error in OnServerInitialized: {ex.Message}");
            }
        }

        private void Unload()
        {
            try
            {
                cleanupTimer?.Destroy();
                Instance = null;

                if (totalEntitiesRemoved > 0)
                {
                    Puts($"[World Modifier] Plugin unloaded - Total entities processed: {totalEntitiesProcessed}, Total removed: {totalEntitiesRemoved}");
                }
                else
                {
                    Puts("[World Modifier] Plugin unloaded safely");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[World Modifier] Error during unload: {ex.Message}");
            }
        }
        #endregion

        #region Safe Cleanup Methods
        private void StartSafeCleanup()
        {
            try
            {
                // Reset counters and state
                totalEntitiesProcessed = 0;
                totalEntitiesRemoved = 0;
                currentEntityIndex = 0;
                entitySnapshot = null;

                // Start the cleanup process with a much more conservative approach
                float interval = config?.ProcessInterval ?? 1f;
                cleanupTimer = timer.Repeat(interval, 0, () => ProcessEntityBatch());
                Puts($"[World Modifier] Started ULTRA-CONSERVATIVE entity cleanup (interval: {interval}s, batch: {config?.BatchSize ?? 10})");
            }
            catch (Exception ex)
            {
                PrintError($"[World Modifier] Error starting cleanup: {ex.Message}");
            }
        }

        private void ProcessEntityBatch()
        {
            try
            {
                // Check FPS before processing if monitoring is enabled
                if (config?.EnableFpsMonitoring == true && ShouldSkipDueToLowFps())
                {
                    return; // Skip this cycle if FPS is too low
                }

                // Create entity snapshot only once at the beginning
                if (entitySnapshot == null)
                {
                    entitySnapshot = BaseNetworkable.serverEntities.ToList();
                    Puts($"[World Modifier] Created entity snapshot with {entitySnapshot.Count} entities");
                }

                // Process a very small batch
                var entitiesToRemove = new List<BaseNetworkable>();
                int batchSize = config?.BatchSize ?? 10;
                int processed = 0;

                // Process from where we left off
                for (int i = currentEntityIndex; i < entitySnapshot.Count && processed < batchSize; i++)
                {
                    var entity = entitySnapshot[i];
                    currentEntityIndex = i + 1;
                    totalEntitiesProcessed++;
                    processed++;

                    if (entity != null && !entity.IsDestroyed && ShouldRemoveEntity(entity))
                    {
                        entitiesToRemove.Add(entity);
                    }
                }

                // Remove entities one by one with delays
                if (entitiesToRemove.Count > 0)
                {
                    RemoveEntitiesSlowly(entitiesToRemove);
                }

                // Log progress every 500 entities processed
                if (totalEntitiesProcessed % 500 == 0)
                {
                    Puts($"[World Modifier] Progress: {totalEntitiesProcessed}/{entitySnapshot.Count} processed, {totalEntitiesRemoved} removed");
                }

                // Check if we've processed all entities
                if (currentEntityIndex >= entitySnapshot.Count)
                {
                    // We've processed all entities, stop the timer
                    cleanupTimer?.Destroy();
                    cleanupTimer = null;
                    entitySnapshot = null;
                    Puts($"[World Modifier] Entity cleanup completed! Total processed: {totalEntitiesProcessed}, Total removed: {totalEntitiesRemoved}");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[World Modifier] Error in ProcessEntityBatch: {ex.Message}");
                // Stop the timer if there's a critical error
                cleanupTimer?.Destroy();
                cleanupTimer = null;
                entitySnapshot = null;
            }
        }

        private bool ShouldSkipDueToLowFps()
        {
            try
            {
                float currentTime = Time.realtimeSinceStartup;
                if (currentTime - lastFpsCheck < 5f) return false; // Check FPS every 5 seconds

                lastFpsCheck = currentTime;
                float currentFps = 1f / Time.deltaTime;
                float threshold = config?.MaxFpsThreshold ?? 20f;

                if (currentFps < threshold)
                {
                    Puts($"[World Modifier] Skipping cleanup cycle - FPS too low: {currentFps:F1} < {threshold}");
                    return true;
                }

                return false;
            }
            catch
            {
                return false; // If we can't check FPS, continue processing
            }
        }

        private void RemoveEntitiesSlowly(List<BaseNetworkable> entities)
        {
            try
            {
                // Remove entities with small delays between each removal
                for (int i = 0; i < entities.Count; i++)
                {
                    var entity = entities[i];
                    float delay = i * 0.1f; // 0.1 second delay between each removal

                    timer.Once(delay, () =>
                    {
                        try
                        {
                            if (entity != null && !entity.IsDestroyed)
                            {
                                entity.Kill();
                                totalEntitiesRemoved++;
                            }
                        }
                        catch (Exception ex)
                        {
                            PrintError($"[World Modifier] Error removing entity with delay: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                PrintError($"[World Modifier] Error in RemoveEntitiesSlowly: {ex.Message}");
            }
        }

        private bool ShouldRemoveEntity(BaseNetworkable entity)
        {
            try
            {
                if (entity == null || entity.IsDestroyed) return false;

                // Get the prefab name safely
                string prefabName = entity.PrefabName;
                if (string.IsNullOrEmpty(prefabName)) return false;

                // Check if this entity should be removed
                return BlockedPrefabs.Contains(prefabName);
            }
            catch (Exception ex)
            {
                // If we can't safely check the entity, don't remove it
                PrintError($"[World Modifier] Error checking entity: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region Commands
        [Command("worldmodifier.status")]
        private void StatusCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            if (config?.EnableCleanup != true)
            {
                player.Reply("[World Modifier] Entity cleanup is DISABLED in config");
                return;
            }

            bool isRunning = cleanupTimer != null && !cleanupTimer.Destroyed;
            float currentFps = 1f / Time.deltaTime;
            int totalEntities = entitySnapshot?.Count ?? BaseNetworkable.serverEntities.Count;

            player.Reply($"[World Modifier] Status:");
            player.Reply($"  - Cleanup running: {(isRunning ? "YES" : "NO")}");
            player.Reply($"  - Current FPS: {currentFps:F1}");
            player.Reply($"  - Progress: {totalEntitiesProcessed}/{totalEntities} ({(totalEntities > 0 ? (totalEntitiesProcessed * 100f / totalEntities):0):F1}%)");
            player.Reply($"  - Entities removed: {totalEntitiesRemoved}");
            player.Reply($"  - Batch size: {config?.BatchSize ?? 10}");
            player.Reply($"  - Process interval: {config?.ProcessInterval ?? 1f}s");
            player.Reply($"  - FPS monitoring: {(config?.EnableFpsMonitoring == true ? "ENABLED" : "DISABLED")}");
            player.Reply($"  - FPS threshold: {config?.MaxFpsThreshold ?? 20f}");
        }

        [Command("worldmodifier.start")]
        private void StartCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            if (config?.EnableCleanup != true)
            {
                player.Reply("[World Modifier] Entity cleanup is DISABLED in config");
                return;
            }

            if (cleanupTimer != null && !cleanupTimer.Destroyed)
            {
                player.Reply("[World Modifier] Cleanup is already running!");
                return;
            }

            StartSafeCleanup();
            player.Reply("[World Modifier] Started entity cleanup process");
        }

        [Command("worldmodifier.stop")]
        private void StopCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            if (cleanupTimer == null || cleanupTimer.Destroyed)
            {
                player.Reply("[World Modifier] Cleanup is not running");
                return;
            }

            cleanupTimer.Destroy();
            cleanupTimer = null;
            player.Reply($"[World Modifier] Stopped cleanup process. Processed: {totalEntitiesProcessed}, Removed: {totalEntitiesRemoved}");
        }
        #endregion
    }
}









