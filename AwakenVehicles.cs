using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using System.Collections.Generic;
using UnityEngine;
using Rust;
using Newtonsoft.Json;

namespace Oxide.Plugins
{
    [Info("awakenVehicles", "Skelee", "1.0.0")]
    [Description("Allows players to spawn vehicles using scrap")]
    public class AwakenVehicles : RustPlugin
    {
        #region Configuration

        private Configuration config;

        public class Configuration
        {
            public int MiniScrapCost = 750;
            public int ScrapHeliScrapCost = 1250;
            public float SpawnDistance = 3f;
            public bool AllowOnWater = false;
            public bool CheckForObstructions = true;
            public string ChatPrefix = "Awaken Vehicles";
            public string ChatPrefixColor = "#7000fd";
            public float CooldownSeconds = 300f; // 5 minutes default cooldown
        }

        protected override void LoadDefaultConfig()
        {
            config = new Configuration();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    throw new JsonException();
                }
            }
            catch
            {
                LoadDefaultConfig();
                PrintWarning("Configuration file is corrupt, using default values");
            }
        }

        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }

        #endregion

        #region Localization

        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["NoPermission"] = "You don't have permission to use this command.",
                ["NotEnoughScrap"] = "You need {0} scrap to spawn a {1}. You have {2} scrap.",
                ["VehicleSpawned"] = "Successfully spawned a {0}!",
                ["SpawnFailed"] = "Failed to spawn vehicle. Try moving to a different location.",
                ["InvalidLocation"] = "Cannot spawn vehicle here. Find a clear area on land.",
                ["OnWater"] = "Cannot spawn vehicles on water.",
                ["Obstructed"] = "Cannot spawn vehicle here - area is obstructed.",
                ["MiniHelicopter"] = "Minicopter",
                ["ScrapHelicopter"] = "Scrap Transport Helicopter",
                ["OnCooldown"] = "You must wait {0} before spawning another vehicle.",
                ["CooldownRemaining"] = "Cooldown remaining: {0}"
            }, this);
        }

        private string GetMessage(string key, string userId = null, params object[] args)
        {
            return string.Format(lang.GetMessage(key, this, userId), args);
        }

        #endregion

        #region Permissions

        private const string MiniPermission = "awakenvehicles.mini";
        private const string ScrapHeliPermission = "awakenvehicles.scrappy";

        private void Init()
        {
            permission.RegisterPermission(MiniPermission, this);
            permission.RegisterPermission(ScrapHeliPermission, this);
        }

        #endregion

        #region Cooldown Management

        private Dictionary<ulong, float> playerCooldowns = new Dictionary<ulong, float>();

        private bool IsOnCooldown(BasePlayer player, out float remainingTime)
        {
            remainingTime = 0f;

            if (!playerCooldowns.ContainsKey(player.userID))
                return false;

            float lastSpawnTime = playerCooldowns[player.userID];
            float timeSinceLastSpawn = Time.realtimeSinceStartup - lastSpawnTime;

            if (timeSinceLastSpawn >= config.CooldownSeconds)
            {
                playerCooldowns.Remove(player.userID);
                return false;
            }

            remainingTime = config.CooldownSeconds - timeSinceLastSpawn;
            return true;
        }

        private void SetCooldown(BasePlayer player)
        {
            playerCooldowns[player.userID] = Time.realtimeSinceStartup;
        }

        private string FormatTime(float seconds)
        {
            if (seconds < 60)
                return $"{seconds:F0} seconds";

            int minutes = (int)(seconds / 60);
            int remainingSeconds = (int)(seconds % 60);

            if (remainingSeconds == 0)
                return $"{minutes} minute{(minutes != 1 ? "s" : "")}";

            return $"{minutes} minute{(minutes != 1 ? "s" : "")} and {remainingSeconds} second{(remainingSeconds != 1 ? "s" : "")}";
        }

        #endregion

        #region Commands

        [ChatCommand("mini")]
        private void CmdSpawnMini(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, MiniPermission))
            {
                SendMessage(player, GetMessage("NoPermission", player.UserIDString));
                return;
            }

            // Check cooldown
            if (IsOnCooldown(player, out float remainingTime))
            {
                SendMessage(player, GetMessage("OnCooldown", player.UserIDString, FormatTime(remainingTime)));
                return;
            }

            SpawnVehicle(player, "assets/content/vehicles/minicopter/minicopter.entity.prefab",
                        config.MiniScrapCost, GetMessage("MiniHelicopter", player.UserIDString));
        }

        [ChatCommand("scrappy")]
        private void CmdSpawnScrapHeli(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, ScrapHeliPermission))
            {
                SendMessage(player, GetMessage("NoPermission", player.UserIDString));
                return;
            }

            // Check cooldown
            if (IsOnCooldown(player, out float remainingTime))
            {
                SendMessage(player, GetMessage("OnCooldown", player.UserIDString, FormatTime(remainingTime)));
                return;
            }

            SpawnVehicle(player, "assets/content/vehicles/scrap heli carrier/scraptransporthelicopter.prefab",
                        config.ScrapHeliScrapCost, GetMessage("ScrapHelicopter", player.UserIDString));
        }

        #endregion

        #region Vehicle Spawning

        private void SpawnVehicle(BasePlayer player, string prefabPath, int scrapCost, string vehicleName)
        {
            // Check if player has enough scrap
            int playerScrap = GetPlayerScrap(player);
            if (playerScrap < scrapCost)
            {
                SendMessage(player, GetMessage("NotEnoughScrap", player.UserIDString, scrapCost, vehicleName, playerScrap));
                return;
            }

            // Find spawn position
            Vector3 spawnPosition = GetSpawnPosition(player);
            if (spawnPosition == Vector3.zero)
            {
                SendMessage(player, GetMessage("InvalidLocation", player.UserIDString));
                return;
            }

            // Check for water if not allowed
            if (!config.AllowOnWater && IsOnWater(spawnPosition))
            {
                SendMessage(player, GetMessage("OnWater", player.UserIDString));
                return;
            }

            // Check for obstructions
            if (config.CheckForObstructions && IsObstructed(spawnPosition))
            {
                SendMessage(player, GetMessage("Obstructed", player.UserIDString));
                return;
            }

            // Remove scrap from player
            if (!RemoveScrap(player, scrapCost))
            {
                SendMessage(player, GetMessage("SpawnFailed", player.UserIDString));
                return;
            }

            // Spawn the vehicle
            BaseEntity vehicle = GameManager.server.CreateEntity(prefabPath, spawnPosition, player.transform.rotation);
            if (vehicle != null)
            {
                vehicle.Spawn();
                SendMessage(player, GetMessage("VehicleSpawned", player.UserIDString, vehicleName));

                // Set the vehicle owner (if applicable)
                if (vehicle is BaseVehicle baseVehicle)
                {
                    baseVehicle.OwnerID = player.userID;
                }

                // Set cooldown after successful spawn
                SetCooldown(player);
            }
            else
            {
                SendMessage(player, GetMessage("SpawnFailed", player.UserIDString));
                // Refund scrap if spawn failed
                GiveScrap(player, scrapCost);
            }
        }

        private Vector3 GetSpawnPosition(BasePlayer player)
        {
            Vector3 forward = player.transform.forward;
            Vector3 spawnPos = player.transform.position + (forward * config.SpawnDistance);
            
            // Adjust height to ground level
            RaycastHit hit;
            if (Physics.Raycast(spawnPos + Vector3.up * 10f, Vector3.down, out hit, 20f, LayerMask.GetMask("Terrain", "World")))
            {
                spawnPos.y = hit.point.y + 1f; // Slightly above ground
            }
            else
            {
                spawnPos.y = TerrainMeta.HeightMap.GetHeight(spawnPos) + 1f;
            }

            return spawnPos;
        }

        private bool IsOnWater(Vector3 position)
        {
            float waterHeight = TerrainMeta.WaterMap.GetHeight(position);
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            return waterHeight > terrainHeight;
        }

        private bool IsObstructed(Vector3 position)
        {
            // Check for obstructions in a small radius around the spawn point
            Collider[] colliders = Physics.OverlapSphere(position, 2f, LayerMask.GetMask("Construction", "Deployed"));
            return colliders.Length > 0;
        }

        #endregion

        #region Scrap Management

        private int GetPlayerScrap(BasePlayer player)
        {
            int scrapAmount = 0;
            
            // Check main inventory
            scrapAmount += GetScrapFromContainer(player.inventory.containerMain);
            
            // Check belt inventory
            scrapAmount += GetScrapFromContainer(player.inventory.containerBelt);
            
            return scrapAmount;
        }

        private int GetScrapFromContainer(ItemContainer container)
        {
            int scrapAmount = 0;
            foreach (Item item in container.itemList)
            {
                if (item.info.shortname == "scrap")
                {
                    scrapAmount += item.amount;
                }
            }
            return scrapAmount;
        }

        private bool RemoveScrap(BasePlayer player, int amount)
        {
            int remaining = amount;
            
            // Remove from main inventory first
            remaining = RemoveScrapFromContainer(player.inventory.containerMain, remaining);
            
            // Remove from belt if needed
            if (remaining > 0)
            {
                remaining = RemoveScrapFromContainer(player.inventory.containerBelt, remaining);
            }
            
            return remaining == 0;
        }

        private int RemoveScrapFromContainer(ItemContainer container, int amount)
        {
            int remaining = amount;
            
            for (int i = container.itemList.Count - 1; i >= 0 && remaining > 0; i--)
            {
                Item item = container.itemList[i];
                if (item.info.shortname == "scrap")
                {
                    int toRemove = Mathf.Min(remaining, item.amount);
                    item.UseItem(toRemove);
                    remaining -= toRemove;
                }
            }
            
            return remaining;
        }

        private void GiveScrap(BasePlayer player, int amount)
        {
            Item scrapItem = ItemManager.Create(ItemManager.FindItemDefinition("scrap"), amount);
            if (scrapItem != null)
            {
                player.GiveItem(scrapItem);
            }
        }

        #endregion

        #region Helpers

        private void SendMessage(BasePlayer player, string message)
        {
            player.ChatMessage($"<color={config.ChatPrefixColor}>{config.ChatPrefix}</color> {message}");
        }

        #endregion
    }
}










