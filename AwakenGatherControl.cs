using HarmonyLib;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using UnityEngine;

// --- Potential 'using' statements you might need based on types found in Assembly-CSharp.dll ---
// using Rust; // Often contains types like BasePlayer, AttackEntity, ItemAmount, ItemDefinition
// using ConVar; // For console variables if used
// using Facepunch; // For Facepunch specific utilities if used
// Add others as identified by your decompiler for types like ItemAmount, AttackEntity, ItemDefinition, BaseMelee etc.

namespace Oxide.Plugins // Opens namespace Oxide.Plugins
{
    [Info("Awaken Gather Control", "Skelee", "1.1.13")]
    [Description("Manages gather rates for Awaken Servers, including Quarry and Giant Excavator boosting.")]
    public class AwakenGatherControl : CovalencePlugin // Opens class AwakenGatherControl
    {
        #region Defines
        private static AwakenGatherControl? Instance;
        private static Harmony? _harmony;
        #endregion

        #region Config
        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("Gather Rates")] Dictionary<string, float> GatherRates = null!,
            [property: JsonProperty("Pickup Rates")] Dictionary<string, float> PickupRates = null!,
            [property: JsonProperty("Quarry Rate")] float QuarryRate = 3f,
            [property: JsonProperty("Excavator Rate")] float ExcavatorRate = 3f)
        { // Opens record Configuration body
            public Configuration() : this(
                new Dictionary<string, float> { ["*"] = 3f },
                new Dictionary<string, float> { ["*"] = 3f, ["Corn Seed"] = 1f, ["Pumpkin Seed"] = 1f, ["Hemp Seed"] = 1f },
                3f,
                3f)
            { } // Closes parameterless constructor body
        } // Closes record Configuration body

        protected override void LoadConfig()
        { // Opens LoadConfig method
            base.LoadConfig();
            try
            { // Opens try block
                config = Config.ReadObject<Configuration>();
                if (config == null)
                { // Opens if block
                    config = LoadDefaultConfig();
                } // Closes if block
                SaveConfig();
            } // Closes try block
            catch (Exception e)
            { // Opens catch block
                config = LoadDefaultConfig();
            } // Closes catch block
        } // Closes LoadConfig method

        private Configuration LoadDefaultConfig()
        { // Opens LoadDefaultConfig method
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig, true);
            return defaultConfig;
        } // Closes LoadDefaultConfig method

        protected override void SaveConfig() => Config.WriteObject(config, true);
        #endregion // Closes region Config

        #region Hooks
        private void Loaded()
        { // Opens Loaded method
            Instance = this; // Set Instance for logging convenience

            if (config == null)
            {
                return;
            }

            _harmony ??= new Harmony("com.Skelee.AwakenGatherControl");

            // --- Patch for ResourceDispenser.GiveResourceFromItem ---
            MethodInfo giveResourceMethod;
            try
            {
                Type[] giveResourceParams = new Type[] {
                    typeof(BasePlayer),
                    typeof(ItemAmount),     // Ensure ItemAmount type is resolvable
                    typeof(float),
                    typeof(float),
                    typeof(AttackEntity)    // Ensure AttackEntity type is resolvable
                };
                giveResourceMethod = AccessTools.Method(typeof(ResourceDispenser), "GiveResourceFromItem", giveResourceParams);

                if (giveResourceMethod != null)
                {
                    _harmony.Patch(giveResourceMethod, transpiler: new HarmonyMethod(typeof(DispenserModify), nameof(DispenserModify.Transpiler)));
                }
            }
            catch (Exception ex) { }


            // --- Patch for ResourceDispenser.AssignFinishBonus ---
            MethodInfo assignBonusMethod;
            try
            {
                Type[] assignBonusParams = new Type[] { 
                    typeof(BasePlayer),
                    typeof(float),
                    typeof(AttackEntity)    // Ensure AttackEntity type is resolvable
                };
                assignBonusMethod = AccessTools.Method(typeof(ResourceDispenser), "AssignFinishBonus", assignBonusParams);
                if (assignBonusMethod != null)
                {
                    _harmony.Patch(assignBonusMethod, transpiler: new HarmonyMethod(typeof(DispenserBonusModify), nameof(DispenserBonusModify.Transpiler)));
                }
            }
            catch (Exception ex) { }


            // --- Patch for GrowableEntity.GiveFruit ---
            MethodInfo giveFruitMethod;
            try
            {
                // Targeting the 4-parameter overload: GiveFruit(BasePlayer player, int amount, bool applyCondition, bool eat)
                Type[] giveFruitParams = new Type[] { 
                    typeof(BasePlayer), 
                    typeof(int), 
                    typeof(bool), // for applyCondition
                    typeof(bool)  // for eat
                };
                giveFruitMethod = AccessTools.Method(typeof(GrowableEntity), "GiveFruit", giveFruitParams);
                
                if (giveFruitMethod != null)
                {
                    _harmony.Patch(giveFruitMethod, transpiler: new HarmonyMethod(typeof(GrowableGatherModify), nameof(GrowableGatherModify.Transpiler)));
                }
            }
            catch (Exception ex) { }


            // --- Patch for CollectibleEntity.DoPickup ---
            MethodInfo doPickupMethod;
            try
            {
                Type[] doPickupParams = new Type[] { // Added parameters based on IL
                    typeof(BasePlayer),
                    typeof(bool) // For the 'eat' parameter
                };
                doPickupMethod = AccessTools.Method(typeof(CollectibleEntity), "DoPickup", doPickupParams); 
                if (doPickupMethod != null)
                {
                    _harmony.Patch(doPickupMethod, transpiler: new HarmonyMethod(typeof(CollectibleModify), nameof(CollectibleModify.Transpiler)));
                }
            }
            catch (Exception ex) { }


            // --- Patch for ExcavatorArm.ProduceResources ---
            MethodInfo produceResourcesMethod;
            try
            {
                // Target "ProduceResources" and specify it's parameterless
                produceResourcesMethod = AccessTools.Method(typeof(ExcavatorArm), "ProduceResources", parameters: new Type[0]); 

                if (produceResourcesMethod != null)
                {
                    _harmony.Patch(produceResourcesMethod, transpiler: new HarmonyMethod(typeof(ExcavatorModify), nameof(ExcavatorModify.Transpiler)));
                }
            }
            catch (Exception ex) { }

            // --- Patch for MiningQuarry.ProcessResources ---
            MethodInfo processQuarryResourcesMethod;
            try
            {
                // MiningQuarry.ProcessResources() is parameterless
                processQuarryResourcesMethod = AccessTools.Method(typeof(MiningQuarry), "ProcessResources", parameters: new Type[0]); 

                if (processQuarryResourcesMethod != null)
                {
                    _harmony.Patch(processQuarryResourcesMethod, transpiler: new HarmonyMethod(typeof(MiningQuarryModify), nameof(MiningQuarryModify.Transpiler)));
                }
            }
            catch (Exception ex) { }

        } // Closes Loaded method

        private void Unload()
        {
            if (_harmony != null && Instance != null)
            {
                _harmony.UnpatchAll("com.Skelee.AwakenGatherControl");
            }
            Instance = null;
            config = null;
        }
        #endregion // Closes region Hooks

        #region Harmony
        
        private static class DispenserModify
        { 
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            { 
                var list = instructions.ToList();
                var giveItemLine = list.FindIndex(i => i.opcode == OpCodes.Callvirt && i.operand is MethodInfo { Name: "OverrideOwnership" });
                if (giveItemLine == -1)
                {
                    return list;
                }
                if (giveItemLine + 2 > list.Count)
                {
                    return list;
                }
                list.InsertRange(giveItemLine + 2, new[] {
                    new CodeInstruction(OpCodes.Ldloc_S, 7), // IMPORTANT: Verify this local variable index (7) for the 'Item' in ResourceDispenser.GiveResourceFromItem
                    new CodeInstruction(OpCodes.Ldarg_0),    
                    new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(DispenserModify), nameof(ManageItemAmount)))
                });
                return list;
            } 

            internal static void ManageItemAmount(Item item, ResourceDispenser dispenser)
            {
                if (AwakenGatherControl.Instance == null || item?.info == null || AwakenGatherControl.config == null || dispenser == null) return;

                Configuration currentConfig = AwakenGatherControl.config;

                BaseEntity dispenserEntity = dispenser.baseEntity;
                if (dispenserEntity == null) return;

                bool isQuarryContextForDispenser = dispenserEntity.GetComponentInParent<MiningQuarry>() != null;

                float rateToApply;

                if (isQuarryContextForDispenser)
                {
                    rateToApply = currentConfig.QuarryRate;
                }
                else
                {
                    rateToApply = GetRate(currentConfig.GatherRates, item.info.displayName.english);
                }

                item.amount = (int)(item.amount * rateToApply);
            }
        } 

        private static class DispenserBonusModify
        { 
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator)
            { 
                var list = instructions.ToList();
                var giveItemLine = list.FindIndex(i => i.opcode == OpCodes.Callvirt && i.operand is MethodInfo mi && mi.Name == "GiveItem" && (mi.DeclaringType == typeof(BasePlayer) || mi.DeclaringType == typeof(BaseEntity)));
                if (giveItemLine == -1)
                {
                    return list;
                }

                if (giveItemLine - 3 < 0) {
                    return list;
                }

                var label = generator.DefineLabel();
                list[giveItemLine - 3].labels.Add(label);

                list.InsertRange(giveItemLine - 3, new[] {
                    new CodeInstruction(OpCodes.Ldloc_S, 4), // Updated local variable index for 'Item obj' in AssignFinishBonus
                    new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(DispenserBonusModify), nameof(ManageItemAmount)))
                });
                return list;
            } 

            internal static void ManageItemAmount(Item item)
            { 
                if (item?.info == null || AwakenGatherControl.config == null) return;
                Configuration currentConfig = AwakenGatherControl.config;
                item.amount = (int)(item.amount * GetRate(currentConfig.GatherRates, item.info.displayName.english));
            } 
        } 

        private static class GrowableGatherModify
        { 
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            { 
                var list = instructions.ToList();
                var giveItemLine = list.FindIndex(i => 
                    i.opcode == OpCodes.Callvirt && 
                    i.operand is MethodInfo mi && 
                    mi.Name == "GiveItem" && 
                    (mi.DeclaringType == typeof(BasePlayer) || mi.DeclaringType == typeof(BaseEntity)) 
                );

                if (giveItemLine == -1)
                {
                    return list;
                }

                int itemLocalVarIndex = 0; // Based on IL of GrowableEntity.GiveFruit(BasePlayer, int, bool applyCondition, bool eat)

                var instructionsToInsert = new List<CodeInstruction>
                {
                    new CodeInstruction(OpCodes.Ldloc_S, (byte)itemLocalVarIndex),
                    new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(GrowableGatherModify), nameof(ManageItemAmount)))
                };

                int insertionPoint = giveItemLine - 3;
                if (insertionPoint < 0) {
                    return list;
                }

                list.InsertRange(insertionPoint, instructionsToInsert);
                
                return list;
            } 

            internal static void ManageItemAmount(Item item)
            { 
                if (item?.info == null || AwakenGatherControl.config == null) return;
                Configuration currentConfig = AwakenGatherControl.config;
                item.amount = (int)(item.amount * GetRate(currentConfig.GatherRates, item.info.displayName.english));
            } 
        } 

        private static class CollectibleModify
        { 
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            { 
                var list = instructions.ToList();
                var giveItemLine = list.FindIndex(i => 
                    i.opcode == OpCodes.Callvirt && 
                    i.operand is MethodInfo mi && 
                    mi.Name == "GiveItem" &&
                    (mi.DeclaringType == typeof(BasePlayer) || mi.DeclaringType == typeof(BaseEntity)) 
                );

                if (giveItemLine == -1)
                {
                    return list;
                }

                // From IL of CollectibleEntity.DoPickup, 'Item obj' is local variable at index 6.
                int itemLocalVarIndex = 6;

                int insertionPoint = giveItemLine - 3;
                if (insertionPoint < 0) {
                    return list;
                }

                try
                {
                    var instructionsToInsert = new List<CodeInstruction>
                    {
                        new CodeInstruction(OpCodes.Ldloc_S, (byte)itemLocalVarIndex),
                        new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(CollectibleModify), nameof(ManageItemAmount)))
                    };
                    list.InsertRange(insertionPoint, instructionsToInsert);
                }
                catch (Exception ex)
                {
                     return instructions; // Return original on error
                }
                
                return list;
            } 

            internal static void ManageItemAmount(Item item)
            {
                if (AwakenGatherControl.Instance == null || item?.info == null || AwakenGatherControl.config == null) return;

                Configuration currentConfig = AwakenGatherControl.config;
                float rateToApply = GetRate(currentConfig.PickupRates, item.info.displayName.english);
                item.amount = (int)(item.amount * rateToApply);
            }
        } 

        private static class ExcavatorModify
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                var itemCreationCallLine = list.FindIndex(i => 
                    i.opcode == OpCodes.Call && 
                    i.operand is MethodInfo mi && 
                    mi.Name == "Create" && 
                    mi.DeclaringType == typeof(ItemManager) &&
                    mi.GetParameters().Length >= 2 && 
                    mi.GetParameters()[0].ParameterType == typeof(ItemDefinition) &&
                    mi.GetParameters()[1].ParameterType == typeof(int)
                );

                if (itemCreationCallLine == -1)
                {
                    return list;
                }

                int itemLocalVarIndex = 8; // Based on IL analysis of ExcavatorArm.ProduceResources
                int insertionPoint = itemCreationCallLine + 2; // After 'call ItemManager.Create' and the subsequent 'stloc.s obj'

                if (insertionPoint <= list.Count && itemCreationCallLine + 1 < list.Count && HarmonyExtensions.IsStloc(list[itemCreationCallLine + 1]))
                {
                    int foundIndex = HarmonyExtensions.GetLocalIndex(list[itemCreationCallLine + 1]);
                    if(foundIndex != -1 && foundIndex != itemLocalVarIndex ) {
                        itemLocalVarIndex = foundIndex;
                    }

                    var instructionsToInsert = new List<CodeInstruction>
                    {
                        new CodeInstruction(OpCodes.Ldloc_S, (byte)itemLocalVarIndex),
                        new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(ExcavatorModify), nameof(ManageItemAmount)))
                    };
                    list.InsertRange(insertionPoint, instructionsToInsert);
                }
                else
                {
                    return list;
                }
                return list;
            }

            internal static void ManageItemAmount(Item item)
            {
                if (item?.info == null || AwakenGatherControl.config == null) return;
                Configuration currentConfig = AwakenGatherControl.config;
                item.amount = (int)(item.amount * currentConfig.ExcavatorRate);
            }
        }

        private static class MiningQuarryModify
        {
            internal static void ManageItemAmount(Item item)
            {
                if (AwakenGatherControl.Instance == null || item?.info == null || AwakenGatherControl.config == null) return;

                Configuration currentConfig = AwakenGatherControl.config;
                item.amount = (int)(item.amount * currentConfig.QuarryRate);
            }

            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();

                var itemManagerCreateCallLine = list.FindIndex(i => 
                    i.opcode == OpCodes.Call && 
                    i.operand is MethodInfo mi && 
                    mi.Name == "Create" && 
                    mi.DeclaringType == typeof(ItemManager) &&
                    mi.GetParameters().Length >= 2 && 
                    mi.GetParameters()[0].ParameterType == typeof(ItemDefinition) &&
                    mi.GetParameters()[1].ParameterType == typeof(int)
                );

                if (itemManagerCreateCallLine == -1)
                {
                    return list;
                }

                int itemLocalVarIndex = 7; // Based on IL of MiningQuarry.ProcessResources, 'Item obj' is local variable V_7 (index 7)
                int insertionPoint = itemManagerCreateCallLine + 2; // After 'call ItemManager.Create' and 'stloc.s obj' (index 7)

                if (insertionPoint <= list.Count && itemManagerCreateCallLine + 1 < list.Count && HarmonyExtensions.IsStloc(list[itemManagerCreateCallLine + 1]))
                {
                    int foundIndex = HarmonyExtensions.GetLocalIndex(list[itemManagerCreateCallLine + 1]);
                     if(foundIndex != -1 && foundIndex != itemLocalVarIndex ) {
                        itemLocalVarIndex = foundIndex;
                    }

                    var instructionsToInsert = new List<CodeInstruction>
                    {
                        new CodeInstruction(OpCodes.Ldloc_S, (byte)itemLocalVarIndex),
                        new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(MiningQuarryModify), nameof(ManageItemAmount)))
                    };
                    list.InsertRange(insertionPoint, instructionsToInsert);
                }
                else
                {
                    return list;
                }
                
                return list;
            }
        }

        private static float GetRate(Dictionary<string, float>? rates, string itemName)
        { 
            if (rates == null) return 1f;
            if (rates.TryGetValue(itemName, out var specificRate))
            {
                return specificRate;
            }
            if (rates.TryGetValue("*", out var defaultRate))
            {
                return defaultRate;
            }
            return 1f;
        } 
        #endregion // Closes region Harmony

    } // Closes class AwakenGatherControl

    #region Harmony Extensions
    static class HarmonyExtensions
    { 
        public static bool IsStloc(this CodeInstruction instruction)
        {
            return instruction.opcode == OpCodes.Stloc ||
                   instruction.opcode == OpCodes.Stloc_0 ||
                   instruction.opcode == OpCodes.Stloc_1 ||
                   instruction.opcode == OpCodes.Stloc_2 ||
                   instruction.opcode == OpCodes.Stloc_3 ||
                   instruction.opcode == OpCodes.Stloc_S;
        }
        
        public static int GetLocalIndex(this CodeInstruction instruction)
        {
            if (instruction.opcode == OpCodes.Stloc_0 || instruction.opcode == OpCodes.Ldloc_0) return 0;
            if (instruction.opcode == OpCodes.Stloc_1 || instruction.opcode == OpCodes.Ldloc_1) return 1;
            if (instruction.opcode == OpCodes.Stloc_2 || instruction.opcode == OpCodes.Ldloc_2) return 2;
            if (instruction.opcode == OpCodes.Stloc_3 || instruction.opcode == OpCodes.Ldloc_3) return 3;
            if (instruction.opcode == OpCodes.Stloc_S || instruction.opcode == OpCodes.Ldloc_S)
            {
                if (instruction.operand is LocalBuilder lbS) return lbS.LocalIndex;
                if (instruction.operand is byte b) return b; 
                if (instruction.operand is sbyte sb) return sb;
            }
            if (instruction.opcode == OpCodes.Stloc || instruction.opcode == OpCodes.Ldloc)
            {
                 if (instruction.operand is LocalBuilder lb) return lb.LocalIndex;
            }
            return -1; 
        }

        public static IEnumerable<CodeInstruction> InsertRange(this IEnumerable<CodeInstruction> list, int index, IEnumerable<CodeInstruction> instructions) =>
            list.Take(index).Concat(instructions).Concat(list.Skip(index));

        public static IEnumerable<CodeInstruction> WithAt(this IEnumerable<CodeInstruction> list, int index, object? operand = null) =>
            list.Take(index).Append(new CodeInstruction(list.ElementAt(index)) { operand = operand ?? list.ElementAt(index).operand }).Concat(list.Skip(index + 1));
    } 
    #endregion // Closes region Harmony Extensions

} // Closes namespace Oxide.Plugins









