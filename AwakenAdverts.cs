using Newtonsoft.Json;
using System;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Adverts", "Skelee", "1.1.0")]
    [Description("Displays automatic messages in chat at regular intervals.")]

    public class AwakenAdverts : CovalencePlugin
    {
        #region Config
        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("Interval (Mins)")] int Interval = 5,
            [property: JsonProperty("Messages")] string[]? Messages = null)
        {
            public Configuration() : this(5, null)
            {
                Messages = new string[]
                {
                    "<color=#7000fd>Awaken</color> <color=#606060>»</color> Have a friend stuck in queue? Purchase VIP or our weekly wipe pass at: <color=#BB9F3C>www.awakenrust.com</color>",
                    "<color=#7000fd>Awaken</color> <color=#606060>»</color> Join our Discord at <color=#7000fd>discord.gg/awakenrust</color> to check out detailed rules and join our ever-growing community!",
                    "<color=#7000fd>Awaken</color> <color=#606060>»</color> To report a player - please use <color=#a0a0a0>F7</color> or the <color=#a0a0a0>#submit-a-ticket</color> function in our Discord.",
                    "Rusty<color=#C1262D>Pot</color> <color=#606060>»</color>  Want to get dripped out in all the latest rust swag? Check out our sponsor Rusty<color=#C1262D>Pot</color>! (18+)",
                    "<color=#7000fd>Awaken</color> <color=#606060>»</color> Cheating/scripting on our server will result in a <color=red>PERMANENT</color> ban with no option to appeal."
                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Defines
        private int nextAdvertIndex;
        #endregion

        #region Hooks
        private void Loaded()
        {
            if ((config?.Messages?.Length ?? 0) == 0) return;
            timer.Every(config?.Interval * 60 ?? 5 * 60, RunAdvert);
        }
        #endregion

        #region Functions
        private void RunAdvert()
        {
            server.Broadcast(config?.Messages?[nextAdvertIndex++] ?? "");
            if (nextAdvertIndex >= (config?.Messages?.Length ?? 0)) nextAdvertIndex = 0;
        }
        #endregion
    }
}










