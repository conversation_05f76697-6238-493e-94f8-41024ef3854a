using Network;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Admin Teleport", "Skelee", "1.1.7")]
    [Description("Provides admins with teleport commands and automatic marker teleport.")]

    public class AwakenAdminTeleport : CovalencePlugin
    {
        #region Defines
        [PluginReference] private Plugin? Vanish;
        private const string TeleportPerm = "awakenadminteleport.teleport";
        private const string MarkerTpPerm = "awakenadminteleport.markertp";
        private readonly Dictionary<string, BasePlayer> _ids = new();
        private readonly Dictionary<BasePlayer, string> _players = new();
        #endregion

        #region Hooks
        private void Loaded()
        {
            permission.RegisterPermission(TeleportPerm, this);
            permission.RegisterPermission(MarkerTpPerm, this);
        }

        private void OnPlayerConnected(BasePlayer? player)
        {
            if (player == null || !player.IsValid()) return;
            var uid = UnityEngine.Random.Range(1000, 9999).ToString();
            while (_ids.ContainsKey(uid) || BasePlayer.activePlayerList.Any(p => p.displayName.Contains(uid)))
                uid = UnityEngine.Random.Range(1000, 9999).ToString();
            _ids[uid] = player;
            _players[player] = uid;
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            if (_players.TryGetValue(player, out var uid))
            {
                _ids.Remove(uid);
                _players.Remove(player);
            }
        }

        private void OnServerCommand(ConsoleSystem.Arg arg)
        {
            if (arg == null || arg.Connection == null || arg.cmd == null) return;

            // [DEBUG] Log app.* commands to verify marker creation - REMOVE AFTER TESTING
            if (arg.cmd.FullName.StartsWith("app.", StringComparison.OrdinalIgnoreCase))
            {
                string argsStr = arg.Args != null ? string.Join(", ", arg.Args) : "none";
                PrintWarning($"[DEBUG] Received command: {arg.cmd.FullName} with args: {argsStr}");
            }

            if (arg.cmd.FullName != "app.addmarker" && arg.cmd.FullName != "app.AddMarker") return;

            var player = arg.Connection.player as BasePlayer;
            if (player == null || !player.IsValid())
            {
                PrintWarning($"[DEBUG] Invalid player for app.addmarker command.");
                return;
            }

            if (!permission.UserHasPermission(player.UserIDString, MarkerTpPerm))
            {
                player.ChatMessage("You don't have permission to teleport to markers.");
                return;
            }

            if (arg.Args == null || arg.Args.Length < 2)
            {
                PrintWarning($"[DEBUG] app.addmarker command has insufficient args: {arg.Args?.Length ?? 0}");
                return;
            }

            if (!float.TryParse(arg.Args[0], out float x) || !float.TryParse(arg.Args[1], out float z))
            {
                PrintWarning($"[DEBUG] Failed to parse coordinates for app.addmarker: x={arg.Args[0]}, z={arg.Args[1]}");
                return;
            }

            Vector3 position = new Vector3(x, TerrainMeta.HeightMap?.GetHeight(x, z) ?? 0, z);
            if (Vector3.Distance(position, Vector3.zero) < 5f)
            {
                player.ChatMessage("Cannot teleport to an invalid marker position.");
                return;
            }

            Teleport(player, position);
            player.ChatMessage("You have teleported to your map marker.");
        }

        private void OnMapMarkerAdded(BasePlayer player, MapMarker marker)
        {
            if (player == null || !player.IsValid() || marker == null) return;

            // [DEBUG] Log marker creation - REMOVE AFTER TESTING
            PrintWarning($"[DEBUG] Map marker added by {player.displayName} (SteamID: {player.userID}) at position {marker.transform.position}");

            if (!permission.UserHasPermission(player.UserIDString, MarkerTpPerm))
            {
                player.ChatMessage("You don't have permission to teleport to markers.");
                return;
            }

            Vector3 position = marker.transform.position;
            if (Vector3.Distance(position, Vector3.zero) < 5f)
            {
                player.ChatMessage("Cannot teleport to an invalid marker position.");
                return;
            }

            Teleport(player, position);
            player.ChatMessage("You have teleported to your map marker.");
        }
        #endregion

        #region Functions
        private void Teleport(BasePlayer? player, Vector3 position)
        {
            if (player == null || !player.IsValid() || Vector3.Distance(position, Vector3.zero) < 5f)
            {
                player?.ChatMessage("Cannot teleport to an invalid position.");
                return;
            }

            try
            {
                player.UpdateActiveItem(new ItemId(0));
                player.EnsureDismounted();
                if (player.HasParent()) player.SetParent(null, true, true);

                if (player.IsConnected)
                {
                    player.EndLooting();
                    if (!player.IsSleeping())
                    {
                        player.SetPlayerFlag(BasePlayer.PlayerFlags.Sleeping, true);
                        player.sleepStartTime = UnityEngine.Time.time;
                        BasePlayer.sleepingPlayerList.Add(player);
                        player.CancelInvoke("InventoryUpdate");
                        player.CancelInvoke("TeamUpdate");
                    }
                }

                player.RemoveFromTriggers();
                player.Teleport(position);

                if (player.IsConnected && !Net.sv.visibility.IsInside(player.net.group, position))
                {
                    player.SetPlayerFlag(BasePlayer.PlayerFlags.ReceivingSnapshot, true);
                    player.ClientRPC(null, "StartLoading");
                    player.SendEntityUpdate();

                    if (!IsInvisible(player))
                    {
                        player.UpdateNetworkGroup();
                        player.SendNetworkUpdateImmediate(false);
                    }
                }
            }
            catch (Exception e)
            {
                PrintError($"Failed to teleport player {player.displayName}: {e.Message}");
                player.ChatMessage("An error occurred while teleporting.");
            }
            finally
            {
                if (!IsInvisible(player))
                    player.ForceUpdateTriggers();
            }
        }

        private bool IsInvisible(BasePlayer? player)
        {
            if (player == null || Vanish == null) return false;
            try
            {
                return Convert.ToBoolean(Vanish.Call("IsInvisible", player));
            }
            catch
            {
                return false;
            }
        }

        private BasePlayer? FindPlayersSingle(string value, BasePlayer? player)
        {
            if (string.IsNullOrEmpty(value))
            {
                player?.ChatMessage("Please provide a player name or ID.");
                return null;
            }

            if (_ids.TryGetValue(value, out var target) && target.IsValid())
                return target;

            var targets = FindPlayers(value, true);
            if (targets.Count == 0)
            {
                player?.ChatMessage("Couldn't find a player with that name.");
                return null;
            }
            if (targets.Count > 1)
            {
                player?.ChatMessage($"Found multiple players: {GetMultiplePlayers(targets)}");
                return null;
            }
            return targets.First();
        }

        private List<BasePlayer> FindPlayers(string arg, bool all = false)
        {
            if (string.IsNullOrEmpty(arg)) return new();
            if (_ids.TryGetValue(arg, out var target) && target.IsValid() && (all || target.IsConnected))
                return new() { target };

            return (all ? BasePlayer.allPlayerList : BasePlayer.activePlayerList)
                .Where(p => p != null && !string.IsNullOrEmpty(p.displayName) && (p.UserIDString == arg || p.displayName.Contains(arg, StringComparison.OrdinalIgnoreCase)))
                .ToList();
        }

        private string GetMultiplePlayers(List<BasePlayer> players) => string.Join(", ", players.Select(p =>
        {
            if (!_players.ContainsKey(p)) OnPlayerConnected(p);
            return $"<color=#FFA500>{_players[p]}</color> - {p.displayName}";
        }));
        #endregion

        #region Commands
        [Command("tp")]
        private void TeleportCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player)
            {
                return;
            }

            if (!permission.UserHasPermission(player.UserIDString, TeleportPerm))
            {
                player.ChatMessage("You don't have permission to use this command.");
                return;
            }

            BasePlayer? target = args switch
            {
                [string arg] => FindPlayersSingle(arg, player),
                [_, .. var rest] => FindPlayersSingle(rest[0], player),
                _ => null
            };

            if (target == null)
            {
                player.ChatMessage("Usage: /tp <player> or /tp <player> <target>");
                return;
            }

            if (target == player)
            {
                player.ChatMessage("You cannot teleport to yourself.");
                return;
            }

            switch (args.Length)
            {
                case 1:
                    Teleport(player, target.transform.position);
                    player.ChatMessage($"You have teleported to {target.displayName}.");
                    break;

                case 2:
                    var origin = FindPlayersSingle(args[0], player);
                    if (origin == null)
                    {
                        player.ChatMessage("Invalid origin player.");
                        return;
                    }
                    if (origin == target)
                    {
                        player.ChatMessage("You cannot teleport a player to the same position.");
                        return;
                    }
                    Teleport(origin, target.transform.position);
                    player.ChatMessage($"You have teleported {origin.displayName} to {target.displayName}.");
                    origin.ChatMessage($"You have been teleported to {target.displayName}.");
                    break;

                default:
                    player.ChatMessage("Usage: /tp <player> or /tp <player> <target>");
                    break;
            }
        }
        #endregion
    }
}









