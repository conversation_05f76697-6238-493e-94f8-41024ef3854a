using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Network;

namespace Oxide.Plugins
{
    [Info("Awaken Item Monitor", "Skelee", "1.1.1")]
    [Description("Manages items dropped on the ground with configurable limits and cleanup.")]

    public class AwakenItemMonitor : CovalencePlugin
    {
        #region Config
        private static ConfigData? _config;

        private record ConfigData(
            [property: JsonProperty("Check Time (Seconds)")] int CheckTime = 30,
            [property: JsonProperty("Despawn Delay (Seconds)")] int DespawnDelay = 5,
            [property: JsonProperty("Max Item Age (Seconds) - Items older than this will be removed")] int MaxItemAge = 300,
            [property: JsonProperty("Item Limits")] Dictionary<string, int> ItemLimits = null!,
            [property: JsonProperty("Fast Despawn Items - These items despawn immediately when dropped")] List<string> FastDespawnItems = null!)
        {
            public ConfigData() : this(30, 5, 300, new Dictionary<string, int> {
                ["*"] = 500,
                ["wood"] = 100,
                ["stones"] = 100,
                ["metal.fragments"] = 50,
                ["sulfur"] = 30,
                ["cloth"] = 50,
                ["leather"] = 30
            }, new List<string> {
                "bone.fragments",
                "fat.animal",
                "meat.raw",
                "skull.human"
            }) { }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                _config = Config.ReadObject<ConfigData>();
                if (_config == null)
                {
                    PrintWarning("Configuration file is null, loading default config.");
                    _config = LoadDefaultConfig();
                }
            }
            catch (Exception e)
            {
                PrintError($"Failed to load config: {e.Message}");
                _config = LoadDefaultConfig();
            }
            SaveConfig();
        }

        private ConfigData LoadDefaultConfig()
        {
            var defaultConfig = new ConfigData();
            Config.WriteObject(defaultConfig, true);
            PrintWarning("Created new default configuration file.");
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(_config, true);
        #endregion

        #region Defines
        private Timer? CleanupTimer;
        private readonly Dictionary<string, int> droppedItemsCache = new();
        private readonly List<string> shortNamesToCleanup = new();
        private readonly List<DroppedItem> invokedItems = new();
        private readonly Dictionary<NetworkableId, float> itemCreationTimes = new();
        #endregion

        #region Hooks
        private void OnServerInitialized(bool initial)
        {
            if (_config == null)
            {
                PrintError("Configuration is null, cannot initialize plugin.");
                return;
            }

            if (initial && _config.ItemLimits.Count == 0)
            {
                FillConfig();
            }

            CleanupTimer?.Destroy();
            CleanupTimer = timer.Every(_config.CheckTime, CheckItems);
        }

        private void Unload()
        {
            CleanupTimer?.Destroy();
            _config = null;
            droppedItemsCache.Clear();
            shortNamesToCleanup.Clear();
            invokedItems.Clear();
            itemCreationTimes.Clear();
        }

        private void OnItemDropped(Item item, BaseEntity entity)
        {
            if (entity is DroppedItem droppedItem)
            {
                itemCreationTimes[droppedItem.net.ID] = UnityEngine.Time.time;

                if (_config?.FastDespawnItems?.Contains(item.info.shortname) == true)
                {
                    droppedItem.Invoke(() => RemoveItem(droppedItem), 1f);
                    invokedItems.Add(droppedItem);
                }
            }
        }
        #endregion

        #region Functions
        private void FillConfig()
        {
            if (_config == null)
            {
                PrintError("Configuration is null, cannot fill item limits.");
                return;
            }
            SaveConfig();
            PrintWarning("Filled configuration with optimized item limits for better performance.");
        }

        private void CheckItems()
        {
            if (_config == null)
            {
                PrintError("Configuration is null, cannot check items.");
                return;
            }

            var currentTime = UnityEngine.Time.time;
            var oldItems = new List<DroppedItem>();

            foreach (var droppedItem in BaseNetworkable.serverEntities.OfType<DroppedItem>())
            {
                if (droppedItem?.item?.info == null) continue;

                var shortname = droppedItem.item.info.shortname;
                droppedItemsCache[shortname] = droppedItemsCache.GetValueOrDefault(shortname) + 1;

                if (itemCreationTimes.TryGetValue(droppedItem.net.ID, out var creationTime))
                {
                    var itemAge = currentTime - creationTime;
                    if (itemAge > _config.MaxItemAge && !invokedItems.Contains(droppedItem))
                    {
                        oldItems.Add(droppedItem);
                    }
                }
                else
                {
                    itemCreationTimes[droppedItem.net.ID] = currentTime;
                }
            }

            foreach (var oldItem in oldItems)
            {
                oldItem.Invoke(() => RemoveItem(oldItem), 0.1f);
                invokedItems.Add(oldItem);
            }

            if (oldItems.Count > 0)
            {
                PrintWarning($"[Item Monitor] Scheduled cleanup for {oldItems.Count} old items (age > {_config.MaxItemAge}s).");
            }

            foreach (var (shortname, count) in droppedItemsCache)
            {
                int maxCount = GetItemLimit(shortname);
                if (maxCount == -1) continue;

                if (count >= maxCount && !shortNamesToCleanup.Contains(shortname))
                {
                    var itemName = ItemManager.FindItemDefinition(shortname)?.displayName.english ?? shortname;
                    PrintWarning($"[Item Monitor] {itemName} is over limit, scheduling cleanup. Amount: {count}, Max Amount: {maxCount}");
                    shortNamesToCleanup.Add(shortname);
                }
            }

            droppedItemsCache.Clear();
            if (shortNamesToCleanup.Count > 0)
            {
                ScheduledCleanup();
            }
        }

        private void ScheduledCleanup()
        {
            if (_config == null) return;

            int cleanupCount = 0;
            foreach (var item in BaseNetworkable.serverEntities.OfType<DroppedItem>())
            {
                if (item?.item?.info == null || item.IsDestroyed || !shortNamesToCleanup.Contains(item.item.info.shortname) || invokedItems.Contains(item)) continue;
                item.Invoke(() => RemoveItem(item), _config.DespawnDelay);
                invokedItems.Add(item);
                cleanupCount++;
            }

            if (cleanupCount > 0)
            {
                PrintWarning($"[Item Monitor] Scheduled cleanup for {cleanupCount} items. Despawn in {_config.DespawnDelay} seconds.");
            }

            shortNamesToCleanup.Clear();
        }

        private void RemoveItem(DroppedItem? item)
        {
            if (item == null || item.IsDestroyed)
            {
                if (item != null)
                {
                    invokedItems.Remove(item);
                    itemCreationTimes.Remove(item.net.ID);
                }
                return;
            }

            invokedItems.Remove(item);
            itemCreationTimes.Remove(item.net.ID);
            item.IdleDestroy();
        }

        private int GetItemLimit(string shortname)
        {
            if (_config == null) return -1;

            if (_config.ItemLimits.TryGetValue(shortname, out var maxCount))
            {
                return maxCount;
            }

            if (_config.ItemLimits.TryGetValue("*", out maxCount))
            {
                return maxCount;
            }

            return -1;
        }
        #endregion
    }
}









