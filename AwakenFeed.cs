using System.Collections.Generic;
using Newtonsoft.Json;
using Oxide.Core.Plugins;

namespace Oxide.Plugins
{
    [Info("Awaken Feed", "Skelee", "1.2.0")]
    public class AwakenFeed : RustPlugin
    {
        #region Config
        private static Configuration? config;

        private class Configuration
        {
            [JsonProperty("Enable Team Feed by Default")]
            public bool EnableTeamFeedByDefault { get; set; } = true;
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    config = LoadDefaultConfig();
                }
            }
            catch (System.Exception e)
            {
                Puts($"Failed to load config: {e.Message}");
                config = LoadDefaultConfig();
            }
            SaveConfig();
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Data
        private class Save
        {
            public Dictionary<string, bool> PlayerTeamFeedSettings { get; set; } = new();
        }

        private Save? _save;

        private void SaveData() => Oxide.Core.Interface.Oxide.DataFileSystem.WriteObject(Name, _save);
        #endregion

        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["AttackerMessage"] = "<size=16><color=#AAFF55>{0}</color> <color=#dedede>({1} HP) -> </color><color=red>{2}</color></size>\n <color=#dedede>{4} | {3}m</color>",
                ["VictimMessage"] = "<color=#AAFF55>{0}</color> ({1} HP) killed you from {3} meters using {4} ({5})!",
                ["TeamFeedEnabled"] = "Team feed is now <color=#00FF00>enabled</color>. You will see your team's kills and deaths.",
                ["TeamFeedDisabled"] = "Team feed is now <color=#FF0000>disabled</color>. You will only see your own kills and deaths.",
                ["TeamFeedHelp"] = "Use <color=#00FFFF>/teamfeed</color> or <color=#00FFFF>/tf</color> to toggle team kill/death notifications."
            }, this);
        }

        #region Hooks
        private void Loaded()
        {
            try
            {
                _save = Oxide.Core.Interface.Oxide.DataFileSystem.ReadObject<Save>(Name) ?? new Save();
                _save.PlayerTeamFeedSettings ??= new Dictionary<string, bool>();
            }
            catch (System.Exception ex)
            {
                PrintError($"Error loading save data: {ex.Message}");
                _save = new Save();
            }

            cmd.AddChatCommand("teamfeed", this, nameof(TeamFeedCommand));
            cmd.AddChatCommand("tf", this, nameof(TeamFeedCommand));
        }
        #endregion

        #region Helpers
        private bool GetTeamFeedSetting(string userId)
        {
            if (_save?.PlayerTeamFeedSettings.TryGetValue(userId, out var setting) == true)
                return setting;
            return config?.EnableTeamFeedByDefault ?? true;
        }

        private void SetTeamFeedSetting(string userId, bool enabled)
        {
            if (_save != null)
            {
                _save.PlayerTeamFeedSettings[userId] = enabled;
                SaveData();
            }
        }
        #endregion

        #region Commands
        private void TeamFeedCommand(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;

            var currentSetting = GetTeamFeedSetting(player.UserIDString);
            var newSetting = !currentSetting;

            SetTeamFeedSetting(player.UserIDString, newSetting);

            var message = newSetting ? "TeamFeedEnabled" : "TeamFeedDisabled";
            SendMessage(player, message);
        }
        #endregion

        private void OnPlayerDeath(BasePlayer victim, HitInfo hitInfo)
        {
            if (victim == null || victim.IsNpc || hitInfo == null) return;

            var attacker = hitInfo.InitiatorPlayer as BasePlayer;

            if (attacker == null || attacker.userID == victim.userID) return;

            var weapon = GetWeaponName(hitInfo) ?? "Unknown Weapon";
            var distance = (int)victim.Distance(attacker);
            var boneArea = GetBoneArea(hitInfo.boneArea);
            var attackerHealth = (int)attacker.health;

            if (attacker.currentTeam != 0)
            {
                foreach (var teamMemberId in attacker.Team.members)
                {
                    var teamMember = RelationshipManager.FindByID(teamMemberId);
                    if (teamMember != null)
                    {
                        if (teamMember == attacker || GetTeamFeedSetting(teamMember.UserIDString))
                        {
                            SendMessage(teamMember, "AttackerMessage", attacker.displayName, attackerHealth, victim.displayName, distance, weapon, boneArea);
                        }
                    }
                }
            }
            else
            {
                SendMessage(attacker, "AttackerMessage", attacker.displayName, attackerHealth, victim.displayName, distance, weapon, boneArea);
            }

            if (victim.currentTeam != 0)
            {
                foreach (var teamMemberId in victim.Team.members)
                {
                    var teamMember = RelationshipManager.FindByID(teamMemberId);
                    if (teamMember != null)
                    {
                        if (teamMember == victim || GetTeamFeedSetting(teamMember.UserIDString))
                        {
                            SendMessage(teamMember, "VictimMessage", attacker.displayName, attackerHealth, victim.displayName, distance, weapon, boneArea);
                        }
                    }
                }
            }
            else
            {
                SendMessage(victim, "VictimMessage", attacker.displayName, attackerHealth, victim.displayName, distance, weapon, boneArea);
            }
        }

        private string GetWeaponName(HitInfo hitInfo)
        {
            return hitInfo?.Weapon?.GetItem()?.info.displayName.english ?? "N/A";
        }

        private string GetBoneArea(HitArea? area)
        {
            if (!area.HasValue) return "Unknown";
            switch (area.Value)
            {
                case HitArea.Head: return "Head";
                case HitArea.Chest: return "Chest";
                case HitArea.Stomach: return "Stomach";
                case HitArea.Arm: return "Arm";
                case HitArea.Leg: return "Leg";
                default: return "Body";
            }
        }

        private void SendMessage(BasePlayer player, string message, params object[] args) =>
            player?.ChatMessage(string.Format(lang.GetMessage(message, this, player.UserIDString), args));
    }
}










