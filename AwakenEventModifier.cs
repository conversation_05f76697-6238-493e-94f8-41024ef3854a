using Facepunch;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Event Modifier", "Skelee", "1.1.1")]
    [Description("Modifies spawn conditions and timing of events.")]

    public class AwakenEventModifier : CovalencePlugin
    {
        #region Config
        private record EventSettings(
            [property: JsonProperty("Disable Spawn")] bool DisableSpawn = false,
            [property: JsonProperty("Minimum Online Players Required (0 = Disabled)")] int MinOnlinePlayers = 0,
            [property: JsonProperty("Minimum Spawn Time")] int MinSpawnTime = 25,
            [property: JsonProperty("Maximum Spawn Time")] int MaxSpawnTime = 30);

        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("Helicopter Settings")] EventSettings? Helicopter = null,
            [property: JsonProperty("Chinook Settings")] EventSettings? Chinook = null,
            [property: JsonProperty("Cargoship Settings")] EventSettings? Cargoship = null,
            [property: JsonProperty("Airdrop Settings")] EventSettings? Airdrop = null)
        {
            public Configuration() : this(new EventSettings(), new EventSettings(), new EventSettings(), new EventSettings()) { }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    config = LoadDefaultConfig();
                }
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                config = LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig, true);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config, true);
        #endregion

        #region Defines
        private readonly List<EventSchedule> events = new List<EventSchedule>();
        private readonly Dictionary<string, EventScheduler> customEvents = new Dictionary<string, EventScheduler>();
        #endregion

        #region Hooks
        private void OnServerInitialized(bool initial)
        {
            if (config == null)
            {
                PrintWarning("Config is null, cannot initialize events.");
                return;
            }

            foreach (var eventSchedule in BaseNetworkable.serverEntities.OfType<EventSchedule>())
            {
                if (eventSchedule == null) continue;
                events.Add(eventSchedule);
                eventSchedule.enabled = false;

                var scheduler = eventSchedule.gameObject.AddComponent<EventScheduler>();
                if (scheduler == null) continue;

                (bool disable, int minPlayers, int minTime, int maxTime) settings = eventSchedule.name switch
                {
                    "assets/bundled/prefabs/world/event_helicopter.prefab" => (config.Helicopter?.DisableSpawn ?? false, config.Helicopter?.MinOnlinePlayers ?? 0, config.Helicopter?.MinSpawnTime ?? 25, config.Helicopter?.MaxSpawnTime ?? 30),
                    "assets/bundled/prefabs/world/event_cargoship.prefab" => (config.Cargoship?.DisableSpawn ?? false, config.Cargoship?.MinOnlinePlayers ?? 0, config.Cargoship?.MinSpawnTime ?? 25, config.Cargoship?.MaxSpawnTime ?? 30),
                    "assets/bundled/prefabs/world/event_cargoheli.prefab" => (config.Chinook?.DisableSpawn ?? false, config.Chinook?.MinOnlinePlayers ?? 0, config.Chinook?.MinSpawnTime ?? 25, config.Chinook?.MaxSpawnTime ?? 30),
                    "assets/bundled/prefabs/world/event_airdrop.prefab" => (config.Airdrop?.DisableSpawn ?? false, config.Airdrop?.MinOnlinePlayers ?? 0, config.Airdrop?.MinSpawnTime ?? 25, config.Airdrop?.MaxSpawnTime ?? 30),
                    _ => (false, 0, 25, 30)
                };

                if (!settings.disable)
                {
                    scheduler.minPlayers = settings.minPlayers;
                    scheduler.minRespawnTime = settings.minTime;
                    scheduler.maxRespawnTime = settings.maxTime;
                    customEvents[eventSchedule.name switch
                    {
                        "assets/bundled/prefabs/world/event_helicopter.prefab" => "Helicopter",
                        "assets/bundled/prefabs/world/event_cargoship.prefab" => "Cargoship",
                        "assets/bundled/prefabs/world/event_cargoheli.prefab" => "Chinook",
                        "assets/bundled/prefabs/world/event_airdrop.prefab" => "Airdrop",
                        _ => "Unknown"
                    }] = scheduler;
                }
            }
        }

        private void Unload() => events.ForEach(e =>
        {
            if (e == null) return;
            if (!e.isActiveAndEnabled) e.SetActive(true);
            if (e.TryGetComponent(out EventScheduler scheduler)) UnityEngine.Object.Destroy(scheduler);
        });

        private void OnEntityDeath(BaseCombatEntity? entity, HitInfo? info) => HandleEntityDespawn(entity?.ShortPrefabName);

        private void OnEntityKill(BaseNetworkable? entity) => HandleEntityDespawn(entity?.ShortPrefabName);

        private void HandleEntityDespawn(string? prefabName)
        {
            if (string.IsNullOrEmpty(prefabName)) return;
            string eventKey = prefabName switch
            {
                "patrolhelicopter" => "Helicopter",
                "cargoshiptest" => "Cargoship",
                "ch47scientists.entity" => "Chinook",
                "cargo_plane" => "Airdrop",
                _ => string.Empty
            };
            if (customEvents.TryGetValue(eventKey, out var scheduler))
            {
                scheduler.despawned = true;
            }
        }
        #endregion

        #region Component
        public class EventScheduler : MonoBehaviour
        {
            public int timeLeft = -1;
            public int minPlayers;
            public int minRespawnTime = 25;
            public int maxRespawnTime = 30;
            public bool despawned = true;
            private TriggeredEvent? tEvent;

            private void Awake() => ServerMgr.Instance.StartCoroutine(Setup());
            private void OnDestroy() => ServerMgr.Instance.StopCoroutine(EventTimer());

            private IEnumerator Setup()
            {
                yield return CoroutineEx.waitForSeconds(1f);
                timeLeft = UnityEngine.Random.Range(minRespawnTime, maxRespawnTime);
                ServerMgr.Instance.StartCoroutine(EventTimer());
            }

            private IEnumerator EventTimer()
            {
                while (true)
                {
                    yield return CoroutineEx.waitForSeconds(60f);
                    if (despawned && --timeLeft == 0)
                    {
                        if (BasePlayer.activePlayerList.Count < minPlayers) continue;
                        tEvent = gameObject?.GetComponent<TriggeredEvent>();
                        if (tEvent != null) tEvent.SendMessage("RunEvent", SendMessageOptions.DontRequireReceiver);
                        timeLeft = UnityEngine.Random.Range(minRespawnTime, maxRespawnTime);
                        despawned = false;
                    }
                }
            }
        }
        #endregion
    }
}









