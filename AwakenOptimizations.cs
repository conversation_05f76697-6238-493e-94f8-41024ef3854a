// Reference: 0Harmony
using HarmonyLib;
using Network;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Optimizations", "Skelee", "1.2.0")]
    [Description("Optimizes server FPS to the maximum for Awaken Servers - includes Carbon hook disabling for high population servers")]
    public class AwakenOptimizations : CovalencePlugin
    {
        #region Defines
        private static Harmony? _harmony;
        #endregion

        #region Config
        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("=== OXIDE HOOKS ===")] string OxideHooksSection = "Configure Oxide hook optimizations below",
            [property: JsonProperty("Disable CanNetworkTo Hook")] bool CanNetworkTo = true,
            [property: JsonProperty("Disable OnTick Hook")] bool OnTick = false,
            [property: JsonProperty("Disable OnPlayerInput Hook")] bool OnPlayerInput = true,
            [property: JsonProperty("Disable CanCreateWorldProjectile Hook")] bool CanCreateWorldProjectile = true,
            [property: JsonProperty("Disable OnCreateWorldProjectile Hook")] bool OnCreateWorldProjectile = true,
            [property: JsonProperty("Disable OnProjectileRicochet Hook")] bool OnProjectileRicochet = true,
            [property: JsonProperty("Disable OnConstructionPlace Hook")] bool OnConstructionPlace = true,
            [property: JsonProperty("Disable OnBuildingSplit Hook")] bool OnBuildingSplit = true,
            [property: JsonProperty("Disable OnDoorOpened Hook")] bool OnDoorOpened = true,
            [property: JsonProperty("Disable OnDoorClosed Hook")] bool OnDoorClosed = true,
            [property: JsonProperty("Disable OnCodeEntered Hook")] bool OnCodeEntered = true,
            [property: JsonProperty("Disable OnWorldPrefabSpawned Hook")] bool OnWorldPrefabSpawned = true,
            [property: JsonProperty("Disable OnEntityFlagsNetworkUpdate Hook")] bool OnEntityFlagsNetworkUpdate = true,
            [property: JsonProperty("Disable OnEntitySnapshot Hook")] bool OnEntitySnapshot = true,
            [property: JsonProperty("Disable OnWeaponFired Hook")] bool OnWeaponFired = true,
            [property: JsonProperty("Disable OnLoseCondition Hook")] bool OnLoseCondition = true,

            [property: JsonProperty("=== CARBON HOOKS ===")] string CarbonHooksSection = "Configure Carbon hook optimizations below - these cause lag at high population",
            [property: JsonProperty("Disable Carbon CanNetworkTo Hook")] bool CarbonCanNetworkTo = true,
            [property: JsonProperty("Disable Carbon OnTick Hook")] bool CarbonOnTick = true,
            [property: JsonProperty("Disable Carbon OnPlayerInput Hook")] bool CarbonOnPlayerInput = true,
            [property: JsonProperty("Disable Carbon CanCreateWorldProjectile Hook")] bool CarbonCanCreateWorldProjectile = true,
            [property: JsonProperty("Disable Carbon OnCreateWorldProjectile Hook")] bool CarbonOnCreateWorldProjectile = true,
            [property: JsonProperty("Disable Carbon OnProjectileRicochet Hook")] bool CarbonOnProjectileRicochet = true,
            [property: JsonProperty("Disable Carbon OnConstructionPlace Hook")] bool CarbonOnConstructionPlace = true,
            [property: JsonProperty("Disable Carbon OnBuildingSplit Hook")] bool CarbonOnBuildingSplit = true,
            [property: JsonProperty("Disable Carbon OnDoorOpened Hook")] bool CarbonOnDoorOpened = true,
            [property: JsonProperty("Disable Carbon OnDoorClosed Hook")] bool CarbonOnDoorClosed = true,
            [property: JsonProperty("Disable Carbon OnCodeEntered Hook")] bool CarbonOnCodeEntered = true,
            [property: JsonProperty("Disable Carbon OnWorldPrefabSpawned Hook")] bool CarbonOnWorldPrefabSpawned = true,
            [property: JsonProperty("Disable Carbon OnEntityFlagsNetworkUpdate Hook")] bool CarbonOnEntityFlagsNetworkUpdate = true,
            [property: JsonProperty("Disable Carbon OnEntitySnapshot Hook")] bool CarbonOnEntitySnapshot = true,
            [property: JsonProperty("Disable Carbon OnWeaponFired Hook")] bool CarbonOnWeaponFired = true,
            [property: JsonProperty("Disable Carbon OnLoseCondition Hook")] bool CarbonOnLoseCondition = true);

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Hooks
        private void Loaded()
        {
            _harmony ??= new Harmony("com.Skelee.AwakenOptimizations");
            _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "ServerUpdate"), transpiler: new HarmonyMethod(typeof(BasePlayerServUpdate), nameof(BasePlayerServUpdate.Transpiler)));
            _harmony.Patch(AccessTools.Method(typeof(PlayerHelicopter), "ServerInit"), transpiler: new HarmonyMethod(typeof(MiniServerInit), nameof(MiniServerInit.Transpiler)));
            _harmony.Patch(AccessTools.Method(typeof(ServerMgr), "Update"), transpiler: new HarmonyMethod(typeof(TurretUpdate), nameof(TurretUpdate.Transpiler)));
            _harmony.Patch(AccessTools.Method(typeof(BaseNetworkable), "OnKilled"), transpiler: new HarmonyMethod(typeof(NoHeliGibs), nameof(NoHeliGibs.Transpiler)));
            // _harmony.Patch(AccessTools.Method(typeof(ServerMgr), "OnValidateAuthTicketResponse"), transpiler: new HarmonyMethod(typeof(AuthSessionKickFix), nameof(AuthSessionKickFix.Transpiler)));

            #region Console Cleanup
            // Temporarily disabled problematic transpilers that cause label issues
            // _harmony.Patch(AccessTools.Method(typeof(ServerMgr), "SpawnNewPlayer"), transpiler: new HarmonyMethod(typeof(ConnectedLogChanges), nameof(ConnectedLogChanges.Transpiler)));
            _harmony.Patch(AccessTools.Method(typeof(ServerMgr), "ReadDisconnectReason"), prefix: new HarmonyMethod(typeof(DisconnectLogChanges), nameof(DisconnectLogChanges.Prefix)));
            // _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "EnterGame"), transpiler: new HarmonyMethod(typeof(HasSpawnedLogRemove), nameof(HasSpawnedLogRemove.Transpiler)));
            // _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "LifeStoryStart"), transpiler: new HarmonyMethod(typeof(LifeStoryLogRemove), nameof(LifeStoryLogRemove.Transpiler)));
            // _harmony.Patch(AccessTools.Method(typeof(SkeletonProperties), "BuildDictionary"), transpiler: new HarmonyMethod(typeof(SkeletonPropertiesLogRemove), nameof(SkeletonPropertiesLogRemove.Transpiler)));
            #endregion

            #region Oxide Hook Removals
            if (config?.CanNetworkTo == true)
            {
                PatchAllMethods(_harmony, typeof(RemoveCanNetworkTo), new[] { typeof(BasePlayer), typeof(BaseNetworkable), typeof(BaseEntity) }, "ShouldNetworkTo");
            }
            if (config?.CanCreateWorldProjectile == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "CreateWorldProjectile"), transpiler: new HarmonyMethod(typeof(RemoveCanCreateWorldProjectile), nameof(RemoveCanCreateWorldProjectile.Transpiler)));
            if (config?.OnCreateWorldProjectile == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "CreateWorldProjectile"), transpiler: new HarmonyMethod(typeof(RemoveOnCreateWorldProjectile), nameof(RemoveOnCreateWorldProjectile.Transpiler)));
            // Disabled due to label issues - if (config?.OnTick == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "OnReceiveTick"), transpiler: new HarmonyMethod(typeof(RemoveOnPlayerTick), nameof(RemoveOnPlayerTick.Transpiler)));
            // Disabled due to label issues - if (config?.OnPlayerInput == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "OnReceiveTick"), transpiler: new HarmonyMethod(typeof(RemoveOnPlayerInput), nameof(RemoveOnPlayerInput.Transpiler)));
            // Disabled due to label issues - if (config?.OnConstructionPlace == true) _harmony.Patch(AccessTools.Method(typeof(Planner), "DoPlacement"), transpiler: new HarmonyMethod(typeof(RemoveOnConstructionPlace), nameof(RemoveOnConstructionPlace.Transpiler)));
            if (config?.OnBuildingSplit == true) _harmony.Patch(AccessTools.Method(typeof(ServerBuildingManager), "Split"), transpiler: new HarmonyMethod(typeof(RemoveOnBuildingSplit), nameof(RemoveOnBuildingSplit.Transpiler)));
            if (config?.OnDoorOpened == true) _harmony.Patch(AccessTools.Method(typeof(Door), "RPC_OpenDoor"), transpiler: new HarmonyMethod(typeof(RemoveOnDoorOpened), nameof(RemoveOnDoorOpened.Transpiler)));
            if (config?.OnDoorClosed == true) _harmony.Patch(AccessTools.Method(typeof(Door), "RPC_CloseDoor"), transpiler: new HarmonyMethod(typeof(RemoveOnDoorClosed), nameof(RemoveOnDoorClosed.Transpiler)));
            if (config?.OnCodeEntered == true) _harmony.Patch(AccessTools.Method(typeof(CodeLock), "UnlockWithCode"), transpiler: new HarmonyMethod(typeof(RemoveOnCodeEntered), nameof(RemoveOnCodeEntered.Transpiler)));
            if (config?.OnEntityFlagsNetworkUpdate == true) _harmony.Patch(AccessTools.Method(typeof(BaseEntity), "SendNetworkUpdate_Flags"), transpiler: new HarmonyMethod(typeof(RemoveOnEntityFlagsNetworkUpdate), nameof(RemoveOnEntityFlagsNetworkUpdate.Transpiler)));
            if (config?.OnEntitySnapshot == true)
            {
                _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "SendEntitySnapshot"), transpiler: new HarmonyMethod(typeof(RemoveOnEntitySnapshotPlayer), nameof(RemoveOnEntitySnapshotPlayer.Transpiler)));
                _harmony.Patch(AccessTools.Method(typeof(BaseNetworkable), "SendAsSnapshot"), transpiler: new HarmonyMethod(typeof(RemoveOnEntitySnapshot), nameof(RemoveOnEntitySnapshot.Transpiler)));
            }
            // Disabled due to label issues - if (config?.OnWeaponFired == true) _harmony.Patch(AccessTools.Method(typeof(BaseProjectile), "CLProject"), transpiler: new HarmonyMethod(typeof(RemoveOnWeaponFired), nameof(RemoveOnWeaponFired.Transpiler)));
            // Disabled due to label issues - if (config?.OnProjectileRicochet == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "OnProjectileRicochet"), transpiler: new HarmonyMethod(typeof(RemoveOnProjectileRicochet), nameof(RemoveOnProjectileRicochet.Transpiler)));
            // Disabled due to label issues - if (config?.OnLoseCondition == true) _harmony.Patch(AccessTools.Method(typeof(Item), "LoseCondition"), transpiler: new HarmonyMethod(typeof(RemoveOnLoseCondition), nameof(RemoveOnLoseCondition.Transpiler)));
            #endregion

            #region Carbon Hook Removals
            if (config?.CarbonCanNetworkTo == true)
            {
                PatchAllMethods(_harmony, typeof(RemoveCarbonCanNetworkTo), new[] { typeof(BasePlayer), typeof(BaseNetworkable), typeof(BaseEntity) }, "ShouldNetworkTo");
            }
            if (config?.CarbonCanCreateWorldProjectile == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "CreateWorldProjectile"), transpiler: new HarmonyMethod(typeof(RemoveCarbonCanCreateWorldProjectile), nameof(RemoveCarbonCanCreateWorldProjectile.Transpiler)));
            if (config?.CarbonOnCreateWorldProjectile == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "CreateWorldProjectile"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnCreateWorldProjectile), nameof(RemoveCarbonOnCreateWorldProjectile.Transpiler)));
            // Disabled due to label issues - if (config?.CarbonOnTick == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "OnReceiveTick"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnPlayerTick), nameof(RemoveCarbonOnPlayerTick.Transpiler)));
            // Disabled due to label issues - if (config?.CarbonOnPlayerInput == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "OnReceiveTick"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnPlayerInput), nameof(RemoveCarbonOnPlayerInput.Transpiler)));
            // Disabled due to label issues - if (config?.CarbonOnConstructionPlace == true) _harmony.Patch(AccessTools.Method(typeof(Planner), "DoPlacement"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnConstructionPlace), nameof(RemoveCarbonOnConstructionPlace.Transpiler)));
            if (config?.CarbonOnBuildingSplit == true) _harmony.Patch(AccessTools.Method(typeof(ServerBuildingManager), "Split"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnBuildingSplit), nameof(RemoveCarbonOnBuildingSplit.Transpiler)));
            if (config?.CarbonOnDoorOpened == true) _harmony.Patch(AccessTools.Method(typeof(Door), "RPC_OpenDoor"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnDoorOpened), nameof(RemoveCarbonOnDoorOpened.Transpiler)));
            if (config?.CarbonOnDoorClosed == true) _harmony.Patch(AccessTools.Method(typeof(Door), "RPC_CloseDoor"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnDoorClosed), nameof(RemoveCarbonOnDoorClosed.Transpiler)));
            if (config?.CarbonOnCodeEntered == true) _harmony.Patch(AccessTools.Method(typeof(CodeLock), "UnlockWithCode"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnCodeEntered), nameof(RemoveCarbonOnCodeEntered.Transpiler)));
            if (config?.CarbonOnEntityFlagsNetworkUpdate == true) _harmony.Patch(AccessTools.Method(typeof(BaseEntity), "SendNetworkUpdate_Flags"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnEntityFlagsNetworkUpdate), nameof(RemoveCarbonOnEntityFlagsNetworkUpdate.Transpiler)));
            if (config?.CarbonOnEntitySnapshot == true)
            {
                _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "SendEntitySnapshot"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnEntitySnapshotPlayer), nameof(RemoveCarbonOnEntitySnapshotPlayer.Transpiler)));
                _harmony.Patch(AccessTools.Method(typeof(BaseNetworkable), "SendAsSnapshot"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnEntitySnapshot), nameof(RemoveCarbonOnEntitySnapshot.Transpiler)));
            }
            // Disabled due to label issues - if (config?.CarbonOnWeaponFired == true) _harmony.Patch(AccessTools.Method(typeof(BaseProjectile), "CLProject"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnWeaponFired), nameof(RemoveCarbonOnWeaponFired.Transpiler)));
            // Disabled due to label issues - if (config?.CarbonOnProjectileRicochet == true) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "OnProjectileRicochet"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnProjectileRicochet), nameof(RemoveCarbonOnProjectileRicochet.Transpiler)));
            // Disabled due to label issues - if (config?.CarbonOnLoseCondition == true) _harmony.Patch(AccessTools.Method(typeof(Item), "LoseCondition"), transpiler: new HarmonyMethod(typeof(RemoveCarbonOnLoseCondition), nameof(RemoveCarbonOnLoseCondition.Transpiler)));
            #endregion
        }

        private void Unload() => _harmony?.UnpatchAll("com.Skelee.AwakenOptimizations");

        private void OnServerInitialized(bool initial) => UpdateTurrets(22f);

        private void OnEntitySpawned(AutoTurret turret) => UpdateTurret(turret, 22f);

        private void OnEntitySpawned(BaseHelicopter heli)
        {
            if (heli != null)
            {
                heli.fireBall.guid = string.Empty;
                heli.explosionEffect.guid = string.Empty;
                heli.serverGibs.guid = string.Empty;
            }
        }
        #endregion

        #region Harmony
        private static Label GetLabel(CodeInstruction inst, ILGenerator? generator = null)
        {
            if (inst.labels == null) inst.labels = new List<Label>();
            if (inst.labels.Count == 0 && generator != null)
            {
                var label = generator.DefineLabel();
                inst.labels.Add(label);
                return label;
            }
            return inst.labels[0];
        }

        private static void UpdateTurrets(float range) => BaseNetworkable.serverEntities.OfType<AutoTurret>().ToList().ForEach(t => UpdateTurret(t, range));
        private static void UpdateTurret(AutoTurret turret, float range)
        {
            turret.sightRange = range;
            if (turret.targetTrigger.TryGetComponent(out SphereCollider collider)) collider.radius = range;
        }

        private static class NoHeliGibs
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindLastIndex(i => i.opcode == OpCodes.Call && i.operand is MethodBase { Name: "Kill" }) is var killLineIndex && killLineIndex != -1
                ? list.Take(killLineIndex - 1).Append(new CodeInstruction(OpCodes.Ldc_I4_0)).Concat(list.Skip(killLineIndex)) : list;
        }

        private static class BasePlayerServUpdate
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Call && i.operand is MethodBase { Name: "LifeStoryUpdate" }) is var lifeStoryLine && lifeStoryLine != -1
                ? list.Take(lifeStoryLine - 8).Concat(list.Skip(lifeStoryLine + 2)) : list;
        }

        private static class MiniServerInit
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldc_R4 && (float)i.operand == 0.0f) is var possibleLine && possibleLine != -1
                ? list.Take(possibleLine - 4).Concat(list.Skip(possibleLine + 4)) : list;
        }

        private static class TurretUpdate
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                if (list.FindLastIndex(i => i.opcode == OpCodes.Ldc_R8 && (double)i.operand == 0.25) is var flameTurretLine && flameTurretLine != -1)
                    list[flameTurretLine] = new CodeInstruction(OpCodes.Ldc_R8, 1.0);
                if (list.FindLastIndex(i => i.opcode == OpCodes.Ldc_R8 && (double)i.operand == 0.5) is var autoTurretLine && autoTurretLine != -1)
                    list[autoTurretLine] = new CodeInstruction(OpCodes.Ldc_R8, 1.0);
                return list;
            }
        }

        private static class AuthSessionKickFix
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator)
            {
                var list = instructions.ToList();
                int line = list.FindLastIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "Steam gave us a 'ok' ticket response for already connected id {0}");
                if (line == -1) return list;

                line += 11;
                var label = GetLabel(list[line], generator);
                list.InsertRange(line, new[] { new CodeInstruction(OpCodes.Ldarg_3), new CodeInstruction(OpCodes.Ldc_I4_5), new CodeInstruction(OpCodes.Bne_Un_S, label), new CodeInstruction(OpCodes.Ret) });
                list[line - 2].operand = GetLabel(list[line], generator);
                return list;
            }
        }

        #region Console Cleanup
        private static class ConnectedLogChanges
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                int connectedLine = list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "{0} with steamid {1} joined from ip {2}");
                if (connectedLine == -1) return list;

                // Just change the log message, don't remove instructions to avoid IL corruption
                list[connectedLine].operand = "{0} [{1}] has connected to the server.";
                return list;
            }
        }

        private static class DisconnectLogChanges
        {
            internal static bool Prefix(Message packet)
            {
                DebugEx.Log($"{packet.connection.username} [{packet.connection.userid}] has disconnected from the server.", StackTraceLogType.None);
                return false;
            }
        }

        private static class HasSpawnedLogRemove
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindLastIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "{0} has spawned") is var hasSpawnedLine && hasSpawnedLine != -1
                ? list.Take(hasSpawnedLine).Concat(list.Skip(hasSpawnedLine + 4)) : list;
        }

        private static class LifeStoryLogRemove
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindLastIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "Stomping old lifeStory") is var lifeStoryLine && lifeStoryLine != -1
                ? list.Take(lifeStoryLine).Concat(list.Skip(lifeStoryLine + 2)) : list;
        }

        private static class SkeletonPropertiesLogRemove
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindLastIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "Bone error in SkeletonProperties.BuildDictionary for ") is var line && line != -1
                ? list.Take(line).Concat(list.Skip(line + 13)) : list;
        }
        #endregion

        #region Oxide Hook Removal
        private static class RemoveCanNetworkTo
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "CanNetworkTo") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 12)) : list;
        }

        private static class RemoveCanCreateWorldProjectile
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "CanCreateWorldProjectile") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveOnCreateWorldProjectile
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnCreateWorldProjectile") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveOnPlayerTick
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnPlayerTick") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 6].WithOperand(GetLabel(list[hookLine + 9], generator))).Concat(list.Skip(hookLine + 9)) : list;
        }

        private static class RemoveOnPlayerInput
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnPlayerInput") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 3].WithOperand(GetLabel(list[hookLine + 8], generator))).Concat(list.Skip(hookLine + 8)) : list;
        }

        private static class RemoveOnConstructionPlace
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnConstructionPlace") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 21].WithOperand(GetLabel(list[hookLine + 19], generator))).Concat(list.Skip(hookLine + 19)) : list;
        }

        private static class RemoveOnBuildingSplit
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnBuildingSplit") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveOnDoorOpened
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnDoorOpened") is var hookLine && hookLine != -1
                ? list.Take(hookLine - 1).Concat(list.Skip(hookLine + 5)) : list;
        }

        private static class RemoveOnDoorClosed
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnDoorClosed") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveOnCodeEntered
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnCodeEntered") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 9)) : list;
        }

        private static class RemoveOnEntityFlagsNetworkUpdate
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnEntityFlagsNetworkUpdate") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 6)) : list;
        }

        private static class RemoveOnEntitySnapshotPlayer
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnEntitySnapshot") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 9)) : list;
        }

        private static class RemoveOnEntitySnapshot
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnEntitySnapshot") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveOnWeaponFired
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnWeaponFired") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 16].WithOperand(GetLabel(list[hookLine + 8], generator))).Concat(list.Skip(hookLine + 8)) : list;
        }

        private static class RemoveOnProjectileRicochet
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "OnProjectileRicochet") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 15].WithOperand(GetLabel(list[hookLine + 7], generator))).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveOnLoseCondition
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnLoseCondition") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 2].WithOperand(GetLabel(list[hookLine + 8], generator))).Concat(list.Skip(hookLine + 8)) : list;
        }
        #endregion

        #region Carbon Hook Removal
        private static class RemoveCarbonCanNetworkTo
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "ICanNetworkTo") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 12)) : list;
        }

        private static class RemoveCarbonCanCreateWorldProjectile
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "ICanCreateWorldProjectile") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveCarbonOnCreateWorldProjectile
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnCreateWorldProjectile") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveCarbonOnPlayerTick
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnPlayerTick") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 6].WithOperand(GetLabel(list[hookLine + 9], generator))).Concat(list.Skip(hookLine + 9)) : list;
        }

        private static class RemoveCarbonOnPlayerInput
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnPlayerInput") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 3].WithOperand(GetLabel(list[hookLine + 8], generator))).Concat(list.Skip(hookLine + 8)) : list;
        }

        private static class RemoveCarbonOnConstructionPlace
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnConstructionPlace") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 21].WithOperand(GetLabel(list[hookLine + 19], generator))).Concat(list.Skip(hookLine + 19)) : list;
        }

        private static class RemoveCarbonOnBuildingSplit
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnBuildingSplit") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveCarbonOnDoorOpened
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnDoorOpened") is var hookLine && hookLine != -1
                ? list.Take(hookLine - 1).Concat(list.Skip(hookLine + 5)) : list;
        }

        private static class RemoveCarbonOnDoorClosed
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnDoorClosed") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveCarbonOnCodeEntered
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnCodeEntered") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 9)) : list;
        }

        private static class RemoveCarbonOnEntityFlagsNetworkUpdate
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnEntityFlagsNetworkUpdate") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 6)) : list;
        }

        private static class RemoveCarbonOnEntitySnapshotPlayer
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnEntitySnapshot") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 9)) : list;
        }

        private static class RemoveCarbonOnEntitySnapshot
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnEntitySnapshot") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveCarbonOnWeaponFired
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnWeaponFired") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 16].WithOperand(GetLabel(list[hookLine + 8], generator))).Concat(list.Skip(hookLine + 8)) : list;
        }

        private static class RemoveCarbonOnProjectileRicochet
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnProjectileRicochet") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 15].WithOperand(GetLabel(list[hookLine + 7], generator))).Concat(list.Skip(hookLine + 7)) : list;
        }

        private static class RemoveCarbonOnLoseCondition
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "IOnLoseCondition") is var hookLine && hookLine != -1
                ? list.Take(hookLine).Append(list[hookLine - 2].WithOperand(GetLabel(list[hookLine + 8], generator))).Concat(list.Skip(hookLine + 8)) : list;
        }
        #endregion
        #endregion

        #region Harmony Extensions
        private static void PatchAllMethods(Harmony harmony, Type patchType, Type[] targetTypes, string methodName) =>
            Array.ForEach(targetTypes, t => harmony.Patch(AccessTools.Method(t, methodName), transpiler: new HarmonyMethod(patchType, nameof(RemoveCanNetworkTo.Transpiler))));
        #endregion
    }

    // Extension methods must be in a separate static class
    internal static class CodeInstructionExtensions
    {
        internal static CodeInstruction WithOperand(this CodeInstruction instruction, object operand) =>
            new(instruction.opcode, operand) { labels = instruction.labels };
    }
}










