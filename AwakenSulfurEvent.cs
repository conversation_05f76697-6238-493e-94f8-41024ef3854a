using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Configuration;
using Oxide.Core.Plugins;
using UnityEngine;
using Oxide.Game.Rust.Cui;
using ProtoBuf;

namespace Oxide.Plugins
{
    [Info("Sulfur Event", "Skelee", "1.5.2")]
    public class AwakenSulfurEvent : RustPlugin
    {
        #region Plugin References
        [PluginReference] private Plugin? MonumentFinder, AwakenClans, ImageLibrary, AwakenVotingSystem, ClanCores, AwakenStats;
        #endregion

        #region Image handling
        void Loaded() {
            if (ImageLibrary == null)
            {
                Puts("Image Library is missing");
                return;
            }
            ImageLibrary?.Call("AddImage", "https://img.drxp.xyz/uploads/w9DuQlaEWB.png", "Image_7425");
        }
        #endregion

        #region Configuration
        private EventConfig config;
        private class EventConfig
        {
            [JsonProperty("EventDuration (in seconds)")]
            public float EventDuration = 900f;

            [JsonProperty("NodeScale (visual size multiplier)")]
            public float NodeScale = 3f;

            [JsonProperty("BubbleRadius (UI render radius)")]
            public float BubbleRadius = 15f;

            [JsonProperty("CreateSphere (create the dome bubble)")]
            public bool CreateSphere = true;

            [JsonProperty("UILabel (UI text)")]
            public string UILabel = "<size=20><b><color=#9CFF1E>AMITY Sulfur</color></b></size>";

            [JsonProperty("DiscordWebhook")]
            public string DiscordWebhook = "https://discord.com/api/webhooks/your_webhook_url";

            [JsonProperty("Items to Remove on Craft/Enter")]
            public List<string> ItemsToRemove { get; set; } = new List<string>();

            [JsonProperty("Gather Yield (leave true. Removes the sparks)")]
            public bool GatherYield { get; set; } = true;

            [JsonProperty("Minimum Ore Amount (Min sulfur given per hit)")]
            public int MinSulfurOreAmount { get; set; } = 1;

            [JsonProperty("Maximum Ore Amount (Max sulfur given per hit)")]
            public int MaxSulfurOreAmount { get; set; } = 5;
        }

        protected override void LoadDefaultConfig()
        {
            config = new EventConfig
            {
                EventDuration = 900f,
                NodeScale = 3f,
                BubbleRadius = 15f,
                CreateSphere = true,
                UILabel = "<size=20><b><color=#9CFF1E>AMITY Sulfur</color></b></size>",
                DiscordWebhook = "https://discord.com/api/webhooks/your_webhook_url",
                ItemsToRemove = new List<string>(),
                GatherYield = true,
                MinSulfurOreAmount = 1,
                MaxSulfurOreAmount = 5
            };
            SaveConfig();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<EventConfig>();
                if (config == null || config.EventDuration <= 0 || config.NodeScale <= 0)
                {
                    Puts("Config was null or invalid, loading default.");
                    LoadDefaultConfig();
                }
                if (config.MinSulfurOreAmount == 0 && config.MaxSulfurOreAmount == 0)
                {
                    config.GatherYield = true;
                    config.MinSulfurOreAmount = 1;
                    config.MaxSulfurOreAmount = 5;
                    Puts("Older config detected, adding new sulfur yield settings.");
                    SaveConfig();
                }
            }
            catch (Exception ex)
            {
                PrintError("Error loading config: " + ex.Message);
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig() => Config.WriteObject(config, true);
        #endregion

        #region Localization
        protected override void LoadDefaultMessages()
        {
            var messages = new Dictionary<string, string>
            {
                { "EventStarted", "<color=#FFCC00>[Sulfur Event]</color> Sulfur Event started at {EVENT_MONUMENT}! Unlimited sulfur for {DURATION_MINUTES} minutes!" },
                { "EventEnded", "<color=#FFCC00>[Sulfur Event]</color> Sulfur Event Ended!\nTotal Sulfur: {TOTAL_SULFUR}\nTotal Farmers: {TOTAL_FARMERS}\nWinning Clan: {WINNING_CLAN} ({WINNING_SULFUR} sulfur)\nOther Clans: {RUNNER_UP} ({RUNNER_UP_SULFUR} sulfur)\nTop Farmer: {TOP_FARMER}" }
            };
            lang.RegisterMessages(messages, this);
        }

        private string GetMessage(string key, string userId) => lang.GetMessage(key, this, userId);
        #endregion

        #region Data Persistence
        private DynamicConfigFile placementsDataFile;
        private DynamicConfigFile clansDataFile;
        private DynamicConfigFile playersDataFile;
        private DynamicConfigFile generalDataFile;

        private StoredData storedData;
        private ClansData clansData;
        private PlayersData playersData;
        private GeneralData generalData;

        private class StoredData { public Dictionary<string, MonumentData> Locations = new Dictionary<string, MonumentData>(); }
        private class MonumentData { public string MonumentName; public Vector3 RelativePosition; public float DomeRadius; }
        private class ClansData { public Dictionary<string, ClanInfo> Clans = new Dictionary<string, ClanInfo>(); }
        private class ClanInfo { public int MemberCount; public float SulfurCollected; }
        private class PlayersData { public Dictionary<string, PlayerInfo> Players = new Dictionary<string, PlayerInfo>(); }
        private class PlayerInfo { public string ClanTag; public float SulfurCollected; public bool IsFarmer; public string DisplayName; }
        private class GeneralData { public int ClansAtEvent; public int TotalFarmers; public float TotalSulfurFarmed; public List<string> Winners = new List<string>(); public List<string> RunnerUps = new List<string>(); public string TopFarmer; }
        #endregion

        #region Event Lifecycle
        private Timer eventTimer;
        private Timer uiCheckTimer;
        private Timer dataSaveTimer;
        private bool eventActive = false;
        private string eventMonument;
        private string currentArenaName = "";
        private Vector3 eventLocation;
        private BaseEntity spawnedSulfurNode;
        private BaseEntity oreParentEntity;
        private List<BaseEntity> dome = new List<BaseEntity>();

        private float eventStartTime;
        private float eventEndTime;

        private const string PERM_USE = "awakensulfurevent.use";
        private const string PERM_ADMIN = "awakensulfurevent.admin";
        #endregion

        #region Map Marker System
        private Dictionary<string, MapNote> activeMarkers = new Dictionary<string, MapNote>();

        private Vector3 GetGroundedPosition(Vector3 position)
        {
            RaycastHit hit;
            if (Physics.Raycast(position + Vector3.up * 50f, Vector3.down, out hit, 100f))
                return hit.point;
            return position;
        }

        private void AddActiveSulfurEventMarker(string markerKey, Vector3 position)
        {
            if (activeMarkers.ContainsKey(markerKey))
                return;

            Vector3 groundedPosition = GetGroundedPosition(position);
            var associatedId = new NetworkableId((uint)UnityEngine.Random.Range(1, int.MaxValue));
            var note = new MapNote
            {
                noteType = 1,
                isPing = true,
                icon = 5,
                colourIndex = 2,
                worldPosition = groundedPosition,
                associatedId = associatedId,
                label = "Sulfur Event"
            };

            foreach (var activePlayer in BasePlayer.activePlayerList)
            {
                SendMapNoteToClient(activePlayer, note);
            }

            activeMarkers[markerKey] = note;
            Puts($"Map marker added for Sulfur Event at position {groundedPosition}.");
        }

        private void RemoveActiveSulfurEventMarker(string markerKey)
        {
            if (!activeMarkers.TryGetValue(markerKey, out var markerNote))
                return;

            using (var mapNoteList = Facepunch.Pool.Get<MapNoteList>())
            {
                mapNoteList.notes = Facepunch.Pool.Get<List<MapNote>>();
                mapNoteList.notes.Add(new MapNote
                {
                    associatedId = markerNote.associatedId,
                    noteType = -1
                });

                foreach (var player in BasePlayer.activePlayerList)
                {
                    player.ClientRPC(null, "Client_ReceivePings", mapNoteList);
                }
                Facepunch.Pool.FreeList(ref mapNoteList.notes);
            }

            activeMarkers.Remove(markerKey);
        }

        private void SendMapNoteToClient(BasePlayer player, MapNote note)
        {
            using (var mapNoteList = Facepunch.Pool.Get<MapNoteList>())
            {
                mapNoteList.notes = Facepunch.Pool.Get<List<MapNote>>();
                mapNoteList.notes.Add(note);
                player.ClientRPC(null, "Client_ReceivePings", mapNoteList);
                Facepunch.Pool.FreeList(ref mapNoteList.notes);
            }
        }
        #endregion

        #region Initialization
        private void Init()
        {
            placementsDataFile = Interface.Oxide.DataFileSystem.GetFile("SulfurEvent_placements");
            clansDataFile = Interface.Oxide.DataFileSystem.GetFile("SulfurEvent_Clans");
            playersDataFile = Interface.Oxide.DataFileSystem.GetFile("SulfurEvent_Players");
            generalDataFile = Interface.Oxide.DataFileSystem.GetFile("SulfurEvent_General");

            LoadPlacementsData();
            LoadClansData();
            LoadPlayersData();
            LoadGeneralData();
            LoadConfig();

            permission.RegisterPermission(PERM_USE, this);
            permission.RegisterPermission(PERM_ADMIN, this);

            dataSaveTimer = timer.Every(5f, SaveDirtyData);
        }
        #endregion

        #region Data Load/Save Methods
        private void LoadPlacementsData() => storedData = placementsDataFile.Exists() ? placementsDataFile.ReadObject<StoredData>() : new StoredData();
        private void SavePlacementsData() => placementsDataFile.WriteObject(storedData);
        private void LoadClansData() => clansData = clansDataFile.Exists() ? clansDataFile.ReadObject<ClansData>() : new ClansData();
        private void SaveClansData() => clansDataFile.WriteObject(clansData);
        private void LoadPlayersData() => playersData = playersDataFile.Exists() ? playersDataFile.ReadObject<PlayersData>() : new PlayersData();
        private void SavePlayersData() => playersDataFile.WriteObject(playersData);
        private void LoadGeneralData() => generalData = generalDataFile.Exists() ? generalDataFile.ReadObject<GeneralData>() : new GeneralData();
        private void SaveGeneralData() => generalDataFile.WriteObject(generalData);
        #endregion

        #region Chat Commands
        [ChatCommand("se")]
        private void HandleSulfurEventCommand(BasePlayer player, string cmd, string[] args)
        {
            if (args.Length == 0)
            {
                SendReply(player, "Usage: /se <set|remove|start|end> <name> [domeRadius]");
                return;
            }

            string sub = args[0].ToLower();
            switch (sub)
            {
                case "set":
                    CmdSet(player, args);
                    break;
                case "remove":
                    CmdRemove(player, args);
                    break;
                case "start":
                    CmdStart(player, args);
                    break;
                case "end":
                    CmdEnd(player, args);
                    break;
                default:
                    SendReply(player, "Invalid subcommand. Use set, remove, start, or end.");
                    break;
            }
        }

        private void CmdSet(BasePlayer player, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, PERM_USE))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            if (args.Length < 3 || !float.TryParse(args[2], out float domeRadius) || domeRadius <= 0)
            {
                SendReply(player, "Usage: /se set <name> <domeRadius> (domeRadius must be a positive number)");
                return;
            }
            string eventName = args[1];
            var monument = GetClosestMonument(player.transform.position);
            if (monument == null)
            {
                SendReply(player, "No valid monument found near you.");
                return;
            }
            Vector3 relativePos = monument.InverseTransformPoint(player.transform.position);
            storedData.Locations[eventName] = new MonumentData { MonumentName = monument.ShortName, RelativePosition = relativePos, DomeRadius = domeRadius };
            SavePlacementsData();
            SendReply(player, $"Set Sulfur Event location '{eventName}' at {monument.ShortName} with dome radius {domeRadius}.");

            if (AwakenVotingSystem == null)
            {
                Puts("AwakenVotingSystem plugin not found - sulfur events will not be votable.");
            }
            else
            {
                Puts($"AwakenVotingSystem plugin found - sulfur event location '{eventName}' is available for voting.");
                // Note: AwakenVotingSystem handles sulfur events through its configuration
                // No need to register individual arenas as it uses a predefined list
            }
        }

        private void CmdRemove(BasePlayer player, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, PERM_USE))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            string eventName = args[1];
            if (storedData.Locations.Remove(eventName))
            {
                SavePlacementsData();
                SendReply(player, $"Removed Sulfur Event location '{eventName}'.");
            }
            else
            {
                SendReply(player, $"Location '{eventName}' not found.");
            }
        }

        private void CmdStart(BasePlayer player, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, PERM_ADMIN))
            {
                SendReply(player, "You don't have permission to start the event.");
                return;
            }
            string eventName = args[1];
            ForceStartSulfurEvent(eventName);
        }

        private void CmdEnd(BasePlayer player, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, PERM_ADMIN))
            {
                SendReply(player, "You don't have permission to end the event.");
                return;
            }

            if (!eventActive)
            {
                SendReply(player, "No sulfur event is currently active.");
                return;
            }

            SendReply(player, "Ending sulfur event...");
            EndSulfurEvent();
            SendReply(player, "Sulfur event ended successfully.");
        }
        #endregion

        private void ScheduleStaticPopup(float delay, string timeText)
        {
            // UI reminders disabled - no popup will be shown to players
            // timer.Once(delay, () =>
            // {
            //     foreach (var ply in BasePlayer.activePlayerList)
            //         rectangle18(ply,
            //             "SULFUR EVENT HAS STARTED",
            //             "ACTIVE FOR...",
            //             timeText
            //         );
            //     timer.Once(12f, () =>
            //     {
            //         foreach (var ply in BasePlayer.activePlayerList)
            //             CuiHelper.DestroyUi(ply, "rectangle18");
            //     });
            // });
        }

        #region Event Methods
        private void ForceStartSulfurEvent(string eventName)
        {
            if (eventActive)
            {
                PrintToChat("Sulfur Event is already active.");
                return;
            }

            if (!storedData.Locations.TryGetValue(eventName, out MonumentData data))
            {
                PrintWarning($"Event location '{eventName}' not found.");
                return;
            }
            var monument = GetMonumentByName(data.MonumentName);
            if (monument == null)
            {
                PrintWarning("Monument not found.");
                return;
            }
            eventLocation = monument.TransformPoint(data.RelativePosition);
            eventMonument = monument.ShortName;
            currentArenaName = eventName;
            eventActive = true;
            eventStartTime = UnityEngine.Time.realtimeSinceStartup;
            eventEndTime = eventStartTime + config.EventDuration;

            clansData = new ClansData();
            playersData = new PlayersData();
            generalData = new GeneralData();

            SpawnSulfurNode(eventLocation);
            CreateHeliCrashEffect(eventLocation);

            string startMsg = GetMessage("EventStarted", null)
                .Replace("{EVENT_MONUMENT}", eventMonument)
                .Replace("{DURATION_MINUTES}", (config.EventDuration / 60).ToString());
            PrintToChat(startMsg);

            ScheduleStaticPopup(0f, "15");

            if (config.EventDuration > 600f)
                ScheduleStaticPopup(config.EventDuration - 600f, "10");

            if (config.EventDuration > 300f)
                ScheduleStaticPopup(config.EventDuration - 300f, "5");

            if (config.EventDuration >  60f)
                ScheduleStaticPopup(config.EventDuration -  60f, "1");

            if (config.EventDuration >   1f)
                ScheduleStaticPopup(config.EventDuration -   1f, "one");


            CreateDome(eventLocation, data.DomeRadius);
            AddActiveSulfurEventMarker("SulfurEvent", eventLocation);

            // Clean up any existing UI for all players when event starts
            CleanupAllUI();

            uiCheckTimer = timer.Every(1f, UpdateUIRender);
            eventTimer = timer.Once(config.EventDuration, EndSulfurEvent);
        }

        private void EndSulfurEvent()
        {
            eventActive = false;
            currentArenaName = "";
            if (spawnedSulfurNode != null && !spawnedSulfurNode.IsDestroyed)
                spawnedSulfurNode.Kill();
            if (oreParentEntity != null && !oreParentEntity.IsDestroyed)
                oreParentEntity.Kill();
            oreParentEntity = null;
            DestroyDome();
            uiCheckTimer?.Destroy();

            // Use comprehensive UI cleanup
            CleanupAllUI();

            RemoveActiveSulfurEventMarker("SulfurEvent");

            EventResults results = CalculateAndLogEventResults();
            string topFarmerName = results.TopFarmer;
            if (!string.IsNullOrEmpty(results.TopFarmer) && playersData.Players.TryGetValue(results.TopFarmer, out PlayerInfo topFarmerInfo))
            {
                topFarmerName = topFarmerInfo.DisplayName;
            }
            else if (!string.IsNullOrEmpty(results.TopFarmer))
            {
                BasePlayer player = BasePlayer.FindByID(ulong.Parse(results.TopFarmer));
                if (player != null)
                {
                    topFarmerName = player.displayName;
                }
            }

            // Award clan core points via API
            if (!string.IsNullOrEmpty(results.WinningClan))
            {
                AwardClanCorePoints(results.WinningClan, "sulfur");

                // Add sulfur win to AwakenStats for the winning clan
                AddSulfurWinToStats(results.WinningClan);
            }

            string embedJson = BuildDiscordEmbed(results, GetTopPlayers());
            SendDiscordEmbed(embedJson);

            string endMsg = GetMessage("EventEnded", null)
                .Replace("{TOTAL_SULFUR}", results.TotalSulfur.ToString("N0"))
                .Replace("{TOTAL_FARMERS}", results.TotalFarmers.ToString())
                .Replace("{WINNING_CLAN}", results.WinningClan)
                .Replace("{WINNING_SULFUR}", results.WinningSulfur.ToString("N0"))
                .Replace("{RUNNER_UP}", results.RunnerUpClan)
                .Replace("{RUNNER_UP_SULFUR}", results.RunnerUpSulfur.ToString("N0"))
                .Replace("{TOP_FARMER}", topFarmerName);
            PrintToChat(endMsg);

            if (AwakenVotingSystem != null)
            {
                Puts("Notifying AwakenVotingSystem of sulfur event completion...");
                // Note: AwakenVotingSystem handles event completion automatically
            }
        }

        private void SpawnSulfurNode(Vector3 location)
        {
            BaseEntity ore = GameManager.server.CreateEntity("assets/bundled/prefabs/autospawn/resource/ores/sulfur-ore.prefab", location);
            if (ore == null) return;
            ore.Spawn();
            ore.SetFlag(BaseEntity.Flags.Busy, true);
            var combatOre = ore as BaseCombatEntity;
            if (combatOre != null)
            {
                combatOre.health = 1000000f;
                combatOre.SendNetworkUpdate();
            }

            BaseEntity parentEntity = GameManager.server.CreateEntity("assets/prefabs/visualization/sphere.prefab", location, Quaternion.identity, true);
            if (parentEntity != null)
            {
                var sphereEntity = parentEntity.GetComponent<SphereEntity>();
                if (sphereEntity != null)
                {
                    sphereEntity.currentRadius = config.NodeScale;
                    sphereEntity.lerpRadius = config.NodeScale;
                    sphereEntity.transform.localScale = new Vector3(config.NodeScale, config.NodeScale, config.NodeScale);
                }
                else
                {
                    parentEntity.transform.localScale = new Vector3(config.NodeScale, config.NodeScale, config.NodeScale);
                }
                parentEntity.Spawn();
                parentEntity.SendNetworkUpdate();
                foreach (Collider col in parentEntity.GetComponentsInChildren<Collider>())
                    UnityEngine.Object.Destroy(col);
                parentEntity.gameObject.layer = (int)Rust.Layer.Reserved1;
                ore.SetParent(parentEntity, false, true);
                ore.transform.localPosition = Vector3.zero;
                ore.transform.localRotation = Quaternion.identity;
                ore.transform.localScale = Vector3.one;
                ore.SendNetworkUpdate();
                oreParentEntity = parentEntity;
            }
            else
            {
                ore.transform.localScale = Vector3.one * config.NodeScale;
                ore.SendNetworkUpdate();
            }
            foreach (Collider col in ore.GetComponentsInChildren<Collider>())
                col.enabled = false;

            spawnedSulfurNode = ore;
        }

        private void CreateHeliCrashEffect(Vector3 location)
        {
            Effect.server.Run("assets/prefabs/npc/patrol helicopter/effects/heli_explosion.prefab", location);
        }
        #endregion

        #region Result Calculation
        private class TopPlayer { public string SteamID; public string DisplayName; public float SulfurCollected; }
        private class EventResults
        {
            public float TotalSulfur;
            public int TotalFarmers;
            public string TopFarmer;
            public string WinningClan;
            public float WinningSulfur;
            public string RunnerUpClan;
            public float RunnerUpSulfur;
            public int ClansAtEvent;
        }

        private EventResults CalculateAndLogEventResults()
        {
            float totalSulfur = 0f;
            int totalFarmers = 0;
            string topFarmer = "";
            float maxPlayerSulfur = 0f;

            foreach (var entry in playersData.Players)
            {
                totalSulfur += entry.Value.SulfurCollected;
                totalFarmers++;
                if (entry.Value.SulfurCollected > maxPlayerSulfur)
                {
                    maxPlayerSulfur = entry.Value.SulfurCollected;
                    topFarmer = entry.Key;
                }
            }

            string winningClan = "";
            string runnerUpClan = "";
            float highestClanSulfur = 0f;
            float secondHighestClanSulfur = 0f;

            foreach (var clanEntry in clansData.Clans)
            {
                if (clanEntry.Value.SulfurCollected > highestClanSulfur)
                {
                    secondHighestClanSulfur = highestClanSulfur;
                    runnerUpClan = winningClan;
                    highestClanSulfur = clanEntry.Value.SulfurCollected;
                    winningClan = clanEntry.Key;
                }
                else if (clanEntry.Value.SulfurCollected > secondHighestClanSulfur)
                {
                    secondHighestClanSulfur = clanEntry.Value.SulfurCollected;
                    runnerUpClan = clanEntry.Key;
                }
            }

            return new EventResults
            {
                TotalSulfur = totalSulfur,
                TotalFarmers = totalFarmers,
                TopFarmer = topFarmer,
                WinningClan = winningClan,
                WinningSulfur = highestClanSulfur,
                RunnerUpClan = runnerUpClan,
                RunnerUpSulfur = secondHighestClanSulfur,
                ClansAtEvent = clansData.Clans.Count
            };
        }

        private List<TopPlayer> GetTopPlayers()
        {
            List<TopPlayer> topPlayers = new List<TopPlayer>();
            foreach (var kvp in playersData.Players)
            {
                topPlayers.Add(new TopPlayer { SteamID = kvp.Key, DisplayName = kvp.Value.DisplayName, SulfurCollected = kvp.Value.SulfurCollected });
            }
            topPlayers.Sort((a, b) => b.SulfurCollected.CompareTo(a.SulfurCollected));
            return topPlayers;
        }
        #endregion

        #region Discord Webhook Reporting
        private string BuildDiscordEmbed(EventResults results, List<TopPlayer> topPlayers)
        {
            var sortedClans = clansData.Clans
                .OrderByDescending(c => c.Value.SulfurCollected)
                .ToList();

            string[] medals = { "🥇", "🥈", "🥉" };

            var winnersSb = new StringBuilder();
            for (int i = 0; i < Math.Min(4, sortedClans.Count); i++)
            {
                var clanTag = sortedClans[i].Key;
                var sulfur  = sortedClans[i].Value.SulfurCollected;
                var medal   = i < medals.Length ? medals[i] + " " : "";
                winnersSb.AppendLine($"{medal}**[{clanTag}]** – Farmed {sulfur:N0} Sulfur");
            }

            var runnerSb = new StringBuilder();
            for (int i = 1; i < sortedClans.Count; i++)
            {
                var clanTag = sortedClans[i].Key;
                var sulfur  = sortedClans[i].Value.SulfurCollected;
                var medal   = i < medals.Length ? medals[i] + " " : "• ";
                runnerSb.AppendLine($"{medal}**[{clanTag}]** – Farmed {sulfur:N0} Sulfur");
            }

            string topFarmerName   = "<unknown>";
            float  topFarmerSulfur = 0f;
            if (playersData.Players.TryGetValue(results.TopFarmer, out var pInfo))
            {
                topFarmerName   = pInfo.DisplayName;
                topFarmerSulfur = pInfo.SulfurCollected;
            }
            var embedObject = new
            {
                content = "",
                embeds = new[]
                {
                    new
                    {
                        title = "Awaken Sulfur",
                        color = 16768601,
                        fields = new[]
                        {
                            new { name = "Details",      value = $"Total Teams: ``{results.ClansAtEvent}``\n Duration: ``{config.EventDuration / 60} min``",    inline = true  },
                            new { name = "‎ ",       value = $"Total Farmers: ``{results.TotalFarmers}``\n Total Sulfur Farmed: ``{results.TotalSulfur:N0}``",     inline = true  },
                            new { name = "Top Farmer", value  = $"**{topFarmerName}** – Farmed {topFarmerSulfur:N0} Sulfur", inline = false },

                            new { name = "Winners",    value = winnersSb.ToString(), inline = false },
                            new { name = "Other Clans", value = runnerSb.ToString(),  inline = false },
                        },
                        footer = new {
                            text     = "Awaken Rust Servers",
                            icon_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213074825252994/yellowoasisglowing.png?ex=683c7132&is=683b1fb2&hm=ac3c31bcc425836719781864c5d994c1e4724965c2b445c6c2843b59e3a7896a&"
                        }
                    }
                },
                username    = "Awaken Events",
                avatar_url  = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213074825252994/yellowoasisglowing.png?ex=683c7132&is=683b1fb2&hm=ac3c31bcc425836719781864c5d994c1e4724965c2b445c6c2843b59e3a7896a&",
                attachments = new object[0]
            };

            return JsonConvert.SerializeObject(embedObject);
        }

        private void SendDiscordEmbed(string embedJson)
        {
            string url = config.DiscordWebhook;
            Dictionary<string, string> headers = new Dictionary<string, string>
            {
                { "Content-Type", "application/json" }
            };

            webrequest.EnqueuePost(url, embedJson, (code, response) =>
            {
                if (code == 200 || code == 204)
                    Puts("Discord embed sent successfully.");
                else
                    PrintWarning($"Failed to send Discord embed. Response code: {code}, Response: {response}");
            }, this, headers);
        }
        #endregion

        #region UI Management
        private void UpdateUIRender()
        {
            foreach (BasePlayer player in BasePlayer.activePlayerList)
            {
                if (player == null || player.IsSleeping())
                    continue;

                float distance = Vector3.Distance(player.transform.position, eventLocation);
                if (distance <= config.BubbleRadius)
                {
                    // UI methods are now disabled internally, but we call them for cleanup
                    ShowStaticUI(player);
                    UpdateTimerUI(player);
                    RemoveBlockedItems(player);
                }
                else
                {
                    // Clean up any existing UI
                    RemoveStaticUI(player);
                    RemoveTimerUI(player);
                }
            }
        }

        private void ShowStaticUI(BasePlayer player)
        {
            string panelName = "SulfurEventUI_Static_" + player.UserIDString;
            CuiHelper.DestroyUi(player, panelName);
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.0" },
                RectTransform = { AnchorMin = "0.3 0.9", AnchorMax = "0.7 0.95" },
                CursorEnabled = false
            }, "Overlay", panelName);
            container.Add(new CuiLabel
            {
                Text = { Text = config.UILabel, FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" }
            }, panelName);
            CuiHelper.AddUi(player, container);
        }

        private void UpdateTimerUI(BasePlayer player)
        {
            string panelName = "SulfurEventUI_Timer_" + player.UserIDString;
            CuiHelper.DestroyUi(player, panelName);
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                Image = { Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.3 0.88", AnchorMax = "0.7 0.93" },
                CursorEnabled = false
            }, "Overlay", panelName);
            float timeLeft = Mathf.Max(0, eventEndTime - UnityEngine.Time.realtimeSinceStartup);
            string timerText = $"<size=14><b><color=#DBE2E9>Time left: {Mathf.FloorToInt(timeLeft / 60)}:{(timeLeft % 60).ToString("00")}</color></b></size>";
            container.Add(new CuiLabel
            {
                Text = { Text = timerText, FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" }
            }, panelName);
            CuiHelper.AddUi(player, container);
        }

        private void RemoveStaticUI(BasePlayer player)
        {
            string panelName = "SulfurEventUI_Static_" + player.UserIDString;
            CuiHelper.DestroyUi(player, panelName);
        }

        private void RemoveTimerUI(BasePlayer player)
        {
            string panelName = "SulfurEventUI_Timer_" + player.UserIDString;
            CuiHelper.DestroyUi(player, panelName);
        }

        private void CleanupAllUI()
        {
            foreach (BasePlayer player in BasePlayer.activePlayerList)
            {
                if (player == null || !player.IsConnected) continue;

                // Remove all possible UI elements
                RemoveStaticUI(player);
                RemoveTimerUI(player);
                CuiHelper.DestroyUi(player, "rectangle18");
                CuiHelper.DestroyUi(player, "rectangle19");
                CuiHelper.DestroyUi(player, "warningIMG");
                CuiHelper.DestroyUi(player, "Panel_5318");
                CuiHelper.DestroyUi(player, "placeholder");
                CuiHelper.DestroyUi(player, "placeholdertime");
                CuiHelper.DestroyUi(player, "MAZEHEADER");

                // Clean up any UI with player-specific names
                CuiHelper.DestroyUi(player, "SulfurEventUI_Static_" + player.UserIDString);
                CuiHelper.DestroyUi(player, "SulfurEventUI_Timer_" + player.UserIDString);
            }
        }
        #endregion


        #region Server Initialization & Cleanup
        private void OnServerInitialized()
        {
            if (MonumentFinder == null || AwakenClans == null)
            {
                PrintError("Missing required dependencies (MonumentFinder, AwakenClans). Unloading SulfurEvent plugin.");
                Interface.Oxide.UnloadPlugin(Name);
                return;
            }

            uiCheckTimer?.Destroy();
            eventTimer?.Destroy();
            dataSaveTimer?.Destroy();

            // Use comprehensive UI cleanup on server initialization
            CleanupAllUI();

            if (spawnedSulfurNode != null && !spawnedSulfurNode.IsDestroyed)
                spawnedSulfurNode.Kill();
            if (oreParentEntity != null && !oreParentEntity.IsDestroyed)
                oreParentEntity.Kill();

            DestroyDome();
            RemoveActiveSulfurEventMarker("SulfurEvent");

            if (AwakenVotingSystem == null)
            {
                Puts("AwakenVotingSystem plugin not found - sulfur events will not be votable.");
                return;
            }

            Puts("AwakenVotingSystem plugin found - sulfur events are available for voting.");
            // Note: AwakenVotingSystem handles sulfur events through its configuration
            // No need to register individual arenas as it uses a predefined list
        }
        #endregion

        #region Data Save Timer
        private void SaveDirtyData()
        {
            SavePlayersData();
            SaveClansData();
        }
        #endregion

        #region Crafting Blocking
        private void OnItemCraftFinished(ItemCraftTask task, Item item)
        {
            if (!eventActive || task == null || item == null)
                return;

            BasePlayer player = item.parent?.playerOwner;
            if (player == null)
                return;

            float distance = Vector3.Distance(player.transform.position, eventLocation);
            if (distance > config.BubbleRadius)
                return;

            if (config.ItemsToRemove == null || config.ItemsToRemove.Count == 0)
                return;

            if (config.ItemsToRemove.Any(x => string.Equals(x, item.info.shortname, StringComparison.OrdinalIgnoreCase)))
            {
                item.Remove();
            }
        }

        #endregion

        #region Clan Core Integration
        private void AwardClanCorePoints(string clanName, string eventType)
        {
            if (ClanCores == null || !ClanCores.IsLoaded)
            {
                PrintWarning("[Sulfur Event] ClanCores plugin not found - cannot award points");
                return;
            }

            try
            {
                bool success = (bool)ClanCores.Call("API_AwardEventPoints", clanName, eventType);
                if (success)
                {
                    Puts($"[Sulfur Event] Successfully awarded clan core points to '{clanName}' for {eventType} event win");
                }
                else
                {
                    PrintWarning($"[Sulfur Event] Failed to award clan core points to '{clanName}'");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[Sulfur Event] Error awarding clan core points: {ex.Message}");
            }
        }
        #endregion

        #region AwakenStats Integration
        private void AddSulfurWinToStats(string clanName)
        {
            if (AwakenStats == null || !AwakenStats.IsLoaded)
            {
                PrintWarning("[Sulfur Event] AwakenStats plugin not found - cannot record event win");
                return;
            }

            try
            {
                // Add sulfur win for the entire clan
                AwakenStats.Call("AddEventWinForClan", clanName, "sulfur");
                Puts($"[Sulfur Event] Successfully recorded sulfur win for clan '{clanName}' in AwakenStats");
            }
            catch (Exception ex)
            {
                PrintError($"[Sulfur Event] Error recording sulfur win in AwakenStats: {ex.Message}");
            }
        }
        #endregion

        [HookMethod("StartEvent")]
        public void StartEvent(string arenaName)
        {
            ForceStartSulfurEvent(arenaName);
        }

        #region API Methods
        [HookMethod("API_GetAvailableArenas")]
        public List<string> API_GetAvailableArenas()
        {
            var arenas = new List<string>();

            if (storedData?.Locations != null)
            {
                foreach (var location in storedData.Locations.Keys)
                {
                    arenas.Add(location);
                }
            }

            return arenas;
        }

        [HookMethod("API_HasArena")]
        public bool API_HasArena(string arenaName)
        {
            if (string.IsNullOrEmpty(arenaName) || storedData?.Locations == null)
                return false;

            return storedData.Locations.ContainsKey(arenaName);
        }

        [HookMethod("API_GetArenaInfo")]
        public Dictionary<string, object> API_GetArenaInfo(string arenaName)
        {
            if (!API_HasArena(arenaName))
                return null;

            var location = storedData.Locations[arenaName];
            return new Dictionary<string, object>
            {
                ["ArenaName"] = arenaName,
                ["MonumentName"] = location.MonumentName,
                ["Position"] = location.RelativePosition,
                ["DomeRadius"] = location.DomeRadius,
                ["IsValid"] = true
            };
        }

        [HookMethod("API_StartEventAtArena")]
        public bool API_StartEventAtArena(string arenaName)
        {
            if (!API_HasArena(arenaName))
            {
                PrintWarning($"Cannot start sulfur event: Arena '{arenaName}' not found!");
                return false;
            }

            if (eventActive)
            {
                PrintWarning("Cannot start sulfur event: An event is already active!");
                return false;
            }

            ForceStartSulfurEvent(arenaName);
            return true;
        }

        [HookMethod("API_IsEventActive")]
        public bool API_IsEventActive()
        {
            return eventActive;
        }

        [HookMethod("API_GetEventStatus")]
        public Dictionary<string, object> API_GetEventStatus()
        {
            var status = new Dictionary<string, object>
            {
                ["IsActive"] = eventActive,
                ["TimeRemaining"] = eventActive ? Mathf.Max(0, eventEndTime - UnityEngine.Time.realtimeSinceStartup) : 0f,
                ["Location"] = eventActive ? eventLocation : Vector3.zero,
                ["ArenaName"] = eventActive ? currentArenaName : "",
                ["ParticipantCount"] = playersData?.Players?.Count ?? 0
            };

            if (eventActive && clansData?.Clans != null)
            {
                status["ClanCount"] = clansData.Clans.Count;
                var topClan = clansData.Clans.OrderByDescending(c => c.Value.SulfurCollected).FirstOrDefault();
                if (!string.IsNullOrEmpty(topClan.Key))
                {
                    status["LeadingClan"] = topClan.Key;
                    status["LeadingSulfur"] = topClan.Value.SulfurCollected;
                }
            }

            return status;
        }
        #endregion
        #region Resource Gathering & Damage Logic
        private bool IsEntityPartOfEvent(BaseEntity entity)
        {
            return eventActive && entity != null &&
                   (entity == spawnedSulfurNode || (oreParentEntity != null && entity.transform.IsChildOf(oreParentEntity.transform)));
        }

        void OnMeleeAttack(BasePlayer attacker, HitInfo info)
        {
            if (!(info.WeaponPrefab is BaseMelee) || attacker == null)
                return;

            if (IsEntityPartOfEvent(info.HitEntity))
            {
                RegisterPlayerForEvent(attacker);
                int sulfurOreAmount = UnityEngine.Random.Range(config.MinSulfurOreAmount, config.MaxSulfurOreAmount + 1);

                if (playersData.Players.TryGetValue(attacker.UserIDString, out PlayerInfo playerInfo))
                {
                    playerInfo.SulfurCollected += sulfurOreAmount;
                    playerInfo.IsFarmer = true;
                }

                string clanTag = GetPlayerClan(attacker);
                if (!string.IsNullOrEmpty(clanTag))
                {
                    if (!clansData.Clans.ContainsKey(clanTag))
                    {
                        clansData.Clans[clanTag] = new ClanInfo { MemberCount = 1, SulfurCollected = 0f };
                    }
                    clansData.Clans[clanTag].SulfurCollected += sulfurOreAmount;
                }

                if (config.GatherYield)
                {
                    ItemDefinition sulfurOreDef = ItemManager.FindItemDefinition("sulfur.ore");
                    if (sulfurOreDef != null)
                    {
                        Item sulfurOreItem = ItemManager.Create(sulfurOreDef, sulfurOreAmount);
                        if (sulfurOreItem != null)
                        {
                            attacker.GiveItem(sulfurOreItem);
                        }
                    }
                }

                info.damageTypes = new Rust.DamageTypeList();
                info.HitMaterial = 0;
                info.PointStart = info.PointEnd = Vector3.zero;
            }
        }

        private object OnEntityTakeDamage(BaseCombatEntity entity, HitInfo info)
        {
            if (IsEntityPartOfEvent(entity))
            {
                info.damageTypes.ScaleAll(0f);
                return true;
            }
            return null;
        }
        private void OnEntityTakeDamage(ResourceEntity instance, HitInfo info)
        {
            if (IsEntityPartOfEvent(instance))
            {
                info.damageTypes.ScaleAll(0f);
            }
        }
        #endregion

        #region Player Registration
        private void RegisterPlayerForEvent(BasePlayer player)
        {
            if (!playersData.Players.ContainsKey(player.UserIDString))
            {
                string clanTag = GetPlayerClan(player);
                playersData.Players[player.UserIDString] = new PlayerInfo
                {
                    ClanTag = clanTag,
                    SulfurCollected = 0f,
                    IsFarmer = false,
                    DisplayName = player.displayName
                };
            }
        }
        #endregion

        #region Dome Management
        private void CreateDome(Vector3 position, float radius)
        {
            if (!config.CreateSphere)
                return;

            BaseEntity entity = GameManager.server.CreateEntity("assets/bundled/prefabs/modding/events/twitch/br_sphere.prefab", position, Quaternion.identity, true);
            if (entity != null)
            {
                var sphere = entity.GetComponent<SphereEntity>();
                if (sphere != null)
                {
                    sphere.currentRadius = radius * 2;
                    sphere.lerpSpeed = 0f;
                    sphere.lerpRadius = sphere.currentRadius;
                }
                entity.Spawn();
                foreach (Collider col in entity.GetComponentsInChildren<Collider>())
                    UnityEngine.Object.Destroy(col);
                entity.gameObject.layer = (int)Rust.Layer.Reserved1;
                dome.Add(entity);
            }
        }

        private void DestroyDome()
        {
            if (dome != null)
            {
                foreach (var ent in dome)
                {
                    if (ent != null && !ent.IsDestroyed)
                        ent.Kill();
                }
                dome.Clear();
            }
        }
        #endregion
        private string FormatTime(float seconds)
        {
            int mins = (int)seconds / 60;
            int secs = (int)seconds % 60;
            return $"{mins:00}:{secs:00}";
        }

        #region UI Functions
        private void rectangle18(BasePlayer player, string headerText, string phaseMessage, string timeText)
        {
            // UI completely disabled - no popup will be shown
            return;

            // var container = new CuiElementContainer();
            // container.Add(new CuiElement {
            //     Name = "rectangle18",
            //     Parent = "Overlay",
            //     DestroyUi = "rectangle18",
            //     Components = {
            //         new CuiImageComponent { Color = "0.03921569 0.03921569 0.03921569 0.93", FadeIn = 1 },
            //         new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-132.84 -259.7", OffsetMax = "132.709 -203.875" }
            //     }
            // });
            // container.Add(new CuiElement {
            //     Name = "rectangle19",
            //     Parent = "rectangle18",
            //     DestroyUi = "rectangle19",
            //     Components = {
            //         new CuiImageComponent { Color = "0.4392157 0.682353 0.1294118 1", FadeIn = 1 },
            //         new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-124.924 -20.263", OffsetMax = "125.327 2.348" }
            //     }
            // });

            // container.Add(new CuiElement {
            //     Name = "warningIMG",
            //     Parent = "rectangle19",
            //     Components = {
            //         new CuiImageComponent { Color = "0.8941177 0.854902 0.8196079 1", Png = ImageLibrary?.Call<string>("GetImage","Image_7425"), FadeIn = 1 },
            //         new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-119.261 -7.061", OffsetMax = "-105.139 7.061" }
            //     }
            // });

            // container.Add(new CuiElement {
            //     Name = "Panel_5318",
            //     Parent = "rectangle19",
            //     DestroyUi = "Panel_5318",
            //     Components = {
            //         new CuiImageComponent { Color = "0.3176471 0.4862745 0.1058824 0.9019608", FadeIn = 1 },
            //         new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "23.499 -8.886", OffsetMax = "122.595 8.958" }
            //     }
            // });
            // container.Add(new CuiElement {
            //     Name     = "placeholder",
            //     Parent   = "rectangle19",
            //     Components = {
            //         new CuiTextComponent { Text = phaseMessage, Font = "robotocondensed-bold.ttf", FontSize = 13, Align = TextAnchor.MiddleLeft, Color = "0.8941177 0.854902 0.8196079 1", FadeIn = 1 },
            //         new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-97.395 -11.006", OffsetMax = "20.595 11.306" }
            //     }
            // });
            // container.Add(new CuiElement {
            //     Name     = "placeholdertime",
            //     Parent   = "rectangle19",
            //     Components = {
            //         new CuiTextComponent { Text = timeText,  Font = "robotocondensed-bold.ttf", FontSize = 11, Align = TextAnchor.MiddleLeft, Color = "0.8941177 0.854902 0.8196079 1", FadeIn = 1 },
            //         new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "23.499 -8.886", OffsetMax = "122.595 8.958" }
            //     }
            // });

            // container.Add(new CuiElement {
            //     Name     = "MAZEHEADER",
            //     Parent   = "rectangle18",
            //     Components = {
            //         new CuiTextComponent { Text = headerText, Font = "robotocondensed-bold.ttf", FontSize = 17, Align = TextAnchor.UpperLeft, Color = "0.8941177 0.854902 0.8196079 1", FadeIn = 1 },
            //         new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-124.924 2.045", OffsetMax = "64.067 24.357" }
            //     }
            // });

            // CuiHelper.AddUi(player, container);
        }
        #endregion

        #region Remove & Block Items
        private void RemoveBlockedItems(BasePlayer player)
        {
            List<ItemContainer> containers = new List<ItemContainer>
            {
                player.inventory.containerMain,
                player.inventory.containerBelt,
                player.inventory.containerWear
            };

            foreach (var container in containers)
            {
                if (container == null)
                    continue;

                for (int i = container.itemList.Count - 1; i >= 0; i--)
                {
                    var item = container.itemList[i];
                    if (config.ItemsToRemove.Any(x => string.Equals(x, item.info.shortname, StringComparison.OrdinalIgnoreCase)))
                    {
                        item.RemoveFromContainer();
                        item.Remove();
                    }
                }
            }
        }
        #endregion

        #region Helper Methods
        private string GetPlayerClan(BasePlayer player)
        {
            if (AwakenClans == null) return "";

            // Use the GetClanTag API method directly with player ID
            var clanTag = AwakenClans.Call("GetClanTag", player.userID);
            return clanTag as string ?? "";
        }

        #region Monument Helper Methods
        private MonumentAdapter GetClosestMonument(Vector3 position)
        {
            var result = MonumentFinder?.Call("API_GetClosest", position) as Dictionary<string, object>;
            return result != null ? new MonumentAdapter(result) : null;
        }

        private MonumentAdapter GetMonumentByName(string monumentName)
        {
            var monuments = MonumentFinder?.Call("API_FindMonuments", "") as List<Dictionary<string, object>>;
            if (monuments == null) return null;
            foreach (var monument in monuments)
            {
                if (monument.ContainsKey("ShortName") && (string)monument["ShortName"] == monumentName)
                    return new MonumentAdapter(monument);
            }
            return null;
        }

        private class MonumentAdapter
        {
            public string ShortName => (string)_monumentInfo["ShortName"];
            public Vector3 Position => (Vector3)_monumentInfo["Position"];
            private Dictionary<string, object> _monumentInfo;
            public MonumentAdapter(Dictionary<string, object> monumentInfo) { _monumentInfo = monumentInfo; }
            public Vector3 TransformPoint(Vector3 localPosition) => ((Func<Vector3, Vector3>)_monumentInfo["TransformPoint"]).Invoke(localPosition);
            public Vector3 InverseTransformPoint(Vector3 worldPosition) => ((Func<Vector3, Vector3>)_monumentInfo["InverseTransformPoint"]).Invoke(worldPosition);
        }
        #endregion
        #endregion
    }
}










