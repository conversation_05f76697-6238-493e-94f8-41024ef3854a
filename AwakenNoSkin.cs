using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using UnityEngine;
using System.Collections.Generic;

namespace Oxide.Plugins {
[Info("awakenNoSkin", "Skelee", "7.0.0")]
[Description("NoSkin command - Note: True client-side skin hiding requires client modifications and cannot be fully implemented server-side")]
public class AwakenNoSkin : CovalencePlugin {
    private Dictionary<ulong, bool> noskinplayers = new Dictionary<ulong, bool>();

    private void Init()
    {
        AddCovalenceCommand("noskin", "CmdNoSkin");
        AddCovalenceCommand("ns", "CmdNoSkin");
    }

    private void CmdNoSkin(IPlayer iplayer, string command, string[] args) {
        if (iplayer?.Object is not BasePlayer player) return;
        ulong id = player.userID;
        bool state = noskinplayers.ContainsKey(id) && noskinplayers[id];
        noskinplayers[id] = !state;

        if (noskinplayers[id]) {
            player.ChatMessage("<color=#ff6b6b>NoSkin Enabled</color> - Note: True skin hiding requires client-side modifications.");
            player.ChatMessage("<color=#ffa500>Server-side skin modification is limited due to networking optimizations.</color>");
        } else {
            player.ChatMessage("<color=#51cf66>NoSkin Disabled</color> - Normal skin display restored.");
        }
    }

    private void OnPlayerDisconnected(BasePlayer player, string reason) {
        if (player == null) return;
        noskinplayers.Remove(player.userID);
    }

    private void Unload() {
        noskinplayers.Clear();
    }
}
}









