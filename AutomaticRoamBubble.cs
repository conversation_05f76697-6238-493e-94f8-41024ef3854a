using Facepunch;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using Rust;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System.Globalization;
using System.Text;

namespace Oxide.Plugins
{
    [Info("AwakenRoamBubble", "Skelee", "1.4.0")]
    [Description("Creates a dedicated PvP zone (Roam Bubble) at a random biome location, tracks kills, AKs, and M2 usage, displays a custom UI, and enforces weapon/attachment restrictions using AwakenClans system. Enhanced Discord embeds with maze-style formatting.")]
    public class AutomaticRoamBubble : CovalencePlugin
    {
        #region Config
        public class UI
        {
            [JsonProperty(PropertyName = "Use UI")] public bool useUI;
            [JsonProperty(PropertyName = "UI Text")] public string UIText;
            [JsonProperty(PropertyName = "UI Text Color")] public string UITextColor;
            [JsonProperty(PropertyName = "UI Text Font Size")] public int UITextFontSize;
            [JsonProperty(PropertyName = "UI Background Color")] public string UIBackgroundColor;
            [JsonProperty(PropertyName = "Logo Image URL")] public string logoImageURL;
        }

        public class Messages
        {
            [JsonProperty(PropertyName = "Display Name for Chat Messages")] public string chatName;
            [JsonProperty(PropertyName = "Display Name Color for Chat Messages")] public string chatNameColor;
            [JsonProperty(PropertyName = "Steam ID Avatar for Chat Messages")] public ulong avatarSteamID;
            [JsonProperty(PropertyName = "Send Global Chat Message when roam starts?")] public bool sendChatMessageOnRoam;
            [JsonProperty(PropertyName = "Send End Result to Global Chat?")] public bool sendEndResultToChat;
            [JsonProperty(PropertyName = "Send End Result to Discord?")] public bool sendEndResultToDiscord;
        }

        public class Discord
        {
            [JsonProperty(PropertyName = "Use Discord Output?")] public bool useDiscord;
            [JsonProperty(PropertyName = "Discord Webhook URL")] public string discordWebhookURL;
            [JsonProperty(PropertyName = "Roam Winners Embed Title")] public string embedTitleWinners;
            [JsonProperty(PropertyName = "Roam Winners Embed Color")] public int embedColorWinners;
            [JsonProperty(PropertyName = "Roam No Winners Embed Title")] public string embedTitleNoWinners;
            [JsonProperty(PropertyName = "Roam No Winners Embed Color")] public int embedColorNoWinners;
            [JsonProperty(PropertyName = "Server Details")] public string serverDetails;
        }

        public class BiomeSettings
        {
            [JsonProperty(PropertyName = "Default Biome")] public string defaultBiome;
            [JsonProperty(PropertyName = "Allowed Biomes")] public List<string> allowedBiomes;
            [JsonProperty(PropertyName = "Snow Positions")] public List<Vector3> snowPositions = new List<Vector3>();
            [JsonProperty(PropertyName = "Desert Positions")] public List<Vector3> desertPositions = new List<Vector3>();
            [JsonProperty(PropertyName = "Grass Positions")] public List<Vector3> grassPositions = new List<Vector3>();
        }

        public class WeaponRestrictions
        {
            [JsonProperty(PropertyName = "Use Allowed Weapons List")] public bool useAllowedWeaponsList;
            [JsonProperty(PropertyName = "Allowed Weapons")] public Dictionary<string, string> allowedWeapons;
            [JsonProperty(PropertyName = "Blocked Weapons")] public Dictionary<string, string> blockedWeapons;
            [JsonProperty(PropertyName = "Blocked Attachments")] public Dictionary<string, string> blockedAttachments;
            [JsonProperty(PropertyName = "Blocked Ammunition")] public Dictionary<string, string> blockedAmmunition;
            [JsonProperty(PropertyName = "Blocked Explosives")] public Dictionary<string, string> blockedExplosives;
            [JsonProperty(PropertyName = "Allow All Other Weapons")] public bool allowOtherWeapons;
            [JsonProperty(PropertyName = "Allow All Other Attachments")] public bool allowOtherAttachments;
        }

        public class DecaySettings
        {
            [JsonProperty(PropertyName = "Enable Accelerated Decay")] public bool enableAcceleratedDecay;
            [JsonProperty(PropertyName = "Decay Speed Multiplier")] public float decaySpeedMultiplier;
            [JsonProperty(PropertyName = "Apply to All Structures")] public bool applyToAllStructures;
            [JsonProperty(PropertyName = "Apply to Barricades Only")] public bool applyToBarricadesOnly;
            [JsonProperty(PropertyName = "Decay Check Interval (seconds)")] public float decayCheckInterval;
        }

        static Configuration config;
        public class Configuration
        {
            [JsonProperty(PropertyName = "Use Clans")] public bool useClans;
            [JsonProperty(PropertyName = "Sphere Radius")] public int sphereRadius;
            [JsonProperty(PropertyName = "Amount of Spheres (More = Darker, creates multiple sphere entities)")] public int numOfSpheres;
            [JsonProperty(PropertyName = "Message Settings")] public Messages messages;
            [JsonProperty(PropertyName = "UI Settings")] public UI ui;
            [JsonProperty(PropertyName = "Discord Settings")] public Discord discord;
            [JsonProperty(PropertyName = "Biome Settings")] public BiomeSettings biomeSettings;
            [JsonProperty(PropertyName = "Weapon Restrictions")] public WeaponRestrictions weaponRestrictions;
            [JsonProperty(PropertyName = "Decay Settings")] public DecaySettings decaySettings;
            [JsonProperty(PropertyName = "Roam Time")] public string roamTime;
            [JsonProperty(PropertyName = "Discord Webhook URL")] public string webhook;
            [JsonProperty(PropertyName = "Debug Mode")] public bool debug;

            public static Configuration DefaultConfig()
            {
                return new Configuration
                {
                    useClans = true,
                    sphereRadius = 150,
                    numOfSpheres = 7,
                    messages = new Messages
                    {
                        chatName = "Roam Bubble",
                        chatNameColor = "#d4af37",
                        avatarSteamID = 76561198194158447,
                        sendChatMessageOnRoam = true,
                        sendEndResultToChat = true,
                        sendEndResultToDiscord = true
                    },
                    ui = new UI
                    {
                        useUI = true,
                        UIText = "ROAMS",
                        UITextColor = "1 0.5 0 1",
                        UITextFontSize = 20,
                        UIBackgroundColor = "0 0 0 0.5",
                        logoImageURL = "https://cdn.awakenrust.com/oasis_roams.png"
                    },
                    discord = new Discord
                    {
                        useDiscord = true,
                        discordWebhookURL = "",
                        embedTitleWinners = "Awaken Roams - {0}x | No BPs | Kits | Shop",
                        embedColorWinners = 3066993,
                        embedTitleNoWinners = "Awaken Roams - No Winners",
                        embedColorNoWinners = 15158332,
                        serverDetails = "US 10x | No BPs | Kits | Shop"
                    },
                    biomeSettings = new BiomeSettings
                    {
                        defaultBiome = "Grass",
                        allowedBiomes = new List<string> { "Snow", "Desert", "Grass" },
                        snowPositions = new List<Vector3>(),
                        desertPositions = new List<Vector3>(),
                        grassPositions = new List<Vector3>()
                    },
                    weaponRestrictions = new WeaponRestrictions
                    {
                        useAllowedWeaponsList = false,
                        allowedWeapons = new Dictionary<string, string>
                        {
                            { "rifle.ak", "AK47" },
                            { "rifle.lr300", "LR-300 Assault Rifle" },
                            { "lmg.m249", "M249" },
                            { "smg.mp5", "MP5A4" },
                            { "smg.custom", "Custom SMG" },
                            { "smg.thompson", "Thompson" },
                            { "pistol.m92", "M92 Pistol" },
                            { "pistol.python", "Python Revolver" },
                            { "pistol.revolver", "Revolver" },
                            { "pistol.semiauto", "Semi-Automatic Pistol" },
                            { "shotgun.spas12", "SPAS-12 Shotgun" },
                            { "crossbow", "Crossbow" },
                            { "bow.hunting", "Hunting Bow" },
                            { "bow.compound", "Compound Bow" }
                        },
                        blockedWeapons = new Dictionary<string, string>
                        {
                            { "rifle.bolt", "Bolt Action Rifle" },
                            { "rifle.l96", "L96 Rifle" },
                            { "shotgun.m4", "M4 Shotgun" },
                            { "shotgun.waterpipe", "Waterpipe Shotgun" },
                            { "shotgun.double", "Double Barrel Shotgun" },
                            { "shotgun.pump", "Pump Shotgun" },
                            { "lmg.m249", "M249" },
                            { "multiplegrenadelauncher", "Multiple Grenade Launcher" },
                            { "rocket.launcher", "Rocket Launcher" }
                        },
                        blockedAttachments = new Dictionary<string, string>
                        {
                            { "weapon.mod.silencer", "Silencer" }
                        },
                        blockedAmmunition = new Dictionary<string, string>(),
                        blockedExplosives = new Dictionary<string, string>(),
                        allowOtherWeapons = true,
                        allowOtherAttachments = true
                    },
                    decaySettings = new DecaySettings
                    {
                        enableAcceleratedDecay = true,
                        decaySpeedMultiplier = 5.0f,
                        applyToAllStructures = true,
                        applyToBarricadesOnly = false,
                        decayCheckInterval = 30.0f
                    },
                    webhook = "REPLACE_WITH_YOUR_DISCORD_WEBHOOK_URL",
                    roamTime = "30m",
                    debug = false
                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = Configuration.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Data File for Player Spawns
        private class PlayerSpawnData
        {
            public Dictionary<string, List<Vector3>> BiomePlayerSpawns { get; set; } = new Dictionary<string, List<Vector3>>
            {
                {"snow", new List<Vector3>()},
                {"desert", new List<Vector3>()},
                {"grass", new List<Vector3>()}
            };
        }

        private PlayerSpawnData playerSpawnData;
        private const string PlayerSpawnDataFile = "automaticroambubble_playerspawns";

        private void LoadPlayerSpawnData()
        {
            playerSpawnData = Interface.Oxide.DataFileSystem.ReadObject<PlayerSpawnData>(PlayerSpawnDataFile);
            if (playerSpawnData == null || playerSpawnData.BiomePlayerSpawns == null)
            {
                playerSpawnData = new PlayerSpawnData();
                SavePlayerSpawnData();
            }
        }

        private void SavePlayerSpawnData()
        {
            Interface.Oxide.DataFileSystem.WriteObject(PlayerSpawnDataFile, playerSpawnData);
        }
        #endregion

        #region State Persistence for Reload/Unload
        private class RoamState
        {
            public List<RoamData> ActiveRoams { get; set; } = new List<RoamData>();
            public DateTime SaveTime { get; set; }
        }

        private class RoamData
        {
            public Vector3 Position { get; set; }
            public string Biome { get; set; }
            public float TimeRemaining { get; set; }
            public float InitialTime { get; set; }
            public int Radius { get; set; }
            public Dictionary<string, TeamStats> TeamStats { get; set; } = new Dictionary<string, TeamStats>();
            public Dictionary<string, PlayerStats> PlayerStats { get; set; } = new Dictionary<string, PlayerStats>();
        }

        private const string RoamStateFile = "automaticroambubble_state";

        private void SaveActiveRoamState()
        {
            if (activeRoams.Count == 0) return;

            var roamState = new RoamState
            {
                SaveTime = DateTime.UtcNow
            };

            foreach (var roam in activeRoams)
            {
                if (roam == null) continue;

                var roamData = new RoamData
                {
                    Position = roam.transform.position,
                    Biome = roam.biome,
                    TimeRemaining = roam.roamTime,
                    InitialTime = roam.initialRoamTime,
                    Radius = config.sphereRadius,
                    TeamStats = new Dictionary<string, TeamStats>(roam.teamStats),
                    PlayerStats = new Dictionary<string, PlayerStats>(roam.playerStats)
                };

                roamState.ActiveRoams.Add(roamData);
            }

            Interface.Oxide.DataFileSystem.WriteObject(RoamStateFile, roamState);
            Puts($"Saved state for {roamState.ActiveRoams.Count} active roam(s)");
        }

        private void RestoreActiveRoamState()
        {
            var roamState = Interface.Oxide.DataFileSystem.ReadObject<RoamState>(RoamStateFile);
            if (roamState?.ActiveRoams == null || roamState.ActiveRoams.Count == 0) return;

            // Check if the save is recent (within 5 minutes)
            var timeSinceSave = DateTime.UtcNow - roamState.SaveTime;
            if (timeSinceSave.TotalMinutes > 5)
            {
                Puts("Roam state is too old, not restoring");
                Interface.Oxide.DataFileSystem.WriteObject(RoamStateFile, new RoamState()); // Clear old state
                return;
            }

            Puts($"Restoring {roamState.ActiveRoams.Count} roam(s) from saved state...");

            foreach (var roamData in roamState.ActiveRoams)
            {
                // Adjust time remaining based on how long the plugin was unloaded
                float adjustedTime = roamData.TimeRemaining - (float)timeSinceSave.TotalSeconds;
                if (adjustedTime <= 0)
                {
                    Puts($"Roam at {roamData.Position} expired while plugin was unloaded, skipping");
                    continue;
                }

                // Recreate the roam bubble
                var bubbleObject = new GameObject();
                var bubbleComp = bubbleObject.AddComponent<AutomaticRoamBubbleComp>();
                bubbleComp.CreateBubble(roamData.Position, roamData.Radius, roamData.Biome, (int)adjustedTime);

                // Restore stats
                bubbleComp.teamStats = roamData.TeamStats;
                bubbleComp.playerStats = roamData.PlayerStats;
                bubbleComp.initialRoamTime = roamData.InitialTime;

                Puts($"Restored roam in {roamData.Biome} biome with {adjustedTime:F0} seconds remaining");
            }

            // Clear the saved state
            Interface.Oxide.DataFileSystem.WriteObject(RoamStateFile, new RoamState());
        }
        #endregion

        #region Local Image Caching
        private string cachedLogoUrl = null;
        private bool isDownloadingLogo = false;

        private void LoadImages()
        {
            // Cache the logo locally to prevent glitching
            CacheLogoLocally();
        }

        private void CacheLogoLocally()
        {
            if (string.IsNullOrEmpty(config?.ui?.logoImageURL) || isDownloadingLogo)
                return;

            string logoUrl = config.ui.logoImageURL;

            // Skip if already cached
            if (!string.IsNullOrEmpty(cachedLogoUrl))
                return;

            // Validate URL
            if (!logoUrl.StartsWith("http://") && !logoUrl.StartsWith("https://"))
                return;

            if (logoUrl.Contains("<color") || logoUrl.Contains("</color>"))
                return;

            isDownloadingLogo = true;

            if (config.debug)
                Puts($"[Roam Logo] Downloading and caching logo from: {logoUrl}");

            // Download and cache the image
            webrequest.Enqueue(logoUrl, null, (code, response) =>
            {
                isDownloadingLogo = false;

                if (code != 200 || string.IsNullOrEmpty(response))
                {
                    PrintWarning($"[Roam Logo] Failed to download logo from {logoUrl} - Code: {code}");
                    return;
                }

                try
                {
                    // For now, just use the original URL since base64 conversion is complex in Oxide
                    // The caching will prevent repeated downloads by storing the URL
                    cachedLogoUrl = logoUrl;

                    if (config.debug)
                        Puts($"[Roam Logo] Logo URL cached successfully: {logoUrl}");

                    // Update UI for all players currently in bubbles
                    RefreshUIForAllPlayers();
                }
                catch (Exception ex)
                {
                    PrintError($"[Roam Logo] Error caching logo: {ex.Message}");
                }

            }, this, Core.Libraries.RequestMethod.GET);
        }

        private void RefreshUIForAllPlayers()
        {
            foreach (var kvp in playersInBubble)
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null && player.IsConnected)
                {
                    // Recreate UI with cached logo
                    CreateRoamUI(player);
                }
            }
        }

        private void CreateLogo(CuiElementContainer container, string parentName)
        {
            // Use cached logo if available, otherwise fall back to direct URL
            string logoUrl = !string.IsNullOrEmpty(cachedLogoUrl) ? cachedLogoUrl : config?.ui?.logoImageURL;

            if (string.IsNullOrEmpty(logoUrl))
            {
                if (config.debug)
                    Puts("[Roam Logo] No logo URL configured or cached");
                return;
            }

            // Validate that the URL is actually a valid image URL and not HTML color tags
            if (logoUrl.Contains("<color") || logoUrl.Contains("</color>"))
            {
                PrintWarning($"[Roam Logo] LogoImageURL contains HTML color tags instead of a valid image URL: {logoUrl}");
                return;
            }

            // Create logo element with cached or direct URL
            if (logoUrl.StartsWith("http://") || logoUrl.StartsWith("https://"))
            {
                if (config.debug)
                    Puts($"[Roam Logo] Creating logo element - Using URL: {logoUrl}");

                container.Add(new CuiElement
                {
                    Name = "roam_logo_image",
                    Parent = parentName,
                    Components =
                    {
                        new CuiRawImageComponent { Url = logoUrl },
                        new CuiRectTransformComponent
                        {
                            AnchorMin = "0.0 0.3",
                            AnchorMax = "0.8 1.0",
                            OffsetMin = "0 0",
                            OffsetMax = "0 0"
                        }
                    }
                });
            }
            else
            {
                if (config.debug)
                    Puts($"[Roam Logo] Invalid logo URL format: {logoUrl}");
            }
        }
        #endregion

        #region Defines
        public static AutomaticRoamBubble Instance;
        [PluginReference] private Plugin? AwakenVotingSystem;
        [PluginReference] private Plugin? AwakenClans;
        [PluginReference] private Plugin? ClanCores;
        [PluginReference] private Plugin? AwakenStats;
        [PluginReference] private Plugin? Vanish;
        [PluginReference] private Plugin? AdminRadar;
        private const string CallRoamPermission = "automaticroambubble.callroam";
        private const string SetLocationPermission = "automaticroambubble.setlocation";
        private List<AutomaticRoamBubbleComp> activeRoams = new List<AutomaticRoamBubbleComp>();
        private Dictionary<string, AutomaticRoamBubbleComp> playersInBubble = new Dictionary<string, AutomaticRoamBubbleComp>();
        private HashSet<ItemId> trackedWeaponItemUids = new HashSet<ItemId>();
        #endregion

        #region Language
        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["NoPermission"] = "You don't have permission to call roams.",
                ["RoamStartedChat"] = "A roam has started! Use /roams to teleport.",
                ["RoamEndedNoWinners"] = "Roam is over, there were no winners due to no participants.",
                ["RoamEndedWinners"] = "Roam is over, {0}'s Team have won the roam.\n\nMembers:\n{1}\nTotal Kills: {2}\nAK Kills: {3}\nM2 Kills: {4}\nTotal Deaths: {5}\nTotal Damage: {6}\nTotal Headshots: {7}",
                ["WeaponRestricted"] = "You cannot use {0} in the roam! This weapon is blocked.",
                ["AttachmentRestricted"] = "You cannot use {0} attachment in the roam! This attachment is blocked.",
                ["AmmunitionRestricted"] = "You cannot use {0} ammunition in the roam! This ammunition is blocked.",
                ["ExplosiveRestricted"] = "You cannot use {0} in the roam! This explosive item is blocked.",
                ["AlreadyInRoam"] = "You are already in a roam bubble!",
                ["LocationSet"] = "Roam Bubble location for {0} biome set to your current position: {1}",
                ["SpawnSet"] = "Roam spawn location for {0} biome set to your current position: {1}",
                ["InvalidBiome"] = "Invalid biome. Use: {0}"
            }, this);
        }
        #endregion

        #region Hooks


        private void CleanupLeftoverBubbles()
        {
            var existingBubbles = UnityEngine.Object.FindObjectsOfType<AutomaticRoamBubbleComp>();
            if (existingBubbles.Length > 0)
            {
                Puts($"AutomaticRoamBubble: Found {existingBubbles.Length} leftover roam bubbles from previous plugin instance. Cleaning up...");

                foreach (var bubble in existingBubbles)
                {
                    try
                    {
                        if (bubble != null)
                        {
                            bubble.DeleteCircle();
                            UnityEngine.Object.DestroyImmediate(bubble);
                        }
                    }
                    catch (System.Exception ex)
                    {
                        PrintError($"Error cleaning up leftover bubble: {ex.Message}");
                    }
                }

                foreach (BasePlayer player in BasePlayer.activePlayerList)
                {
                    if (player != null && player.IsConnected)
                    {
                        CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                    }
                }

                Puts("AutomaticRoamBubble: Leftover bubble cleanup complete.");
            }
        }

        void OnServerInitialized()
        {
            if (AwakenClans == null)
            {
                PrintWarning("AwakenClans plugin not found! Players will be treated as solo players.");
                PrintWarning("Make sure AwakenClans plugin is installed and loaded for proper clan functionality.");
            }
            else
            {
                Puts("AwakenClans plugin detected and integrated successfully.");
            }
        }

        void Unload()
        {
            Puts("AutomaticRoamBubble: Plugin unloading - saving state and cleaning up...");

            // Save current roam state before cleanup
            SaveActiveRoamState();

            CleanupAllPlayerUIs();

            CleanupAllRoamBubbles();

            playersInBubble.Clear();
            trackedWeaponItemUids.Clear();

            playerCache.Clear();
            cacheTimestamps.Clear();

            Puts($"AutomaticRoamBubble: Cleanup complete. Removed {activeRoams.Count} active roam bubbles.");
        }

        void Loaded()
        {
            Instance = this;
            permission.RegisterPermission(CallRoamPermission, this);
            permission.RegisterPermission(SetLocationPermission, this);
            LoadPlayerSpawnData();
            LoadImages(); // This will now cache the logo locally to prevent glitching

            CleanupLeftoverBubbles();

            // Restore any saved roam state after a short delay
            timer.Once(2f, () => {
                RestoreActiveRoamState();
            });

            if (config?.debug == true)
                Puts("[Roam Debug] Plugin loaded with local logo caching system enabled");
        }

        private void CleanupAllPlayerUIs()
        {
            foreach (var kvp in playersInBubble.ToList())
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null && player.IsConnected)
                {
                    CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                }
            }
        }

        private void CleanupAllRoamBubbles()
        {
            // Clean up all active roam bubbles with proper cleanup
            foreach (AutomaticRoamBubbleComp roamBubble in activeRoams.ToList())
            {
                if (roamBubble != null)
                {
                    try
                    {
                        // Call the bubble's cleanup method first
                        roamBubble.DeleteCircle();

                        // Then destroy the component
                        UnityEngine.Object.DestroyImmediate(roamBubble);
                    }
                    catch (System.Exception ex)
                    {
                        PrintError($"Error cleaning up roam bubble: {ex.Message}");
                    }
                }
            }

            // Clear the list
            activeRoams.Clear();
        }

        private static Dictionary<string, BasePlayer> playerCache = new Dictionary<string, BasePlayer>();
        private static readonly TimeSpan cacheTTL = TimeSpan.FromSeconds(5);
        private static Dictionary<string, DateTime> cacheTimestamps = new Dictionary<string, DateTime>();

        object OnEntityTakeDamage(BasePlayer victim, HitInfo info)
        {
            BasePlayer attacker = info?.InitiatorPlayer;
            if (attacker == null) return null;

            AutomaticRoamBubbleComp roamBubble;
            if (!Instance.playersInBubble.TryGetValue(attacker.UserIDString, out roamBubble)) return null;

            AutomaticRoamBubbleComp roamBubbleToCompare;
            if (!Instance.playersInBubble.TryGetValue(victim.UserIDString, out roamBubbleToCompare)) return null;


            if (!IsWeaponAllowed(info.Weapon) || !AreAttachmentsAllowed(info.Weapon))
            {
                attacker.ChatMessage(string.Format(Instance.lang.GetMessage("WeaponRestricted", Instance), info.Weapon?.ShortPrefabName ?? "unknown weapon"));
                return true;
            }

            TeamStats teamStats;
            string attackersTeam = GetTeamTag(attacker);

            if (!string.IsNullOrEmpty(attackersTeam))
            {
                if (!roamBubble.teamStats.TryGetValue(attackersTeam, out teamStats))
                {
                    List<string> members = GetTeamMembers(attacker);
                    if (members.Contains(victim.UserIDString)) return null;

                    roamBubble.teamStats.Add(attackersTeam, new TeamStats
                    {
                        members = members,
                        kills = 0,
                        deaths = 0,
                        headshots = info.isHeadshot ? 1 : 0,
                        damage = Convert.ToInt32(info.damageTypes.Total()),
                        akKills = 0,
                        m2Kills = 0
                    });
                }
                else
                {
                    if (teamStats.members.Contains(victim.UserIDString)) return null;
                    if (info.isHeadshot) teamStats.headshots += 1;
                    teamStats.damage += Convert.ToInt32(info.damageTypes.Total());

                }
            }


            if (roamBubble.playerStats.ContainsKey(attacker.UserIDString))
            {
                if (info.isHeadshot)
                    roamBubble.playerStats[attacker.UserIDString].headshots += 1;
                roamBubble.playerStats[attacker.UserIDString].damage += Convert.ToInt32(info.damageTypes.Total());
            }

            return null;
        }

        void OnEntityDeath(BasePlayer victim, HitInfo info)
        {
            BasePlayer attacker = info?.InitiatorPlayer;
            if (attacker == null || !attacker.userID.IsSteamId()) return;

            // Check if either the victim OR attacker is in a roam bubble
            AutomaticRoamBubbleComp roamBubble = null;
            bool victimInBubble = playersInBubble.TryGetValue(victim.UserIDString, out roamBubble);
            bool attackerInBubble = playersInBubble.TryGetValue(attacker.UserIDString, out var attackerBubble);

            // If neither player is in a roam bubble, ignore this death
            if (!victimInBubble && !attackerInBubble) return;

            // Use the bubble from whichever player is in one (prefer victim's bubble if both are in bubbles)
            if (!victimInBubble && attackerInBubble)
                roamBubble = attackerBubble;

            if (roamBubble == null) return;

            // Check weapon and attachment restrictions
            if (!IsWeaponAllowed(info.Weapon) || !AreAttachmentsAllowed(info.Weapon))
            {
                if (config.debug)
                    Puts($"[Roam Debug] Kill ignored due to restricted weapon: {info.Weapon?.ShortPrefabName ?? "unknown"}");
                return; // Ignore kill if weapon/attachment is restricted
            }

            if (config.debug)
                Puts($"[Roam Debug] Processing kill: {attacker.displayName} killed {victim.displayName} with {info.Weapon?.ShortPrefabName ?? "unknown"}");

            TeamStats teamStats;
            string attackersTeam = GetTeamTag(attacker);
            if (!string.IsNullOrEmpty(attackersTeam))
            {
                if (!roamBubble.teamStats.TryGetValue(attackersTeam, out teamStats))
                {
                    List<string> members = GetTeamMembers(attacker);
                    if (members.Contains(victim.UserIDString)) return; // Don't track friendly fire kills

                    roamBubble.teamStats.Add(attackersTeam, new TeamStats
                    {
                        members = members,
                        kills = 1,
                        deaths = 0,
                        headshots = info.isHeadshot ? 1 : 0,
                        damage = Convert.ToInt32(info.damageTypes.Total()),
                        akKills = IsWeaponAK(info.Weapon) ? 1 : 0,
                        m2Kills = IsWeaponM2(info.Weapon) ? 1 : 0
                    });
                }
                else
                {
                    if (teamStats.members.Contains(victim.UserIDString)) return; // Don't track friendly fire kills

                    teamStats.kills += 1;
                    if (info.isHeadshot) teamStats.headshots += 1;
                    teamStats.damage += Convert.ToInt32(info.damageTypes.Total());
                    if (IsWeaponAK(info.Weapon)) teamStats.akKills += 1;
                    if (IsWeaponM2(info.Weapon)) teamStats.m2Kills += 1;
                }
            }

            // Update player stats for the attacker (initialize if needed)
            if (!roamBubble.playerStats.ContainsKey(attacker.UserIDString))
            {
                roamBubble.playerStats[attacker.UserIDString] = new PlayerStats();
                if (config.debug)
                    Puts($"[Roam Debug] Initialized player stats for attacker: {attacker.displayName}");
            }

            roamBubble.playerStats[attacker.UserIDString].kills += 1;
            if (info.isHeadshot)
                roamBubble.playerStats[attacker.UserIDString].headshots += 1;
            roamBubble.playerStats[attacker.UserIDString].damage += Convert.ToInt32(info.damageTypes.Total());

            if (config.debug)
                Puts($"[Roam Debug] Updated attacker stats: {attacker.displayName} - Kills: {roamBubble.playerStats[attacker.UserIDString].kills}, Deaths: {roamBubble.playerStats[attacker.UserIDString].deaths}");

            string victimsTeam = GetTeamTag(victim);
            if (!string.IsNullOrEmpty(victimsTeam))
            {
                if (!roamBubble.teamStats.TryGetValue(victimsTeam, out teamStats))
                {
                    List<string> members = GetTeamMembers(victim);
                    roamBubble.teamStats.Add(victimsTeam, new TeamStats
                    {
                        members = members,
                        kills = 0,
                        deaths = 1,
                        headshots = 0,
                        damage = 0,
                        akKills = 0,
                        m2Kills = 0
                    });
                }
                else
                    teamStats.deaths += 1;
            }

            // Update player stats for the victim (death) - initialize if needed
            if (!roamBubble.playerStats.ContainsKey(victim.UserIDString))
            {
                roamBubble.playerStats[victim.UserIDString] = new PlayerStats();
                if (config.debug)
                    Puts($"[Roam Debug] Initialized player stats for victim: {victim.displayName}");
            }

            roamBubble.playerStats[victim.UserIDString].deaths += 1;

            if (config.debug)
                Puts($"[Roam Debug] Updated victim stats: {victim.displayName} - Kills: {roamBubble.playerStats[victim.UserIDString].kills}, Deaths: {roamBubble.playerStats[victim.UserIDString].deaths}");

            if (playersInBubble.ContainsKey(victim.UserIDString))
            {
                playersInBubble.Remove(victim.UserIDString);
                CuiHelper.DestroyUi(victim, "AwakenRoamsUI");
            }
        }

        void OnPlayerDeath(BasePlayer player, HitInfo info)
        {
            // Handle all player deaths (not just PvP kills)
            if (player == null) return;

            // Remove player from roam bubble if they're in one
            if (playersInBubble.ContainsKey(player.UserIDString))
            {
                playersInBubble.Remove(player.UserIDString);
                CuiHelper.DestroyUi(player, "AwakenRoamsUI");

                if (config?.debug == true)
                    Puts($"[Roam Debug] Removed {player.displayName} from roam bubble due to death");
            }
        }

        void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            // Handle players disconnecting while in a roam
            if (player == null) return;

            // Remove player from roam bubble if they're in one
            if (playersInBubble.ContainsKey(player.UserIDString))
            {
                playersInBubble.Remove(player.UserIDString);

                if (config?.debug == true)
                    Puts($"[Roam Debug] Removed {player.displayName} from roam bubble due to disconnect");
            }
        }

        object CanUseAttachment(Item item, BasePlayer player)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;
            if (!IsAttachmentAllowed(item.info.shortname))
            {
                player.ChatMessage(string.Format(lang.GetMessage("AttachmentRestricted", this), item.info.shortname));
                return false;
            }
            return null;
        }

        object CanUseItem(BasePlayer player, Item item)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;

            // Check ammunition
            if (item.info.category == ItemCategory.Ammunition && !IsAmmunitionAllowed(item.info.shortname))
            {
                player.ChatMessage(string.Format(lang.GetMessage("AmmunitionRestricted", this), item.info.shortname));
                return false;
            }

            // Check explosives
            if (!IsExplosiveAllowed(item.info.shortname))
            {
                player.ChatMessage(string.Format(lang.GetMessage("ExplosiveRestricted", this), item.info.shortname));
                return false;
            }

            return null;
        }

        object CanThrowExplosive(BasePlayer player, BaseEntity explosive)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;

            if (explosive != null && !IsExplosiveAllowed(explosive.ShortPrefabName))
            {
                player.ChatMessage(string.Format(lang.GetMessage("ExplosiveRestricted", this), explosive.ShortPrefabName));
                return false;
            }

            return null;
        }

        void OnPlayerActiveItemChanged(BasePlayer player, Item oldItem, Item newItem)
        {
            if (player == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(player.UserIDString)) return;

            // If the new item is a weapon, check if it's allowed
            if (newItem?.info?.category == ItemCategory.Weapon)
            {
                if (!IsWeaponAllowedByShortname(newItem.info.shortname))
                {
                    // Force unequip the weapon immediately
                    NextTick(() =>
                    {
                        if (player != null && player.IsConnected && newItem != null)
                        {
                            // Clear the active item to prevent equipping
                            player.svActiveItemID = new ItemId(0);
                            player.SendNetworkUpdate();
                            SendMessage(player, "WeaponRestricted", newItem.info.displayName.english);
                        }
                    });
                    return;
                }

                // Check attachments on the weapon
                if (!AreAttachmentsAllowed(newItem.GetHeldEntity()))
                {
                    NextTick(() =>
                    {
                        if (player != null && player.IsConnected && newItem != null)
                        {
                            // Clear the active item to prevent equipping
                            player.svActiveItemID = new ItemId(0);
                            player.SendNetworkUpdate();
                            SendMessage(player, "AttachmentRestricted", "weapon attachment");
                        }
                    });
                }
            }
        }

        object CanDeployItem(BasePlayer player, Deployer deployer, uint entityId)
        {
            if (!playersInBubble.ContainsKey(player.UserIDString)) return null;

            if (deployer?.GetItem()?.info?.GetComponent<ItemModDeployable>()?.entityPrefab?.resourcePath != null)
            {
                string prefabPath = deployer.GetItem().info.GetComponent<ItemModDeployable>().entityPrefab.resourcePath.ToLower();
                string itemShortname = deployer.GetItem().info.shortname.ToLower();

                // Debug logging
                Puts($"[Roam Debug] CanDeployItem - Item: {itemShortname}, PrefabPath: {prefabPath}");

                // Check for barricade by item shortname and prefab path
                if (itemShortname.Contains("barricade.wood.cover_double") ||
                    prefabPath.Contains("barricade.wood.cover_double") ||
                    prefabPath.Contains("barricade_cover_wood_double") ||
                    prefabPath.Contains("barricade.cover.wood_double"))
                {
                    Puts($"[Roam Debug] Allowing barricade deployment: {itemShortname}");
                    return null; // Allow barricade deployment
                }
                else
                {
                    Puts($"[Roam Debug] Blocking deployment: {itemShortname}");
                    player.ChatMessage("Only barricades can be deployed in the roam bubble! All other deployables are blocked.");
                    return false; // Block all other deployables
                }
            }

            return null;
        }

        object CanBuild(Planner planner, Construction prefab, Construction.Target target)
        {
            BasePlayer player = planner.GetOwnerPlayer();
            if (player == null) return null;

            // Check if the player is in a roam bubble
            if (Instance.playersInBubble.TryGetValue(player.UserIDString, out AutomaticRoamBubbleComp roamBubble))
            {
                string prefabShortName = prefab.fullName.ToLower();

                // Debug logging
                Puts($"[Roam Debug] CanBuild - FullName: {prefab.fullName}, ShortName: {prefabShortName}");

                // Check for barricade by multiple possible identifiers
                if (prefabShortName.Contains("barricade.cover.wood_double") ||
                    prefabShortName.Contains("barricade.wood.cover_double") ||
                    prefabShortName.Contains("barricade_cover_wood_double"))
                {
                    Puts($"[Roam Debug] Allowing barricade build: {prefabShortName}");
                    return null; // Allow barricade placement only
                }
                else
                {
                    Puts($"[Roam Debug] Blocking build: {prefabShortName}");
                    // Block ALL other structures in the roam bubble (including sleeping bags)
                    player.ChatMessage("Only barricades can be placed in the roam bubble! All other building is blocked.");
                    return false; // Prevent all other builds
                }
            }

            return null; // Allow builds outside the roam bubble
        }

        void OnEntityBuilt(Planner planner, GameObject go)
{
    BasePlayer player = planner.GetOwnerPlayer();
    if (player == null) return;

    // Check if the player is in a roam bubble
    if (Instance.playersInBubble.TryGetValue(player.UserIDString, out AutomaticRoamBubbleComp roamBubble))
    {
        BaseEntity entity = go.GetComponent<BaseEntity>();
        if (entity == null) return;

        string prefabName = entity.PrefabName;
        string prefabShortName = entity.ShortPrefabName;

        // Also check the item that was used to place this entity
        Item activeItem = player.GetActiveItem();
        string itemShortname = activeItem?.info?.shortname ?? "unknown";

        // Debug logging to see what's being placed
        Puts($"Entity built in roam bubble: {prefabName} | Short: {prefabShortName} | Item: {itemShortname} | Player: {player.displayName}");

        // Additional debug logging for barricade detection (updated for correct prefab)
        bool isBarricadeByPrefab = prefabName.Contains("barricade.cover.wood_double") || prefabName.Contains("barricade.wood.cover_double");
        bool isBarricadeByShort = prefabShortName.Contains("barricade.cover.wood_double") || prefabShortName.Contains("barricade.wood.cover_double");
        bool isBarricadeByItem = itemShortname.Contains("barricade.cover.wood_double") || itemShortname.Contains("barricade.wood.cover_double");
        Puts($"Barricade detection: PrefabMatch={isBarricadeByPrefab}, ShortMatch={isBarricadeByShort}, ItemMatch={isBarricadeByItem}");

        // Check if the placed entity is a wooden barricade cover (check multiple possible names)
        if (isBarricadeByPrefab || isBarricadeByShort || isBarricadeByItem)
        {
            try
            {
                Vector3 position = entity.transform.position;
                Quaternion rotation = entity.transform.rotation;

                // Check if barricade is rotated (not default orientation)
                if (IsBarricadeRotated(rotation))
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Barricade is rotated - keeping as normal barricade: {rotation.eulerAngles}");
                    return; // Keep as normal barricade if rotated
                }

                // Check if there are players in the spawn area
                if (IsPlayerInSpawnArea(position))
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Player detected in high wall spawn area - keeping as normal barricade");
                    return; // Keep as normal barricade if player would be inside
                }

                // Rotate the high wall 180 degrees from the barricade's rotation
                Quaternion rotatedRotation = rotation * Quaternion.Euler(0, 180, 0);

                if (config.debug)
                    Puts($"Barricade detected! Position: {position}, Original Rotation: {rotation}, Rotated: {rotatedRotation}");

                // Spawn a wooden high wall instead (matching the large wall in the screenshot)
                var wallPrefab = "assets/prefabs/building/wall.external.high.wood/wall.external.high.wood.prefab";
                BaseEntity highWall = GameManager.server.CreateEntity(wallPrefab, position, rotatedRotation, true);
                if (highWall != null)
                {
                    // Set the player as the owner of the high wall
                    highWall.OwnerID = player.userID;

                    highWall.Spawn();
                    highWall.SetParent(null); // Ensure it’s not parented to anything
                    highWall.SendNetworkUpdate(); // Update clients

                    Puts($"Successfully spawned high wall at {position} for {player.displayName} (Owner: {player.userID})");

                    entity.Kill();
                }
                else
                {
                    PrintError($"Failed to create high wall entity for {player.displayName}");
                    player.ChatMessage("❌ Failed to replace barricade with high wall.");
                }
            }
            catch (System.Exception ex)
            {
                PrintError($"Error replacing barricade with high wall: {ex.Message}");
                player.ChatMessage("❌ Error occurred while replacing barricade.");
            }
        }
        // Sleeping bags are already allowed and don’t need replacement
    }
}

        private void ReplaceBarricadeWithHighWall(BaseEntity barricade, Vector3 position, Quaternion rotation, BasePlayer player)
        {
            try
            {
                // Check if barricade is rotated
                if (IsBarricadeRotated(rotation))
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Barricade is rotated - keeping as normal barricade");
                    return; // Keep as normal barricade if rotated
                }

                // Check for player collision
                if (IsPlayerInSpawnArea(position))
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Player collision detected - keeping as normal barricade");
                    return; // Keep as normal barricade if player collision
                }

                // Rotate the high wall 180 degrees from the barricade's rotation
                Quaternion rotatedRotation = rotation * Quaternion.Euler(0, 180, 0);

                // Spawn a wooden high wall instead
                var wallPrefab = "assets/prefabs/building/wall.external.high.wood/wall.external.high.wood.prefab";
                BaseEntity highWall = GameManager.server.CreateEntity(wallPrefab, position, rotatedRotation, true);

                if (highWall != null)
                {
                    // Set the player as the owner of the high wall
                    highWall.OwnerID = player.userID;

                    highWall.Spawn();
                    highWall.SetParent(null); // Ensure it's not parented to anything
                    highWall.SendNetworkUpdate(); // Update clients

                    Puts($"Successfully replaced barricade with high wall at {position} for player {player.displayName} (Owner: {player.userID})");

                    // Destroy the placed barricade cover
                    barricade.Kill();
                }
                else
                {
                    PrintError($"Failed to create high wall entity for player {player.displayName}");
                }
            }
            catch (System.Exception ex)
            {
                PrintError($"Error replacing barricade with high wall: {ex.Message}");
            }
        }

        // Helper method to check if barricade is rotated from default orientation
        private bool IsBarricadeRotated(Quaternion rotation)
        {
            // Get the Y rotation angle (yaw)
            float yRotation = rotation.eulerAngles.y;

            // Normalize angle to 0-360 range
            while (yRotation < 0) yRotation += 360;
            while (yRotation >= 360) yRotation -= 360;

            // Check if rotation is close to default orientations (0°, 90°, 180°, 270°)
            // Allow small tolerance for floating point precision
            float tolerance = 5f;

            bool isDefault = (yRotation <= tolerance || yRotation >= (360 - tolerance)) || // 0°
                           (Math.Abs(yRotation - 90) <= tolerance) ||                      // 90°
                           (Math.Abs(yRotation - 180) <= tolerance) ||                     // 180°
                           (Math.Abs(yRotation - 270) <= tolerance);                       // 270°

            if (config.debug)
                Puts($"[Roam Debug] Barricade rotation check: {yRotation:F1}° - IsDefault: {isDefault}");

            return !isDefault; // Return true if NOT in default orientation
        }

        // Helper method to check if players are in the wall spawn area
        private bool IsPlayerInSpawnArea(Vector3 position)
        {
            // Check for players in a larger area around the wall spawn position
            float checkRadius = 3f; // 3 meter radius to be safe

            var colliders = Physics.OverlapSphere(position, checkRadius, LayerMask.GetMask("Player (Server)"));

            foreach (var collider in colliders)
            {
                var player = collider.GetComponentInParent<BasePlayer>();
                if (player != null && player.IsConnected && !player.IsDead())
                {
                    if (config.debug)
                        Puts($"[Roam Debug] Player {player.displayName} found at distance {Vector3.Distance(position, player.transform.position):F1}m from wall spawn");
                    return true;
                }
            }

            // Also check for players using a simple distance check as backup
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player != null && player.IsConnected && !player.IsDead())
                {
                    float distance = Vector3.Distance(position, player.transform.position);
                    if (distance <= checkRadius)
                    {
                        if (config.debug)
                            Puts($"[Roam Debug] Player {player.displayName} found at distance {distance:F1}m from wall spawn (backup check)");
                        return true;
                    }
                }
            }

            return false;
        }

        // Helper method to check if there are walls at the spawn position
        private bool IsWallAtPosition(Vector3 position, float radius = 2f)
        {
            var colliders = Physics.OverlapSphere(position, radius);

            foreach (var collider in colliders)
            {
                var entity = collider.GetComponentInParent<BaseEntity>();
                if (entity != null && !entity.IsDestroyed)
                {
                    // Check if it's a wall or building block
                    if (entity.PrefabName.Contains("wall") || entity is BuildingBlock)
                    {
                        if (config.debug)
                            Puts($"[Roam Debug] Wall/building detected at spawn position: {entity.ShortPrefabName}");
                        return true;
                    }
                }
            }

            return false;
        }
        #endregion

        #region Components

        public class PlayerStats
        {
            public int kills = 0;
            public int deaths = 0;
            public int headshots = 0;
            public int damage = 0;
        }
        public class TeamStats
        {
            public List<string> members = new List<string>();
            public int kills = 0;
            public int deaths = 0;
            public int damage = 0;
            public int headshots = 0;
            public int akKills = 0;
            public int m2Kills = 0;
        }

        public class TeamGroup
        {
            public string Team { get; set; }
            public int TotalKills { get; set; }
            public int TotalDeaths { get; set; }
            public int TotalDamage { get; set; }
            public int TotalHeadshots { get; set; }
            public int AKKills { get; set; }
            public int M2Kills { get; set; }
            public List<PlayerData> Players { get; set; } = new List<PlayerData>();
        }

        public class PlayerData
        {
            public string uid { get; set; }
            public int kills { get; set; }
            public int deaths { get; set; }
            public int headshots { get; set; }
            public int damage { get; set; }
            public float kdr { get; set; }
            public string name { get; set; }
        }

        public class AutomaticRoamBubbleComp : MonoBehaviour
        {
            public SphereCollider innerCollider;
            public List<SphereEntity> innerSpheres = new List<SphereEntity>();
            public MapMarkerGenericRadius roamMarker = null;
            public VendingMachineMapMarker vendingMarker = null;
            public Dictionary<string, TeamStats> teamStats = new Dictionary<string, TeamStats>();
            public Dictionary<string, PlayerStats> playerStats = new Dictionary<string, PlayerStats>();
            public Dictionary<string, WeaponCount> weaponCounts = new Dictionary<string, WeaponCount>();
            public string biome = "Grass";
            public float roamTime = 0f;
            public float roamEndTime;
            public float initialRoamTime;
            private float lastTimerUpdate = 0f;
            private Timer decayTimer;

            public class WeaponCount
    {
        public int akCount = 0;
        public int m2Count = 0;
    }

    void Awake()
    {
        gameObject.layer = (int)Layer.Reserved1;
        gameObject.name = "Roam Bubble";
        enabled = false;
    }

    void Update()
    {
        if (roamTime > 0)
        {
            roamTime -= UnityEngine.Time.deltaTime;

            // Update timer UI only every minute (60 seconds) instead of every frame
            if (UnityEngine.Time.time - lastTimerUpdate >= 60f)
            {
                lastTimerUpdate = UnityEngine.Time.time;

                foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
                {
                    BasePlayer player = BasePlayer.Find(kvp.Key);
                    if (player != null && player.IsConnected)
                    {
                        Instance.UpdateTimerUI(player, roamTime);
                    }
                }
            }
        }
        else
        {
            KeyValuePair<string, TeamStats> winningTeam = teamStats.Count > 0
                ? teamStats.Aggregate((x, y) => x.Value.kills > y.Value.kills ? x : y)
                : default(KeyValuePair<string, TeamStats>);

            bool isNull = winningTeam.Equals(default(KeyValuePair<string, TeamStats>)) || winningTeam.Value.kills == 0;

            if (isNull)
            {
                if (config.messages.sendEndResultToChat)
                    Instance.SendGlobalMessage(Instance.lang.GetMessage("RoamEndedNoWinners", Instance));

                if (config.messages.sendEndResultToDiscord)
                {
                    Instance.SendRoamResults(this); // Call SendRoamResults on the current instance (this)
                }
            }
            else
            {
                StringBuilder members = new StringBuilder();
                string topKiller = "";
                int maxKills = 0;
                foreach (string pid in winningTeam.Value.members)
                {
                    BasePlayer ply = BasePlayer.FindAwakeOrSleeping(pid);
                    if (ply == null) members.Append(pid + " - 0 kills - 0 deaths\n");
                    else
                    {
                        int playerKills = teamStats[winningTeam.Key].kills / teamStats[winningTeam.Key].members.Count;
                        int playerDeaths = teamStats[winningTeam.Key].deaths / teamStats[winningTeam.Key].members.Count;
                        members.Append($"{ply.displayName} - {playerKills} kills - {playerDeaths} deaths\n");

                        if (playerKills > maxKills)
                        {
                            maxKills = playerKills;
                            topKiller = ply.displayName;
                        }
                    }
                }

                int totalKills = 0, totalAKKills = 0, totalM2Kills = 0, totalDeaths = 0, totalDamage = 0, totalHeadshots = 0;
                int totalPlayers = 0, totalTeams = 0, totalAKs = 0, totalM2s = 0;
                foreach (var stats in teamStats.Values)
                {
                    totalKills += stats.kills;
                    totalAKKills += stats.akKills;
                    totalM2Kills += stats.m2Kills;
                    totalDeaths += stats.deaths;
                    totalDamage += stats.damage;
                    totalHeadshots += stats.headshots;
                    totalPlayers += stats.members.Count;
                }
                // Count only actual clan teams (not individual players)
                totalTeams = Instance.CountActualTeams(teamStats);

                // Calculate total AKs and M2s in the roam
                foreach (var weaponCount in weaponCounts.Values)
                {
                    totalAKs += weaponCount.akCount;
                    totalM2s += weaponCount.m2Count;
                }

                if (config.messages.sendEndResultToChat)
                    Instance.SendInGameRoamResults(this); // Pass 'this' (AutomaticRoamBubbleComp) to match the method signature

                if (config.messages.sendEndResultToDiscord)
                {
                    Instance.SendRoamResults(this); // Call SendRoamResults on the current instance (this)
                }
            }

            DeleteCircle();
            DestroyImmediate(gameObject);
        }
    }

    void OnDestroy()
    {
        DeleteCircle();
        Instance.activeRoams.Remove(this);
        CancelInvoke(nameof(UpdatePlayerUI)); // Clean up the invoke when the roam ends
        CancelInvoke(nameof(CheckForHelicoptersInBubble)); // Clean up helicopter monitoring
        CancelInvoke(nameof(ContinuousWeaponCheck)); // Clean up weapon monitoring
    }

    void OnTriggerEnter(Collider col)
    {
        BasePlayer player = col?.GetComponentInParent<BasePlayer>();
        if (player == null) return;

        // Exclude vanished admins from roam participation
        if (Instance.IsPlayerVanished(player)) return;

        // Check if player is mounted on a helicopter and prevent entry
        if (Instance.IsPlayerOnHelicopter(player))
        {
            Instance.EjectPlayerFromHelicopter(player);
            player.ChatMessage("❌ You cannot enter the roam bubble while flying a helicopter! You have been ejected.");
            return;
        }

        // Check if player is already in bubble to avoid duplicate key error
        if (!Instance.playersInBubble.ContainsKey(player.UserIDString))
        {
            Instance.playersInBubble.Add(player.UserIDString, this);
        }

        // Initialize player stats and weapon counts when entering the bubble
        if (!playerStats.ContainsKey(player.UserIDString))
        {
            playerStats[player.UserIDString] = new PlayerStats();
        }
        if (!weaponCounts.ContainsKey(player.UserIDString))
        {
            weaponCounts[player.UserIDString] = new WeaponCount();
            CountPlayerWeapons(player); // Count AKs and M2s for this player
        }

        if (config.ui.useUI)
        {
            Instance.CreateRoamUI(player);
        }

        EnforceRestrictions(player);

        // Start updating the UI every 5 seconds for this player
        InvokeRepeating(nameof(UpdatePlayerUI), 0f, 5f); // Start immediately and repeat every 5 seconds

        // Also start updating weapon counts periodically (like maze plugin)
        InvokeRepeating(nameof(UpdateWeaponCounts), 10f, 30f); // Update weapon counts every 30 seconds

        // Start continuous weapon restriction monitoring
        InvokeRepeating(nameof(ContinuousWeaponCheck), 1f, 2f); // Check every 2 seconds

        // Start accelerated decay if enabled
        StartAcceleratedDecay();

        // Start helicopter monitoring
        InvokeRepeating(nameof(CheckForHelicoptersInBubble), 5f, 5f); // Check every 5 seconds
    }

    void OnTriggerExit(Collider col)
    {
        BasePlayer player = col?.GetComponentInParent<BasePlayer>();
        if (player == null || !Instance.playersInBubble.ContainsKey(player.UserIDString)) return;

        // Always remove from bubble regardless of vanish status (in case they vanished while inside)
        Instance.playersInBubble.Remove(player.UserIDString);

        if (config.ui.useUI)
        {
            CuiHelper.DestroyUi(player, "AwakenRoamsUI");
        }

        // Stop updating the UI for this player when they leave
        CancelInvoke(nameof(UpdatePlayerUI));

        // Stop helicopter monitoring when last player leaves
        if (Instance.playersInBubble.Count == 0)
        {
            CancelInvoke(nameof(CheckForHelicoptersInBubble));
            CancelInvoke(nameof(ContinuousWeaponCheck));
        }
    }

    private void UpdatePlayerUI()
    {
        foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
        {
            BasePlayer player = BasePlayer.Find(kvp.Key);
            if (player != null && player.IsConnected && config.ui.useUI && !Instance.IsPlayerVanished(player))
            {
                Instance.CreateRoamUI(player, roamTime); // Recreate the UI with updated stats
            }
        }
    }

    private void UpdateWeaponCounts()
    {
        // Update weapon counts for all players in this roam bubble (like maze plugin)
        foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
        {
            BasePlayer player = BasePlayer.Find(kvp.Key);
            if (player != null && player.IsConnected)
            {
                CountPlayerWeapons(player);
            }
        }
    }

    private void CheckForHelicoptersInBubble()
    {
        // Check all players in this bubble for helicopter usage
        foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
        {
            BasePlayer player = BasePlayer.Find(kvp.Key);
            if (player != null && player.IsConnected && !Instance.IsPlayerVanished(player))
            {
                if (Instance.IsPlayerOnHelicopter(player))
                {
                    Instance.EjectPlayerFromHelicopter(player);
                    player.ChatMessage("❌ Helicopters are not allowed in the roam bubble! You have been ejected.");

                    if (config.debug)
                    {
                        Instance.Puts($"[Roam Debug] Ejected {player.displayName} from helicopter inside bubble");
                    }
                }
            }
        }
    }

    private void StartAcceleratedDecay()
    {
        if (!config.decaySettings.enableAcceleratedDecay)
        {
            if (config.debug)
                Instance.Puts("[Roam Decay] Accelerated decay is disabled in config");
            return;
        }

        if (config.debug)
            Instance.Puts($"[Roam Decay] Starting accelerated decay timer - Interval: {config.decaySettings.decayCheckInterval}s, Multiplier: {config.decaySettings.decaySpeedMultiplier}x");

        decayTimer = Instance.timer.Repeat(config.decaySettings.decayCheckInterval, 0, () =>
        {
            if (this == null || gameObject == null)
            {
                if (config.debug)
                    Instance.Puts("[Roam Decay] Decay timer stopped - roam component destroyed");
                return;
            }

            // Find all structures within the bubble radius
            var structures = new List<BaseEntity>();
            var colliders = Physics.OverlapSphere(transform.position, innerCollider.radius);

            if (config.debug)
                Instance.Puts($"[Roam Decay] Found {colliders.Length} colliders in bubble radius {innerCollider.radius}m at position {transform.position}");

            foreach (var collider in colliders)
            {
                var entity = collider.GetComponentInParent<BaseEntity>();
                if (entity == null) continue;

                // Check if it's a structure that should decay
                if (ShouldAccelerateDecay(entity))
                {
                    structures.Add(entity);
                }
            }

            if (config.debug)
                Instance.Puts($"[Roam Decay] Found {structures.Count} structures eligible for decay");

            // Apply accelerated decay to found structures
            foreach (var structure in structures)
            {
                ApplyAcceleratedDecay(structure);
            }
        });
    }

    public bool ShouldAccelerateDecay(BaseEntity entity)
    {
        if (entity == null || entity.IsDestroyed) return false;

        bool shouldDecay = false;
        string entityType = entity.GetType().Name;
        string prefabName = entity.ShortPrefabName;

        // Check if it's a building block or deployable
        if (entity is BuildingBlock || entity is Deployable)
        {
            // If apply to all structures is enabled
            if (config.decaySettings.applyToAllStructures)
            {
                shouldDecay = true;
                if (config.debug)
                    Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - All structures enabled");
            }

            // If apply to barricades only
            else if (config.decaySettings.applyToBarricadesOnly)
            {
                if (entity.PrefabName.Contains("barricade.cover.wood_double") || entity.PrefabName.Contains("barricade.wood.cover_double"))
                {
                    shouldDecay = true;
                    if (config.debug)
                        Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - Barricade only mode");
                }
            }
        }

        // Also check for other structure types
        else if (entity is Door || entity is StorageContainer || entity is BaseOven ||
            entity is Workbench || entity is RepairBench || entity is ResearchTable ||
            entity is AutoTurret || entity is FlameTurret || entity is GunTrap ||
            entity is Landmine || entity is BearTrap || entity is SleepingBag ||
            entity is Signage || entity is Locker || entity is VendingMachine)
        {
            if (config.decaySettings.applyToAllStructures)
            {
                shouldDecay = true;
                if (config.debug)
                    Instance.Puts($"[Roam Decay] {entityType} ({prefabName}) marked for decay - Deployable/structure");
            }
        }

        return shouldDecay;
    }

    public void ApplyAcceleratedDecay(BaseEntity entity)
    {
        if (entity == null || entity.IsDestroyed) return;

        var combatEntity = entity as BaseCombatEntity;
        if (combatEntity == null) return;

        // Calculate accelerated decay damage based on max health
        float maxHealth = combatEntity.MaxHealth();
        float currentHealth = combatEntity.Health();

        // Calculate decay damage as a percentage of max health for more effective decay
        float decayPercentage = 0.05f * config.decaySettings.decaySpeedMultiplier; // 5% base decay per interval
        float acceleratedDecayRate = maxHealth * decayPercentage;

        // Ensure minimum decay damage
        if (acceleratedDecayRate < 1.0f)
            acceleratedDecayRate = 1.0f * config.decaySettings.decaySpeedMultiplier;

        // Apply decay damage
        var hitInfo = new HitInfo();
        hitInfo.damageTypes.Set(Rust.DamageType.Decay, acceleratedDecayRate);
        hitInfo.DoHitEffects = false;
        hitInfo.HitMaterial = 0;

        combatEntity.OnAttacked(hitInfo);

        if (config.debug)
        {
            Instance.Puts($"[Roam Decay] Applied {acceleratedDecayRate:F1} decay damage to {entity.ShortPrefabName} (Health: {combatEntity.Health():F1}/{maxHealth:F1})");
        }
    }

    public void CreateBubble(Vector3 position, float initialRadius, string biome, int time)
    {
        transform.position = position;
        transform.rotation = Quaternion.identity;
        roamTime = time;
        this.biome = biome;
        roamEndTime = UnityEngine.Time.realtimeSinceStartup + time; // Store end time
        initialRoamTime = time;
        lastTimerUpdate = UnityEngine.Time.time; // Initialize timer update tracking

        // Remove resource pickups (stone, sulfur, metal, driftwood, etc.) within the bubble radius when the roam starts
        Instance.RemoveResourcePickups(position, initialRadius);

        // Remove all existing buildings within the bubble radius when the roam starts
        Instance.RemoveAllBuildings(position, initialRadius);

        for (int i = 0; i < config.numOfSpheres; i++)
        {
            var sphere = (SphereEntity)GameManager.server.CreateEntity("assets/prefabs/visualization/sphere.prefab", position, Quaternion.identity, true);
            sphere.currentRadius = initialRadius * 2;
            sphere.lerpSpeed = 0;
            sphere.enableSaving = false;
            sphere.Spawn();
            innerSpheres.Add(sphere);
        }

        var innerRB = innerSpheres[0].gameObject.AddComponent<Rigidbody>();
        innerRB.useGravity = false;
        innerRB.isKinematic = true;

        innerCollider = gameObject.AddComponent<SphereCollider>();
        innerCollider.transform.position = innerSpheres[0].transform.position;
        innerCollider.isTrigger = true;
        innerCollider.radius = initialRadius;

        vendingMarker = GameManager.server.CreateEntity("assets/prefabs/deployable/vendingmachine/vending_mapmarker.prefab", position).GetComponent<VendingMachineMapMarker>();
        vendingMarker.enableSaving = false;
        vendingMarker.markerShopName = "Awaken Roams";
        vendingMarker.Spawn();
        vendingMarker.SendNetworkUpdate();

        roamMarker = GameManager.server.CreateEntity("assets/prefabs/tools/map/genericradiusmarker.prefab", position) as MapMarkerGenericRadius;
        if (roamMarker != null)
        {
            roamMarker.alpha = 0.6f;
            roamMarker.color1 = Color.black;
            roamMarker.color2 = Color.black;
            roamMarker.radius = initialRadius * 0.0062f;
            roamMarker.Spawn();
            roamMarker.SendUpdate();
        }

        gameObject.SetActive(true);
        enabled = true;
        Instance.activeRoams.Add(this);

        // Start accelerated decay if enabled
        if (config.decaySettings.enableAcceleratedDecay)
        {
            StartAcceleratedDecay();
        }
    }

    public void DeleteCircle()
    {
        // Stop decay timer
        decayTimer?.Destroy();
        decayTimer = null;

        // Remove sphere entities with batched deletion to prevent FPS drops
        if (innerSpheres.Count > 0)
        {
            Instance.StartBatchedSphereRemoval(innerSpheres.ToList());
        }
        innerSpheres.Clear();

        // Remove roam marker
        if (roamMarker != null && roamMarker.IsValid())
        {
            roamMarker.Kill();
        }

        // Remove vending-style marker
        if (vendingMarker != null && vendingMarker.IsValid())
        {
            vendingMarker.Kill();
        }

        // Clean up players and UI
        var players = Pool.GetList<KeyValuePair<string, AutomaticRoamBubbleComp>>();
        players.AddRange(Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList());

        foreach (var kvp in players)
        {
            Instance.playersInBubble.Remove(kvp.Key);

            BasePlayer player = BasePlayer.Find(kvp.Key);
            if (player != null && player.IsConnected)
            {
                CuiHelper.DestroyUi(player, "AwakenRoamsUI");
            }
        }

        Pool.FreeList(ref players);
        Instance.activeRoams.Remove(this);
    }

    private void EnforceRestrictions(BasePlayer player)
    {
        List<Item> items = new List<Item>();
        if (player.inventory.containerMain != null)
            items.AddRange(player.inventory.containerMain.itemList);
        if (player.inventory.containerBelt != null)
            items.AddRange(player.inventory.containerBelt.itemList);
        if (player.inventory.containerWear != null)
            items.AddRange(player.inventory.containerWear.itemList);

        // Also force unequip any currently held restricted weapon
        Item activeItem = player.GetActiveItem();
        if (activeItem?.info?.category == ItemCategory.Weapon && !Instance.IsWeaponAllowedByShortname(activeItem.info.shortname))
        {
            player.svActiveItemID = new ItemId(0);
            player.SendNetworkUpdate();
        }

        foreach (var item in items.ToList()) // Use ToList to avoid modification during iteration
        {
            bool shouldRemove = false;
            string restrictionMessage = "";

            // Check weapons
            if (item.info.category == ItemCategory.Weapon && !Instance.IsWeaponAllowedByShortname(item.info.shortname))
            {
                shouldRemove = true;
                restrictionMessage = string.Format(Instance.lang.GetMessage("WeaponRestricted", Instance), item.info.shortname);
            }
            // Check attachments
            else if (item.info.category == ItemCategory.Component && !Instance.IsAttachmentAllowed(item.info.shortname))
            {
                shouldRemove = true;
                restrictionMessage = string.Format(Instance.lang.GetMessage("AttachmentRestricted", Instance), item.info.shortname);
            }
            // Check ammunition
            else if (item.info.category == ItemCategory.Ammunition && !Instance.IsAmmunitionAllowed(item.info.shortname))
            {
                shouldRemove = true;
                restrictionMessage = string.Format(Instance.lang.GetMessage("AmmunitionRestricted", Instance), item.info.shortname);
            }
            // Check explosives (tools, weapons, or misc items that are explosive)
            else if (!Instance.IsExplosiveAllowed(item.info.shortname))
            {
                shouldRemove = true;
                restrictionMessage = string.Format(Instance.lang.GetMessage("ExplosiveRestricted", Instance), item.info.shortname);
            }

            if (shouldRemove)
            {
                item.Remove();
                player.ChatMessage(restrictionMessage);
            }
        }
    }

    private void ContinuousWeaponCheck()
    {
        // Continuously check all players in this roam bubble for restricted weapons
        foreach (var kvp in Instance.playersInBubble.Where(kvp => kvp.Value == this).ToList())
        {
            BasePlayer player = BasePlayer.Find(kvp.Key);
            if (player != null && player.IsConnected && !Instance.IsPlayerVanished(player))
            {
                // Check currently held weapon
                Item activeItem = player.GetActiveItem();
                if (activeItem?.info?.category == ItemCategory.Weapon)
                {
                    if (!Instance.IsWeaponAllowedByShortname(activeItem.info.shortname))
                    {
                        // Force unequip immediately
                        player.svActiveItemID = new ItemId(0);
                        player.SendNetworkUpdate();
                        player.ChatMessage($"❌ {activeItem.info.displayName.english} is not allowed in roam events!");
                    }
                    else if (!Instance.AreAttachmentsAllowed(activeItem.GetHeldEntity()))
                    {
                        // Force unequip due to restricted attachments
                        player.svActiveItemID = new ItemId(0);
                        player.SendNetworkUpdate();
                        player.ChatMessage("❌ Your weapon has restricted attachments!");
                    }
                }
            }
        }
    }

    private void CountPlayerWeapons(BasePlayer player)
    {
        if (player == null || string.IsNullOrEmpty(player.UserIDString))
        {
            return;
        }

        // Ensure weaponCounts exists for the player; initialize if not present
        if (!weaponCounts.ContainsKey(player.UserIDString))
        {
            weaponCounts[player.UserIDString] = new WeaponCount();
        }

        WeaponCount count = weaponCounts[player.UserIDString];

        // Reset counts before recounting (like maze plugin approach)
        count.akCount = 0;
        count.m2Count = 0;

        List<Item> items = new List<Item>();

        // Safely check and add items from each inventory container
        try
        {
            if (player.inventory?.containerMain != null && player.inventory.containerMain.itemList != null)
                items.AddRange(player.inventory.containerMain.itemList.Where(i => i != null));

            if (player.inventory?.containerBelt != null && player.inventory.containerBelt.itemList != null)
                items.AddRange(player.inventory.containerBelt.itemList.Where(i => i != null));

            if (player.inventory?.containerWear != null && player.inventory.containerWear.itemList != null)
                items.AddRange(player.inventory.containerWear.itemList.Where(i => i != null));
        }
        catch (Exception ex)
        {
            return;
        }

        // Count AK-47s and M2s with enhanced tracking (like maze plugin)
        foreach (var item in items)
        {
            if (item == null || item.info == null || string.IsNullOrEmpty(item.info.shortname))
                continue;

            string shortname = item.info.shortname.ToLower();

            // Enhanced weapon detection similar to maze plugin
            if (shortname == "ak47u.entity" || shortname.Contains("ak47u.entity"))
            {
                count.akCount += item.amount;

                // Track individual item UIDs for better tracking (like maze plugin)
                if (item.uid.IsValid)
                {
                    // Store tracked weapon UIDs for potential future use
                    Instance.trackedWeaponItemUids.Add(item.uid);
                }
            }
            else if (shortname == "lmg.m249" || shortname.Contains("lmg.m249") || shortname == "rifle.m2" || shortname.Contains("rifle.m2"))
            {
                count.m2Count += item.amount;

                // Track individual item UIDs for better tracking (like maze plugin)
                if (item.uid.IsValid)
                {
                    // Store tracked weapon UIDs for potential future use
                    Instance.trackedWeaponItemUids.Add(item.uid);
                }
            }
        }
    }
}
        #endregion

        #region Helper Functions
        public string GetTeamTag(BasePlayer player)
        {
            // Extract clan tag directly from display name
            string displayName = player.displayName;

            // Look for clan tag pattern like [123] at the start of display name
            if (displayName.StartsWith("[") && displayName.Contains("]"))
            {
                int endBracket = displayName.IndexOf(']');
                if (endBracket > 1) // Must have at least one character between brackets
                {
                    string extractedTag = displayName.Substring(1, endBracket - 1); // Extract content between first [ and ]

                    // Clean up any duplicate tags (like [123] [123] -> just 123)
                    if (extractedTag.Contains("] ["))
                    {
                        extractedTag = extractedTag.Split(new string[] { "] [" }, StringSplitOptions.None)[0];
                    }

                    return extractedTag; // Return just the clan tag (e.g., "123")
                }
            }

            // Fallback: Try AwakenClans API if display name parsing fails
            if (AwakenClans != null)
            {
                var clan = AwakenClans.Call("GetClan", player.userID);
                if (clan != null)
                {
                    var clanName = AwakenClans.Call("GetClanName", clan);
                    string name = clanName as string;
                    if (!string.IsNullOrEmpty(name))
                    {
                        return name;
                    }
                }

                var clanTag = AwakenClans.Call("GetClanTag", player.userID);
                string tag = clanTag as string;
                if (!string.IsNullOrEmpty(tag))
                {
                    return tag;
                }
            }

            // Fall back to clean player name if not in a clan
            return GetCleanPlayerName(player);
        }

        private string GetCleanPlayerName(BasePlayer player)
        {
            // Get the original player name without any formatting
            // Use _displayName (internal field) or strip formatting from displayName
            string name = player._displayName ?? player.displayName;

            // Remove any clan tag formatting that might be in the display name
            if (name.Contains("[") && name.Contains("]"))
            {
                // Remove clan tag pattern like "[123] PlayerName" -> "PlayerName"
                int endBracket = name.IndexOf(']');
                if (endBracket >= 0 && endBracket < name.Length - 1)
                {
                    name = name.Substring(endBracket + 1).Trim();
                }
            }

            return name;
        }

        private string GetTeamDisplayName(string teamKey, List<string> members)
        {
            // If the teamKey looks like a clan identifier, use it
            if (IsActualClanTag(teamKey))
            {
                return teamKey; // Return clan tag or clan name
            }

            // If teamKey is a player name, try to get their clan identifier
            if (members.Count > 0 && AwakenClans != null)
            {
                BasePlayer firstPlayer = BasePlayer.FindAwakeOrSleeping(members[0]);
                if (firstPlayer != null)
                {
                    // Try to get clan tag first
                    var clanTag = AwakenClans.Call("GetClanTag", firstPlayer.userID) as string;
                    if (!string.IsNullOrEmpty(clanTag))
                    {
                        return clanTag;
                    }

                    // Try to get clan name as fallback
                    var clan = AwakenClans.Call("GetClan", firstPlayer.userID);
                    if (clan != null)
                    {
                        var clanName = AwakenClans.Call("GetClanName", clan) as string;
                        if (!string.IsNullOrEmpty(clanName))
                        {
                            return clanName;
                        }
                    }
                }
            }

            // Fall back to the original team key
            return teamKey;
        }

        private List<string> GetTeamMembers(BasePlayer player)
        {
            if (AwakenClans == null) return new List<string> { player.UserIDString };

            // Get the clan object using the GetClan API
            var clan = AwakenClans.Call("GetClan", player.userID);
            if (clan != null)
            {
                // Access the ClanMemebers property directly from the clan object
                var clanMembersProperty = clan.GetType().GetProperty("ClanMemebers");
                if (clanMembersProperty != null)
                {
                    var clanMembers = clanMembersProperty.GetValue(clan) as Dictionary<string, string>;
                    if (clanMembers != null && clanMembers.Count > 0)
                    {
                        return clanMembers.Keys.ToList();
                    }
                }
            }
            return new List<string> { player.UserIDString }; // Default to solo player if not in a clan
        }

        private void SendMessage(BasePlayer player, string key, params object[] args)
        {
            player.SendConsoleCommand("chat.add2", new object[] { 2, config.messages.avatarSteamID, string.Format(this.lang.GetMessage(key, this), args), config.messages.chatName, config.messages.chatNameColor, 1f });
        }

        private void SendGlobalMessage(string message)
        {
            ConsoleNetwork.BroadcastToAllClients("chat.add2", new object[] { 2, config.messages.avatarSteamID, message, config.messages.chatName, config.messages.chatNameColor, 1f });
        }

        private void SendBigGlobalMessage(string message)
        {
            // Send a larger, more prominent message with bigger chat name
            ConsoleNetwork.BroadcastToAllClients("chat.add2", new object[] { 0, config.messages.avatarSteamID, $"<size=20><color=#CCCCCC><b>{message}</b></color></size>", $"<size=18><b>{config.messages.chatName}</b></size>", config.messages.chatNameColor, 1f });
        }

        private void SendInGameRoamResults(AutomaticRoamBubbleComp roam)
        {
            // If no teams or no kills, send no winners message globally
            if (roam.teamStats.Count == 0 || roam.teamStats.Values.All(stats => stats.kills == 0))
            {
                SendGlobalMessage(Instance.lang.GetMessage("RoamEndedNoWinners", Instance));
                return;
            }

            // Calculate totals across all teams
            int totalKills = 0, totalAKs = 0, totalM2s = 0, totalDamage = 0;

            // Aggregate stats for all teams
            foreach (var teamEntry in roam.teamStats)
            {
                var stats = teamEntry.Value;
                totalKills += stats.kills;
                totalDamage += stats.damage;

                // Calculate total AK and M2 counts from weaponCounts
                if (roam.weaponCounts != null && roam.weaponCounts.ContainsKey(teamEntry.Key))
                {
                    totalAKs += roam.weaponCounts[teamEntry.Key].akCount;
                    totalM2s += roam.weaponCounts[teamEntry.Key].m2Count;
                }
            }

            // Get winning team (team with most kills)
            var winningTeam = roam.teamStats.OrderByDescending(t => t.Value.kills).FirstOrDefault();

            if (winningTeam.Key == null || winningTeam.Value.kills == 0)
            {
                SendGlobalMessage(Instance.lang.GetMessage("RoamEndedNoWinners", Instance));
                return;
            }

            // Create the exact maze-style winner message format
            StringBuilder message = new StringBuilder();

            // Get proper team display name (clan tag/name, not player name)
            string teamDisplayName = GetTeamDisplayName(winningTeam.Key, winningTeam.Value.members);

            // Header line with exact format: "ROAM EVENT WINNERS - [clan tag]"
            message.Append($"<color=#CCCCCC><size=16><b>ROAM EVENT WINNERS - {teamDisplayName}</b></size></color>");

            // Stats line with exact format: "AK: X M249: X Kills: X Dmg: X,XXX"
            message.Append($"\n<color=#7000fd><b>AK: {totalAKs} M249: {totalM2s} Kills: {totalKills} Dmg: {totalDamage:N0}</b></color>");

            // Get top 3 players from winning team sorted by kills
            var topPlayers = winningTeam.Value.members
                .Select(pid =>
                {
                    BasePlayer ply = BasePlayer.FindAwakeOrSleeping(pid);
                    int kills = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].kills : 0;
                    return new { DisplayName = ply?.displayName ?? "Unknown", Kills = kills };
                })
                .OrderByDescending(p => p.Kills)
                .Take(3)
                .ToList();

            // Player lines with exact format: "[clan] playername - X kills" (smaller text for player names)
            foreach (var player in topPlayers)
            {
                if (player.Kills > 0) // Only show players with kills
                {
                    message.Append($"\n<color=#CCCCCC><size=12><b>[{teamDisplayName}] {player.DisplayName} - {player.Kills} kills</b></size></color>");
                }
            }

            SendBigGlobalMessage(message.ToString());

            // Award clan core points via API
            Instance.AwardClanCorePoints(winningTeam.Key, "roam");

            // Add roam win to AwakenStats for the winning clan
            Instance.AddRoamWinToStats(winningTeam.Key);
        }

        private class DiscordField
        {
            public string Name { get; set; }
            public string Value { get; set; }
            public bool Inline { get; set; }

            public DiscordField(string name, string value, bool inline = false)
            {
                Name = name;
                Value = value;
                Inline = inline;
            }
        }

        private void SendDiscordMessage(string webhook, string embedName, int embedColor, Dictionary<string, string> values, string content = null)
        {
            try
            {
                var embed = new
                {
                    title = embedName,
                    color = embedColor,
                    fields = values.Select(kv => new { name = kv.Key, value = kv.Value, inline = false }).ToArray()
                };

                var payload = new
                {
                    content = content,
                    embeds = new[] { embed }
                };

                string jsonPayload = JsonConvert.SerializeObject(payload);
                webrequest.Enqueue(webhook, jsonPayload, (code, response) =>
                {
                    if (code != 200)
                    {
                        Debug.LogError($"Failed to send message to Discord. Status code: {code}");
                    }
                }, this, RequestMethod.POST, new Dictionary<string, string> { { "Content-Type", "application/json" } });
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error sending message to Discord: {ex.Message}");
            }
        }

        private void RemoveResourcePickups(Vector3 position, float radius)
        {
            Puts($"Starting optimized resource cleanup at position {position} with radius {radius}");

            // Start the optimized batched removal process
            StartBatchedEntityRemoval(position, radius);
        }

        private void RemoveAllBuildings(Vector3 position, float radius)
        {
            Puts($"Starting building removal at position {position} with radius {radius}");

            // Start the building removal process
            StartBatchedBuildingRemoval(position, radius);
        }

        private void StartBatchedEntityRemoval(Vector3 position, float radius)
        {
            try
            {
                // Use a layer mask to include natural resources and world objects, but avoid unnecessary layers
                var entities = Physics.OverlapSphere(position, radius, LayerMask.GetMask("Default", "Terrain", "World", "Resource"));

                if (entities.Length == 0)
                {
                    Puts("No entities found in cleanup area");
                    return;
                }

                // Filter entities to only those we want to remove
                var entitiesToRemove = new List<BaseEntity>();
                foreach (var entity in entities)
                {
                    BaseEntity baseEntity = entity.GetComponent<BaseEntity>();
                    if (baseEntity == null || baseEntity.IsDestroyed) continue;

                    string prefabName = baseEntity.PrefabName.ToLower();

                    // Targeted list of lag-causing resource pickups, driftwood, cacti, plants, and berries (excluding trees)
                    if (prefabName.Contains("pickup.stone") ||
                        prefabName.Contains("pickup.metal.ore") ||
                        prefabName.Contains("pickup.sulfur.ore") ||
                        prefabName.Contains("pickup.wood") || // Driftwood and other wood pickups
                        prefabName.Contains("collectable") || // Generic collectables (e.g., mushrooms, plants)
                        prefabName.Contains("ore") || // Additional ore types
                        prefabName.Contains("resource") || // General resource entities
                        prefabName.Contains("driftwood") || // Specifically target driftwood
                        prefabName.Contains("cactus") || // Target cacti (e.g., small or large cacti)
                        prefabName.Contains("plant.") || // Target plants (e.g., hemp, corn, pumpkin)
                        prefabName.Contains("berry") || // Target berry bushes
                        prefabName.Contains("bush.")) // Target bushes
                    {
                        entitiesToRemove.Add(baseEntity);
                    }
                }

                if (entitiesToRemove.Count == 0)
                {
                    Puts("No lag-causing entities found to remove");
                    return;
                }

                Puts($"Found {entitiesToRemove.Count} entities to remove - starting batched removal");

                // Start the batched removal process
                ProcessEntityRemovalBatch(entitiesToRemove, 0);
            }
            catch (Exception ex)
            {
                Puts($"Error in StartBatchedEntityRemoval: {ex.Message}");
            }
        }

        private void StartBatchedBuildingRemoval(Vector3 position, float radius)
        {
            try
            {
                // Use a broader layer mask to catch all building entities
                var entities = Physics.OverlapSphere(position, radius, LayerMask.GetMask("Default", "Construction", "Deployed"));

                if (entities.Length == 0)
                {
                    Puts("No entities found in building cleanup area");
                    return;
                }

                // Filter entities to only buildings and deployables
                var buildingsToRemove = new List<BaseEntity>();
                foreach (var entity in entities)
                {
                    BaseEntity baseEntity = entity.GetComponent<BaseEntity>();
                    if (baseEntity == null || baseEntity.IsDestroyed) continue;

                    // Check if it's a building block, deployable, or structure
                    if (baseEntity is BuildingBlock ||
                        baseEntity is Deployable ||
                        baseEntity is Door ||
                        baseEntity is StorageContainer ||
                        baseEntity is BaseOven ||
                        baseEntity is Workbench ||
                        baseEntity is RepairBench ||
                        baseEntity is ResearchTable ||
                        baseEntity is AutoTurret ||
                        baseEntity is FlameTurret ||
                        baseEntity is GunTrap ||
                        baseEntity is Landmine ||
                        baseEntity is BearTrap ||
                        baseEntity is SleepingBag ||
                        baseEntity is Signage ||
                        baseEntity is Locker ||
                        baseEntity is VendingMachine)
                    {
                        buildingsToRemove.Add(baseEntity);
                    }
                }

                if (buildingsToRemove.Count == 0)
                {
                    Puts("No buildings found to remove");
                    return;
                }

                Puts($"Found {buildingsToRemove.Count} buildings to remove - starting batched removal");

                // Start the batched building removal process
                ProcessBuildingRemovalBatch(buildingsToRemove, 0);
            }
            catch (Exception ex)
            {
                Puts($"Error in StartBatchedBuildingRemoval: {ex.Message}");
            }
        }

        private void ProcessEntityRemovalBatch(List<BaseEntity> entitiesToRemove, int currentIndex)
        {
            try
            {
                int batchSize = 5; // Remove only 5 entities per frame to prevent FPS drops
                int processed = 0;
                int removed = 0;

                // Process a small batch of entities
                for (int i = currentIndex; i < entitiesToRemove.Count && processed < batchSize; i++)
                {
                    var entity = entitiesToRemove[i];
                    if (entity != null && !entity.IsDestroyed)
                    {
                        try
                        {
                            string prefabName = entity.PrefabName;
                            entity.Kill();
                            removed++;
                            // Puts($"Removed lag-causing entity: {prefabName}"); // Disabled to reduce console spam
                        }
                        catch (Exception ex)
                        {
                            Puts($"Failed to remove entity {entity.PrefabName}: {ex.Message}");
                        }
                    }
                    processed++;
                }

                int nextIndex = currentIndex + processed;

                // If there are more entities to process, schedule the next batch
                if (nextIndex < entitiesToRemove.Count)
                {
                    // Schedule next batch in 0.1 seconds to spread the load
                    timer.Once(0.1f, () => ProcessEntityRemovalBatch(entitiesToRemove, nextIndex));

                    if (removed > 0)
                    {
                        Puts($"Batch complete: removed {removed} entities. Progress: {nextIndex}/{entitiesToRemove.Count}");
                    }
                }
                else
                {
                    // All entities processed
                    Puts($"Entity cleanup complete! Total entities removed: {removed}");
                }
            }
            catch (Exception ex)
            {
                Puts($"Error in ProcessEntityRemovalBatch: {ex.Message}");
            }
        }

        private void ProcessBuildingRemovalBatch(List<BaseEntity> buildingsToRemove, int currentIndex)
        {
            try
            {
                int batchSize = 3; // Remove only 3 buildings per frame to prevent FPS drops (buildings are larger)
                int processed = 0;
                int removed = 0;

                // Process a small batch of buildings
                for (int i = currentIndex; i < buildingsToRemove.Count && processed < batchSize; i++)
                {
                    var building = buildingsToRemove[i];
                    if (building != null && !building.IsDestroyed)
                    {
                        try
                        {
                            string prefabName = building.PrefabName;
                            building.Kill();
                            removed++;
                            if (config.debug)
                            {
                                Puts($"Removed building: {prefabName}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Puts($"Failed to remove building {building.PrefabName}: {ex.Message}");
                        }
                    }
                    processed++;
                }

                int nextIndex = currentIndex + processed;

                // If there are more buildings to process, schedule the next batch
                if (nextIndex < buildingsToRemove.Count)
                {
                    // Schedule next batch in 0.15 seconds to spread the load (slower for buildings)
                    timer.Once(0.15f, () => ProcessBuildingRemovalBatch(buildingsToRemove, nextIndex));

                    if (removed > 0)
                    {
                        Puts($"Building removal batch complete: removed {removed} buildings. Progress: {nextIndex}/{buildingsToRemove.Count}");
                    }
                }
                else
                {
                    // All buildings processed
                    Puts($"Building cleanup complete! Total buildings removed: {removed}");
                }
            }
            catch (Exception ex)
            {
                Puts($"Error in ProcessBuildingRemovalBatch: {ex.Message}");
            }
        }

        private void StartBatchedSphereRemoval(List<SphereEntity> spheresToRemove)
        {
            if (spheresToRemove.Count == 0) return;

            Puts($"Starting batched removal of {spheresToRemove.Count} sphere entities");
            ProcessSphereRemovalBatch(spheresToRemove, 0);
        }

        private void ProcessSphereRemovalBatch(List<SphereEntity> spheresToRemove, int currentIndex)
        {
            try
            {
                int batchSize = 3; // Remove only 3 spheres per frame (spheres are larger objects)
                int processed = 0;
                int removed = 0;

                // Process a small batch of spheres
                for (int i = currentIndex; i < spheresToRemove.Count && processed < batchSize; i++)
                {
                    var sphere = spheresToRemove[i];
                    if (sphere != null && sphere.IsValid())
                    {
                        try
                        {
                            sphere.Kill();
                            removed++;
                        }
                        catch (Exception ex)
                        {
                            Puts($"Failed to remove sphere entity: {ex.Message}");
                        }
                    }
                    processed++;
                }

                int nextIndex = currentIndex + processed;

                // If there are more spheres to process, schedule the next batch
                if (nextIndex < spheresToRemove.Count)
                {
                    // Schedule next batch in 0.05 seconds (faster for spheres since there are fewer)
                    timer.Once(0.05f, () => ProcessSphereRemovalBatch(spheresToRemove, nextIndex));

                    if (removed > 0)
                    {
                        Puts($"Sphere batch complete: removed {removed} spheres. Progress: {nextIndex}/{spheresToRemove.Count}");
                    }
                }
                else
                {
                    // All spheres processed
                    Puts($"Sphere cleanup complete! Total spheres removed: {removed}");
                }
            }
            catch (Exception ex)
            {
                Puts($"Error in ProcessSphereRemovalBatch: {ex.Message}");
            }
        }

        private void SendRoamResults(AutomaticRoamBubbleComp roam)
        {
            if (string.IsNullOrEmpty(config.webhook))
            {
                PrintError("Discord webhook URL is not configured.");
                return;
            }

            // Calculate team statistics similar to maze plugin
            var teamGroups = roam.teamStats
                .Select(kvp => new TeamGroup {
                    Team = kvp.Key,
                    TotalKills = kvp.Value.kills,
                    TotalDeaths = kvp.Value.deaths,
                    TotalDamage = kvp.Value.damage,
                    TotalHeadshots = kvp.Value.headshots,
                    AKKills = kvp.Value.akKills,
                    M2Kills = kvp.Value.m2Kills,
                    Players = kvp.Value.members.Select(pid => {
                        BasePlayer ply = BasePlayer.FindAwakeOrSleeping(pid);
                        int kills = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].kills : 0;
                        int deaths = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].deaths : 0;
                        int headshots = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].headshots : 0;
                        int damage = roam.playerStats.ContainsKey(pid) ? roam.playerStats[pid].damage : 0;
                        float kdr = deaths > 0 ? (float)kills / deaths : kills;
                        return new PlayerData { uid = pid, kills = kills, deaths = deaths, headshots = headshots, damage = damage, kdr = kdr, name = ply != null ? GetCleanPlayerName(ply) : "Unknown" };
                    }).OrderByDescending(p => p.kills).ToList()
                })
                .OrderByDescending(g => g.TotalKills)
                .ToList();

            // Get proper team display name for Discord embed
            var firstTeam = teamGroups.FirstOrDefault();
            string winningTeam = firstTeam != null ? GetTeamDisplayName(firstTeam.Team, firstTeam.Players.Select(p => p.uid).ToList()) : "No Team";

            // Build Winners section (top team only)
            var winGroup = teamGroups.FirstOrDefault();
            string winnersText;
            if (winGroup != null && winGroup.TotalKills > 0)
            {
                var allMembers = winGroup.Players
                    .Select(p => $"- [{p.name}](http://steamcommunity.com/profiles/{p.uid}) – {p.kills} Kills / {p.deaths} Deaths / {p.headshots} HS / {p.kdr:0.00} KDR");
                winnersText = $"- **[{winGroup.Team}]** (Total: {winGroup.TotalKills} Kills / {winGroup.TotalDeaths} Deaths / {winGroup.TotalHeadshots} HS)\n"
                            + string.Join("\n", allMembers);
            }
            else winnersText = "No winners data.";

            // Build Runner-ups section (other teams)
            var runnerUps = teamGroups
                .Where(g => g.Team != winningTeam)
                .Take(2)
                .Select((g, i) => $"- **[{g.Team}]** – {g.TotalKills} Kills / {g.TotalDeaths} Deaths");
            string runnerUpsText = runnerUps.Any()
                ? string.Join("\n", runnerUps)
                : "No runner-ups.";

            // Find top fragger across all teams
            var topFragger = teamGroups
                .SelectMany(g => g.Players)
                .OrderByDescending(p => p.kills)
                .ThenByDescending(p => p.kdr)
                .FirstOrDefault();
            string topFraggerText = topFragger != null
                ? $"- [{topFragger.name}](http://steamcommunity.com/profiles/{topFragger.uid}) – ``{topFragger.kills} Kills`` / ``{topFragger.deaths} Deaths``"
                : "No data.";

            // Calculate totals
            int totalKills = teamGroups.Sum(g => g.TotalKills);
            int totalDeaths = teamGroups.Sum(g => g.TotalDeaths);
            int totalDamage = teamGroups.Sum(g => g.TotalDamage);
            int totalHeadshots = teamGroups.Sum(g => g.TotalHeadshots);
            int totalPlayers = teamGroups.Sum(g => g.Players.Count);
            // Count only actual clan teams (not individual players)
            int totalTeams = Instance.CountActualTeamsFromGroups(teamGroups);
            int totalAKs = roam.weaponCounts.Values.Sum(w => w.akCount);
            int totalM2s = roam.weaponCounts.Values.Sum(w => w.m2Count);

            // Calculate duration
            TimeSpan duration = TimeSpan.FromSeconds(roam.initialRoamTime - roam.roamTime);

            // Build fields array dynamically (like maze plugin)
            var fieldsList = new List<object>();

            // Add statistics fields similar to maze plugin
            fieldsList.Add(new { name = "Details:", value = $"Total Kills: ``{totalKills:N0}``\nDuration: ``{duration.Minutes} min``", inline = true });
            fieldsList.Add(new { name = "‎", value = $"Total Players: ``{totalPlayers}``\nTotal Teams: ``{totalTeams}``", inline = true });
            fieldsList.Add(new { name = "Loot Check:", value = $"AKs: ``{totalAKs}``\nM2s: ``{totalM2s}``", inline = true });
            fieldsList.Add(new { name = "‎", value = $"{topFraggerText}", inline = false });
            fieldsList.Add(new { name = "Winners:", value = $"**[{winningTeam}]**", inline = true });
            fieldsList.Add(new { name = "Kills:", value = $"``{winGroup?.TotalKills ?? 0}``", inline = true });
            fieldsList.Add(new { name = "Damage / Headshots:", value = $"``{winGroup?.TotalDamage ?? 0}`` / ``{winGroup?.TotalHeadshots ?? 0}``", inline = true });

            // Add Members field with properly formatted member list
            if (winGroup != null && winGroup.Players.Any())
            {
                var membersList = winGroup.Players
                    .Select(p => $"- [{p.name}](http://steamcommunity.com/profiles/{p.uid}) – ``{p.kills} Kills`` / ``{p.deaths} Deaths``");
                string membersText = string.Join("\n", membersList);
                fieldsList.Add(new { name = "Members:", value = membersText, inline = false });
            }

            // Create Discord embed (using maze plugin style)
            var payload = new
            {
                content = "",
                embeds = new[]
                {
                    new
                    {
                        title = "Awaken Roams",
                        color = 16711680,
                        fields = fieldsList.ToArray(),
                        thumbnail = new {
                            url = config?.ui?.logoImageURL ?? "https://cdn.awakenrust.com/oasis_roams.png"
                        },
                        footer = new {
                            text = "Awaken Rust Servers",
                            icon_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1384783101514748007/MAIN.png"
                        }
                    }
                },
                username = "Awaken Events",
                avatar_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1384783101514748007/MAIN.png",
                attachments = new object[0]
            };

            string json = JsonConvert.SerializeObject(payload, Formatting.None);
            webrequest.EnqueuePost(config.webhook, json, (code, res) =>
            {
                if (code is not 200 and not 204)
                    PrintError($"Discord webhook error: {code} – {res}");
            }, this, RequestHeaders);
        }
        private static Dictionary<string, string> RequestHeaders => new Dictionary<string, string>
        {
            ["Content-Type"] = "application/json"
        };

        private bool IsWeaponAK(BaseEntity weapon)
        {
            if (weapon == null) return false;
            return weapon.ShortPrefabName.Contains("ak47u.entity"); // Adjust based on Rust's prefab names for AK-47
        }

        private bool IsWeaponM2(BaseEntity weapon)
        {
            if (weapon == null) return false;
            return weapon.ShortPrefabName.Contains("lmg.m249"); // Adjust based on Rust's prefab names for M2
        }

        private bool IsWeaponAllowed(BaseEntity weapon)
        {
            if (weapon == null) return true; // Allow null weapons (not held)
            string prefabName = weapon.ShortPrefabName;
            return IsWeaponAllowedByShortname(prefabName);
        }

        private bool IsWeaponAllowedByShortname(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return true;

            // If using allowed weapons list, check if weapon is in the allowed list
            if (config.weaponRestrictions.useAllowedWeaponsList)
            {
                return config.weaponRestrictions.allowedWeapons.ContainsKey(shortname);
            }

            // Otherwise use the blocked weapons list approach
            if (config.weaponRestrictions.blockedWeapons.ContainsKey(shortname)) return false;
            return config.weaponRestrictions.allowOtherWeapons;
        }

        private bool IsAttachmentAllowed(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return false;
            if (config.weaponRestrictions.blockedAttachments.ContainsKey(shortname)) return false;
            return config.weaponRestrictions.allowOtherAttachments;
        }

        private bool IsAmmunitionAllowed(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return true;
            if (config.weaponRestrictions.blockedAmmunition.ContainsKey(shortname)) return false;
            return true;
        }

        private bool IsExplosiveAllowed(string shortname)
        {
            if (string.IsNullOrEmpty(shortname)) return true;
            if (config.weaponRestrictions.blockedExplosives.ContainsKey(shortname)) return false;
            return true;
        }

        private bool AreAttachmentsAllowed(BaseEntity weapon)
        {
            if (weapon == null) return true;
            BaseProjectile projectile = weapon as BaseProjectile;
            if (projectile == null) return true;

            // Get the Item from the weapon if possible (requires player context)
            var ownerPlayer = projectile.GetOwnerPlayer();
            if (ownerPlayer == null) return true; // No player context, assume allowed

            Item activeItem = ownerPlayer.GetActiveItem();
            if (activeItem == null || activeItem.GetHeldEntity() != weapon) return true; // Not the active weapon

            // Check the item's contents for attachments
            if (activeItem.contents != null && activeItem.contents.itemList != null)
            {
                foreach (Item mod in activeItem.contents.itemList)
                {
                    if (!IsAttachmentAllowed(mod.info.shortname)) return false;
                }
            }
            return true;
        }

        private Vector3 GetRandomSpawnInBiome(string biome)
        {
            switch (biome.ToLower())
            {
                case "snow":
                    if (config.biomeSettings.snowPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.snowPositions[UnityEngine.Random.Range(0, config.biomeSettings.snowPositions.Count)];
                case "desert":
                    if (config.biomeSettings.desertPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.desertPositions[UnityEngine.Random.Range(0, config.biomeSettings.desertPositions.Count)];
                case "grass":
                    if (config.biomeSettings.grassPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.grassPositions[UnityEngine.Random.Range(0, config.biomeSettings.grassPositions.Count)];
                default:
                    if (config.biomeSettings.grassPositions.Count == 0) return Vector3.zero;
                    return config.biomeSettings.grassPositions[UnityEngine.Random.Range(0, config.biomeSettings.grassPositions.Count)];
            }
        }

        private Vector3 GetRandomPlayerSpawnInBiome(string biome)
        {
            string biomeLower = biome.ToLower();
            if (playerSpawnData.BiomePlayerSpawns.ContainsKey(biomeLower) && playerSpawnData.BiomePlayerSpawns[biomeLower].Count > 0)
            {
                return playerSpawnData.BiomePlayerSpawns[biomeLower][UnityEngine.Random.Range(0, playerSpawnData.BiomePlayerSpawns[biomeLower].Count)];
            }
            return GetRandomSpawnInBiome(biome); // Fallback to config spawn if no player spawn is set
        }
        #endregion

        #region Commands
        [Command("forceroam")]
        private void RoamCMD(IPlayer iPlayer, string command, string[] args)
        {
            // Add null safety check with stack trace for debugging
            if (iPlayer == null)
            {
                Puts("Error: RoamCMD called with null player - use StartRoamEvent hook instead for programmatic calls");
                Puts($"Stack trace: {System.Environment.StackTrace}");
                return;
            }

            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (player == null)
            {
                Puts("Error: Player object is null");
                return;
            }

            if (!permission.UserHasPermission(player.UserIDString, CallRoamPermission))
            {
                SendMessage(player, "NoPermission");
                return;
            }

            // Convert config.roamTime from "30m" format to seconds
            if (string.IsNullOrEmpty(config?.roamTime))
            {
                SendMessage(player, "Error: Roam time not configured properly");
                Puts("Error: config.roamTime is null or empty");
                return;
            }

            string roamTimeStr = config.roamTime.ToLower();
            int timeInSeconds;
            try
            {
                if (roamTimeStr.EndsWith("m"))
                {
                    int minutes = int.Parse(roamTimeStr.TrimEnd('m'));
                    timeInSeconds = minutes * 60; // Convert minutes to seconds
                }
                else
                {
                    timeInSeconds = int.Parse(roamTimeStr); // Fallback if it's just a number
                }
            }
            catch (Exception ex)
            {
                SendMessage(player, "Error: Invalid roam time format in config");
                Puts($"Error parsing roam time '{roamTimeStr}': {ex.Message}");
                return;
            }

            // Determine the biome (default to config if not provided)
            if (config?.biomeSettings == null || string.IsNullOrEmpty(config.biomeSettings.defaultBiome))
            {
                SendMessage(player, "Error: Biome settings not configured properly");
                Puts("Error: config.biomeSettings or defaultBiome is null");
                return;
            }

            string biome = config.biomeSettings.defaultBiome.ToLower();
            if (args.Length > 0)
            {
                biome = args[0].ToLower();
                if (!config.biomeSettings.allowedBiomes.Contains(biome, StringComparer.OrdinalIgnoreCase))
                {
                    SendMessage(player, $"Invalid biome. Allowed biomes are: {string.Join(", ", config.biomeSettings.allowedBiomes)}");
                    return;
                }
            }

            Vector3 position = GetRandomSpawnInBiome(biome);
            if (position == Vector3.zero)
            {
                SendMessage(player, "No spawn locations set for this biome. Use /setlocation to set locations.");
                return;
            }

            var bubbleObject = new GameObject();
            if (bubbleObject == null)
            {
                SendMessage(player, "Error: Failed to create roam bubble object");
                Puts("Error: Failed to create new GameObject");
                return;
            }

            var bubbleComp = bubbleObject.AddComponent<AutomaticRoamBubbleComp>();
            if (bubbleComp == null)
            {
                SendMessage(player, "Error: Failed to create roam bubble component");
                Puts("Error: Failed to add AutomaticRoamBubbleComp");
                return;
            }

            bubbleComp.CreateBubble(position, config.sphereRadius, biome, timeInSeconds);

            if (config.messages?.sendChatMessageOnRoam == true)
                SendBigGlobalMessage(string.Format(this.lang.GetMessage("RoamStartedChat", this), biome, timeInSeconds));
        }

        [Command("roams")]
        private void RoamsCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            // Check if player is in bubble dictionary
            if (playersInBubble.ContainsKey(player.UserIDString))
            {
                // Double-check if player is actually within a roam bubble
                AutomaticRoamBubbleComp roamBubble = playersInBubble[player.UserIDString];
                if (roamBubble != null && roamBubble.innerCollider != null)
                {
                    // Verify player is actually within the bubble's bounds
                    float distance = Vector3.Distance(player.transform.position, roamBubble.transform.position);
                    if (distance <= config.sphereRadius)
                    {
                        SendMessage(player, "AlreadyInRoam");
                        return;
                    }
                    else
                    {
                        // Player is not actually in the bubble, remove them from the dictionary
                        playersInBubble.Remove(player.UserIDString);
                        CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                        if (config.debug)
                            Puts($"[Roam Debug] Cleaned up stale entry for {player.displayName} - not actually in bubble");
                    }
                }
                else
                {
                    // Roam bubble is null or invalid, remove player from dictionary
                    playersInBubble.Remove(player.UserIDString);
                    CuiHelper.DestroyUi(player, "AwakenRoamsUI");
                    if (config.debug)
                        Puts($"[Roam Debug] Cleaned up invalid roam bubble entry for {player.displayName}");
                }
            }

            // Check if there are any active roams BEFORE proceeding with teleport logic
            if (activeRoams == null || activeRoams.Count == 0)
            {
                SendMessage(player, "There isn't a roam active.");
                return;
            }

            // Filter out any null entries in activeRoams
            var validRoams = activeRoams.Where(r => r != null).ToList();
            if (validRoams.Count == 0)
            {
                SendMessage(player, "There isn't a roam active.");
                return;
            }

            AutomaticRoamBubbleComp targetBubble = validRoams.FirstOrDefault();
            if (targetBubble == null || targetBubble.innerCollider == null)
            {
                SendMessage(player, "There isn't a valid roam active.");
                return;
            }

            string biome = targetBubble.biome.ToLower();
            Vector3 bubblePosition = targetBubble.transform.position;
            float bubbleRadius = targetBubble.innerCollider.radius;
            Vector3 spawnPosition;

            bool spawnNearTeam = args.Length > 0 && args[0].ToLower() == "team";

            if (spawnNearTeam)
            {
                // Try to find a teammate within the bubble
                BasePlayer teammate = FindTeammateInBubble(player, targetBubble);
                if (teammate != null)
                {
                    // Spawn near teammate
                    spawnPosition = FindValidSpawnPosition(teammate.transform.position, 5f, bubblePosition, biome);
                    if (spawnPosition != Vector3.zero)
                    {
                        player.Teleport(spawnPosition);
                        SendMessage(player, $"Teleported near your teammate in the {biome} roam bubble!");
                        return;
                    }
                }
                // If no teammate found or no valid position near teammate, fall back to random spawn
                SendMessage(player, "No teammates found in the roam bubble, using random spawn instead.");
            }

            // Default random spawn behavior
            spawnPosition = GetRandomPlayerSpawnInBiome(biome);
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(spawnPosition);
            spawnPosition.y = terrainHeight;

            if (Vector3.Distance(spawnPosition, bubblePosition) <= bubbleRadius && TerrainMeta.WaterMap.GetHeight(spawnPosition) < terrainHeight)
            {
                player.Teleport(spawnPosition);
                SendMessage(player, $"Teleported to a random spot in the {biome} roam bubble!");
            }
            else
            {
                spawnPosition = FindValidSpawnPosition(bubblePosition, bubbleRadius, Vector3.zero, biome);
                if (spawnPosition != Vector3.zero)
                {
                    player.Teleport(spawnPosition);
                    SendMessage(player, $"Teleported to a random spot in the {biome} roam bubble (adjusted position)!");
                }
                else
                {
                    SendMessage(player, "Failed to find a valid spawn location within the roam bubble.");
                }
            }
        }

        [Command("setspawns")]
        private void SetSpawnsCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, SetLocationPermission))
            {
                SendMessage(player, "NoPermission");
                return;
            }

            if (args.Length != 1 || !config.biomeSettings.allowedBiomes.Contains(args[0], StringComparer.OrdinalIgnoreCase))
            {
                SendMessage(player, "InvalidBiome", string.Join(", ", config.biomeSettings.allowedBiomes));
                return;
            }

            string biome = args[0].ToLower();
            Vector3 position = player.transform.position;
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight;

            if (!playerSpawnData.BiomePlayerSpawns.ContainsKey(biome))
            {
                playerSpawnData.BiomePlayerSpawns[biome] = new List<Vector3>();
            }

            if (!playerSpawnData.BiomePlayerSpawns[biome].Contains(position))
            {
                playerSpawnData.BiomePlayerSpawns[biome].Add(position);
                SavePlayerSpawnData();
                SendMessage(player, "SpawnSet", biome, position.ToString());
            }
            else
            {
                SendMessage(player, "This position is already set as a player spawn for the {0} biome.", biome);
            }
        }

        [Command("setlocation")]
        private void SetLocationCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, SetLocationPermission))
            {
                SendMessage(player, "NoPermission");
                return;
            }

            if (args.Length != 1)
            {
                SendMessage(player, "InvalidBiome", string.Join(", ", config.biomeSettings.allowedBiomes));
                return;
            }

            string biome = args[0].ToLower();
            if (!config.biomeSettings.allowedBiomes.Contains(biome, StringComparer.OrdinalIgnoreCase))
            {
                SendMessage(player, "InvalidBiome", string.Join(", ", config.biomeSettings.allowedBiomes));
                return;
            }

            Vector3 position = player.transform.position;
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight; // Ensure the position is on land

            // Add or update the spawn location for the biome
            switch (biome)
            {
                case "snow":
                    if (config.biomeSettings.snowPositions.Count == 0 || !config.biomeSettings.snowPositions.Contains(position))
                        config.biomeSettings.snowPositions.Add(position);
                    else
                        config.biomeSettings.snowPositions[config.biomeSettings.snowPositions.IndexOf(position)] = position;
                    break;
                case "desert":
                    if (config.biomeSettings.desertPositions.Count == 0 || !config.biomeSettings.desertPositions.Contains(position))
                        config.biomeSettings.desertPositions.Add(position);
                    else
                        config.biomeSettings.desertPositions[config.biomeSettings.desertPositions.IndexOf(position)] = position;
                    break;
                case "grass":
                    if (config.biomeSettings.grassPositions.Count == 0 || !config.biomeSettings.grassPositions.Contains(position))
                        config.biomeSettings.grassPositions.Add(position);
                    else
                        config.biomeSettings.grassPositions[config.biomeSettings.grassPositions.IndexOf(position)] = position;
                    break;
            }

            SaveConfig();
            SendMessage(player, "LocationSet", biome, position.ToString());
        }

        [Command("reloadimages")]
        private void ReloadImagesCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            LoadImages();
            player.Reply("Reloading images from config...");
        }

        [Command("checkimage")]
        private void CheckImageCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Logo System Status ===");
            player.Reply($"Config logo URL: {config?.ui?.logoImageURL ?? "null"}");
            player.Reply($"Logo validated: {!string.IsNullOrEmpty(cachedLogoUrl)}");
            player.Reply($"Currently validating: {isDownloadingLogo}");

            if (!string.IsNullOrEmpty(cachedLogoUrl))
            {
                player.Reply($"Validated logo URL: {cachedLogoUrl}");
                player.Reply("✅ Logo URL is working and validated");
            }

            if (!string.IsNullOrEmpty(config?.ui?.logoImageURL))
            {
                string logoUrl = config.ui.logoImageURL;

                // Check if the URL contains HTML color tags
                if (logoUrl.Contains("<color") || logoUrl.Contains("</color>"))
                {
                    player.Reply("❌ ERROR: Logo URL contains HTML color tags instead of a valid image URL!");
                    player.Reply("This is causing the UI display issues you're seeing.");
                    player.Reply("Please update your config file to use a proper image URL like:");
                    player.Reply("\"logoImageURL\": \"https://cdn.awakenrust.com/awaken_roams.png\"");
                    player.Reply("Or set it to an empty string \"\" to disable the logo.");
                }
                else if (logoUrl.StartsWith("http://") || logoUrl.StartsWith("https://"))
                {
                    player.Reply("✅ Logo URL is configured correctly");
                    player.Reply("Using URL validation system to prevent glitching");
                }
                else
                {
                    player.Reply("⚠️ Logo URL doesn't start with http:// or https:// - may not load properly");
                }
            }
            else
            {
                player.Reply("❌ No logo URL configured in config");
            }
        }

        [Command("refreshlogo")]
        private void RefreshLogoCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Refreshing Logo Validation ===");

            // Clear current validation
            cachedLogoUrl = null;
            isDownloadingLogo = false;

            player.Reply("✅ Cleared existing logo validation");

            // Re-validate logo URL
            CacheLogoLocally();

            if (isDownloadingLogo)
            {
                player.Reply("🔄 Started validating logo from configured URL");
                player.Reply("Logo will be validated and ready for use");
                player.Reply("This ensures the logo loads properly without glitching");
            }
            else
            {
                player.Reply("❌ Failed to start logo validation - check URL configuration");
            }
        }

        [Command("cleanbubbles")]
        private void CleanBubblesCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            int bubbleCount = activeRoams.Count;
            int playerCount = playersInBubble.Count;

            // Clean up all active roam bubbles
            CleanupAllPlayerUIs();
            CleanupAllRoamBubbles();

            // Clear tracking data
            playersInBubble.Clear();

            // Also clean up any leftover bubbles
            CleanupLeftoverBubbles();

            player.Reply($"✅ Cleaned up {bubbleCount} active roam bubbles and removed UI from {playerCount} players.");
            Puts($"Admin {player.Name} manually cleaned up all roam bubbles.");
        }

        [Command("debugbarricade")]
        private void DebugBarricadeCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Get the item the player is holding
            Item activeItem = basePlayer.GetActiveItem();
            if (activeItem == null)
            {
                player.Reply("❌ You must be holding an item to debug.");
                return;
            }

            player.Reply($"=== Item Debug Info ===");
            player.Reply($"Item Name: {activeItem.info.displayName.english}");
            player.Reply($"Item Shortname: {activeItem.info.shortname}");

            // Check if it's a deployable
            var deployable = activeItem.info.GetComponent<ItemModDeployable>();
            if (deployable != null)
            {
                player.Reply($"Deployable Prefab: {deployable.entityPrefab.resourcePath}");

                // Check if it matches our barricade patterns
                string prefabPath = deployable.entityPrefab.resourcePath;
                bool matchesPattern = prefabPath.Contains("barricade.cover.wood_double") ||
                                    prefabPath.Contains("barricade_cover_wood_double");
                player.Reply($"Matches Barricade Pattern: {matchesPattern}");
            }
            else
            {
                player.Reply("❌ Item is not deployable.");
            }
        }

        [Command("debugweapon")]
        private void DebugWeaponCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Get the item the player is holding
            Item activeItem = basePlayer.GetActiveItem();
            if (activeItem == null)
            {
                player.Reply("❌ You must be holding an item to debug.");
                return;
            }

            player.Reply($"=== Weapon Debug Info ===");
            player.Reply($"Item Name: {activeItem.info.displayName.english}");
            player.Reply($"Item Shortname: {activeItem.info.shortname}");
            player.Reply($"Item Category: {activeItem.info.category}");

            // Check if it's a weapon
            if (activeItem.info.category == ItemCategory.Weapon)
            {
                var heldEntity = activeItem.GetHeldEntity();
                if (heldEntity != null)
                {
                    player.Reply($"Held Entity Prefab: {heldEntity.PrefabName}");
                    player.Reply($"Held Entity Short Prefab: {heldEntity.ShortPrefabName}");
                }

                // Check weapon restrictions
                bool allowedByShortname = IsWeaponAllowedByShortname(activeItem.info.shortname);
                bool allowedByEntity = IsWeaponAllowed(heldEntity);

                player.Reply($"Allowed by Shortname: {allowedByShortname}");
                player.Reply($"Allowed by Entity: {allowedByEntity}");

                if (config.weaponRestrictions.useAllowedWeaponsList)
                {
                    player.Reply($"Using Allowed Weapons List: {config.weaponRestrictions.useAllowedWeaponsList}");
                    player.Reply($"Is in Allowed List: {config.weaponRestrictions.allowedWeapons.ContainsKey(activeItem.info.shortname)}");
                    player.Reply($"Total Allowed Weapons: {config.weaponRestrictions.allowedWeapons.Count}");

                    // Show first few allowed weapons for reference
                    var allowedList = config.weaponRestrictions.allowedWeapons.Take(5).Select(kvp => kvp.Key);
                    player.Reply($"Sample Allowed: {string.Join(", ", allowedList)}");
                }
                else
                {
                    player.Reply($"Using Blocked Weapons List: {!config.weaponRestrictions.useAllowedWeaponsList}");
                    player.Reply($"Is Blocked: {config.weaponRestrictions.blockedWeapons.ContainsKey(activeItem.info.shortname)}");
                    player.Reply($"Allow Other Weapons: {config.weaponRestrictions.allowOtherWeapons}");
                }

                // Check attachments on the weapon
                bool attachmentsAllowed = Instance.AreAttachmentsAllowed(heldEntity);
                player.Reply($"Attachments Allowed: {attachmentsAllowed}");
                player.Reply($"Allow Other Attachments Config: {config.weaponRestrictions.allowOtherAttachments}");

                // List attachments if any
                if (activeItem.contents != null && activeItem.contents.itemList != null && activeItem.contents.itemList.Count > 0)
                {
                    player.Reply("=== Weapon Attachments ===");
                    foreach (Item mod in activeItem.contents.itemList)
                    {
                        bool modAllowed = Instance.IsAttachmentAllowed(mod.info.shortname);
                        bool isBlocked = config.weaponRestrictions.blockedAttachments.ContainsKey(mod.info.shortname);
                        player.Reply($"• {mod.info.displayName.english} ({mod.info.shortname}) - Allowed: {modAllowed}, Blocked: {isBlocked}");
                    }
                }
                else
                {
                    player.Reply("No attachments on this weapon.");
                }

                // Check if it's an AK or M249
                bool isAK = activeItem.info.shortname.Contains("ak47u.entity");
                bool isM249 = activeItem.info.shortname.Contains("lmg.m249");
                player.Reply($"Is AK: {isAK}");
                player.Reply($"Is M249: {isM249}");
            }
            else
            {
                player.Reply("❌ Item is not a weapon.");
            }
        }

        [Command("listallowedweapons")]
        private void ListAllowedWeaponsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Roam Bubble Weapon Configuration ===");

            if (config.weaponRestrictions.useAllowedWeaponsList)
            {
                player.Reply("✅ Using ALLOWED weapons list (whitelist mode)");
                player.Reply($"Total allowed weapons: {config.weaponRestrictions.allowedWeapons.Count}");
                player.Reply("--- Allowed Weapons ---");

                foreach (var weapon in config.weaponRestrictions.allowedWeapons)
                {
                    player.Reply($"• {weapon.Key} - {weapon.Value}");
                }
            }
            else
            {
                player.Reply("❌ Using BLOCKED weapons list (blacklist mode)");
                player.Reply($"Allow other weapons: {config.weaponRestrictions.allowOtherWeapons}");
                player.Reply($"Total blocked weapons: {config.weaponRestrictions.blockedWeapons.Count}");
                player.Reply("--- Blocked Weapons ---");

                foreach (var weapon in config.weaponRestrictions.blockedWeapons)
                {
                    player.Reply($"• {weapon.Key} - {weapon.Value}");
                }
            }

            player.Reply("=== Attachment Configuration ===");
            player.Reply($"Allow Other Attachments: {config.weaponRestrictions.allowOtherAttachments}");
            player.Reply($"Total blocked attachments: {config.weaponRestrictions.blockedAttachments.Count}");

            if (config.weaponRestrictions.blockedAttachments.Count > 0)
            {
                player.Reply("--- Blocked Attachments ---");
                foreach (var attachment in config.weaponRestrictions.blockedAttachments)
                {
                    player.Reply($"• {attachment.Key} - {attachment.Value}");
                }
            }

            player.Reply("=== Configuration Help ===");
            player.Reply("To switch to allowed weapons list mode:");
            player.Reply("Set 'Use Allowed Weapons List': true in config");
            player.Reply("Then only weapons in the 'Allowed Weapons' list will be permitted");
        }

        [Command("testdecay")]
        private void TestDecayCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You must be inside a roam bubble to test decay");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];

            player.Reply("=== Decay System Test ===");
            player.Reply($"Decay Enabled: {config.decaySettings.enableAcceleratedDecay}");
            player.Reply($"Decay Multiplier: {config.decaySettings.decaySpeedMultiplier}x");
            player.Reply($"Check Interval: {config.decaySettings.decayCheckInterval}s");
            player.Reply($"Apply to All Structures: {config.decaySettings.applyToAllStructures}");
            player.Reply($"Apply to Barricades Only: {config.decaySettings.applyToBarricadesOnly}");
            player.Reply($"Debug Mode: {config.debug}");

            // Manually trigger decay check
            if (roamBubble != null)
            {
                player.Reply("--- Manual Decay Check ---");

                var structures = new List<BaseEntity>();
                var colliders = Physics.OverlapSphere(roamBubble.transform.position, roamBubble.innerCollider.radius);

                player.Reply($"Found {colliders.Length} colliders in bubble");

                foreach (var collider in colliders)
                {
                    var entity = collider.GetComponentInParent<BaseEntity>();
                    if (entity == null) continue;

                    if (roamBubble.ShouldAccelerateDecay(entity))
                    {
                        structures.Add(entity);
                        player.Reply($"• {entity.GetType().Name} ({entity.ShortPrefabName}) - Health: {(entity as BaseCombatEntity)?.Health():F1}/{(entity as BaseCombatEntity)?.MaxHealth():F1}");
                    }
                }

                player.Reply($"Found {structures.Count} structures eligible for decay");

                if (structures.Count > 0)
                {
                    player.Reply("Applying decay to structures...");
                    foreach (var structure in structures)
                    {
                        roamBubble.ApplyAcceleratedDecay(structure);
                    }
                    player.Reply("✅ Decay applied!");
                }
                else
                {
                    player.Reply("❌ No structures found to decay");
                }
            }
        }

        [Command("testbarricade")]
        private void TestBarricadeCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You must be inside a roam bubble to test barricade replacement");
                return;
            }

            player.Reply("=== Barricade Replacement Test ===");
            player.Reply("Place a wooden barricade and it should automatically convert to a high wall with you as the owner.");
            player.Reply("The high wall will have your UserID set as the OwnerID.");
            player.Reply($"Your UserID: {basePlayer.userID}");

            // Give the player a barricade to test with
            var barricadeItem = ItemManager.CreateByName("barricade.wood.cover", 1);
            if (barricadeItem != null)
            {
                basePlayer.GiveItem(barricadeItem);
                player.Reply("✅ Given you a wooden barricade to test with!");
                player.Reply("Place it and watch it convert to a high wall owned by you.");
            }
            else
            {
                player.Reply("❌ Failed to create barricade item");
            }
        }

        [Command("listattachments")]
        private void ListAttachmentsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Available Weapon Attachments ===");
            player.Reply($"Allow Other Attachments: {config.weaponRestrictions.allowOtherAttachments}");
            player.Reply($"Blocked Attachments: {config.weaponRestrictions.blockedAttachments.Count}");

            if (config.weaponRestrictions.blockedAttachments.Count > 0)
            {
                player.Reply("--- Blocked Attachments ---");
                foreach (var attachment in config.weaponRestrictions.blockedAttachments)
                {
                    player.Reply($"❌ {attachment.Key} - {attachment.Value}");
                }
            }

            player.Reply("--- Common Allowed Attachments ---");
            var allowedAttachments = new Dictionary<string, string>
            {
                { "weapon.mod.holosight", "Holographic Sight" },
                { "weapon.mod.simple", "Simple Sight" },
                { "weapon.mod.8x.scope", "8x Zoom Scope" },
                { "weapon.mod.16x.scope", "16x Zoom Scope" },
                { "weapon.mod.muzzlebrake", "Muzzle Brake" },
                { "weapon.mod.muzzleboost", "Muzzle Boost" },
                { "weapon.mod.flashlight", "Flashlight" },
                { "weapon.mod.lasersight", "Laser Sight" },
                { "weapon.mod.burstmodule", "Burst Fire Module" },
                { "weapon.mod.small.scope", "Small Scope" }
            };

            foreach (var attachment in allowedAttachments)
            {
                bool isBlocked = config.weaponRestrictions.blockedAttachments.ContainsKey(attachment.Key);
                string status = isBlocked ? "❌ BLOCKED" : "✅ ALLOWED";
                player.Reply($"{status} {attachment.Key} - {attachment.Value}");
            }

            player.Reply("=== Configuration Info ===");
            if (config.weaponRestrictions.allowOtherAttachments)
            {
                player.Reply("✅ All attachments are allowed except those in the blocked list");
            }
            else
            {
                player.Reply("❌ Only attachments NOT in the blocked list are allowed");
            }
        }

        [Command("teststats")]
        private void TestStatsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You must be inside a roam bubble to test stats");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];

            player.Reply("=== Kill/Death Tracking Test ===");
            player.Reply($"Debug Mode: {config.debug}");
            player.Reply($"Your UserID: {basePlayer.userID}");

            // Show current player stats
            if (roamBubble.playerStats.ContainsKey(basePlayer.UserIDString))
            {
                var stats = roamBubble.playerStats[basePlayer.UserIDString];
                player.Reply($"Your Stats - Kills: {stats.kills}, Deaths: {stats.deaths}, Headshots: {stats.headshots}, Damage: {stats.damage}");
            }
            else
            {
                player.Reply("❌ No player stats found - this might be the issue!");
                // Initialize stats for testing
                roamBubble.playerStats[basePlayer.UserIDString] = new PlayerStats();
                player.Reply("✅ Initialized your player stats for testing");
            }

            // Show team stats
            string teamTag = GetTeamTag(basePlayer);
            player.Reply($"Your Team: {teamTag}");

            if (roamBubble.teamStats.ContainsKey(teamTag))
            {
                var teamStats = roamBubble.teamStats[teamTag];
                player.Reply($"Team Stats - Kills: {teamStats.kills}, Deaths: {teamStats.deaths}, Members: {teamStats.members.Count}");
            }
            else
            {
                player.Reply("❌ No team stats found");
            }

            // Show all players in bubble
            player.Reply("--- Players in Bubble ---");
            foreach (var kvp in playersInBubble.Where(kvp => kvp.Value == roamBubble))
            {
                var bubblePlayer = BasePlayer.Find(kvp.Key);
                if (bubblePlayer != null)
                {
                    var hasStats = roamBubble.playerStats.ContainsKey(kvp.Key);
                    player.Reply($"• {bubblePlayer.displayName} ({kvp.Key}) - Has Stats: {hasStats}");
                }
            }

            player.Reply("=== Testing Instructions ===");
            player.Reply("1. Kill another player in the roam bubble");
            player.Reply("2. Check console for debug messages");
            player.Reply("3. Run /teststats again to see updated stats");
            player.Reply("4. Check UI to see if kills/deaths display correctly");
        }

        [Command("testm249")]
        private void TestM249Command(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            player.Reply("=== M249 Weapon Test ===");

            // Test M249 weapon allowance
            string m249Shortname = "lmg.m249";
            bool isAllowed = IsWeaponAllowedByShortname(m249Shortname);

            player.Reply($"M249 Shortname: {m249Shortname}");
            player.Reply($"M249 Allowed: {isAllowed}");

            // Check configuration
            player.Reply($"Use Allowed Weapons List: {config.weaponRestrictions.useAllowedWeaponsList}");
            player.Reply($"Allow All Other Weapons: {config.weaponRestrictions.allowOtherWeapons}");

            // Check if M249 is in allowed list
            bool inAllowedList = config.weaponRestrictions.allowedWeapons.ContainsKey(m249Shortname);
            player.Reply($"M249 in Allowed List: {inAllowedList}");

            // Check if M249 is in blocked list
            bool inBlockedList = config.weaponRestrictions.blockedWeapons.ContainsKey(m249Shortname);
            player.Reply($"M249 in Blocked List: {inBlockedList}");

            // Give M249 for testing
            var m249Item = ItemManager.CreateByName(m249Shortname, 1);
            if (m249Item != null)
            {
                basePlayer.GiveItem(m249Item);
                player.Reply("✅ Given you an M249 to test with!");

                // Also give ammo
                var ammoItem = ItemManager.CreateByName("ammo.rifle", 100);
                if (ammoItem != null)
                {
                    basePlayer.GiveItem(ammoItem);
                    player.Reply("✅ Given you 100 rifle ammo!");
                }
            }
            else
            {
                player.Reply("❌ Failed to create M249 item");
            }

            player.Reply("=== Expected Behavior ===");
            if (config.weaponRestrictions.useAllowedWeaponsList)
            {
                player.Reply("• Using allowed weapons list mode");
                player.Reply("• M249 should work if it's in the allowed list");
                player.Reply("• M249 should be blocked if NOT in the allowed list");
            }
            else
            {
                player.Reply("• Using blocked weapons list mode");
                player.Reply("• M249 should work if it's NOT in the blocked list");
                player.Reply("• M249 should be blocked if it's in the blocked list");
            }
        }

        [Command("endroam")]
        private void EndRoamCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            // Check if player is in a roam bubble
            if (!playersInBubble.ContainsKey(basePlayer.UserIDString))
            {
                player.Reply("❌ You are not currently in a roam bubble");
                player.Reply("Move into the roam bubble you want to end and try again");
                return;
            }

            var roamBubble = playersInBubble[basePlayer.UserIDString];
            if (roamBubble == null)
            {
                player.Reply("❌ Error: Could not find roam bubble component");
                return;
            }

            // Get roam bubble info before ending
            Vector3 roamPosition = roamBubble.transform.position;
            float roamRadius = roamBubble.innerCollider.radius;
            int playersInRoam = playersInBubble.Count(kvp => kvp.Value == roamBubble);

            player.Reply("=== Ending Roam Bubble ===");
            player.Reply($"Position: {roamPosition}");
            player.Reply($"Radius: {roamRadius}m");
            player.Reply($"Players in roam: {playersInRoam}");

            // Confirm the action
            if (args.Length > 0 && args[0].ToLower() == "confirm")
            {
                // End the roam bubble
                try
                {
                    // Notify all players in the bubble
                    var playersToNotify = playersInBubble.Where(kvp => kvp.Value == roamBubble).ToList();
                    foreach (var kvp in playersToNotify)
                    {
                        BasePlayer bubblePlayer = BasePlayer.Find(kvp.Key);
                        if (bubblePlayer != null && bubblePlayer.IsConnected)
                        {
                            bubblePlayer.ChatMessage($"<color=#ff6500>🔴 ROAM ENDED</color> - The roam event has been ended by an admin");
                        }
                    }

                    // Destroy the roam bubble
                    roamBubble.DeleteCircle();
                    UnityEngine.Object.DestroyImmediate(roamBubble);

                    player.Reply("✅ Roam bubble ended successfully!");
                    player.Reply($"Notified {playersToNotify.Count} players about the roam ending");

                    if (config.debug)
                        Puts($"[Roam Debug] Admin {player.Name} ended roam at {roamPosition}");
                }
                catch (System.Exception ex)
                {
                    player.Reply($"❌ Error ending roam: {ex.Message}");
                    PrintError($"Error ending roam: {ex.Message}");
                }
            }
            else
            {
                player.Reply("⚠️ This will end the roam event and remove all players from it");
                player.Reply($"Type '/endroam confirm' to confirm ending this roam");
                player.Reply("This action cannot be undone!");
            }
        }

        [Command("listroams")]
        private void ListRoamsCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Active Roam Bubbles ===");

            // Get all unique roam bubbles
            var uniqueRoams = playersInBubble.Values.Distinct().ToList();

            if (uniqueRoams.Count == 0)
            {
                player.Reply("❌ No active roam bubbles found");
                return;
            }

            player.Reply($"Found {uniqueRoams.Count} active roam bubble(s):");

            for (int i = 0; i < uniqueRoams.Count; i++)
            {
                var roam = uniqueRoams[i];
                if (roam == null || roam.gameObject == null) continue;

                Vector3 position = roam.transform.position;
                float radius = roam.innerCollider.radius;
                int playerCount = playersInBubble.Count(kvp => kvp.Value == roam);

                player.Reply($"--- Roam {i + 1} ---");
                player.Reply($"Position: {position.x:F1}, {position.y:F1}, {position.z:F1}");
                player.Reply($"Radius: {radius}m");
                player.Reply($"Players: {playerCount}");

                // List players in this roam
                var playersInThisRoam = playersInBubble.Where(kvp => kvp.Value == roam).ToList();
                if (playersInThisRoam.Count > 0)
                {
                    player.Reply("Players in roam:");
                    foreach (var kvp in playersInThisRoam.Take(5)) // Show max 5 players
                    {
                        BasePlayer roamPlayer = BasePlayer.Find(kvp.Key);
                        if (roamPlayer != null)
                        {
                            player.Reply($"  • {roamPlayer.displayName}");
                        }
                    }
                    if (playersInThisRoam.Count > 5)
                    {
                        player.Reply($"  ... and {playersInThisRoam.Count - 5} more");
                    }
                }
            }

            player.Reply("=== Commands ===");
            player.Reply("• /endroam - End the roam you're standing in");
            player.Reply("• /endroam confirm - Confirm ending the roam");
            player.Reply("• /listroams - Show this list again");
        }

        [Command("testcollision")]
        private void TestCollisionCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            BasePlayer basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            Vector3 playerPos = basePlayer.transform.position;

            player.Reply("=== Collision Detection Test ===");
            player.Reply($"Your Position: {playerPos}");

            // Test rotation detection
            Quaternion testRotation = basePlayer.transform.rotation;
            bool isRotated = IsBarricadeRotated(testRotation);
            player.Reply($"Current Rotation: {testRotation.eulerAngles.y:F1}° - Is Rotated: {isRotated}");

            // Test player collision detection
            bool playerCollision = IsPlayerInSpawnArea(playerPos);
            player.Reply($"Player Collision at your position: {playerCollision}");

            // Test wall detection
            bool wallDetected = IsWallAtPosition(playerPos, 2f);
            player.Reply($"Wall detected at your position: {wallDetected}");

            // Test different positions around the player
            player.Reply("--- Testing positions around you ---");
            for (int i = 0; i < 4; i++)
            {
                float angle = i * 90f;
                Vector3 testPos = playerPos + new Vector3(Mathf.Cos(angle * Mathf.Deg2Rad) * 3f, 0, Mathf.Sin(angle * Mathf.Deg2Rad) * 3f);

                bool playerCol = IsPlayerInSpawnArea(testPos);
                bool wallCol = IsWallAtPosition(testPos, 2f);

                player.Reply($"Position {angle}°: Player collision: {playerCol}, Wall collision: {wallCol}");
            }

            player.Reply("=== Rotation Test Values ===");
            player.Reply("Default orientations (should NOT be rotated):");
            player.Reply("• 0° (North) - Should be false");
            player.Reply("• 90° (East) - Should be false");
            player.Reply("• 180° (South) - Should be false");
            player.Reply("• 270° (West) - Should be false");
            player.Reply("Any other angle should be true (rotated)");

            player.Reply("=== How It Works ===");
            player.Reply("1. Barricades placed at default angles (0°, 90°, 180°, 270°) convert to walls");
            player.Reply("2. Barricades placed at other angles stay as barricades");
            player.Reply("3. If a player is within 3m of wall spawn, stays as barricade");
            player.Reply("4. Spawn points avoid walls and players within 2m radius");
        }
        #endregion

        #region UI
        private string HexToCuiColor(string hex, int alpha = 100)
        {
            if (hex.StartsWith("#"))
                hex = hex.Substring(1);

            if (hex.Length != 6)
                return "1 1 1 1"; // Default to white if invalid

            try
            {
                int r = Convert.ToInt32(hex.Substring(0, 2), 16);
                int g = Convert.ToInt32(hex.Substring(2, 2), 16);
                int b = Convert.ToInt32(hex.Substring(4, 2), 16);
                float a = alpha / 100f;

                return $"{r / 255f} {g / 255f} {b / 255f} {a}";
            }
            catch
            {
                return "1 1 1 1"; // Default to white if conversion fails
            }
        }



        private void CreateRoamUI(BasePlayer player, float time = -1)
        {
            if (player == null || !player.IsConnected)
            {
                return;
            }

            // Destroy existing UI to prevent caching issues
            CuiHelper.DestroyUi(player, "AwakenRoamsUI");

            AutomaticRoamBubbleComp roamBubble;
            if (!playersInBubble.TryGetValue(player.UserIDString, out roamBubble))
            {
                return;
            }

            var container = new CuiElementContainer();

            // Main UI container
            container.Add(new CuiElement
            {
                Name = "AwakenRoamsUI",
                Parent = "Hud",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1"
                    }
                }
            });

            // Frame 2 - Main container (positioned next to health bar, bottom-right)
            container.Add(new CuiElement
            {
                Name = "Frame 2",
                Parent = "AwakenRoamsUI",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "1 0",
                        AnchorMax = "1 0",
                        OffsetMin = "-320 20",
                        OffsetMax = "-200 140"
                    }
                }
            });

            // Frame 3 - Stats container (positioned within Frame 2)
            container.Add(new CuiElement
            {
                Name = "Frame 3",
                Parent = "Frame 2",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1",
                        OffsetMin = "0 0",
                        OffsetMax = "0 0"
                    }
                }
            });

            // Awaken Roams Image - Direct URL method
            CreateLogo(container, "Frame 3");

            // Get player stats for display
            int playerKills = GetPlayerKills(player);
            int playerDeaths = GetPlayerDeaths(player);
            // Count teams based on players currently in bubble, not just team stats
            int totalTeams = Instance.CountTeamsInBubble(roamBubble);

            // Format time remaining
            string timeText = time >= 0 ? FormatTime(time) : "00:00";

            // KILLS text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "KILLS {k}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"KILLS {playerKills}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.3",
                        AnchorMax = "0.5 0.4",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // DEATHS text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "DEATHS {d}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"DEATHS {playerDeaths}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.2",
                        AnchorMax = "0.5 0.3",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // TEAMS text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "TEAMS {k}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"TEAMS {totalTeams}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.1",
                        AnchorMax = "0.5 0.2",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // Time text with dynamic data (positioned lower to avoid overlap, moved right 10px)
            container.Add(new CuiElement
            {
                Name = "{time}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = timeText, Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.0",
                        AnchorMax = "0.5 0.1",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            try
            {
                CuiHelper.AddUi(player, container);
            }
            catch (Exception ex)
            {
                // Handle or log the exception if needed
            }
        }

        private string FormatTime(float timeInSeconds)
        {
            TimeSpan timeSpan = TimeSpan.FromSeconds(timeInSeconds);
            return $"{timeSpan.Minutes.ToString("D2")}:{timeSpan.Seconds.ToString("D2")}";
        }

        private void UpdateTimerUI(BasePlayer player, float time)
        {
            if (player == null || !player.IsConnected)
            {
                return;
            }

            // Update the time text in the existing UI
            string timeText = FormatTime(time);
            CuiHelper.DestroyUi(player, "{time}");

            var container = new CuiElementContainer();

            container.Add(new CuiElement
            {
                Name = "{time}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = timeText, Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.0",
                        AnchorMax = "0.5 0.1",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            try
            {
                CuiHelper.AddUi(player, container);
            }
            catch (Exception ex)
            {
                // Handle or log the exception if needed
            }
        }

        private int GetPlayerKills(BasePlayer player)
        {
            if (player == null) return 0;
            AutomaticRoamBubbleComp bubble = playersInBubble.GetValueOrDefault(player.UserIDString);
            if (bubble != null && bubble.playerStats != null && bubble.playerStats.TryGetValue(player.UserIDString, out PlayerStats stats))
            {
                return stats.kills;
            }
            return 0;
        }

        private int GetPlayerDeaths(BasePlayer player)
        {
            if (player == null) return 0;
            AutomaticRoamBubbleComp bubble = playersInBubble.GetValueOrDefault(player.UserIDString);
            if (bubble != null && bubble.playerStats != null && bubble.playerStats.TryGetValue(player.UserIDString, out PlayerStats stats))
            {
                return stats.deaths;
            }
            return 0;
        }

        private BasePlayer FindTeammateInBubble(BasePlayer player, AutomaticRoamBubbleComp bubble)
        {
            // Use AwakenClans to find clan members in the bubble
            if (AwakenClans == null)
            {
                return null; // No teammates if AwakenClans not available
            }

            var clanMembers = AwakenClans?.Call("GetClanMembers", player.UserIDString);
            if (clanMembers != null && clanMembers is List<string> membersList)
            {
                foreach (var memberId in membersList)
                {
                    if (memberId == player.UserIDString) continue;

                    // Safely parse the member ID
                    if (ulong.TryParse(memberId, out ulong memberUlong))
                    {
                        BasePlayer teammate = BasePlayer.FindByID(memberUlong);
                        if (teammate != null && Vector3.Distance(teammate.transform.position, bubble.transform.position) <= bubble.innerCollider.radius)
                        {
                            return teammate;
                        }
                    }
                }
            }
            return null;
        }
        #endregion

        #region Get Spawn Locations
        private Vector3 FindValidSpawnPosition(Vector3 bubblePosition, float radius, Vector3 nearPosition, string biome)
        {
            int maxAttempts = 100; // Increased attempts to ensure a valid position is found
            for (int i = 0; i < maxAttempts; i++) // Try up to 100 times to find a valid position
            {
                Vector3 randomOffset = UnityEngine.Random.insideUnitSphere * radius;
                randomOffset.y = 0; // Keep on the horizontal plane
                Vector3 testPosition = bubblePosition + randomOffset;

                // If nearPosition is provided (for team member), prioritize positions close to it
                if (nearPosition != Vector3.zero)
                {
                    Vector3 direction = (nearPosition - bubblePosition).normalized;
                    testPosition = bubblePosition + (direction * UnityEngine.Random.Range(0f, radius * 0.8f)); // Stay closer to team member
                }

                // Ensure the position is within the bubble
                if (Vector3.Distance(testPosition, bubblePosition) > radius)
                {
                    continue; // Skip if outside the bubble
                }

                // Adjust position to terrain height
                float terrainHeight = TerrainMeta.HeightMap.GetHeight(testPosition);
                testPosition.y = terrainHeight;

                // Check if the position is on land and not in water
                if (TerrainMeta.WaterMap.GetHeight(testPosition) < terrainHeight)
                {
                    // Additional check to ensure the position is within the biome's land area
                    if (IsPositionOnLandInBiome(testPosition, biome))
                    {
                        // Verify the position is not too close to water edges or cliffs (optional safety check)
                        if (IsPositionSafe(testPosition, biome))
                        {
                            // NEW: Check if there are walls or structures at this spawn position
                            if (!IsWallAtPosition(testPosition, 2f))
                            {
                                // NEW: Check if there are other players too close to this spawn position
                                if (!IsPlayerInSpawnArea(testPosition))
                                {
                                    if (config.debug)
                                        Puts($"[Roam Debug] Found valid spawn position at {testPosition} (attempt {i + 1})");
                                    return testPosition; // Found a valid position: on land, not in water, within bubble, no walls, no players
                                }
                                else if (config.debug)
                                {
                                    Puts($"[Roam Debug] Spawn position rejected - player too close (attempt {i + 1})");
                                }
                            }
                            else if (config.debug)
                            {
                                Puts($"[Roam Debug] Spawn position rejected - wall collision detected (attempt {i + 1})");
                            }
                        }
                    }
                }
            }

            if (config.debug)
                Puts($"[Roam Debug] Failed to find valid spawn position after {maxAttempts} attempts");
            return Vector3.zero; // No valid position found after attempts
        }

        // Helper method to verify if a position is on land in the specified biome
        private bool IsPositionOnLandInBiome(Vector3 position, string biome)
        {
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight;

            // Check if the position is above water and on valid terrain for the biome
            if (TerrainMeta.WaterMap.GetHeight(position) < terrainHeight)
            {
                // Optional: Add biome-specific terrain checks if needed (e.g., snow, desert, grass biomes)
                // For simplicity, rely on the water/land check here
                return true;
            }
            return false;
        }

        // Helper method to ensure the position is safe (not on water edges or cliffs)
        private bool IsPositionSafe(Vector3 position, string biome)
        {
            float terrainHeight = TerrainMeta.HeightMap.GetHeight(position);
            position.y = terrainHeight;

            // Check a small radius around the position to ensure no nearby water or cliffs
            float checkRadius = 2f; // Small radius to check nearby terrain
            for (int i = 0; i < 8; i++) // Check 8 points around the position
            {
                float angle = (2 * Mathf.PI * i) / 8;
                Vector3 checkPosition = position + new Vector3(Mathf.Cos(angle) * checkRadius, 0, Mathf.Sin(angle) * checkRadius);
                float checkHeight = TerrainMeta.HeightMap.GetHeight(checkPosition);
                checkPosition.y = checkHeight;

                if (TerrainMeta.WaterMap.GetHeight(checkPosition) >= checkHeight || Mathf.Abs(checkHeight - terrainHeight) > 1f) // Cliff or water nearby
                {
                    return false;
                }
            }
            return true;
        }

        // Helper methods to count actual clan teams (not individual players)
        private int CountActualTeams(Dictionary<string, TeamStats> teamStats)
        {
            int clanTeams = 0;
            foreach (var teamEntry in teamStats)
            {
                string teamName = teamEntry.Key;
                var stats = teamEntry.Value;

                // Only count teams that have actually participated (kills OR deaths > 0)
                if (stats.kills > 0 || stats.deaths > 0)
                {
                    // Count as team if: multiple members OR verified clan tag
                    if (stats.members.Count > 1 || IsActualClanTag(teamName))
                    {
                        clanTeams++;
                    }
                }
            }
            return clanTeams;
        }

        private int CountActualTeamsFromGroups(List<TeamGroup> teamGroups)
        {
            int clanTeams = 0;
            foreach (var group in teamGroups)
            {
                string teamName = group.Team;
                int playerCount = group.Players.Count;
                int totalKills = group.TotalKills;
                int totalDeaths = group.TotalDeaths;

                // Only count teams that have actually participated (kills OR deaths > 0)
                if (totalKills > 0 || totalDeaths > 0)
                {
                    // Count as team if: multiple members OR verified clan tag
                    if (playerCount > 1 || IsActualClanTag(teamName))
                    {
                        clanTeams++;
                    }
                }
            }
            return clanTeams;
        }

        private bool IsActualClanTag(string teamName)
        {
            // Check if this is a clan name/tag (not an individual player name)
            if (string.IsNullOrEmpty(teamName)) return false;

            // Check if teamName looks like a clan tag (short, alphanumeric, no spaces)
            if (!teamName.Contains(" ") && teamName.Length >= 2 && teamName.Length <= 10)
            {
                // Check if it's all alphanumeric (typical clan tag pattern)
                if (teamName.All(c => char.IsLetterOrDigit(c)))
                {
                    return true;
                }
            }

            // Also check if any active player has this in their display name as a clan tag
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player.displayName.StartsWith($"[{teamName}]"))
                {
                    return true;
                }
            }

            // Fallback: Try AwakenClans API if available
            if (AwakenClans != null)
            {
                foreach (var player in BasePlayer.activePlayerList)
                {
                    // Check clan tag first
                    var clanTag = AwakenClans.Call("GetClanTag", player.userID) as string;
                    if (!string.IsNullOrEmpty(clanTag) && clanTag.Equals(teamName, StringComparison.OrdinalIgnoreCase))
                    {
                        return true;
                    }

                    // Check clan name as fallback
                    var clan = AwakenClans.Call("GetClan", player.userID);
                    if (clan != null)
                    {
                        var clanName = AwakenClans.Call("GetClanName", clan) as string;
                        if (!string.IsNullOrEmpty(clanName) && clanName.Equals(teamName, StringComparison.OrdinalIgnoreCase))
                        {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        // New method to count teams based on players currently in bubble
        private int CountTeamsInBubble(AutomaticRoamBubbleComp roamBubble)
        {
            HashSet<string> uniqueTeams = new HashSet<string>();

            // Get all players currently in this bubble
            foreach (var kvp in playersInBubble.Where(kvp => kvp.Value == roamBubble))
            {
                BasePlayer player = BasePlayer.Find(kvp.Key);
                if (player != null && player.IsConnected && !IsPlayerVanished(player))
                {
                    string teamTag = GetTeamTag(player);

                    // Only count actual clan tags, not individual player names
                    if (IsActualClanTag(teamTag))
                    {
                        uniqueTeams.Add(teamTag);
                    }
                }
            }

            return uniqueTeams.Count;
        }
        #endregion

        // Voting system integration for AwakenVotingSystem
        #region VotingSystemIntegration

        // Called by AwakenVotingSystem when a roam event is selected
        [HookMethod("StartRoamEvent")]
        public void StartRoamEvent(string biome = null, int duration = 0)
        {
            // Use provided parameters or fall back to config defaults
            string selectedBiome = !string.IsNullOrEmpty(biome) ? biome : config.biomeSettings.defaultBiome;
            int roamDuration = duration > 0 ? duration : ParseRoamTime(config.roamTime);

            // Validate biome
            if (!config.biomeSettings.allowedBiomes.Contains(selectedBiome, StringComparer.OrdinalIgnoreCase))
            {
                PrintError($"Invalid biome '{selectedBiome}' selected from voting system. Using default biome.");
                selectedBiome = config.biomeSettings.defaultBiome;
            }

            // Get spawn position for the selected biome
            Vector3 position = GetRandomSpawnInBiome(selectedBiome);
            if (position == Vector3.zero)
            {
                PrintError($"No spawn locations set for {selectedBiome} biome. Cannot start roam event.");
                return;
            }

            // Create the roam bubble
            var bubbleObject = new GameObject();
            var bubbleComp = bubbleObject.AddComponent<AutomaticRoamBubbleComp>();
            bubbleComp.CreateBubble(position, config.sphereRadius, selectedBiome, roamDuration);

            // Send start message
            if (config.messages.sendChatMessageOnRoam)
            {
                SendBigGlobalMessage(string.Format(this.lang.GetMessage("RoamStartedChat", this), selectedBiome, roamDuration));
            }

            Puts($"Roam event started via voting system: {selectedBiome} biome, {roamDuration} seconds duration");
        }

        // Called by AwakenVotingSystem to get available roam options
        [HookMethod("GetRoamOptions")]
        public Dictionary<string, object> GetRoamOptions()
        {
            var options = new Dictionary<string, object>();

            // Add biome options
            var biomes = new List<object>();
            foreach (string biome in config.biomeSettings.allowedBiomes)
            {
                // Check if biome has spawn locations
                Vector3 testPosition = GetRandomSpawnInBiome(biome);
                if (testPosition != Vector3.zero)
                {
                    biomes.Add(new Dictionary<string, object>
                    {
                        ["name"] = biome,
                        ["displayName"] = char.ToUpper(biome[0]) + biome.Substring(1), // Capitalize first letter
                        ["available"] = true
                    });
                }
            }

            options["biomes"] = biomes;
            options["defaultDuration"] = ParseRoamTime(config.roamTime);
            options["eventType"] = "roam";

            return options;
        }

        // Called by AwakenVotingSystem to check if roam can start
        [HookMethod("CanStartRoamEvent")]
        public bool CanStartRoamEvent()
        {
            // Check if there's already an active roam
            if (activeRoams.Count > 0)
            {
                return false;
            }

            // Check if at least one biome has spawn locations
            foreach (string biome in config.biomeSettings.allowedBiomes)
            {
                Vector3 testPosition = GetRandomSpawnInBiome(biome);
                if (testPosition != Vector3.zero)
                {
                    return true;
                }
            }

            return false;
        }

        #region API Methods for Voting System

        // API method to check if any roam events are currently active
        [HookMethod("API_IsEventActive")]
        public bool API_IsEventActive()
        {
            return activeRoams.Count > 0;
        }

        // API method to get detailed event status
        [HookMethod("API_GetEventStatus")]
        public Dictionary<string, object> API_GetEventStatus()
        {
            var status = new Dictionary<string, object>
            {
                ["IsActive"] = activeRoams.Count > 0,
                ["EventCount"] = activeRoams.Count,
                ["EventType"] = "roam",
                ["PluginName"] = "AutomaticRoamBubble"
            };

            if (activeRoams.Count > 0)
            {
                var activeRoam = activeRoams[0]; // Get first active roam
                status["TimeRemaining"] = activeRoam.roamTime;
                status["Location"] = activeRoam.transform.position;
                status["Biome"] = activeRoam.biome;
                status["ParticipantCount"] = playersInBubble.Count(kvp => kvp.Value == activeRoam && !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(kvp.Key))));
                status["Radius"] = activeRoam.innerCollider?.radius ?? config.sphereRadius;

                // Team statistics
                status["TeamCount"] = activeRoam.teamStats.Count;
                status["TotalKills"] = activeRoam.teamStats.Values.Sum(t => t.kills);
                status["TotalDeaths"] = activeRoam.teamStats.Values.Sum(t => t.deaths);
            }
            else
            {
                status["TimeRemaining"] = 0f;
                status["Location"] = Vector3.zero;
                status["Biome"] = "";
                status["ParticipantCount"] = 0;
                status["Radius"] = 0f;
                status["TeamCount"] = 0;
                status["TotalKills"] = 0;
                status["TotalDeaths"] = 0;
            }

            return status;
        }

        // API method to get all active events (for multiple roam support)
        [HookMethod("API_GetActiveEvents")]
        public List<Dictionary<string, object>> API_GetActiveEvents()
        {
            var events = new List<Dictionary<string, object>>();

            foreach (var roam in activeRoams)
            {
                if (roam == null) continue;

                var eventData = new Dictionary<string, object>
                {
                    ["EventType"] = "roam",
                    ["TimeRemaining"] = roam.roamTime,
                    ["Location"] = roam.transform.position,
                    ["Biome"] = roam.biome,
                    ["ParticipantCount"] = playersInBubble.Count(kvp => kvp.Value == roam && !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(kvp.Key)))),
                    ["Radius"] = roam.innerCollider?.radius ?? config.sphereRadius,
                    ["TeamCount"] = roam.teamStats.Count,
                    ["TotalKills"] = roam.teamStats.Values.Sum(t => t.kills),
                    ["TotalDeaths"] = roam.teamStats.Values.Sum(t => t.deaths),
                    ["StartTime"] = roam.initialRoamTime - roam.roamTime
                };

                events.Add(eventData);
            }

            return events;
        }

        // API method to force end all active roam events
        [HookMethod("API_EndAllEvents")]
        public bool API_EndAllEvents()
        {
            if (activeRoams.Count == 0) return false;

            var roamsToEnd = activeRoams.ToList();
            foreach (var roam in roamsToEnd)
            {
                if (roam != null)
                {
                    roam.roamTime = 0f; // This will trigger the end sequence in Update()
                }
            }

            return true;
        }

        // API method to get event configuration info
        [HookMethod("API_GetEventConfig")]
        public Dictionary<string, object> API_GetEventConfig()
        {
            return new Dictionary<string, object>
            {
                ["DefaultDuration"] = ParseRoamTime(config.roamTime),
                ["SphereRadius"] = config.sphereRadius,
                ["AvailableBiomes"] = config.biomeSettings.allowedBiomes,
                ["DefaultBiome"] = config.biomeSettings.defaultBiome,
                ["MaxConcurrentEvents"] = 1, // Currently only supports 1 roam at a time
                ["WeaponRestrictionsEnabled"] = config.weaponRestrictions != null,
                ["DecayAccelerationEnabled"] = config.decaySettings?.enableAcceleratedDecay ?? false
            };
        }

        #endregion

        // Helper method to parse roam time from config
        private int ParseRoamTime(string roamTimeStr)
        {
            if (string.IsNullOrEmpty(roamTimeStr)) return 1800; // Default 30 minutes

            try
            {
                roamTimeStr = roamTimeStr.ToLower();
                if (roamTimeStr.EndsWith("m"))
                {
                    int minutes = int.Parse(roamTimeStr.TrimEnd('m'));
                    return minutes * 60; // Convert minutes to seconds
                }
                else
                {
                    return int.Parse(roamTimeStr); // Assume it's already in seconds
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error parsing roam time '{roamTimeStr}': {ex.Message}");
                return 1800; // Default 30 minutes
            }
        }

        // Called when AwakenVotingSystem plugin loads
        void OnPluginLoaded(Plugin plugin)
        {
            if (plugin?.Name == "AwakenVotingSystem")
            {
                Puts("AwakenVotingSystem detected - roam voting integration enabled");
                AwakenVotingSystem = plugin;
            }
        }

        // Called when AwakenVotingSystem plugin unloads
        void OnPluginUnloaded(Plugin plugin)
        {
            if (plugin?.Name == "AwakenVotingSystem")
            {
                Puts("AwakenVotingSystem unloaded - roam voting integration disabled");
                AwakenVotingSystem = null;
            }
        }
        #endregion

        #region Clan Core Integration
        private void AwardClanCorePoints(string clanName, string eventType)
        {
            if (ClanCores == null || !ClanCores.IsLoaded)
            {
                PrintWarning("[Roam Bubble] ClanCores plugin not found - cannot award points");
                return;
            }

            try
            {
                bool success = (bool)ClanCores.Call("API_AwardEventPoints", clanName, eventType);
                if (success)
                {
                    Puts($"[Roam Bubble] Successfully awarded clan core points to '{clanName}' for {eventType} event win");
                }
                else
                {
                    PrintWarning($"[Roam Bubble] Failed to award clan core points to '{clanName}'");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[Roam Bubble] Error awarding clan core points: {ex.Message}");
            }
        }
        #endregion

        #region AwakenStats Integration
        private void AddRoamWinToStats(string clanName)
        {
            if (AwakenStats == null || !AwakenStats.IsLoaded)
            {
                PrintWarning("[Roam Bubble] AwakenStats plugin not found - cannot record event win");
                return;
            }

            try
            {
                // Add roam win for the entire clan
                AwakenStats.Call("AddEventWinForClan", clanName, "roam");
                Puts($"[Roam Bubble] Successfully recorded roam win for clan '{clanName}' in AwakenStats");
            }
            catch (Exception ex)
            {
                PrintError($"[Roam Bubble] Error recording roam win in AwakenStats: {ex.Message}");
            }
        }
        #endregion

        #region Vanish Detection

        private bool IsPlayerVanished(BasePlayer player)
        {
            if (player == null) return false;

            // Check if player is admin and vanished using common vanish plugins
            if (Vanish != null)
            {
                var isVanished = Vanish.Call("IsInvisible", player);
                if (isVanished is bool && (bool)isVanished)
                {
                    return true;
                }
            }

            // Check AdminRadar vanish
            if (AdminRadar != null)
            {
                var isVanished = AdminRadar.Call("IsInvisible", player);
                if (isVanished is bool && (bool)isVanished)
                {
                    return true;
                }
            }

            // Additional check for admin invisibility (some plugins use this)
            if (player.IsAdmin && player.limitNetworking)
            {
                return true;
            }

            return false;
        }

        #endregion

        #region Helicopter Detection and Prevention

        private bool IsPlayerOnHelicopter(BasePlayer player)
        {
            if (player == null || player.isMounted == false) return false;

            var mountedVehicle = player.GetMountedVehicle();
            if (mountedVehicle == null) return false;

            // Check for different types of helicopters
            return mountedVehicle is BaseHelicopter ||
                   mountedVehicle.PrefabName.Contains("helicopter") ||
                   mountedVehicle.PrefabName.Contains("heli") ||
                   mountedVehicle.PrefabName.Contains("minicopter") ||
                   mountedVehicle.PrefabName.Contains("scrapheli") ||
                   mountedVehicle.PrefabName.Contains("attackhelicopter");
        }

        private void EjectPlayerFromHelicopter(BasePlayer player)
        {
            if (player == null || !player.isMounted) return;

            try
            {
                var mountedVehicle = player.GetMountedVehicle();
                if (mountedVehicle != null)
                {
                    // Get a safe position to eject the player
                    Vector3 ejectPosition = GetSafeEjectPosition(player.transform.position);

                    // Dismount the player
                    player.DismountObject();

                    // Move player to safe position
                    player.MovePosition(ejectPosition);
                    player.SendNetworkUpdateImmediate();

                    // Add a small delay to ensure proper dismounting
                    Instance.timer.Once(0.1f, () => {
                        if (player != null && player.IsConnected)
                        {
                            player.Teleport(ejectPosition);
                        }
                    });

                    if (config.debug)
                    {
                        Instance.Puts($"[Roam Debug] Ejected {player.displayName} from helicopter at {ejectPosition}");
                    }
                }
            }
            catch (Exception ex)
            {
                Instance.PrintError($"Error ejecting player from helicopter: {ex.Message}");
            }
        }

        private Vector3 GetSafeEjectPosition(Vector3 originalPosition)
        {
            // Try to find a safe position on the ground
            Vector3 groundPosition = originalPosition;

            // Raycast down to find ground
            RaycastHit hit;
            if (Physics.Raycast(originalPosition, Vector3.down, out hit, 200f, LayerMask.GetMask("Terrain", "World", "Default")))
            {
                groundPosition = hit.point + Vector3.up * 2f; // Add 2 meters above ground
            }
            else
            {
                // Fallback: use terrain height
                float terrainHeight = TerrainMeta.HeightMap.GetHeight(originalPosition);
                groundPosition.y = terrainHeight + 2f;
            }

            // Ensure the position is not in water
            if (TerrainMeta.WaterMap.GetHeight(groundPosition) > groundPosition.y)
            {
                groundPosition.y = TerrainMeta.WaterMap.GetHeight(groundPosition) + 2f;
            }

            return groundPosition;
        }

        #endregion

        #region Oxide API Hooks for Other Plugins

        [HookMethod("IsRoamEventRunning")]
        public bool IsRoamEventRunning()
        {
            return activeRoams.Count > 0;
        }

        [HookMethod("GetRoamEventStatus")]
        public Dictionary<string, object> GetRoamEventStatus()
        {
            return API_GetEventStatus();
        }

        [HookMethod("GetActiveRoamEvents")]
        public List<Dictionary<string, object>> GetActiveRoamEvents()
        {
            return API_GetActiveEvents();
        }

        [HookMethod("GetRoamEventConfig")]
        public Dictionary<string, object> GetRoamEventConfig()
        {
            return API_GetEventConfig();
        }

        [HookMethod("IsPlayerInRoamBubble")]
        public bool IsPlayerInRoamBubble(string userID)
        {
            if (!playersInBubble.ContainsKey(userID)) return false;

            // Check if player is vanished
            var player = BasePlayer.FindByID(ulong.Parse(userID));
            return !IsPlayerVanished(player);
        }

        [HookMethod("GetPlayerRoamStats")]
        public Dictionary<string, object> GetPlayerRoamStats(string userID)
        {
            if (!playersInBubble.ContainsKey(userID))
            {
                return new Dictionary<string, object>
                {
                    ["inRoam"] = false,
                    ["error"] = "Player not in roam bubble"
                };
            }

            var roamBubble = playersInBubble[userID];
            var player = BasePlayer.FindByID(ulong.Parse(userID));

            // Get player stats from the roam bubble
            PlayerStats playerStats = null;
            if (roamBubble.playerStats.ContainsKey(userID))
            {
                playerStats = roamBubble.playerStats[userID];
            }

            if (playerStats == null)
            {
                return new Dictionary<string, object>
                {
                    ["inRoam"] = true,
                    ["userID"] = userID,
                    ["kills"] = 0,
                    ["deaths"] = 0,
                    ["headshots"] = 0,
                    ["damage"] = 0,
                    ["kdr"] = 0.0f,
                    ["joinTime"] = DateTime.UtcNow,
                    ["team"] = GetTeamTag(player)
                };
            }

            return new Dictionary<string, object>
            {
                ["inRoam"] = true,
                ["userID"] = userID,
                ["kills"] = playerStats.kills,
                ["deaths"] = playerStats.deaths,
                ["headshots"] = playerStats.headshots,
                ["damage"] = playerStats.damage,
                ["kdr"] = playerStats.deaths > 0 ? (float)playerStats.kills / playerStats.deaths : playerStats.kills,
                ["joinTime"] = DateTime.UtcNow, // We don't track join time in PlayerStats, so use current time
                ["team"] = GetTeamTag(player)
            };
        }

        [HookMethod("GetRoamBubbleInfo")]
        public Dictionary<string, object> GetRoamBubbleInfo()
        {
            if (activeRoams.Count == 0)
            {
                return new Dictionary<string, object>
                {
                    ["active"] = false,
                    ["error"] = "No active roam bubble"
                };
            }

            var currentBubble = activeRoams[0];
            return new Dictionary<string, object>
            {
                ["active"] = true,
                ["position"] = new Dictionary<string, float>
                {
                    ["x"] = currentBubble.transform.position.x,
                    ["y"] = currentBubble.transform.position.y,
                    ["z"] = currentBubble.transform.position.z
                },
                ["radius"] = currentBubble.innerCollider?.radius ?? config.sphereRadius,
                ["biome"] = currentBubble.biome,
                ["startTime"] = currentBubble.initialRoamTime,
                ["duration"] = currentBubble.roamTime,
                ["participantCount"] = playersInBubble.Count(kvp => !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(kvp.Key))))
            };
        }

        [HookMethod("GetRoamParticipants")]
        public List<string> GetRoamParticipants()
        {
            return playersInBubble.Keys.Where(userID => !IsPlayerVanished(BasePlayer.FindByID(ulong.Parse(userID)))).ToList();
        }

        [HookMethod("GetRoamParticipantDetails")]
        public List<Dictionary<string, object>> GetRoamParticipantDetails()
        {
            var participants = new List<Dictionary<string, object>>();

            foreach (var kvp in playersInBubble)
            {
                var player = BasePlayer.FindByID(ulong.Parse(kvp.Key));

                // Exclude vanished players from participant details
                if (IsPlayerVanished(player)) continue;

                var roamBubble = kvp.Value;
                var userID = kvp.Key;

                // Get player stats from the roam bubble
                PlayerStats playerStats = null;
                if (roamBubble.playerStats.ContainsKey(userID))
                {
                    playerStats = roamBubble.playerStats[userID];
                }

                var kills = playerStats?.kills ?? 0;
                var deaths = playerStats?.deaths ?? 0;
                var headshots = playerStats?.headshots ?? 0;
                var damage = playerStats?.damage ?? 0;

                participants.Add(new Dictionary<string, object>
                {
                    ["userID"] = userID,
                    ["name"] = player?.displayName ?? "Unknown",
                    ["kills"] = kills,
                    ["deaths"] = deaths,
                    ["headshots"] = headshots,
                    ["damage"] = damage,
                    ["kdr"] = deaths > 0 ? (float)kills / deaths : kills,
                    ["team"] = GetTeamTag(player),
                    ["joinTime"] = DateTime.UtcNow, // We don't track join time, so use current time
                    ["isOnline"] = player?.IsConnected ?? false
                });
            }

            return participants.OrderByDescending(p => (int)p["kills"]).ToList();
        }

        [HookMethod("ForceStartRoamEvent")]
        public bool ForceStartRoamEvent(Vector3 position, string biome = null)
        {
            if (activeRoams.Count > 0)
            {
                return false;
            }

            try
            {
                if (string.IsNullOrEmpty(biome))
                {
                    biome = config.biomeSettings.defaultBiome;
                }

                StartRoamEvent(biome);
                return true;
            }
            catch
            {
                return false;
            }
        }

        [HookMethod("ForceEndRoamEvent")]
        public bool ForceEndRoamEvent()
        {
            if (activeRoams.Count == 0)
            {
                return false;
            }

            try
            {
                var roamsToEnd = activeRoams.ToList();
                foreach (var roam in roamsToEnd)
                {
                    if (roam != null)
                    {
                        roam.roamTime = 0f; // This will trigger the end sequence
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion
    }
}

