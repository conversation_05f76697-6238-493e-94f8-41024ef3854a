using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Airdrop Limiter", "Skelee", "1.1.0")]
    [Description("Limits the number of airdrops a player can throw with cooldowns.")]

    public class AwakenAirdropLimiter : CovalencePlugin
    {
        #region Config
        private static Configuration? config;

        private class Configuration
        {
            [JsonProperty("Airdrop Cooldown Time (Secs)")]
            public float AirdropCooldownTime { get; set; } = 60f;

            [JsonProperty("Max Airdrop Count")]
            public int MaxAirdropCount { get; set; } = 3;
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Defines
        private readonly Dictionary<string, List<float>> airdropHandle = new Dictionary<string, List<float>>();
        #endregion

        #region Hooks
        private void OnExplosiveThrown(BasePlayer? player, BaseEntity? entity, ThrownWeapon? item) => OnExplosiveDropped(player, entity);

        private void OnExplosiveDropped(BasePlayer? player, BaseEntity? entity)
        {
            if (entity?.ShortPrefabName != "grenade.supplysignal.deployed" || player == null) return;

            if (!airdropHandle.ContainsKey(player.UserIDString))
                airdropHandle[player.UserIDString] = new List<float>();
            var airdrops = airdropHandle[player.UserIDString];
            airdrops.RemoveAll(time => Time.time - time >= (config?.AirdropCooldownTime ?? 60f));

            if (airdrops.Count >= (config?.MaxAirdropCount ?? 3))
            {
                player.ChatMessage("You have thrown the maximum airdrops and must wait for the cooldown.");
                entity.Kill();

                var item = ItemManager.CreateByName("supply.signal", 1);
                if (item != null) player.inventory.GiveItem(item, player.inventory.containerBelt);
            }
            else
                airdrops.Add(Time.time);
        }
        #endregion
    }
}










