using Facepunch;
using CompanionServer;
using ConVar;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Chat", "Skelee", "1.0.3")]
    [Description("Gives players colors when they type in chat.")]

    public class AwakenChat : CovalencePlugin
    {
        #region Defines
        [PluginReference] private Plugin? AwakenAdminMenu, AwakenClans;
        private const string chatmodperm = "awakenchat.chatmod";
        private Timer? mutedPlayersTimer;
        private readonly Dictionary<string, BasePlayer> playerIds = new Dictionary<string, BasePlayer>();
        #endregion

        #region Config
        private static Configuration? config;

        private record Filter(
            [property: JsonProperty("Max Capital Characters")] int MaxCaps = 10,
            [property: JsonProperty("Max Special Characters")] int MaxSpecialChars = 5);

        private record RoleColor(
            [property: JsonProperty("Group Name")] string GroupName = "",
            [property: JsonProperty("Color")] string Color = "#55aaff");

        private record Configuration(
            [property: JsonProperty("Chat Filter")] Filter? ChatFilter = null,
            [property: JsonProperty("Clan Tag Color")] string ClanTagColor = "#ffa500",
            [property: JsonProperty("Default Player Color")] string DefaultPlayerColor = "#55aaff",
            [property: JsonProperty("Special Players")] Dictionary<string, string> SpecialPlayers = null!,
            [property: JsonProperty("Role Colors")] List<RoleColor> RoleColors = null!,
            [property: JsonProperty("Enable Clan Debug")] bool EnableClanDebug = false,
            [property: JsonProperty("Use Direct Clan Access")] bool UseDirectClanAccess = true,
            [property: JsonProperty("Use Full Clan Name")] bool UseFullClanName = true)
        {
            public Configuration() : this(
                new Filter(),
                "#ffa500",
                "#55aaff",
                new Dictionary<string, string>
                {
                    ["76561199826171165"] = "#6e6969"
                },
                new List<RoleColor>
                {
                    new("admin", "#AAFF55"),
                    new("vip", "#c063c2"),
                    new("freevip", "#c063c2"),
                    new("ares", "#d4af37"),
                    new("hades", "#d4af37"),
                    new("poseidon", "#d4af37"),
                    new("deity", "#d4af37")
                },
                false,
                true,
                true)
            { }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();

                // Clean up duplicate role colors
                if (config?.RoleColors != null)
                {
                    var uniqueRoles = new Dictionary<string, string>();
                    foreach (var role in config.RoleColors)
                    {
                        if (!string.IsNullOrEmpty(role.GroupName))
                        {
                            uniqueRoles[role.GroupName.ToLower()] = role.Color;
                        }
                    }

                    config = config with {
                        RoleColors = uniqueRoles.Select(kvp => new RoleColor(kvp.Key, kvp.Value)).ToList()
                    };
                }

                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Data
        private class Save
        {
            public Dictionary<string, int> MutedPlayers { get; set; } = new();
            public HashSet<string> Profanities { get; set; } = new();
        }

        private Save? _save;

        private void SaveData() => Interface.Oxide.DataFileSystem.WriteObject(Name, _save);
        #endregion

        #region Functions
        private void ManageTimer(bool start = true)
        {
            if (!start && mutedPlayersTimer?.Destroyed == false) { mutedPlayersTimer.Destroy(); SaveData(); }
            mutedPlayersTimer = timer.Every(60, () =>
            {
                try
                {
                    if (_save?.MutedPlayers == null || _save.MutedPlayers.Count == 0) return;

                    var expiredMutes = new List<string>();
                    foreach (var (id, time) in _save.MutedPlayers.ToList())
                    {
                        if ((time - 1) <= 0)
                            expiredMutes.Add(id);
                        else
                            _save.MutedPlayers[id] = time - 1;
                    }

                    foreach (var id in expiredMutes)
                        _save.MutedPlayers.Remove(id);

                    if (expiredMutes.Count > 0)
                        SaveData();
                }
                catch (Exception ex)
                {
                    PrintError($"Error in mute timer: {ex.Message}");
                }
            });
        }

        private string CheckText(BasePlayer? player, string text)
        {
            if (player == null) return text;

            if (HasBlacklistedWord(text))
            {
                player.ChatMessage("You have a blacklisted word in your message, please try again.");
                return string.Empty;
            }

            if (HasAdvertisements(text))
            {
                player.ChatMessage("Your message contains an advert, please remove and try again.");
                return string.Empty;
            }

            if (HasToManyCaps(text))
            {
                player.ChatMessage("Too many caps in this message, reduce and try again!");
                return string.Empty;
            }

            if (HasToManySpecialChars(text))
            {
                player.ChatMessage("Too many special characters in this message, reduce and try again!");
                return string.Empty;
            }

            return text;
        }

        private static readonly Regex ipRegex = new(@"(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d{2,5})?)");
        private static readonly Regex domainRegex = new(@"(\w{2,}\.\w{2,3}\.\w{2,3}|\w{2,}\.\w{2,3}(:\d{2,5})?)$", RegexOptions.IgnoreCase);
        private bool HasAdvertisements(string text) => ipRegex.Matches(text).Count + domainRegex.Matches(text.ToLower()).Count > 0;

        private bool HasBlacklistedWord(string text) => _save?.Profanities.Any(word => text.Replace(" ", "").Contains(word, StringComparison.OrdinalIgnoreCase)) == true;

        private bool HasToManyCaps(string text) => text.Count(char.IsUpper) > (config?.ChatFilter?.MaxCaps ?? 10);

        private bool HasToManySpecialChars(string text) => text.Count(c => !char.IsLetterOrDigit(c) && c != ' ') > (config?.ChatFilter?.MaxSpecialChars ?? 5);

        private BasePlayer? FindPlayersSingle(string value, BasePlayer player)
        {
            if (string.IsNullOrEmpty(value)) return null;

            if (playerIds.TryGetValue(value, out var target) && target.IsValid()) return target;

            var foundPlayers = FindPlayers(value, true);
            switch (foundPlayers.Count)
            {
                case 0:
                    player.ChatMessage("Couldn't find a player with that name.");
                    return null;
                case > 1:
                    player.ChatMessage($"Found multiple players: {GetMultiplePlayers(foundPlayers)}\nType /mute 'ID'.");
                    return null;
                default:
                    return foundPlayers.First();
            }
        }

        private List<BasePlayer> FindPlayers(string arg, bool all = false)
        {
            if (string.IsNullOrEmpty(arg)) return new List<BasePlayer>();

            if (playerIds.TryGetValue(arg, out var target) && target.IsValid() && (all || target.IsConnected))
                return new List<BasePlayer> { target };

            return (all ? BasePlayer.allPlayerList : BasePlayer.activePlayerList)
                .Where(p => p != null && !string.IsNullOrEmpty(p.displayName) && (p.UserIDString == arg || p.displayName.Contains(arg, StringComparison.OrdinalIgnoreCase)))
                .ToList();
        }

        private string GetMultiplePlayers(List<BasePlayer> players) => string.Join(",", players.Select(p => $"\n<color=#FFA500>{playerIds.FirstOrDefault(x => x.Value == p).Key}</color> - {p.displayName}"));
        #endregion

        #region Commands
        [Command("mute")]
        private void MuteCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player || !permission.UserHasPermission(player.UserIDString, chatmodperm)) return;
            if (args.Length != 2) { player.ChatMessage("You must provide a name/steamid and mute duration."); return; }

            var mutePlayer = FindPlayersSingle(args[0], player);
            if (mutePlayer == null) return;

            if (!int.TryParse(args[1], out var muteTime) || (muteTime != -1 && muteTime <= 0)) { player.ChatMessage("Please enter a valid number of minutes or -1 for permanent."); return; }

            var (muteMessage, notifyMessage) = muteTime == -1 ?
                ($"You have been muted permanently by {player.displayName}", $"You have permanently muted {mutePlayer.displayName}!") :
                ($"You have been muted for {muteTime} min(s)", $"You have muted {mutePlayer.displayName} for {muteTime} min(s)");

            if (_save!.MutedPlayers.ContainsKey(mutePlayer.UserIDString))
            {
                _save.MutedPlayers[mutePlayer.UserIDString] = muteTime == -1 ? -1 : _save.MutedPlayers[mutePlayer.UserIDString] + muteTime;
                muteMessage = muteTime == -1 ? muteMessage : $"You have had {muteTime} min(s) added to your mute time.";
                notifyMessage = muteTime == -1 ? notifyMessage : $"You have added {muteTime} min(s) to {mutePlayer.displayName}'s mute time!";
            }
            else
                _save.MutedPlayers[mutePlayer.UserIDString] = muteTime;

            mutePlayer.ChatMessage(muteMessage);
            player.ChatMessage(notifyMessage);
            SaveData();
        }

        [Command("unmute")]
        private void UnmuteCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player || !permission.UserHasPermission(player.UserIDString, chatmodperm)) return;
            if (args.Length != 1) { player.ChatMessage("You must provide a name/steamid to unmute."); return; }

            var unmutePlayer = FindPlayersSingle(args[0], player);
            if (unmutePlayer == null) return;
            if (!_save!.MutedPlayers.ContainsKey(unmutePlayer.UserIDString)) { player.ChatMessage("This player is not muted."); return; }

            _save.MutedPlayers.Remove(unmutePlayer.UserIDString);
            SaveData();
            unmutePlayer.ChatMessage($"You have been unmuted by {player.displayName}!");
            player.ChatMessage($"You have unmuted {unmutePlayer.displayName}!");
        }

        [Command("filter")]
        private void FilterCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player || !permission.UserHasPermission(player.UserIDString, chatmodperm)) return;
            if (args.Length != 2) { player.ChatMessage("You must specify an action (add/remove) and a word."); return; }

            switch (args[0])
            {
                case "add":
                    if (_save!.Profanities.Contains(args[1])) { player.ChatMessage("The filter already contains this word."); return; }
                    _save.Profanities.Add(args[1]);
                    player.ChatMessage($"You have added '{args[1]}' to the profanity filter.");
                    break;

                case "remove":
                    if (!_save!.Profanities.Contains(args[1])) { player.ChatMessage("The filter does not contain this word."); return; }
                    _save.Profanities.Remove(args[1]);
                    player.ChatMessage($"You have removed '{args[1]}' from the profanity filter.");
                    break;
            }
            SaveData();
        }

        [Command("testclantag")]
        private void TestClanTagCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player || !permission.UserHasPermission(player.UserIDString, chatmodperm)) return;

            BasePlayer? targetPlayer = player;
            if (args.Length > 0)
            {
                targetPlayer = FindPlayersSingle(args[0], player);
                if (targetPlayer == null) return;
            }

            // Temporarily enable debugging for this test
            bool originalDebug = config?.EnableClanDebug ?? false;
            if (config != null) config = config with { EnableClanDebug = true };

            player.ChatMessage($"=== Clan Tag Debug for {targetPlayer.displayName} ===");
            player.ChatMessage($"UserID: {targetPlayer.userID}");
            player.ChatMessage($"UserIDString: {targetPlayer.UserIDString}");
            player.ChatMessage($"Debug enabled: {config?.EnableClanDebug}");
            player.ChatMessage($"Direct access enabled: {config?.UseDirectClanAccess}");

            if (AwakenClans == null)
            {
                player.ChatMessage("AwakenClans plugin is not loaded!");
                return;
            }

            player.ChatMessage("--- Testing API Methods ---");

            // Test all methods
            var method1 = AwakenClans.Call("GetClanTag", targetPlayer.userID);
            var method2 = AwakenClans.Call("GetClanTag", targetPlayer.UserIDString);
            var method3 = AwakenClans.Call("GetClan", targetPlayer.userID);
            var method4 = AwakenClans.Call("GetClan", targetPlayer.UserIDString);
            var method5 = AwakenClans.Call("IsInClan", targetPlayer.userID);

            player.ChatMessage($"GetClanTag(ulong): '{method1}' (Type: {method1?.GetType().Name ?? "null"})");
            player.ChatMessage($"GetClanTag(string): '{method2}' (Type: {method2?.GetType().Name ?? "null"})");
            player.ChatMessage($"GetClan(ulong): {(method3 != null ? $"Found {method3.GetType().Name}" : "null")}");
            player.ChatMessage($"GetClan(string): {(method4 != null ? $"Found {method4.GetType().Name}" : "null")}");
            player.ChatMessage($"IsInClan(ulong): {method5}");

            if (method3 != null)
            {
                var clanName = AwakenClans.Call("GetClanName", method3);
                player.ChatMessage($"GetClanName from clan object: '{clanName}'");
            }

            player.ChatMessage("--- Testing Direct Access ---");
            var directTag = GetClanTagDirectAccess(targetPlayer);
            player.ChatMessage($"Direct access result: '{directTag}'");

            player.ChatMessage("--- Final Results ---");
            var finalTag = GetPlayerClanTag(targetPlayer);
            player.ChatMessage($"Final clan tag result: '{finalTag}'");
            player.ChatMessage($"Formatted display name: '{FormatDisplayName(targetPlayer)}'");

            // Restore original debug setting
            if (config != null) config = config with { EnableClanDebug = originalDebug };
        }

        [Command("clanchatdebug")]
        private void ClanChatDebugCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player || !permission.UserHasPermission(player.UserIDString, chatmodperm)) return;

            if (args.Length == 0)
            {
                player.ChatMessage($"Current debug setting: {config?.EnableClanDebug}");
                player.ChatMessage($"Current direct access setting: {config?.UseDirectClanAccess}");
                player.ChatMessage("Usage: /clanchatdebug <on|off> [direct]");
                return;
            }

            bool enable = args[0].ToLower() == "on";
            bool directAccess = args.Length > 1 && args[1].ToLower() == "direct";

            if (config != null)
            {
                config = config with { EnableClanDebug = enable };
                if (directAccess)
                    config = config with { UseDirectClanAccess = enable };
            }

            SaveConfig();
            player.ChatMessage($"Clan debug set to: {enable}");
            if (directAccess)
                player.ChatMessage($"Direct access set to: {enable}");
        }

        [Command("quickclantest")]
        private void QuickClanTestCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player) return;

            // Temporarily enable debugging
            bool originalDebug = config?.EnableClanDebug ?? false;
            if (config != null) config = config with { EnableClanDebug = true };

            player.ChatMessage("=== Quick Clan Test ===");
            player.ChatMessage($"Use Full Clan Name: {config?.UseFullClanName}");

            // Test the current implementation
            var clanTag = GetPlayerClanTag(player);
            var formattedName = FormatDisplayName(player);

            player.ChatMessage($"Your clan tag/name: '{clanTag}'");
            player.ChatMessage($"Your formatted name: '{formattedName}'");

            // Check what the raw API returns
            if (AwakenClans != null)
            {
                var rawTag = AwakenClans.Call("GetClanTag", player.userID);
                var rawClan = AwakenClans.Call("GetClan", player.userID);
                player.ChatMessage($"Raw GetClanTag: '{rawTag}' (Type: {rawTag?.GetType().Name ?? "null"})");
                player.ChatMessage($"Raw GetClan: {(rawClan != null ? $"Found {rawClan.GetType().Name}" : "null")}");

                if (rawClan != null)
                {
                    // Show both clan name and clan tag
                    var clanType = rawClan.GetType();
                    var clanNameProp = clanType.GetProperty("ClanName");
                    var clanTagProp = clanType.GetProperty("ClanTag");

                    var fullClanName = clanNameProp?.GetValue(rawClan) as string;
                    var shortClanTag = clanTagProp?.GetValue(rawClan) as string;

                    player.ChatMessage($"Full Clan Name: '{fullClanName}'");
                    player.ChatMessage($"Short Clan Tag: '{shortClanTag}'");

                    // Show all properties for debugging
                    var properties = clanType.GetProperties();
                    player.ChatMessage($"Clan object has {properties.Length} properties:");
                    foreach (var prop in properties.Take(8)) // Show first 8 properties
                    {
                        try
                        {
                            var value = prop.GetValue(rawClan);
                            if (value is Dictionary<string, string> dict)
                            {
                                player.ChatMessage($"  {prop.Name}: Dictionary with {dict.Count} entries");
                            }
                            else if (value is List<string> list)
                            {
                                player.ChatMessage($"  {prop.Name}: List with {list.Count} entries");
                            }
                            else
                            {
                                player.ChatMessage($"  {prop.Name}: '{value}' ({prop.PropertyType.Name})");
                            }
                        }
                        catch (Exception ex)
                        {
                            player.ChatMessage($"  {prop.Name}: Error - {ex.Message}");
                        }
                    }
                }
            }
            else
            {
                player.ChatMessage("AwakenClans plugin not found!");
            }

            // Restore original debug setting
            if (config != null) config = config with { EnableClanDebug = originalDebug };
        }

        [Command("testgroups")]
        private void TestGroupsCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player || !permission.UserHasPermission(player.UserIDString, chatmodperm)) return;

            BasePlayer? targetPlayer = player;
            if (args.Length > 0)
            {
                targetPlayer = FindPlayersSingle(args[0], player);
                if (targetPlayer == null) return;
            }

            player.ChatMessage($"=== Group Debug for {targetPlayer.displayName} ===");
            player.ChatMessage($"UserID: {targetPlayer.userID}");
            player.ChatMessage($"UserIDString: {targetPlayer.UserIDString}");

            // Get all groups for the player
            var playerGroups = permission.GetUserGroups(targetPlayer.UserIDString);
            if (playerGroups != null && playerGroups.Length > 0)
            {
                player.ChatMessage($"Player has {playerGroups.Length} groups:");
                foreach (var group in playerGroups)
                {
                    player.ChatMessage($"  - '{group}'");
                }
            }
            else
            {
                player.ChatMessage("Player has no groups assigned.");
            }

            // Check configured role colors
            if (config?.RoleColors != null && config.RoleColors.Count > 0)
            {
                player.ChatMessage($"\nConfigured role colors ({config.RoleColors.Count}):");
                foreach (var roleColor in config.RoleColors)
                {
                    var hasGroup = playerGroups?.Any(g => string.Equals(g, roleColor.GroupName, StringComparison.OrdinalIgnoreCase)) == true;
                    player.ChatMessage($"  - '{roleColor.GroupName}' -> {roleColor.Color} {(hasGroup ? "✅ MATCH" : "❌")}");
                }
            }
            else
            {
                player.ChatMessage("\nNo role colors configured.");
            }

            // Test the actual color function
            var finalColor = GetPlayerChatColor(targetPlayer);
            player.ChatMessage($"\nFinal color result: {finalColor}");
            player.ChatMessage($"Formatted display name: '{FormatDisplayName(targetPlayer)}'");
        }

        [Command("reloadchatconfig")]
        private void ReloadChatConfigCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.Object is not BasePlayer player || !permission.UserHasPermission(player.UserIDString, chatmodperm)) return;

            try
            {
                LoadConfig();
                player.ChatMessage("Chat configuration reloaded successfully!");

                // Show current role colors
                if (config?.RoleColors != null && config.RoleColors.Count > 0)
                {
                    player.ChatMessage($"Loaded {config.RoleColors.Count} role colors:");
                    foreach (var roleColor in config.RoleColors)
                    {
                        player.ChatMessage($"  - '{roleColor.GroupName}' -> {roleColor.Color}");
                    }
                }
                else
                {
                    player.ChatMessage("No role colors configured.");
                }
            }
            catch (Exception ex)
            {
                player.ChatMessage($"Error reloading config: {ex.Message}");
                PrintError($"Error reloading chat config: {ex.Message}");
            }
        }
        #endregion

        #region Hooks
        private void Loaded()
        {
            if (AwakenAdminMenu == null) { Debug.LogWarning("Admin Menu is not loaded, and color chat requires it!"); }
            permission.RegisterPermission(chatmodperm, this);

            try
            {
                _save = Interface.Oxide.DataFileSystem.ReadObject<Save>(Name) ?? new Save();
                // Ensure collections are initialized
                _save.MutedPlayers ??= new Dictionary<string, int>();
                _save.Profanities ??= new HashSet<string>();
            }
            catch (Exception ex)
            {
                PrintError($"Error loading save data: {ex.Message}");
                _save = new Save();
            }
        }

        private void OnServerInitialized(bool initial)
        {
            if (_save != null)
                ManageTimer();
            else
                PrintError("Save data not initialized, timer not started");

            foreach (var player in BasePlayer.activePlayerList)
                OnPlayerConnected(player);
        }

        private void Unload() => ManageTimer(false);

        private void OnServerSave() => SaveData();

        private void OnPlayerConnected(BasePlayer player)
        {
            var uid = UnityEngine.Random.Range(1000, 9999).ToString();
            while (playerIds.ContainsKey(uid) || BasePlayer.activePlayerList.Any(p => p.displayName.Contains(uid)))
                uid = UnityEngine.Random.Range(1000, 9999).ToString();
            playerIds.TryAdd(uid, player);
        }

        private object? OnPlayerChat(BasePlayer? player, string message, Chat.ChatChannel channel)
        {
            if (string.IsNullOrEmpty(message) || player == null) return null;

            var color = GetPlayerChatColor(player);
            var newMessage = CheckText(player, message);
            if (string.IsNullOrEmpty(newMessage)) return true;

            if (!player.IsAdmin && CheckChatCooldown(player)) return true;

            var displayName = FormatDisplayName(player);
            var chatEntry = new Chat.ChatEntry
            {
                Channel = channel,
                Message = newMessage,
                UserId = player.userID.ToString(),
                Username = displayName,
                Color = color,
                Time = (int)DateTimeOffset.UtcNow.ToUnixTimeSeconds()
            };

            switch (channel)
            {
                case Chat.ChatChannel.Global:
                    if (_save!.MutedPlayers.TryGetValue(player.UserIDString, out var muteTime))
                    {
                        player.ChatMessage(muteTime == -1 ? "You are permanently muted from global chat." : $"You are muted from global chat for {muteTime} min(s).");
                        return true;
                    }
                    ConsoleNetwork.BroadcastToAllClients("chat.add2", 0, player.userID, newMessage, displayName, color, 1f);
                    break;

                case Chat.ChatChannel.Team:
                    if (RelationshipManager.ServerInstance.FindPlayersTeam(player.userID) is { } playerTeam)
                    {
                        var onlineConnections = playerTeam.GetOnlineMemberConnections();
                        if (onlineConnections != null) ConsoleNetwork.SendClientCommand(onlineConnections, "chat.add2", 1, player.userID, newMessage, displayName, color, 1f);
                        playerTeam.BroadcastTeamChat(player.userID, displayName, newMessage, color);
                    }
                    break;

                case Chat.ChatChannel.Cards:
                    if (player.isMounted && player.GetMountedVehicle() is CardTable { } table && table.GameController.PlayerIsInGame(player))
                    {
                        var connections = Facepunch.Pool.GetList<Network.Connection>();
                        table.GameController.GetConnectionsInGame(connections);
                        if (connections.Count > 0) ConsoleNetwork.SendClientCommand(connections, "chat.add2", 3, player.userID, newMessage, displayName, color, 1f);
                        Facepunch.Pool.FreeList(ref connections);
                    }
                    break;

                default: return null;
            }

            player.NextChatTime = UnityEngine.Time.realtimeSinceStartup + 1.5f;

            // Note: Chat.History CircularBuffer methods are not accessible in current API
            // Chat history is managed internally by Rust game engine

            RCon.Broadcast(RCon.LogType.Chat, chatEntry);
            return true;
        }
        #endregion

        #region Helpers
        private string GetPlayerChatColor(BasePlayer player)
        {
            try
            {
                // Check for special players first
                if (config?.SpecialPlayers?.TryGetValue(player.UserIDString, out var specialColor) == true)
                    return specialColor;

                // Check admin menu color override
                if (AwakenAdminMenu?.Call<string>("GetColor", player) is { } adminColor &&
                    !string.IsNullOrEmpty(adminColor) &&
                    AwakenAdminMenu.Call<bool>("InAdmInMode", player))
                    return adminColor;

                // Check configurable role colors with proper error handling
                if (config?.RoleColors != null && config.RoleColors.Count > 0)
                {
                    // Get all groups for the player once
                    var playerGroups = permission.GetUserGroups(player.UserIDString);
                    if (playerGroups != null && playerGroups.Length > 0)
                    {
                        // Check each role color configuration
                        foreach (var roleColor in config.RoleColors)
                        {
                            if (string.IsNullOrEmpty(roleColor.GroupName)) continue;

                            // Check if player has this group (case-insensitive)
                            if (playerGroups.Any(group => string.Equals(group, roleColor.GroupName, StringComparison.OrdinalIgnoreCase)))
                            {
                                if (!string.IsNullOrEmpty(roleColor.Color))
                                    return roleColor.Color;
                            }
                        }
                    }
                }

                // Return default color
                return config?.DefaultPlayerColor ?? "#55aaff";
            }
            catch (Exception ex)
            {
                PrintError($"Error getting player chat color for {player.displayName}: {ex.Message}");
                return config?.DefaultPlayerColor ?? "#55aaff";
            }
        }

        private bool CheckChatCooldown(BasePlayer player)
        {
            if (player.NextChatTime == 0f) player.NextChatTime = UnityEngine.Time.realtimeSinceStartup - 30f;
            if (player.NextChatTime <= UnityEngine.Time.realtimeSinceStartup) return false;

            player.NextChatTime += 2f;
            var nextChatTime = player.NextChatTime - UnityEngine.Time.realtimeSinceStartup;
            player.ChatMessage($"You're chatting too fast - try again in {(nextChatTime + 0.5f):F0} seconds");
            if (nextChatTime > 120f) player.Kick("Chatting too fast");
            return true;
        }

        /// <summary>
        /// Gets a clean player name without Steam IDs or unwanted additions
        /// </summary>
        private string GetCleanPlayerName(BasePlayer player)
        {
            if (player == null) return "Unknown";

            // Start with the original name, but if it's null/empty, use displayName and clean it aggressively
            string name = !string.IsNullOrEmpty(player._name) ? player._name : (player.displayName ?? "Unknown");

            // Remove Steam ID patterns in all formats
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\(76561198\d{9}\)", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\[76561198\d{9}\]", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"76561198\d{9}", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\(\d{17}\)", "").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\[\d{17}\]", "").Trim();

            // Remove clan tag patterns: [TAG] or (TAG) - but only if they look like clan tags (2-6 uppercase chars)
            name = System.Text.RegularExpressions.Regex.Replace(name, @"[\[\(][A-Z0-9]{2,6}[\]\)]", "").Trim();

            // Remove any remaining empty brackets or parentheses
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*\(\s*\)\s*", " ").Trim();
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s*\[\s*\]\s*", " ").Trim();

            // Clean up multiple spaces
            name = System.Text.RegularExpressions.Regex.Replace(name, @"\s+", " ").Trim();

            // If name is empty after cleaning, return "Unknown"
            return string.IsNullOrEmpty(name) ? "Unknown" : name;
        }

        private string FormatDisplayName(BasePlayer player)
        {
            // Use clean player name instead of potentially modified displayName
            var displayName = GetCleanPlayerName(player);
            var playerColor = GetPlayerChatColor(player);
            var clanTagColor = config?.ClanTagColor ?? "#ffa500";

            if (config?.EnableClanDebug == true)
                PrintWarning($"[Awaken Chat] DEBUG: FormatDisplayName for {player.displayName} -> clean name: {displayName}");

            var clanTag = GetPlayerClanTag(player);
            if (!string.IsNullOrEmpty(clanTag))
            {
                // Clan tag gets configurable color, username gets the player's assigned color
                displayName = $"<color={clanTagColor}>[{clanTag}]</color> <color={playerColor}>{displayName}</color>";

                if (config?.EnableClanDebug == true)
                    PrintWarning($"[Awaken Chat] DEBUG: Added clan tag '{clanTag}' to display name");
            }
            else
            {
                // No clan tag, just apply player color to username
                displayName = $"<color={playerColor}>{displayName}</color>";

                if (config?.EnableClanDebug == true)
                    PrintWarning($"[Awaken Chat] DEBUG: No clan tag found, using player color only");
            }

            if (config?.EnableClanDebug == true)
                PrintWarning($"[Awaken Chat] DEBUG: Final formatted name: '{displayName}'");

            return displayName;
        }

        private string GetPlayerClanTag(BasePlayer player)
        {
            try
            {
                if (AwakenClans == null)
                {
                    if (config?.EnableClanDebug == true)
                        PrintWarning($"[Awaken Chat] AwakenClans plugin is null for player {player.displayName}");
                    return string.Empty;
                }

                if (config?.EnableClanDebug == true)
                    PrintWarning($"[Awaken Chat] DEBUG: Getting clan tag for {player.displayName} (ID: {player.userID})");

                // Method 1: Try GetClan API and extract full name or tag based on config
                try
                {
                    var clanObj = AwakenClans.Call("GetClan", player.userID);
                    if (config?.EnableClanDebug == true)
                        PrintWarning($"[Awaken Chat] DEBUG: GetClan(ulong) returned: {(clanObj != null ? $"object of type {clanObj.GetType().Name}" : "null")}");
                    if (clanObj != null)
                    {
                        var clanType = clanObj.GetType();

                        // If configured to use full clan name, try to get ClanName first
                        if (config?.UseFullClanName == true)
                        {
                            var clanNameProperty = clanType.GetProperty("ClanName");
                            if (clanNameProperty != null)
                            {
                                var fullClanName = clanNameProperty.GetValue(clanObj) as string;
                                if (config?.EnableClanDebug == true)
                                    PrintWarning($"[Awaken Chat] DEBUG: Direct ClanName property: '{fullClanName}'");
                                if (!string.IsNullOrEmpty(fullClanName))
                                {
                                    if (config?.EnableClanDebug == true)
                                        PrintWarning($"[Awaken Chat] DEBUG: Method 1A SUCCESS (Full Name) - returning '{fullClanName}'");
                                    return fullClanName;
                                }
                            }
                        }

                        // Try to get the ClanTag property as fallback or primary (if not using full name)
                        var clanTagProperty = clanType.GetProperty("ClanTag");
                        if (clanTagProperty != null)
                        {
                            var directTag = clanTagProperty.GetValue(clanObj) as string;
                            if (config?.EnableClanDebug == true)
                                PrintWarning($"[Awaken Chat] DEBUG: Direct ClanTag property: '{directTag}'");
                            if (!string.IsNullOrEmpty(directTag))
                            {
                                if (config?.EnableClanDebug == true)
                                    PrintWarning($"[Awaken Chat] DEBUG: Method 1B SUCCESS (Tag) - returning '{directTag}'");
                                return directTag;
                            }
                        }

                        // Fallback to GetClanName API
                        var clanName = AwakenClans.Call("GetClanName", clanObj);
                        if (config?.EnableClanDebug == true)
                            PrintWarning($"[Awaken Chat] DEBUG: GetClanName API returned: '{clanName}' (Type: {clanName?.GetType().Name ?? "null"})");
                        if (clanName is string clanNameStr && !string.IsNullOrEmpty(clanNameStr))
                        {
                            if (config?.EnableClanDebug == true)
                                PrintWarning($"[Awaken Chat] DEBUG: Method 1C SUCCESS (API Name) - returning '{clanNameStr}'");
                            return clanNameStr;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (config?.EnableClanDebug == true)
                        PrintError($"[Awaken Chat] DEBUG: Method 1 failed: {ex.Message}");
                }

                // Method 2: Direct GetClanTag API call with ulong (fallback)
                try
                {
                    var result1 = AwakenClans.Call("GetClanTag", player.userID);
                    if (config?.EnableClanDebug == true)
                        PrintWarning($"[Awaken Chat] DEBUG: GetClanTag(ulong) returned: '{result1}' (Type: {result1?.GetType().Name ?? "null"})");
                    if (result1 is string clanTag1 && !string.IsNullOrEmpty(clanTag1))
                    {
                        if (config?.EnableClanDebug == true)
                            PrintWarning($"[Awaken Chat] DEBUG: Method 2 SUCCESS - returning '{clanTag1}'");
                        return clanTag1;
                    }
                }
                catch (Exception ex)
                {
                    if (config?.EnableClanDebug == true)
                        PrintError($"[Awaken Chat] DEBUG: Method 2 failed: {ex.Message}");
                }

                // Method 3: Try with string userID instead of ulong
                try
                {
                    var result2 = AwakenClans.Call("GetClanTag", player.UserIDString);
                    if (config?.EnableClanDebug == true)
                        PrintWarning($"[Awaken Chat] DEBUG: GetClanTag(string) returned: '{result2}' (Type: {result2?.GetType().Name ?? "null"})");
                    if (result2 is string clanTag2 && !string.IsNullOrEmpty(clanTag2))
                    {
                        if (config?.EnableClanDebug == true)
                            PrintWarning($"[Awaken Chat] DEBUG: Method 3 SUCCESS - returning '{clanTag2}'");
                        return clanTag2;
                    }
                }
                catch (Exception ex)
                {
                    if (config?.EnableClanDebug == true)
                        PrintError($"[Awaken Chat] DEBUG: Method 3 failed: {ex.Message}");
                }

                // Method 4: Try direct data access if enabled
                if (config?.UseDirectClanAccess == true)
                {
                    var directResult = GetClanTagDirectAccess(player);
                    if (!string.IsNullOrEmpty(directResult))
                    {
                        if (config?.EnableClanDebug == true)
                            PrintWarning($"[Awaken Chat] DEBUG: Direct access SUCCESS - returning '{directResult}'");
                        return directResult;
                    }
                }

                if (config?.EnableClanDebug == true)
                    PrintWarning($"[Awaken Chat] DEBUG: All methods failed for {player.displayName} - returning empty string");
                return string.Empty;
            }
            catch (Exception ex)
            {
                PrintError($"[Awaken Chat] Error getting clan tag for {player.displayName}: {ex.Message}");
                if (config?.EnableClanDebug == true)
                    PrintError($"[Awaken Chat] Stack trace: {ex.StackTrace}");
                return string.Empty;
            }
        }

        private string GetClanTagDirectAccess(BasePlayer player)
        {
            try
            {
                if (AwakenClans == null) return string.Empty;

                if (config?.EnableClanDebug == true)
                    PrintWarning($"[Awaken Chat] DEBUG: Trying direct access for {player.displayName}");

                // Try to access the plugin's internal data directly using reflection
                var pluginType = AwakenClans.GetType();

                // Look for clan cache or data fields
                var clanCacheField = pluginType.GetField("clanCache", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (clanCacheField != null)
                {
                    var clanCache = clanCacheField.GetValue(AwakenClans);
                    if (config?.EnableClanDebug == true)
                        PrintWarning($"[Awaken Chat] DEBUG: Found clanCache field: {clanCache?.GetType().Name ?? "null"}");

                    if (clanCache is System.Collections.IEnumerable enumerable)
                    {
                        foreach (var clan in enumerable)
                        {
                            if (clan == null) continue;

                            // Try to get clan members
                            var membersProperty = clan.GetType().GetProperty("ClanMemebers");
                            if (membersProperty?.GetValue(clan) is Dictionary<string, string> members)
                            {
                                if (members.ContainsKey(player.UserIDString))
                                {
                                    // Found the player in this clan

                                    // If configured to use full clan name, try to get ClanName first
                                    if (config?.UseFullClanName == true)
                                    {
                                        var nameProperty = clan.GetType().GetProperty("ClanName");
                                        if (nameProperty?.GetValue(clan) is string clanName && !string.IsNullOrEmpty(clanName))
                                        {
                                            if (config?.EnableClanDebug == true)
                                                PrintWarning($"[Awaken Chat] DEBUG: Direct access found full clan name: '{clanName}'");
                                            return clanName;
                                        }
                                    }

                                    // Fallback to clan tag
                                    var tagProperty = clan.GetType().GetProperty("ClanTag");
                                    if (tagProperty?.GetValue(clan) is string tag && !string.IsNullOrEmpty(tag))
                                    {
                                        if (config?.EnableClanDebug == true)
                                            PrintWarning($"[Awaken Chat] DEBUG: Direct access found clan tag: '{tag}'");
                                        return tag;
                                    }
                                }
                            }
                        }
                    }
                }

                // Try alternative field names
                var playerClansField = pluginType.GetField("playerClans", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (playerClansField != null)
                {
                    var playerClans = playerClansField.GetValue(AwakenClans);
                    if (config?.EnableClanDebug == true)
                        PrintWarning($"[Awaken Chat] DEBUG: Found playerClans field: {playerClans?.GetType().Name ?? "null"}");

                    if (playerClans is Dictionary<string, object> playerClanDict && playerClanDict.TryGetValue(player.UserIDString, out var clanInfo))
                    {
                        // If configured to use full clan name, try to get ClanName first
                        if (config?.UseFullClanName == true)
                        {
                            var nameProperty = clanInfo.GetType().GetProperty("ClanName");
                            if (nameProperty?.GetValue(clanInfo) is string clanName && !string.IsNullOrEmpty(clanName))
                            {
                                if (config?.EnableClanDebug == true)
                                    PrintWarning($"[Awaken Chat] DEBUG: Direct access via playerClans found full name: '{clanName}'");
                                return clanName;
                            }
                        }

                        // Fallback to clan tag
                        var tagProperty = clanInfo.GetType().GetProperty("ClanTag");
                        if (tagProperty?.GetValue(clanInfo) is string tag && !string.IsNullOrEmpty(tag))
                        {
                            if (config?.EnableClanDebug == true)
                                PrintWarning($"[Awaken Chat] DEBUG: Direct access via playerClans found tag: '{tag}'");
                            return tag;
                        }
                    }
                }

                if (config?.EnableClanDebug == true)
                    PrintWarning($"[Awaken Chat] DEBUG: Direct access failed - no clan data found");
                return string.Empty;
            }
            catch (Exception ex)
            {
                if (config?.EnableClanDebug == true)
                    PrintError($"[Awaken Chat] DEBUG: Direct access exception: {ex.Message}");
                return string.Empty;
            }
        }
        #endregion
    }
}










