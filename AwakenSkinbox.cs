using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Game.Rust.Cui;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Skinbox", "Skelee", "1.1.2")]
    [Description("Allows you to reskin items by placing them in the skinbox and selecting a new skin!")]
    public class AwakenSkinbox : CovalencePlugin
    {
        #region Fields
        private readonly Dictionary<string, HashSet<ulong>> _skinList = new();
        private readonly Dictionary<ulong, string> _skinNameLookup = new();
        private bool _skinsLoaded;
        private SortBy _sorting;
        private int _newSkins; // Track new skins added
        private bool _hasChanged; // Track config changes

        private static AwakenSkinbox? Instance;
        private static Func<string, ulong, string>? GetMessage;
        public enum SortBy { Config, ConfigReversed, Alphabetical }

        private readonly Dictionary<string, SBInfo> activeSkinboxes = new();

        // Mapping of skin titles to Rust item shortnames
        private readonly Dictionary<string, string> _skinTitleToShortname = new()
        {
            { "Minecart Planter", "minecartplanter" },
            { "Bath Tub Planter", "bathtubplanter" },
            { "Triangle Rail Road Planter", "trianglerailroadplanter" }
        };
        #endregion

        #region Classes
        private class SBInfo
        {
            public Item? Item;
            public int Page;
            public int MaxPage;
            public List<ulong> Skins = new();
            public bool CustomSkinbox;
        }

        private class UI4
        {
            public float xMin, yMin, xMax, yMax;
            public UI4(float xmin, float ymin, float xmax, float ymax) => (xMin, yMin, xMax, yMax) = (xmin, ymin, xmax, ymax);
            public static UI4 Full => new(0, 0, 1, 1);
            public UI4 Padding(float padding) => new(xMin + padding, yMin + padding, xMax - padding, yMax - padding);
        }
        #endregion

        #region Hooks
        private void Loaded()
        {
            Instance = this;
            if (Configuration?.Permissions != null)
            {
                permission.RegisterPermission(Configuration.Permissions.UseSkinbox, this);
            }
            _sorting = Configuration?.Skins != null && Enum.TryParse<SortBy>(Configuration.Skins.Sorting, true, out var sort) ? sort : SortBy.Config;
            GetMessage = (key, userId) => lang.GetMessage(key, this, userId.ToString());

            if (Configuration != null)
            {
                foreach (var cmd in Configuration.SkinboxCommands) AddCovalenceCommand(cmd, nameof(CmdSkinBox));
                foreach (var cmd in Configuration.CustomSkinboxCommands) AddCovalenceCommand(cmd, nameof(CmdAwakenSkinBox));
            }
        }

        protected override void LoadDefaultMessages() => lang.RegisterMessages(new Dictionary<string, string>
        {
            ["NoAPIKey"] = "The server owner has not entered a Steam API key in the config. Unable to continue!",
            ["SkinsLoading"] = "SkinBox is still gathering skins. Please try again soon",
            ["NoPermission"] = "You don't have permission to use the SkinBox",
            ["ToNearPlayer"] = "The SkinBox is currently not usable at this place"
        }, this);

        private void OnServerInitialized()
        {
            if (string.IsNullOrEmpty(Configuration?.Skins.SteamAPIKey))
            {
                PrintWarning("No Steam API key has been set in the config. Unable to load skins!");
                return;
            }

            if (Configuration?.Skins.LoadApprovedSkins == true)
            {
                ServerMgr.Instance.StartCoroutine(LoadApprovedSkins());
            }
            else
            {
                _skinsLoaded = true;
            }
        }

        private void Unload()
        {
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, UI_PANEL);
                CuiHelper.DestroyUi(player, UI_POPUP);
            }
            activeSkinboxes.Clear();
        }

        private void OnLootEntity(BasePlayer player, BaseEntity entity)
        {
            if (player == null || entity is not StorageContainer container || container.ShortPrefabName != "box.wooden.large" || container.OwnerID != 0 || player.inventory.loot.entitySource != container)
                return;
            MakeSkinbox(player);
        }

        private void OnLootEntityEnd(BasePlayer player, BaseCombatEntity entity)
        {
            if (player == null || entity is not StorageContainer container || container.ShortPrefabName != "box.wooden.large" || container.OwnerID != 0)
                return;
            activeSkinboxes.Remove(player.UserIDString);
        }
        #endregion

        #region Functions
        private IEnumerator LoadApprovedSkins()
        {
            _newSkins = 0;
            _hasChanged = false;
            yield return SendWorkshopQuery(0, 0);
            if (_hasChanged && Configuration != null)
            {
                SaveConfig();
            }

            SortSkinLists();
            _skinsLoaded = true;
            PrintWarning($"[Awaken Skinbox] Loaded {_skinList.Values.Sum(x => x.Count)} skins across {_skinList.Count} items. New skins added: {_newSkins}");
        }

        private IEnumerator SendWorkshopQuery(int page, int success, ConsoleSystem.Arg? arg = null, string? perm = null)
        {
            if (Configuration == null)
            {
                PrintError("Configuration is null, cannot load skins.");
                yield break;
            }

            var url = $"https://api.steampowered.com/IPublishedFileService/QueryFiles/v1/?key={Configuration.Skins.SteamAPIKey}&format=json&query_type=1&page={page}&numperpage=100&appid=252490&return_tags=true&return_metadata=true";
            var www = new WWW(url);
            yield return www;

            if (string.IsNullOrEmpty(www.text) || www.text.Contains("error"))
            {
                PrintWarning($"[Awaken Skinbox] Error loading skins: {www.text}");
                yield break;
            }

            var response = JsonConvert.DeserializeObject<SteamResponse>(www.text);
            if (response?.response?.publishedfiledetails == null)
            {
                PrintWarning($"[Awaken Skinbox] Error parsing skin response: {www.text}");
                yield break;
            }

            int totalPages = (int)Math.Ceiling(response.response.total / 100.0);
            foreach (var item in response.response.publishedfiledetails)
            {
                if (item.result != 1 || item.consumer_app_id != 252490 || !item.tags.Any(t => t.tag == "Skin") || !ulong.TryParse(item.publishedfileid, out var skinId))
                    continue;

                success++;
                // Try to extract shortname from tags
                string? shortname = item.tags.FirstOrDefault(t => t.tag.StartsWith("itemname:"))?.tag.Substring(9);
                if (string.IsNullOrEmpty(shortname))
                {
                    // Fallback: Extract shortname from title
                    string title = item.title ?? item.file_description ?? "";
                    // [DEBUG] Log skin title and tags - REMOVE AFTER TESTING
                    PrintWarning($"[DEBUG] Processing skin: Title: {title}, Tags: {string.Join(", ", item.tags.Select(t => t.tag))}");
                    
                    // Map title to shortname
                    shortname = _skinTitleToShortname.FirstOrDefault(kvp => title.StartsWith(kvp.Key, StringComparison.OrdinalIgnoreCase)).Value;
                    if (string.IsNullOrEmpty(shortname))
                    {
                        // [DEBUG] Log skipped skin - REMOVE AFTER TESTING
                        PrintWarning($"[DEBUG] Skipped skin ID {skinId}: Could not determine shortname for '{title}'.");
                        continue;
                    }
                }

                var itemDefinition = ItemManager.FindItemDefinition(shortname);
                if (itemDefinition == null)
                {
                    // [DEBUG] Log invalid item definition - REMOVE AFTER TESTING
                    PrintWarning($"[DEBUG] Skipped skin ID {skinId}: Invalid item definition for shortname '{shortname}'.");
                    continue;
                }

                // [DEBUG] Log successful mapping - REMOVE AFTER TESTING
                PrintWarning($"[DEBUG] Mapped skin ID {skinId} to item '{itemDefinition.shortname}' (Title: {item.title})");

                var list = new List<ulong>();
                if (!Configuration.SkinList.TryGetValue(itemDefinition.shortname, out var configSkins))
                {
                    configSkins = new();
                    Configuration.SkinList[itemDefinition.shortname] = configSkins;
                    _hasChanged = true;
                }

                if (!configSkins.Contains(skinId))
                {
                    configSkins.Add(skinId);
                    _newSkins++;
                    _hasChanged = true;
                    list.Add(skinId);
                    _skinNameLookup[skinId] = item.title;
                }

                if (list.Count > 0)
                {
                    var skins = _skinList.GetValueOrDefault(itemDefinition.shortname) ?? (_skinList[itemDefinition.shortname] = new());
                    int removeCount = 0;

                    foreach (var skin in list)
                    {
                        if (Configuration.Skins.RemoveApproved && Configuration.SkinList.TryGetValue(itemDefinition.shortname, out var configSkins2) && configSkins2.Remove(skin))
                        {
                            removeCount++;
                            _hasChanged = true;
                        }
                        skins.Add(skin);
                    }

                    if (removeCount > 0)
                    {
                        PrintWarning($"[Awaken Skinbox] Removed {removeCount} approved skin IDs for {itemDefinition.shortname} from config");
                    }
                }
            }

            yield return CoroutineEx.waitForEndOfFrame;
            yield return CoroutineEx.waitForEndOfFrame;

            if (page < totalPages)
            {
                yield return SendWorkshopQuery(page + 1, success, arg, perm);
            }
        }

        private void SortSkinLists()
        {
            if (Configuration == null) return;

            foreach (var (shortname, skins) in _skinList)
            {
                var sortedSkins = _sorting switch
                {
                    SortBy.Config => Configuration.SkinList.TryGetValue(shortname, out var configSkins) ? configSkins.Where(skins.Contains).ToList() : new(),
                    SortBy.ConfigReversed => Configuration.SkinList.TryGetValue(shortname, out var configSkins) ? configSkins.Where(skins.Contains).Reverse().ToList() : new(),
                    SortBy.Alphabetical => skins.OrderBy(s => _skinNameLookup.TryGetValue(s, out var name) ? name : "").ToList(),
                    _ => new()
                };

                skins.Clear();
                foreach (var skin in sortedSkins) skins.Add(skin);
            }
        }

        private void MakeSkinbox(BasePlayer player, bool customSkinbox = false)
        {
            if (player == null) return;
            if (!_skinsLoaded)
            {
                player.ChatMessage(GetMessage?.Invoke("SkinsLoading", player.userID) ?? "Skins are loading.");
                return;
            }
            if (!permission.UserHasPermission(player.UserIDString, Configuration?.Permissions.UseSkinbox ?? "awakenskinbox.use"))
            {
                player.ChatMessage(GetMessage?.Invoke("NoPermission", player.userID) ?? "No permission.");
                return;
            }
            if (!customSkinbox && player.Distance(player.inventory.loot.entitySource?.transform.position ?? Vector3.zero) > 3f)
            {
                player.ChatMessage(GetMessage?.Invoke("ToNearPlayer", player.userID) ?? "Too far from skinbox.");
                return;
            }

            var container = player.inventory.loot.containers.FirstOrDefault();
            if (container == null) return;

            var item = container.itemList.FirstOrDefault();
            if (item == null) return;

            if (!_skinList.TryGetValue(item.info.shortname, out var skins) || skins.Count == 0) return;

            var info = activeSkinboxes.GetValueOrDefault(player.UserIDString) ?? (activeSkinboxes[player.UserIDString] = new SBInfo());
            info.Item = item;
            info.Page = 0;
            info.Skins = skins.ToList();
            info.MaxPage = (int)Math.Ceiling(skins.Count / 30.0) - 1;
            info.CustomSkinbox = customSkinbox;

            DrawSkinboxUI(player);
        }

        private void DrawSkinboxUI(BasePlayer player)
        {
            if (player == null || !activeSkinboxes.TryGetValue(player.UserIDString, out var info) || info.Item == null) return;

            CuiHelper.DestroyUi(player, UI_PANEL);
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{Container.xMin} {Container.yMin}", AnchorMax = $"{Container.xMax} {Container.yMax}" },
                Image = { Color = "0.1 0.1 0.1 0.95" },
                CursorEnabled = true
            }, "Overlay", UI_PANEL);

            int startIndex = info.Page * 30;
            int endIndex = Math.Min(startIndex + 30, info.Skins.Count);

            for (int i = startIndex; i < endIndex; i++)
            {
                int index = i - startIndex;
                int row = index / 6;
                int col = index % 6;

                var skinId = info.Skins[i];
                var position = new UI4(0.02f + (col * 0.16f), 0.82f - (row * 0.16f), 0.16f + (col * 0.16f), 0.96f - (row * 0.16f));
                var button = UI.Button(container, UI_PANEL, "", BUTTON_COLOR, position);

                UI.Image(container, UI_PANEL, $"https://steamcommunity-a.akamaihd.net/economy/image/{GetIconURL(info.Item.info.itemid, skinId)}", position.Padding(0.01f));
                UI.Button(container, UI_PANEL, _skinNameLookup.TryGetValue(skinId, out var name) ? name : skinId.ToString(), BUTTON_TEXT_COLOR, Text, $"skinbox.select {skinId}");
            }

            if (info.MaxPage > 0)
            {
                var pagePanel = UI.Panel(container, UI_PANEL, PAGE_COLOR, new UI4(0.02f, 0.02f, 0.98f, 0.1f));
                UI.Label(container, pagePanel, $"Page {info.Page + 1}/{info.MaxPage + 1}", PAGE_TEXT_COLOR, 14, UI4.Full, TextAnchor.MiddleCenter);

                if (info.Page > 0)
                    UI.Button(container, pagePanel, "Previous", BUTTON_TEXT_COLOR, BackButton, "skinbox.pageprev");

                if (info.Page < info.MaxPage)
                    UI.Button(container, pagePanel, "Next", BUTTON_TEXT_COLOR, NextButton, "skinbox.pagenext");
            }

            CuiHelper.AddUi(player, container);
        }

        private string GetIconURL(int itemId, ulong skinId) => skinId > 0 ? $"https://steamcommunity-a.akamaihd.net/economy/image/class/252490/{itemId}/{skinId}/512fx512f" : string.Empty;

        private void ChangePage(BasePlayer player, int direction)
        {
            if (player == null || !activeSkinboxes.TryGetValue(player.UserIDString, out var info)) return;
            info.Page = Mathf.Clamp(info.Page + direction, 0, info.MaxPage);
            DrawSkinboxUI(player);
        }

        internal void PopupMessage(BasePlayer player, string message)
        {
            if (player == null) return;
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                RectTransform = { AnchorMin = $"{Popup.xMin} {Popup.yMin}", AnchorMax = $"{Popup.xMax} {Popup.yMax}" },
                Image = { Color = "0 0 0 0.8" },
                CursorEnabled = true
            }, "Overlay", UI_POPUP);
            UI.Label(container, UI_POPUP, message, BUTTON_TEXT_COLOR, 15, UI4.Full, TextAnchor.UpperRight);

            CuiHelper.DestroyUi(player, UI_POPUP);
            CuiHelper.AddUi(player, container);
            player.Invoke(() => CuiHelper.DestroyUi(player, UI_POPUP), 5f);
        }
        #endregion

        #region Commands
        [Command("skinbox.select")]
        private void CmdSelectSkin(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || args.Length == 0 || !ulong.TryParse(args[0], out var skinId)) return;
            var player = (BasePlayer)iPlayer.Object;
            if (player == null) return;

            if (!activeSkinboxes.TryGetValue(player.UserIDString, out var info) || info.Item == null) return;
            if (!info.Skins.Contains(skinId)) return;

            info.Item.skin = skinId;
            info.Item.MarkDirty();

            if (info.CustomSkinbox)
            {
                player.EndLooting();
                PopupMessage(player, "Item skin changed successfully!");
            }
        }

        [Command("skinbox")]
        private void CmdSkinBox(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || ((BasePlayer)iPlayer.Object)?.inventory.loot.IsLooting() == true) return;
            var player = (BasePlayer)iPlayer.Object;
            if (player == null) return;
            timer.Once(1f, () => MakeSkinbox(player));
        }

        [Command("oskinbox")]
        private void CmdAwakenSkinBox(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || ((BasePlayer)iPlayer.Object)?.inventory.loot.IsLooting() == true) return;
            var player = (BasePlayer)iPlayer.Object;
            if (player == null) return;
            timer.Once(1f, () => MakeSkinbox(player, true));
        }

        [Command("skinbox.pagenext")]
        private void CmdPageNext(IPlayer iPlayer, string command, string[] args)
        {
            var player = (BasePlayer)iPlayer.Object;
            if (player != null) ChangePage(player, 1);
        }

        [Command("skinbox.pageprev")]
        private void CmdPagePrev(IPlayer iPlayer, string command, string[] args)
        {
            var player = (BasePlayer)iPlayer.Object;
            if (player != null) ChangePage(player, -1);
        }
        #endregion

        #region Config
        private record SkinInfo(string SkinName = "", ulong SkinID = 0);
        private static ConfigData? Configuration;

        private record ConfigData(
            [property: JsonProperty("Permissions")] PermissionSettings Permissions,
            [property: JsonProperty("Skins")] SkinSettings Skins,
            [property: JsonProperty("SkinList")] Dictionary<string, List<ulong>> SkinList,
            [property: JsonProperty("SkinBox Commands")] List<string> SkinboxCommands,
            [property: JsonProperty("Custom SkinBox Commands")] List<string> CustomSkinboxCommands)
        {
            public ConfigData() : this(new(), new(), new(), new() { "skinbox" }, new() { "oskinbox" }) { }
        }

        private record PermissionSettings(
            [property: JsonProperty("Use SkinBox")] string UseSkinbox = "awakenskinbox.use");

        private record SkinSettings(
            [property: JsonProperty("Steam API Key")] string SteamAPIKey = "",
            [property: JsonProperty("Load Approved Skins")] bool LoadApprovedSkins = true,
            [property: JsonProperty("Remove Approved Skins From Config")] bool RemoveApproved = true,
            [property: JsonProperty("Sorting")] string Sorting = "Config");

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                Configuration = Config.ReadObject<ConfigData>();
                if (Configuration == null)
                {
                    PrintWarning("Configuration is null, loading default config.");
                    Configuration = LoadDefaultConfig();
                }
            }
            catch (Exception e)
            {
                PrintError($"Failed to load config: {e.Message}");
                Configuration = LoadDefaultConfig();
            }
            SaveConfig();
        }

        private ConfigData LoadDefaultConfig()
        {
            var defaultConfig = new ConfigData();
            Config.WriteObject(defaultConfig, true);
            PrintWarning("Created new default configuration file.");
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(Configuration, true);
        #endregion

        #region UI
        private const string UI_PANEL = "SkinBox_UI";
        private const string UI_POPUP = "SkinBox_Popup";
        private const string PAGE_COLOR = "0.65 0.65 0.65 0.06";
        private const string PAGE_TEXT_COLOR = "0.7 0.7 0.7 1.0";
        private const string BUTTON_COLOR = "0.75 0.75 0.75 0.1";
        private const string BUTTON_TEXT_COLOR = "0.77 0.68 0.68 1";

        private readonly UI4 Popup = new(0.65f, 0.8f, 0.99f, 0.99f);
        private readonly UI4 Container = new(0.9505f, 0.15f, 0.99f, 0.6f);
        private readonly UI4 BackButton = new(0f, 0.7f, 1f, 1f);
        private readonly UI4 Text = new(0f, 0.3f, 1f, 0.7f);
        private readonly UI4 NextButton = new(0f, 0f, 1f, 0.3f);

        private static class UI
        {
            public static string Panel(CuiElementContainer container, string parent, string color, UI4 dimensions)
            {
                var name = CuiHelper.GetGuid();
                container.Add(new CuiPanel
                {
                    RectTransform = { AnchorMin = $"{dimensions.xMin} {dimensions.yMin}", AnchorMax = $"{dimensions.xMax} {dimensions.yMax}" },
                    Image = { Color = color }
                }, parent, name);
                return name;
            }

            public static string Button(CuiElementContainer container, string parent, string text, string textColor, UI4 dimensions, string command = "")
            {
                var name = CuiHelper.GetGuid();
                container.Add(new CuiButton
                {
                    RectTransform = { AnchorMin = $"{dimensions.xMin} {dimensions.yMin}", AnchorMax = $"{dimensions.xMax} {dimensions.yMax}" },
                    Button = { Command = command, Color = "0 0 0 0" },
                    Text = { Text = text, FontSize = 12, Align = TextAnchor.MiddleCenter, Color = textColor }
                }, parent, name);
                return name;
            }

            public static void Label(CuiElementContainer container, string parent, string text, string textColor, int size, UI4 dimensions, TextAnchor align)
            {
                container.Add(new CuiLabel
                {
                    RectTransform = { AnchorMin = $"{dimensions.xMin} {dimensions.yMin}", AnchorMax = $"{dimensions.xMax} {dimensions.yMax}" },
                    Text = { Text = text, FontSize = size, Align = align, Color = textColor }
                }, parent);
            }

            public static void Image(CuiElementContainer container, string parent, string url, UI4 dimensions)
            {
                container.Add(new CuiElement
                {
                    Parent = parent,
                    Components =
                    {
                        new CuiRawImageComponent { Url = url, Color = "1 1 1 1" },
                        new CuiRectTransformComponent { AnchorMin = $"{dimensions.xMin} {dimensions.yMin}", AnchorMax = $"{dimensions.xMax} {dimensions.yMax}" }
                    }
                });
            }
        }
        #endregion

        #region Steam API
        private readonly List<ulong> _skinsToVerify = new();

        private class SteamResponse
        {
            public ResponseData? response { get; set; }
            public class ResponseData
            {
                public int total { get; set; }
                public List<PublishedFileDetails>? publishedfiledetails { get; set; }
            }
            public class PublishedFileDetails
            {
                public int result { get; set; }
                public string? publishedfileid { get; set; }
                public int consumer_app_id { get; set; }
                public string? title { get; set; }
                public string? file_description { get; set; }
                public string? preview_url { get; set; }
                public List<Tag>? tags { get; set; }
            }
            public class Tag
            {
                public string? tag { get; set; }
            }
        }
        #endregion
    }
}









