using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using System.Globalization;
using UnityEngine;
using Newtonsoft.Json.Linq;
using Oxide.Core.Configuration;
using Oxide.Core.Libraries;
using Oxide.Plugins;
using System;
using System.Collections.Generic;
using System.Linq;
using Facepunch.Math;
using UnityEngine.AI;
using Rust;
using Time = UnityEngine.Time;
using System.Text.RegularExpressions;
using System.Text;
using UnityEngine.UI;
using Facepunch;
using System.Collections;
using UnityEngine.UIElements;
using System.Net;
using Rust.UI;
using Newtonsoft.Json.Bson;
using ProtoBuf;
using ConVar;
using Oxide.Game.Rust.Libraries;

namespace Oxide.Plugins
{
    [Info("Awaken Raid Bubble", "Skelee", "2.0.1")]
    [Description("Raid bubble system for Awaken Servers")]
    public class AwakenRaidBubble : RustPlugin
    {
        #region Varibales

        [PluginReference]
        private Plugin AwakenClans, Vanish;

        private static AwakenRaidBubble ins;
        private const string AdminPerm = "RaidBubble.admin";

        private bool Debug = false;
        private Dictionary<string, RaidBubbleComp> RaidBubblesRunning = new();

        private Dictionary<string, string> _cachedImages = new Dictionary<string, string>();
        private readonly Dictionary<string, string> imageUrls = new()
        {
            { "RaidBubble.checkpoint", "https://cdn.awakenrust.com/raid_bar_arrow_oasis1.png" },
            { "RaidBubble.timeicon", "https://cdn.awakenrust.com/time.png" },
            { "RaidBubble.mapicon", "https://cdn.awakenrust.com/marker.png" },
            { "RaidBubble.rightarrow", "https://cdn.awakenrust.com/right.png" },
            { "RaidBubble.leftarrow", "https://cdn.awakenrust.com/left.png" },
            { "RaidBubble.circle", "https://cdn.awakenrust.com/circle.png" },
        };

        private List<GracePeriod> GraceZone = new();
        private Dictionary<string, List<SphereEntity>> cachedSpheres = new();

        #endregion

        #region Config

        private Configuration config;

        private class DiscordSetting
        {
            [JsonProperty("Webhook URL")]
            public string WebhookURL { get; set; }

            [JsonProperty("WinMessage")]
            public string WinMessage { get; set; }

            [JsonProperty("LoseMessage")]
            public string LoseMessage { get; set; }

            [JsonProperty("Use Rich Embeds")]
            public bool UseRichEmbeds { get; set; } = true;

            [JsonProperty("Embed Colors")]
            public EmbedColors Colors { get; set; } = new EmbedColors();
        }

        private class EmbedColors
        {
            [JsonProperty("Raid Started")] public int RaidStarted { get; set; } = 0xFF6B6B; // Red
            [JsonProperty("Raid Won")] public int RaidWon { get; set; } = 0x51CF66; // Green
            [JsonProperty("Raid Defended")] public int RaidDefended { get; set; } = 0x339AF0; // Blue
        }

        private class DemoSettings
        {
            [JsonProperty("Enable Demo Recording")]
            public bool EnableDemoRecording { get; set; } = true;

            [JsonProperty("CDN Upload URL")]
            public string CDNUploadURL { get; set; } = "https://your-cdn-upload-endpoint.com/upload";

            [JsonProperty("CDN API Key")]
            public string CDNApiKey { get; set; } = "YOUR_CDN_API_KEY_HERE";

            [JsonProperty("Demo Quality")]
            public int DemoQuality { get; set; } = 30; // FPS for demo recording

            [JsonProperty("Demo Duration Limit (minutes)")]
            public int DemoDurationLimit { get; set; } = 30; // Max demo length
        }

        private class Configuration
        {
            [JsonProperty("Raid Bubble timer in seconds")]
            public int RaidBubbleTimer { get; set; }

            [JsonProperty("Raid Bubble area radius")]
            public float RaidBubbleRadius { get; set; }

            [JsonProperty("Raid Bubble raider hold timer in seconds")]
            public int RaidBubbleHoldTimer { get; set; }

            [JsonProperty("Raid Bubble check points", ObjectCreationHandling = ObjectCreationHandling.Replace)]
            public Dictionary<int, int> checkpoints { get; set; }

            [JsonProperty("Raid Bubble grace timer in seconds")]
            public int RaidBubbleGraceTimer { get; set; }

            [JsonProperty("Raid Bubble grace radius")]
            public int RaidBubbleGraceRadius { get; set; }

            [JsonProperty("Explosive Tracking Radius")]
            public float ExplosiveTrackingRadius { get; set; }

            [JsonProperty("Enable Explosive Tracking")]
            public bool EnableExplosiveTracking { get; set; }

            [JsonProperty("Rocket Threshold to Start Tracking")]
            public int RocketThresholdForTracking { get; set; }

            [JsonProperty("Discord Setting")]
            public DiscordSetting discordSetting { get; set; }

            [JsonProperty("Demo Recording Settings")]
            public DemoSettings demoSettings { get; set; }

            public VersionNumber Version { get; set; }
        }

        private Configuration GetBaseConfig()
        {
            return new Configuration
            {
                RaidBubbleTimer = 300,
                RaidBubbleRadius = 100,
                RaidBubbleHoldTimer = 1800,
                checkpoints = new Dictionary<int, int>
                {
                    { 300, 300 },
                    { 600, 300 },
                    { 900, 300 }
                },
                RaidBubbleGraceTimer = 600,
                RaidBubbleGraceRadius = 150,
                ExplosiveTrackingRadius = 200f,
                EnableExplosiveTracking = true,
                RocketThresholdForTracking = 5,
                discordSetting = new DiscordSetting()
                {
                    WebhookURL = "",
                    WinMessage = "{0} team has successfully raided {1} team in {2}.",
                    LoseMessage = "{0} team has successfully defended from {1} team.",
                    UseRichEmbeds = true,
                    Colors = new EmbedColors()
                },
                demoSettings = new DemoSettings(),
                Version = Version
            };
        }

        protected override void LoadDefaultConfig()
            => config = GetBaseConfig();

        protected override void LoadConfig()
        {
            base.LoadConfig();
            config = Config.ReadObject<Configuration>();

            if (config.Version < Version)
                UpdateConfigValues();

            Config.WriteObject(config, true);
        }

        protected override void SaveConfig()
            => Config.WriteObject(config, true);

        private void UpdateConfigValues()
        {
            PrintWarning("Your config file is outdated! Updating config values...");

            config.Version = Version;
            PrintWarning("Config updated!");
        }

        #endregion

        #region Data

        private SavedData _savedData;

        private class SavedData
        {
            public Dictionary<string, RaidBubbleSetup> RaidBubbles = new Dictionary<string, RaidBubbleSetup>();
            public Dictionary<string, HashSet<uint>> DestroyedTCs = new Dictionary<string, HashSet<uint>>();
        }

        private class RaidBubbleSetup
        {
            public Vector3 Position { get; set; }
            public float Radius { get; set; }
            public bool Active { get; set; } = false;
            public string DefenderClanName { get; set; }
            public string AttackerClanName { get; set; }
            public List<ulong> DefenderClanIds { get; set; } = new List<ulong>();
            public List<ulong> AttackerClanIds { get; set; } = new List<ulong>();

        }

        private void LoadSavedData()
        {
            _savedData = Interface.Oxide.DataFileSystem.ReadObject<SavedData>($"{Name}/RaidBubble");
            if (_savedData == null)
            {
                _savedData = new SavedData();
            }
        }

        private void SaveSavedData()
        {
            Interface.Oxide.DataFileSystem.WriteObject($"{Name}/RaidBubble", _savedData);
        }

        private class GracePeriod
        {
            public string Name;
            public Vector3 Postion;
            public float Radius;
            public int time;
            public Timer timer;
            public float StartTime;
            public List<SphereEntity> Spheres = new List<SphereEntity>();

            public void CreateDome()
            {
                StartTime = Time.realtimeSinceStartup;

                for (int i = 0; i < 3; i++)
                {
                    BaseEntity entity = GameManager.server.CreateEntity("assets/bundled/prefabs/modding/events/twitch/br_sphere_purple.prefab", Postion, new Quaternion(), true);
                    SphereEntity redSphere = entity.GetComponent<SphereEntity>();
                    redSphere.currentRadius = Radius * 2;
                    redSphere.lerpSpeed = 0f;
                    redSphere.lerpRadius = redSphere.currentRadius;
                    entity.Spawn();
                    BaseEntity ent = GameManager.server.CreateEntity("assets/prefabs/visualization/sphere.prefab", Postion, new Quaternion(), true);
                    SphereEntity bubble = ent.GetComponent<SphereEntity>();
                    bubble.currentRadius = Radius * 2;
                    bubble.lerpSpeed = 0f;
                    ent.Spawn();
                    Spheres.Add(bubble);
                    Spheres.Add(redSphere);
                }
            }

            public int GetRemainingTime()
            {
                float elapsedTime = Time.realtimeSinceStartup - StartTime;
                int remaining = Mathf.Max(0, time - Mathf.FloorToInt(elapsedTime));
                return remaining;
            }

            public void DestoryDome()
            {
                for (int i = Spheres.Count - 1; i >= 0; i--)
                {
                    var ent = Spheres[i];
                    if (ent != null)
                    {
                        ent.Kill();
                        Spheres.RemoveAt(i);
                    }
                }
            }


            public void DestoryTimer()
            {
                timer?.Destroy();
            }
        }

        #endregion

        #region Lang

        private static string GetMessage(string message, params object[] args)
        {
            return string.Format(ins.lang.GetMessage(message, ins), args);
        }

        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["Title"] = "<color=#9CFF1E>awaken</color>: ",
                ["StartingRaidBubble"] = "<color=red>{0}</color> team has started a raid on <color=blue>{1}</color> team.",
                ["RaidBubbleSuccessfullRaid"] = "<color=red>{0}</color> team has successfully raided <color=blue>{1}</color> team in <color=green>{2}</color> amount of time.",
                ["RaidBubbleDefenderRaid"] = "<color=blue>{0}</color> team has successfully defended from <color=red>{1}</color> team.",
                ["GracePeriodStarted"] = "Grace period has started in this area, building can't be damaged now.",
                ["GracePeriodEnded"] = "Grace period has ended.",
                ["GracePeriodNoDamage"] = "You can't damage buildings during grace period.",
                ["GraceNoActive"] = "<color=#DBE2E9>There are no active grace periods.</color>",
                ["GraceListStart"] = "<color=#DBE2E9>Active Grace Periods:</color>",
                ["GraceList"] = "<color=#DBE2E9>Grace period active at:</color> <color=#9CFF1E>{0}</color> | Time remaining: <color=#FFA500>{1}</color>",
                ["RaidBubbleStartDiscord"] = "{0} team has started a raid on {1} team.",
            }, this);
        }

        #endregion

        #region Commands

        [ChatCommand("rb_stop")]
        private void CmdRBStart(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length == 0)
            {
                SendReply(player, "Syntax: /rb_stop <BubbleName>");
                return;
            }

            string bubbleName = args[0];

            if (string.IsNullOrEmpty(bubbleName))
            {
                SendReply(player, "Syntax: /rb_stop <BubbleName>");
                return;
            }

            if (RaidBubblesRunning.TryGetValue(bubbleName, out var bubblesRunning))
            {
                bubblesRunning.TryForceStop();
            }
            else
            {
                SendReply(player, "No bubble running with that name.");
                return;
            }
        }

        [ChatCommand("setbubble")]
        private void CmdSetBubble(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length == 0)
            {
                SendReply(player, "Syntax: /setbubble <Radius> <ClanName>");
                return;
            }

            if (args.Length < 2)
            {
                SendReply(player, "Syntax: /setbubble <Radius> <ClanName>");
                return;
            }
            if (!float.TryParse(args[0], out float radius))
            {
                SendReply(player, "Invalid radius.");
                return;
            }
            string clanName = args[1];

            if (_savedData.RaidBubbles.TryGetValue(clanName, out var existingBubble))
            {
                // Update existing bubble
                SendReply(player, "Updating existing Raid Bubble...");

                // Remove old detection zone if it exists
                if (RaidBubblesRunning.ContainsKey(clanName))
                {
                    var oldZone = RaidBubblesRunning[clanName];
                    RaidBubblesRunning.Remove(clanName);
                    UnityEngine.Object.Destroy(oldZone.gameObject);
                }

                existingBubble.Position = player.transform.position;
                existingBubble.Radius = radius;

                // Create new detection zone
                CreateDetectionZone(existingBubble);

                player.SendConsoleCommand("ddraw.sphere", 10, Color.blue, player.transform.position, radius);
                SendReply(player, $"Raid Bubble updated for {clanName}");
                SendReply(player, $"Detection radius: {radius * 2}m, Raid bubble radius: {radius}m");
                SaveSavedData();
            }
            else
            {
                // Calculate center position of clan's base
                Vector3 centerPosition = CalculateClanBaseCenterPosition(clanName, player.transform.position, radius);

                // Create new bubble
                var raidBubbleSetup = new RaidBubbleSetup
                {
                    Position = centerPosition,
                    Radius = radius,
                    DefenderClanName = clanName
                };

                _savedData.RaidBubbles.Add(clanName, raidBubbleSetup);

                // Create the detection zone immediately
                CreateDetectionZone(raidBubbleSetup);

                player.SendConsoleCommand("ddraw.sphere", 10, Color.blue, centerPosition, radius);
                SendReply(player, $"Raid Bubble detection zone created for {clanName} at center position {centerPosition}");
                SendReply(player, $"Detection radius: {radius * 2}m, Raid bubble radius: {radius}m");
                SaveSavedData();
            }
        }

        [ChatCommand("removebubble")]
        private void CmdRemoveBubble(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length == 0)
            {
                SendReply(player, "Syntax: /removebubble <ClanName>");
                return;
            }
            if (args.Length < 1)
            {
                SendReply(player, "Syntax: /removebubble <ClanName>");
                return;
            }
            string clanName = args[0];
            if (!_savedData.RaidBubbles.TryGetValue(clanName, out var value))
            {
                SendReply(player, "No bubble found for this clan.");
                return;
            }

            // Remove detection zone if it exists
            if (RaidBubblesRunning.ContainsKey(clanName))
            {
                var detectionZone = RaidBubblesRunning[clanName];
                RaidBubblesRunning.Remove(clanName);
                UnityEngine.Object.Destroy(detectionZone.gameObject);
                if (Debug) Puts($"[DETECTION ZONE] Removed detection zone for {clanName}");
            }

            _savedData.RaidBubbles.Remove(clanName);
            SendReply(player, "Raid Bubble and detection zone removed for " + clanName);
        }

        [ChatCommand("bubble_list")]
        private void CmdBubbleList(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (_savedData.RaidBubbles.Count == 0)
            {
                SendReply(player, "No Raid Bubbles found.");
                return;
            }
            foreach (var kvp in _savedData.RaidBubbles)
            {
                SendReply(player, $"Clan: {kvp.Key}, Radius: {kvp.Value.Radius}, Position: {kvp.Value.Position}");
            }
        }

        [ChatCommand("bubble_debug")]
        private void CmdBubbleDebug(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (RaidBubblesRunning.Count == 0)
            {
                SendReply(player, "No active raid bubbles.");
                return;
            }

            var sb = new StringBuilder();
            sb.AppendLine("Raid Bubble Debug Info:");
            foreach (var kvp in RaidBubblesRunning)
            {
                var raid = kvp.Value;
                sb.AppendLine($"Clan: {kvp.Key}");
                sb.AppendLine($"  Attacker: {raid.raidBubbleSetup.AttackerClanName}");
                sb.AppendLine($"  Defender: {raid.raidBubbleSetup.DefenderClanName}");
                sb.AppendLine($"  RaiderTime: {raid.RaiderTime}");
                sb.AppendLine($"  HoldTime: {raid.HoldTime}");
                sb.AppendLine($"  Progress: {(float)raid.RaiderTime / raid.HoldTime * 100:F1}%");
                sb.AppendLine($"  Should End: {raid.RaiderTime >= raid.HoldTime}");
                sb.AppendLine($"  Is Overtime: {raid.isOvertime}");
                sb.AppendLine($"  Is Ending: {raid.isEnding}");
                sb.AppendLine($"  Attacker Count: {raid.RaiderCount()}");
                sb.AppendLine($"  Defender Count: {raid.DefenderCount()}");
            }
            SendReply(player, sb.ToString());
        }

        [ChatCommand("bubble_force_end")]
        private void CmdForceEndBubble(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length < 1)
            {
                SendReply(player, "Usage: /bubble_force_end <ClanName>");
                return;
            }

            string clanName = args[0];
            if (!RaidBubblesRunning.ContainsKey(clanName))
            {
                SendReply(player, $"No active raid bubble found for clan: {clanName}");
                return;
            }

            var raid = RaidBubblesRunning[clanName];
            raid.AttackerWon = true;
            raid.StartCoroutine(raid.EndBubble());
            SendReply(player, $"Force ended raid bubble for {clanName}");
        }

        [ChatCommand("check_bubble")]
        private void CmdTestRadius(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length == 0)
            {
                SendReply(player, "Syntax: /check_bubble <ClanName>");
                return;
            }
            if (args.Length < 1)
            {
                SendReply(player, "Syntax: /check_bubble <ClanName>");
                return;
            }
            string clanName = args[0];
            if (!_savedData.RaidBubbles.TryGetValue(clanName, out var value))
            {
                SendReply(player, "No bubble found for this clan.");
                return;
            }
            player.SendConsoleCommand("ddraw.sphere", 30, Color.blue, value.Position, value.Radius);
        }

        [ChatCommand("grace")]
        private void CmdGracePeriod(BasePlayer player, string command, string[] args)
        {
            if (args.Length == 0)
            {
                if (GraceZone.Count == 0)
                {
                    CM(player, GetMessage("GraceNoActive"));
                    return;
                }

                StringBuilder graceMessage = Facepunch.Pool.Get<StringBuilder>();
                try
                {
                    graceMessage.AppendLine(GetMessage("GraceListStart"));

                    foreach (var grace in GraceZone)
                    {
                        int remainingTime = grace.GetRemainingTime();
                        string formattedTime = TimeSpan.FromSeconds(remainingTime).ToString(@"mm\:ss");
                        string gridPosition = GetGridPosition(grace.Postion);
                        graceMessage.AppendLine(GetMessage("GraceList", gridPosition, formattedTime));
                    }

                    SendReply(player, graceMessage.ToString().Trim());
                }
                finally
                {
                    Facepunch.Pool.FreeUnmanaged(ref graceMessage);
                }

                return;
            }

            if (args.Length != 3)
            {
                SendReply(player, "Syntax: /grace <Name> <size> <time>");
                return;
            }

            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }
            string graceName = args[0];
            if (!float.TryParse(args[1], out float radius))
            {
                SendReply(player, "Example: /grace <Name> <size> <time>");
                return;
            }
            if (!int.TryParse(args[2], out int durationInMinutes))
            {
                SendReply(player, "Example: /grace <Name> <size> <time>");
                return;
            }

            Vector3 position = player.transform.position;
            GracePeriod gracePeriod = new GracePeriod
            {
                Name = graceName,
                Postion = position,
                Radius = radius,
                time = durationInMinutes * 60
            };

            gracePeriod.CreateDome();
            GraceZone.Add(gracePeriod);
            string grid = GetGridPosition(position);
            gracePeriod.timer = timer.Once(gracePeriod.time, () =>
            {
                gracePeriod.DestoryDome();
                gracePeriod.DestoryTimer();
                GraceZone.Remove(gracePeriod);
                // if(false) { Broadcast($"<color=#DBE2E9>Grace period has ended at</color> <color=#9CFF1E>{grid}</color>"); }
            });

            SendReply(player, $"Grace period '{graceName}' started at your location! Duration: {durationInMinutes} minutes, Radius: {radius}");
            // if(false) { Broadcast($"<color=#DBE2E9>Grace period started at</color> <color=#9CFF1E>{grid}</color>"); }
        }

        [ChatCommand("removegrace")]
        private void CmdRemoveGrace(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            // If no arguments, try to find grace bubble at player's location
            if (args.Length == 0)
            {
                GracePeriod target = null;
                foreach (var grace in GraceZone)
                {
                    float distance = Vector3.Distance(player.transform.position, grace.Postion);
                    if (distance <= grace.Radius)
                    {
                        target = grace;
                        break;
                    }
                }

                if (target == null)
                {
                    SendReply(player, "No grace bubble found at your location. Use: /removegrace <GraceBubbleName>");
                    return;
                }

                target.DestoryDome();
                target.DestoryTimer();
                GraceZone.Remove(target);
                SendReply(player, $"Grace bubble '{target.Name}' removed (found at your location).");
                return;
            }

            // Original logic with grace name
            string graceName = args[0];
            GracePeriod namedTarget = null;
            foreach (var grace in GraceZone)
            {
                if (grace.Name == graceName)
                {
                    namedTarget = grace;
                    break;
                }
            }
            if (namedTarget == null)
            {
                SendReply(player, $"No grace bubble found with the name '{graceName}'.");
                return;
            }
            namedTarget.DestoryDome();
            namedTarget.DestoryTimer();
            GraceZone.Remove(namedTarget);
            SendReply(player, $"Grace bubble '{graceName}' removed.");
        }

        [ChatCommand("fb")]
        private void CmdForceBubbleRemove(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length == 0)
            {
                SendReply(player, "Syntax: /fb <ClanName>");
                return;
            }
            if (args.Length < 1)
            {
                SendReply(player, "Syntax: /fb <ClanName>");
                return;
            }
            string raidName = args[0];

            if (cachedSpheres.TryGetValue(raidName, out var list))
            {
                foreach (var sphere in list)
                {
                    if (sphere != null)
                        sphere?.Kill();
                }
                cachedSpheres.Remove(raidName);
                SendReply(player, "Raid Bubble removed for " + raidName);
            }
            else
            {
                SendReply(player, "No bubble found with that name.");
            }
        }

        [ChatCommand("rb_help")]
        private void CmdRbHelp(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            SendReply(player, "<color=#00FF00>Raid Bubble Help Commands:</color>\n" + string.Join("\n", HelpCommands));
        }

        [ChatCommand("rb_explosives")]
        private void CmdExplosiveStats(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (RaidBubblesRunning.Count == 0)
            {
                SendReply(player, "No active raid bubbles.");
                return;
            }

            foreach (var kvp in RaidBubblesRunning)
            {
                var raid = kvp.Value;
                var sb = new StringBuilder();
                sb.AppendLine($"<color=#00FF00>Raid: {kvp.Key}</color>");
                sb.AppendLine($"Explosive Tracking: {(raid.ExplosiveTrackingActive ? "<color=#00FF00>Active</color>" : "<color=#FF0000>Inactive</color>")}");

                if (raid.ExplosiveTrackingActive)
                {
                    sb.AppendLine($"Tracked Rockets: {raid.TotalRockets} (Attackers: {raid.AttackerRockets}, Defenders: {raid.DefenderRockets})");
                    sb.AppendLine($"Tracked C4: {raid.TotalC4} (Attackers: {raid.AttackerC4}, Defenders: {raid.DefenderC4})");
                }
                else
                {
                    int totalPreRockets = raid.GetPreTrackingRockets();
                    sb.AppendLine($"Pre-tracking Rockets: {totalPreRockets} (Threshold: {config.RocketThresholdForTracking})");
                    sb.AppendLine($"Pre-tracking C4: {raid.GetPreTrackingC4()}");
                    sb.AppendLine($"<color=#FFAA00>Tracking will activate after {config.RocketThresholdForTracking} rockets</color>");
                }

                sb.AppendLine($"Tracking Radius: {config.ExplosiveTrackingRadius}m");

                SendReply(player, sb.ToString());
            }
        }

        [ChatCommand("rb_debug")]
        private void CmdRaidBubbleDebug(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            var sb = new StringBuilder();
            sb.AppendLine("<color=#00FF00>=== Raid Bubble Debug Info ===</color>");

            // Player clan info
            var playerClan = GetPlayerClanName(player.userID);
            sb.AppendLine($"Your clan: '{playerClan}'");

            // Configured bubbles
            sb.AppendLine($"Configured bubbles: {_savedData.RaidBubbles.Count}");
            foreach (var kvp in _savedData.RaidBubbles)
            {
                var bubble = kvp.Value;
                var distance = Vector3.Distance(player.transform.position, bubble.Position);
                sb.AppendLine($"  • {kvp.Key}: Radius {bubble.Radius}m, Distance {distance:F1}m");
            }

            // Active raids
            sb.AppendLine($"Active raids: {RaidBubblesRunning.Count}");
            foreach (var kvp in RaidBubblesRunning)
            {
                sb.AppendLine($"  • {kvp.Key}: {kvp.Value.raidBubbleSetup.AttackerClanName} vs {kvp.Value.raidBubbleSetup.DefenderClanName}");
            }

            // AwakenClans status
            sb.AppendLine($"AwakenClans plugin: {(AwakenClans != null ? "Connected" : "Not found")}");

            SendReply(player, sb.ToString());
        }

        [ChatCommand("rb_test")]
        private void CmdTestRaidBubble(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length < 2)
            {
                SendReply(player, "Usage: /rb_test <AttackerClan> <DefenderClan>");
                return;
            }

            string attackerClan = args[0];
            string defenderClan = args[1];

            // Find a bubble for the defender clan
            if (!_savedData.RaidBubbles.TryGetValue(defenderClan, out var bubble))
            {
                SendReply(player, $"No raid bubble configured for clan '{defenderClan}'");
                return;
            }

            if (RaidBubblesRunning.ContainsKey(defenderClan))
            {
                SendReply(player, $"Raid bubble already running for '{defenderClan}'");
                return;
            }

            SendReply(player, $"Manually starting raid bubble: {attackerClan} vs {defenderClan}");
            StartRaidBubble(bubble, attackerClan, defenderClan);
        }

        [ChatCommand("rb_count")]
        private void CmdTestPlayerCounts(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            var sb = new StringBuilder();
            sb.AppendLine("<color=#00FF00>=== Player Count Debug ===</color>");

            if (RaidBubblesRunning.Count == 0)
            {
                SendReply(player, "No active raid bubbles.");
                return;
            }

            foreach (var kvp in RaidBubblesRunning)
            {
                var raid = kvp.Value;
                sb.AppendLine($"\n<color=#FFFF00>Raid: {kvp.Key}</color>");
                sb.AppendLine($"Attacker Clan: {raid.raidBubbleSetup.AttackerClanName}");
                sb.AppendLine($"Defender Clan: {raid.raidBubbleSetup.DefenderClanName}");
                sb.AppendLine($"Players in bubble: {raid.GetPlayerCount()}");
                sb.AppendLine($"Raid Status: {(raid.actualRaidStarted ? "<color=#00FF00>ACTIVE</color>" : "<color=#FFA500>WAITING FOR TC BREAK</color>")}");

                // Force count update with debug
                int attackerCount = raid.RaiderCount();
                int defenderCount = raid.DefenderCount();

                sb.AppendLine($"Attacker Count: {attackerCount}");
                sb.AppendLine($"Defender Count: {defenderCount}");

                sb.AppendLine("\n<color=#CYAN>Players in bubble:</color>");
                var playersInBubble = raid.GetPlayersInBubble();
                foreach (var p in playersInBubble)
                {
                    if (p == null) continue;
                    var clan = GetPlayerClanName(p.userID) ?? "No Clan";
                    var hasWeapon = raid.CheckPlayerHasWeapon(p);
                    sb.AppendLine($"  {p.displayName} ({clan}) - Weapon: {hasWeapon}, Alive: {p.IsAlive()}, Sleeping: {p.IsSleeping()}");
                }
            }

            SendReply(player, sb.ToString());
        }

        [ChatCommand("rb_test_clan")]
        private void CmdTestClanSimple(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            SendReply(player, "=== SIMPLE CLAN TEST ===");
            SendReply(player, $"Your UserID: {player.userID}");
            SendReply(player, $"AwakenClans plugin: {(AwakenClans != null ? "Connected" : "NULL")}");

            if (AwakenClans != null)
            {
                try
                {
                    var result1 = AwakenClans.Call<string>("GetClanName", player.userID);
                    SendReply(player, $"GetClanName result: '{result1 ?? "null"}'");

                    var result2 = AwakenClans.Call<string>("GetClanOf", player.userID);
                    SendReply(player, $"GetClanOf result: '{result2 ?? "null"}'");

                    var result3 = AwakenClans.Call<string>("GetClanTag", player.userID);
                    SendReply(player, $"GetClanTag result: '{result3 ?? "null"}'");
                }
                catch (Exception ex)
                {
                    SendReply(player, $"Error calling clan API: {ex.Message}");
                }
            }

            var finalResult = GetPlayerClanName(player.userID);
            SendReply(player, $"Final GetPlayerClanName result: '{finalResult}'");
        }

        [ChatCommand("rb_clan")]
        private void CmdTestClanDetection(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            var sb = new StringBuilder();
            sb.AppendLine("<color=#00FF00>=== Clan Detection Test ===</color>");

            // Test your own clan
            var yourClan = GetPlayerClanName(player.userID);
            sb.AppendLine($"Your clan name: '{yourClan}'");

            // Test GetClanTag directly
            if (AwakenClans != null)
            {
                try
                {
                    // Test all the API methods we're trying to use
                    sb.AppendLine("=== Testing All API Methods ===");

                    var method1 = AwakenClans.Call<string>("GetClanName", player.userID);
                    sb.AppendLine($"GetClanName(ulong): '{method1}'");

                    var method2 = AwakenClans.Call<string>("GetClanOf", player.userID);
                    sb.AppendLine($"GetClanOf(ulong): '{method2}'");

                    var method3 = AwakenClans.Call<string>("GetClanTag", player.userID);
                    sb.AppendLine($"GetClanTag(ulong): '{method3}'");

                    var method4 = AwakenClans.Call<string>("GetClanOf", player.UserIDString);
                    sb.AppendLine($"GetClanOf(string): '{method4}'");

                    var method5 = AwakenClans.Call<string>("GetClanTag", player.UserIDString);
                    sb.AppendLine($"GetClanTag(string): '{method5}'");

                    var clan = AwakenClans.Call("GetClan", player.userID);
                    if (clan != null)
                    {
                        sb.AppendLine($"GetClan returned: {clan.GetType().Name}");

                        // List all properties
                        var properties = clan.GetType().GetProperties();
                        sb.AppendLine("Available properties:");
                        foreach (var prop in properties.Take(10)) // Limit to first 10
                        {
                            try
                            {
                                var value = prop.GetValue(clan);
                                sb.AppendLine($"  {prop.Name}: '{value}'");
                            }
                            catch (Exception ex)
                            {
                                sb.AppendLine($"  {prop.Name}: Error - {ex.Message}");
                            }
                        }

                        // Test GetClanNameByTag if we have a tag
                        var clanTagProp = clan.GetType().GetProperty("ClanTag");
                        if (clanTagProp?.GetValue(clan) is string tag && !string.IsNullOrEmpty(tag))
                        {
                            var nameFromTag = GetClanNameByTag(tag);
                            sb.AppendLine($"GetClanNameByTag('{tag}'): '{nameFromTag}'");
                        }
                    }
                    else
                    {
                        sb.AppendLine("GetClan returned null");
                    }
                }
                catch (Exception ex)
                {
                    sb.AppendLine($"Error testing clan APIs: {ex.Message}");
                    sb.AppendLine($"Stack trace: {ex.StackTrace}");
                }
            }
            else
            {
                sb.AppendLine("AwakenClans plugin not found!");
            }

            SendReply(player, sb.ToString());
        }

        [ChatCommand("rb_debug_toggle")]
        private void CmdToggleDebug(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            Debug = !Debug;
            SendReply(player, $"Raid Bubble debug mode: {(Debug ? "<color=#00FF00>ENABLED</color>" : "<color=#FF0000>DISABLED</color>")}");
        }

        [ChatCommand("rb_force_tc_break")]
        private void CmdForceTCBreak(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length < 2)
            {
                SendReply(player, "Usage: /rb_force_tc_break <AttackerClan> <DefenderClan>");
                return;
            }

            string attackerClan = args[0];
            string defenderClan = args[1];

            if (!_savedData.RaidBubbles.ContainsKey(defenderClan))
            {
                SendReply(player, $"No raid bubble configured for defender clan: {defenderClan}");
                return;
            }

            var raidBubbleData = _savedData.RaidBubbles[defenderClan];
            SendReply(player, $"Force triggering TC break for {defenderClan} vs {attackerClan}");

            // Simulate TC break
            Puts($"[FORCE TC BREAK] Manually triggered by {player.displayName}");
            StartRaidBubble(raidBubbleData, attackerClan, defenderClan);
        }

        [ChatCommand("rb_status")]
        private void CmdRaidBubbleStatus(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            var sb = new StringBuilder();
            sb.AppendLine("<color=#00FF00>=== Raid Bubble Status ===</color>");
            sb.AppendLine($"Debug Mode: {(Debug ? "<color=#00FF00>ENABLED</color>" : "<color=#FF0000>DISABLED</color>")}");
            sb.AppendLine($"Your Clan: '{GetPlayerClanName(player.userID) ?? "No Clan"}'");
            sb.AppendLine($"Your Position: {player.transform.position}");
            sb.AppendLine();

            sb.AppendLine($"<color=#FFFF00>Configured Bubbles: {_savedData.RaidBubbles.Count}</color>");
            foreach (var kvp in _savedData.RaidBubbles)
            {
                var bubble = kvp.Value;
                var distance = Vector3.Distance(player.transform.position, bubble.Position);
                sb.AppendLine($"  • {kvp.Key}: Pos {bubble.Position}, Radius {bubble.Radius}m, Distance {distance:F1}m");
            }
            sb.AppendLine();

            sb.AppendLine($"<color=#FFFF00>Active Raids: {RaidBubblesRunning.Count}</color>");
            foreach (var kvp in RaidBubblesRunning)
            {
                var raid = kvp.Value;
                sb.AppendLine($"  • {kvp.Key}: {raid.raidBubbleSetup.AttackerClanName} vs {raid.raidBubbleSetup.DefenderClanName}");
                sb.AppendLine($"    Detection Mode: {raid.detectionOnlyMode}, Actual Raid: {raid.actualRaidStarted}");
            }

            SendReply(player, sb.ToString());
        }

        [ChatCommand("rb_spawn_now")]
        private void CmdSpawnBubbleNow(BasePlayer player, string command, string[] args)
        {
            if (!HasPermission(player, AdminPerm))
            {
                SendReply(player, "You don't have permission to use this command.");
                return;
            }

            SendReply(player, "<color=#FF0000>FORCING BUBBLE SPAWN AT YOUR LOCATION!</color>");
            Puts($"[FORCE SPAWN] {player.displayName} is forcing bubble spawn at {player.transform.position}");

            // Get player's clan
            string playerClan = GetPlayerClanName(player.userID) ?? "TestClan";

            // Create or get existing bubble setup
            RaidBubbleSetup bubbleSetup;
            if (_savedData.RaidBubbles.ContainsKey(playerClan))
            {
                bubbleSetup = _savedData.RaidBubbles[playerClan];
                SendReply(player, $"Using existing bubble config for {playerClan}");
            }
            else
            {
                bubbleSetup = new RaidBubbleSetup
                {
                    Position = player.transform.position,
                    Radius = 100f,
                    DefenderClanName = playerClan,
                    Active = false
                };
                _savedData.RaidBubbles[playerClan] = bubbleSetup;
                SaveSavedData();
                SendReply(player, $"Created new bubble config for {playerClan}");
            }

            // Force start the raid bubble
            Puts($"[FORCE SPAWN] Starting raid bubble for {playerClan} vs TestAttackers");
            StartRaidBubble(bubbleSetup, "TestAttackers", playerClan);

            SendReply(player, "<color=#00FF00>Raid bubble force started! Check for visual spheres and UI.</color>");
        }

        private static readonly string[] HelpCommands = new string[]
        {
            "rb_stop <ClanName> - Stops the Raid Bubble",
            "setbubble <Radius> <ClanName> - Creates a Raid Bubble in that position",
            "removebubble <ClanName> - Removes the Raid Bubble",
            "check_bubble <ClanName> - Checks the trigger bubble radius",
            "bubble_list - Lists all the Raid Bubbles",
            "rb_explosives - Shows explosive tracking statistics for active raids",
            "rb_debug - Shows debug information for troubleshooting",
            "rb_debug_toggle - Toggles debug mode on/off",
            "rb_status - Shows current raid bubble status and configuration",
            "rb_spawn_now - FORCE spawns a bubble at your current location (EMERGENCY FIX)",
            "rb_force_tc_break <AttackerClan> <DefenderClan> - Force triggers a TC break for testing",
            "rb_test <AttackerClan> <DefenderClan> - Manually starts a raid bubble for testing",
            "rb_clan - Tests clan detection and shows your clan info",
            "rb_count - Shows detailed player counts and debug info for active raids",
            "grace <Name> <size> <time> - Starts a grace period with a name",
            "removegrace <GraceBubbleName> - Removes the grace period with the specified name",
            "fb <ClanName> - Force removes the bubbles when its stuck",
            "",
            "Explosive Tracking: Activates after 5 rockets within 200m radius of clan base",
        };

        #endregion

        #region Init

        private void OnServerInitialized()
        {
            ins = this;
            LoadSavedData();
            CacheImages();
            RegisterAllPerms();

            // Recreate detection zones for all configured raid bubbles on server start
            RecreateDetectionZones();
        }

        private void RecreateDetectionZones()
        {
            if (_savedData?.RaidBubbles == null) return;

            foreach (var kvp in _savedData.RaidBubbles)
            {
                var clanName = kvp.Key;
                var raidBubbleSetup = kvp.Value;

                // Only create detection zone if there isn't already an active raid bubble
                if (!RaidBubblesRunning.ContainsKey(clanName))
                {
                    CreateDetectionZone(raidBubbleSetup);
                    if (Debug)
                        Puts($"[SERVER INIT] Recreated detection zone for {clanName}");
                }
            }
        }

        // Image handling methods - using RoamBubble approach without ImageManager dependency

        private void Unload()
        {
            SaveSavedData();
            RaidBubblesRunning.Clear();
            cachedSpheres.Clear();
        }

        private void OnServerSave()
        {
            SaveSavedData();
        }

        #endregion

        #region Explosive Tracking
        private void OnExplosiveThrown(BasePlayer player, BaseEntity entity, ThrownWeapon weapon)
        {
            if (!config.EnableExplosiveTracking || player == null || entity == null) return;

            // Check if this is a C4 explosive
            if (entity.ShortPrefabName.Contains("explosive.timed"))
            {
                TrackExplosiveUsage(player, entity.transform.position, "C4");
                Puts($"[C4 TRACKING] {player.displayName} threw C4 at {entity.transform.position}");
            }
        }

        private void OnRocketLaunched(BasePlayer player, BaseEntity rocket)
        {
            if (!config.EnableExplosiveTracking || player == null || rocket == null) return;

            // Track rocket usage
            TrackExplosiveUsage(player, rocket.transform.position, "Rocket");
            Puts($"[ROCKET TRACKING] {player.displayName} launched rocket at {rocket.transform.position}");
        }

        private void OnEntitySpawned(BaseEntity entity)
        {
            if (!config.EnableExplosiveTracking || entity == null) return;

            // Track rockets when they spawn
            if (entity.ShortPrefabName.Contains("rocket_basic") ||
                entity.ShortPrefabName.Contains("rocket_hv") ||
                entity.ShortPrefabName.Contains("rocket_fire"))
            {
                var rocket = entity as TimedExplosive;
                if (rocket?.creatorEntity is BasePlayer player)
                {
                    TrackExplosiveUsage(player, entity.transform.position, "Rocket");
                }
            }

            // Track C4 when it spawns
            if (entity.ShortPrefabName.Contains("explosive.timed"))
            {
                var c4 = entity as TimedExplosive;
                if (c4?.creatorEntity is BasePlayer player)
                {
                    TrackExplosiveUsage(player, entity.transform.position, "C4");
                }
            }
        }

        private void TrackExplosiveUsage(BasePlayer player, Vector3 position, string explosiveType)
        {
            if (player == null) return;

            Puts($"[EXPLOSIVE TRACKING] {explosiveType} detected from {player.displayName} at {position}");

            bool foundActiveRaid = false;
            foreach (var raidBubble in RaidBubblesRunning.Values)
            {
                float distance = Vector3.Distance(position, raidBubble.RaidPosition);
                if (distance <= config.ExplosiveTrackingRadius)
                {
                    foundActiveRaid = true;
                    raidBubble.RecordExplosiveUsage(player.userID, explosiveType);

                    if (!raidBubble.ExplosiveTrackingActive && explosiveType == "Rocket")
                    {
                        int totalPreTrackingRockets = raidBubble.GetPreTrackingRockets() + raidBubble.TotalRockets;
                        if (totalPreTrackingRockets >= config.RocketThresholdForTracking)
                        {
                            raidBubble.ExplosiveTrackingActive = true;
                            raidBubble.StartDemoRecording();
                            raidBubble.BroadcastRaidBubble($"Explosive tracking activated! {config.RocketThresholdForTracking} rockets detected.");
                        }
                    }
                    break;
                }
            }

            if (!foundActiveRaid && explosiveType == "Rocket")
            {
                string playerClan = GetPlayerClanName(player.userID);
                if (!string.IsNullOrEmpty(playerClan))
                {
                    foreach (var kvp in _savedData.RaidBubbles)
                    {
                        var bubble = kvp.Value;
                        float distance = Vector3.Distance(position, bubble.Position);

                        if (distance <= config.ExplosiveTrackingRadius)
                        {
                            var nearbyEnemies = BasePlayer.activePlayerList.Where(p =>
                                Vector3.Distance(p.transform.position, position) <= config.ExplosiveTrackingRadius &&
                                !string.IsNullOrEmpty(GetPlayerClanName(p.userID)) &&
                                GetPlayerClanName(p.userID) != playerClan
                            ).ToList();

                            if (nearbyEnemies.Any() && !RaidBubblesRunning.ContainsKey(kvp.Key))
                            {
                                string defenderClan = kvp.Key;
                                StartRaidBubble(bubble, playerClan, defenderClan);
                                Puts($"[ROCKET TRIGGER] Started raid bubble due to rocket within 200m: {playerClan} vs {defenderClan}");
                                break;
                            }
                        }
                    }
                }
            }
        }
        #endregion

        #region Hooks

        /// <summary>
        /// This hook is the definitive trigger for a raid bubble when a TC is destroyed.
        /// It is more reliable than predicting destruction in OnEntityTakeDamage.
        /// </summary>
        private void OnEntityDeath(BaseCombatEntity entity, HitInfo info)
        {
            Puts($"[OnEntityDeath] Entity died: {entity?.GetType().Name ?? "null"}");

            // We only care about TCs being destroyed
            if (entity == null || !(entity is BuildingPrivlidge tc))
            {
                if (entity != null) Puts($"[OnEntityDeath] Not a TC, ignoring: {entity.GetType().Name}");
                return;
            }

            Puts($"[OnEntityDeath] TC DESTROYED! Position: {tc.transform.position}");

            // We need an attacker to start a raid
            var attacker = info?.InitiatorPlayer;
            if (attacker == null || !attacker.userID.IsSteamId())
            {
                Puts($"[OnEntityDeath] No valid attacker found - attacker: {attacker?.displayName ?? "null"}");
                return;
            }

            Puts($"[OnEntityDeath] Valid attacker found: {attacker.displayName} ({attacker.userID})");

            // Get clan information for both parties
            Puts($"[OnEntityDeath] Getting clan info - TC Owner: {tc.OwnerID}, Attacker: {attacker.userID}");
            Puts($"[OnEntityDeath] AwakenClans plugin status: {(AwakenClans != null ? "Connected" : "NULL")}");

            string defenderClan = GetPlayerClanName(tc.OwnerID);
            string attackerClan = GetPlayerClanName(attacker.userID);

            Puts($"[OnEntityDeath] Clan detection results - Defender: '{defenderClan}', Attacker: '{attackerClan}'");

            // In debug mode, force bubble creation regardless of clan issues
            if (Debug)
            {
                Puts($"[OnEntityDeath] DEBUG MODE - Forcing bubble creation regardless of clan status");

                // Use fallback clans if detection failed
                if (string.IsNullOrEmpty(defenderClan))
                {
                    defenderClan = "fart"; // Use the clan name from your setbubble command
                    Puts($"[OnEntityDeath] DEBUG MODE - Using fallback defender clan: '{defenderClan}'");
                }

                if (string.IsNullOrEmpty(attackerClan))
                {
                    attackerClan = "TestAttackers";
                    Puts($"[OnEntityDeath] DEBUG MODE - Using fallback attacker clan: '{attackerClan}'");
                }

                // If same clan, make attacker different
                if (attackerClan == defenderClan)
                {
                    attackerClan = "TestAttackers";
                    Puts($"[OnEntityDeath] DEBUG MODE - Changed attacker clan to avoid same clan issue. Defender: '{defenderClan}', Attacker: '{attackerClan}'");
                }
            }
            else
            {
                // Normal mode - don't trigger on friendly fire or if clans are invalid
                if (string.IsNullOrEmpty(defenderClan) || string.IsNullOrEmpty(attackerClan) || attackerClan == defenderClan)
                {
                    Puts($"[OnEntityDeath] Invalid clans on TC destruction. Defender: '{defenderClan}', Attacker: '{attackerClan}'. Aborting.");
                    return;
                }
            }

            if (Debug) Puts($"[OnEntityDeath] TC destroyed. Defender: {defenderClan}, Attacker: {attackerClan}.");

            // Case 1: A detection zone already exists. We need to activate the full raid.
            if (RaidBubblesRunning.TryGetValue(defenderClan, out var existingRaid))
            {
                if (existingRaid.detectionOnlyMode)
                {
                    if (Debug) Puts($"[OnEntityDeath] Found existing detection zone for {defenderClan}. Triggering actual raid.");
                    existingRaid.raidBubbleSetup.AttackerClanName = attackerClan; // Ensure attacker is updated
                    existingRaid.TriggerActualRaid();
                }
                else
                {
                    if (Debug) Puts($"[OnEntityDeath] Raid already fully active for {defenderClan}. No action needed.");
                }
                return;
            }

            // Case 2: No raid/detection zone is running. Check if a bubble is configured and start it from scratch.
            if (_savedData.RaidBubbles.TryGetValue(defenderClan, out var raidBubbleData))
            {
                if (Debug) Puts($"[OnEntityDeath] Found configured bubble for {defenderClan}. Starting new raid vs {attackerClan}.");
                StartRaidBubble(raidBubbleData, attackerClan, defenderClan);
            }
            else
            {
                if (Debug) Puts($"[OnEntityDeath] No configured bubble found for defender clan '{defenderClan}'. Cannot start raid.");
            }
        }

        private object OnEntityTakeDamage(BaseCombatEntity entity, HitInfo hit)
        {
            if (entity == null || hit == null || entity.OwnerID == 0 || hit.InitiatorPlayer == null)
            {
                return null;
            }

            // This block for debugging TC damage can stay, but the predictive logic was removed.
            if (entity is BuildingPrivlidge)
            {
                if (Debug)
                {
                    Puts($"[TC DAMAGE DEBUG] TC hit by {hit.InitiatorPlayer.displayName} - Health: {entity.Health()}/{entity.MaxHealth()}");
                    Puts($"[TC DAMAGE DEBUG] Damage amount: {hit.damageTypes.Total()}");
                }
            }

            if (!IsValidRaidTarget(entity))
            {
                if (Debug) Puts($"[RAID DEBUG] Not a valid raid target: {entity.GetType().Name}");
                return null;
            }

            BasePlayer attacker = hit.InitiatorPlayer;

            // Debug bypass for testing - allow owner to trigger their own bubble when debug is enabled
            bool isOwnerAttack = entity.OwnerID == attacker.userID;
            bool isAlliedAttack = IsAllied(entity.OwnerID, attacker.userID);

            if (Debug)
            {
                Puts($"[RAID DEBUG] Owner check - Entity Owner: {entity.OwnerID}, Attacker: {attacker.userID}");
                Puts($"[RAID DEBUG] Is owner attack: {isOwnerAttack}, Is allied attack: {isAlliedAttack}");
                Puts($"[RAID DEBUG] Debug mode enabled - allowing owner to trigger bubble for testing");
            }

            if (!attacker.userID.IsSteamId() || (!Debug && (isOwnerAttack || isAlliedAttack)))
            {
                if (Debug) Puts($"[RAID DEBUG] Attack blocked - SteamId: {attacker.userID.IsSteamId()}, Owner: {isOwnerAttack}, Allied: {isAlliedAttack}");
                return null;
            }

            if (GraceZone.Count > 0)
            {
                foreach (var kvp in GraceZone)
                {
                    float distance = Vector3.Distance(entity.transform.position, kvp.Postion);
                    if (distance <= kvp.Radius)
                    {
                        CM(attacker, GetMessage("GracePeriodNoDamage"));
                        return true;
                    }
                }
            }

            if (RaidBubblesRunning.Count > 0)
            {
                foreach (var raidBubble in RaidBubblesRunning.Values)
                {
                    if (!raidBubble.GracePeriod) continue;

                    if (Vector3.Distance(entity.transform.position, raidBubble.RaidPosition) <= config.RaidBubbleGraceRadius)
                    {
                        var gracePeriodAttackerClan = GetPlayerClanName(attacker.userID);
                        var gracePeriodDefenderClan = raidBubble.raidBubbleSetup.DefenderClanName;

                        if (gracePeriodAttackerClan != gracePeriodDefenderClan && !IsPlayerMergeAllyOfClan(attacker.userID, gracePeriodDefenderClan))
                        {
                            CM(attacker, GetMessage("GracePeriodNoDamage"));
                            return true;
                        }
                    }
                }
            }

            if (hit.WeaponPrefab == null)
                return null;

            string weaponName = hit.WeaponPrefab.ShortPrefabName;
            if (!weaponName.StartsWith("rocket_") && !weaponName.Contains("explosive."))
                return null;

            string attackerClan = GetPlayerClanName(attacker.userID);
            string defenderClan = GetPlayerClanName(entity.OwnerID);

            if (Debug)
            {
                Puts($"[Raid Bubble Debug] Explosive damage detected:");
                Puts($"  Entity: {entity.GetType().Name} ({entity.ShortPrefabName})");
                Puts($"  Attacker: {attacker.displayName} (ID: {attacker.userID})");
                Puts($"  Attacker Clan: '{attackerClan}'");
                Puts($"  Building Owner: {entity.OwnerID}");
                Puts($"  Defender Clan: '{defenderClan}'");
                Puts($"  Weapon: {weaponName}");
                Puts($"  Position: {entity.transform.position}");
            }

            if (string.IsNullOrEmpty(attackerClan))
            {
                if (Debug) Puts($"[Raid Bubble Debug] Attacker {attacker.displayName} has no clan - raid bubble not triggered");
                return null;
            }

            if (string.IsNullOrEmpty(defenderClan))
            {
                if (Debug) Puts($"[Raid Bubble Debug] Building owner {entity.OwnerID} has no clan - raid bubble not triggered");
                return null;
            }

            // Check if there's already an active raid bubble for this defender clan
            if (RaidBubblesRunning.ContainsKey(defenderClan))
            {
                var existingRaid = RaidBubblesRunning[defenderClan];

                // If it's in detection mode, trigger the actual raid
                if (existingRaid.detectionOnlyMode && !existingRaid.actualRaidStarted)
                {
                    if (Debug) Puts($"[Raid Bubble Debug] Detection zone exists for {defenderClan} - triggering actual raid vs {attackerClan}");

                    // Update the attacker clan info
                    existingRaid.raidBubbleSetup.AttackerClanName = attackerClan;
                    existingRaid.raidBubbleSetup.AttackerClanIds = GetClanMembers(attackerClan)?.ToList() ?? new List<ulong>();

                    // Trigger the actual raid
                    existingRaid.TriggerActualRaid();
                    return null;
                }
                else
                {
                    if (Debug) Puts($"[Raid Bubble Debug] Raid bubble already active for {defenderClan} - no new bubble spawn");
                    return null;
                }
            }

            if (_savedData.RaidBubbles == null || _savedData.RaidBubbles.Count == 0)
            {
                if (Debug) Puts("[Raid Bubble Debug] No raid bubbles configured");
                return null;
            }

            foreach (var kvp in _savedData.RaidBubbles)
            {
                RaidBubbleSetup raidBubbleData = kvp.Value;

                if (!raidBubbleData.DefenderClanName.Equals(defenderClan, StringComparison.OrdinalIgnoreCase))
                {
                    if (Debug) Puts($"[Raid Bubble Debug] Clan mismatch: bubble for '{raidBubbleData.DefenderClanName}' vs defender '{defenderClan}'");
                    continue;
                }

                if (entity is BuildingPrivlidge tcCheck)
                {
                    if (IsTCAlreadyDestroyed(defenderClan, (uint)tcCheck.net.ID.Value))
                    {
                        if (Debug) Puts($"[Raid Bubble Debug] TC {tcCheck.net.ID.Value} already destroyed for clan {defenderClan} - no bubble spawn");
                        continue;
                    }

                    if (tcCheck.IsDead() || tcCheck.Health() <= 0)
                    {
                        if (Debug) Puts($"[Raid Bubble Debug] TC {tcCheck.net.ID.Value} is already dead/destroyed - no bubble spawn");
                        MarkTCAsDestroyed(defenderClan, (uint)tcCheck.net.ID.Value);
                        continue;
                    }
                }

                float distance = Vector3.Distance(entity.transform.position, raidBubbleData.Position);
                if (Debug) Puts($"[Raid Bubble Debug] Distance check: {distance:F1}m (max: {raidBubbleData.Radius}m)");

                if (distance <= raidBubbleData.Radius)
                {
                    if (Debug) Puts($"[Raid Bubble Debug] Starting raid bubble for {defenderClan} vs {attackerClan}");

                    if (entity is BuildingPrivlidge tcMark)
                    {
                        MarkTCAsDestroyed(defenderClan, (uint)tcMark.net.ID.Value);
                    }

                    StartRaidBubble(raidBubbleData, attackerClan, defenderClan);
                    break;
                }
            }

            return null;
        }

        private void OnPlayerDeath(BasePlayer player, HitInfo hit)
        {
            if (!player.userID.IsSteamId()) return;

            if (RaidBubblesRunning.Count > 0)
            {
                foreach (var kvp in RaidBubblesRunning)
                {
                    RaidBubbleComp raidBubble = kvp.Value;

                    // Handle player removal from raid bubble
                    raidBubble.HandleDeath(player);

                    // Track kills and deaths if explosive tracking is active
                    if (raidBubble.ExplosiveTrackingActive && raidBubble.InRaidBubble.Contains(player))
                    {
                        var victimClan = GetPlayerClanName(player.userID) ?? "";
                        var isVictimAttacker = victimClan == raidBubble.raidBubbleSetup.AttackerClanName || IsPlayerMergeAllyOfClan(player.userID, raidBubble.raidBubbleSetup.AttackerClanName);
                        var isVictimDefender = victimClan == raidBubble.raidBubbleSetup.DefenderClanName || IsPlayerMergeAllyOfClan(player.userID, raidBubble.raidBubbleSetup.DefenderClanName);

                        if (isVictimAttacker)
                            raidBubble.AttackerDeaths++;
                        else if (isVictimDefender)
                            raidBubble.DefenderDeaths++;

                        // Track killer if available
                        if (hit?.InitiatorPlayer != null)
                        {
                            var killerClan = GetPlayerClanName(hit.InitiatorPlayer.userID) ?? "";
                            var isKillerAttacker = killerClan == raidBubble.raidBubbleSetup.AttackerClanName || IsPlayerMergeAllyOfClan(hit.InitiatorPlayer.userID, raidBubble.raidBubbleSetup.AttackerClanName);
                            var isKillerDefender = killerClan == raidBubble.raidBubbleSetup.DefenderClanName || IsPlayerMergeAllyOfClan(hit.InitiatorPlayer.userID, raidBubble.raidBubbleSetup.DefenderClanName);

                            if (isKillerAttacker)
                                raidBubble.AttackerKills++;
                            else if (isKillerDefender)
                                raidBubble.DefenderKills++;

                            if (Debug) Puts($"[Kill Tracking] {hit.InitiatorPlayer.displayName} killed {player.displayName} during raid");
                        }
                    }

                    break;
                }
            }
        }

        private void OnPlayerDisconnected(BasePlayer player)
        {
            if (player == null) return;

            if (RaidBubblesRunning.Count > 0)
            {
                foreach (var kvp in RaidBubblesRunning)
                {
                    RaidBubbleComp raidBubble = kvp.Value;

                    raidBubble.HandleDeath(player);
                    //Puts("on death removed");
                    break;
                }
            }
        }

        private void OnVanishDisappear(BasePlayer player)
        {
            if (RaidBubblesRunning.Count > 0)
            {
                foreach (var kvp in RaidBubblesRunning)
                {
                    RaidBubbleComp raidBubble = kvp.Value;
                    raidBubble.HandleDeath(player);
                    break;
                }
            }
        }

        private bool IsValidRaidTarget(BaseCombatEntity entity)
        {
            if (entity == null) return false;

            if (entity is BuildingBlock) return true;
            if (entity is BuildingPrivlidge) return true;
            if (entity is Door) return true;
            if (entity is StorageContainer) return true;
            if (entity is Workbench) return true;
            if (entity is SleepingBag) return true;
            if (entity is AutoTurret) return true;
            if (entity is CodeLock || entity is KeyLock) return true;

            if (Debug)
            {
                Puts($"[Raid Bubble Debug] Unknown entity type: {entity.GetType().Name} ({entity.ShortPrefabName})");
            }

            return false;
        }

        private bool IsTCAlreadyDestroyed(string clanName, uint tcNetId)
        {
            if (_savedData.DestroyedTCs == null)
                _savedData.DestroyedTCs = new Dictionary<string, HashSet<uint>>();

            if (!_savedData.DestroyedTCs.ContainsKey(clanName))
                return false;

            return _savedData.DestroyedTCs[clanName].Contains(tcNetId);
        }

        private void MarkTCAsDestroyed(string clanName, uint tcNetId)
        {
            if (_savedData.DestroyedTCs == null)
                _savedData.DestroyedTCs = new Dictionary<string, HashSet<uint>>();

            if (!_savedData.DestroyedTCs.ContainsKey(clanName))
                _savedData.DestroyedTCs[clanName] = new HashSet<uint>();

            _savedData.DestroyedTCs[clanName].Add(tcNetId);
            SaveSavedData();

            if (Debug) Puts($"[TC Tracking] Marked TC {tcNetId} as destroyed for clan {clanName}");
        }

        private bool IsPlayerMergeAllyOfClan(ulong playerId, string clanName)
        {
            if (AwakenClans == null || playerId == 0 || string.IsNullOrEmpty(clanName))
                return false;

            try
            {
                var playerClan = GetPlayerClanName(playerId);
                if (string.IsNullOrEmpty(playerClan)) return false;

                // Get a member of the target clan to check merge ally status
                var targetClanMembers = GetClanMembers(clanName);
                if (targetClanMembers.Count == 0) return false;

                var targetMemberId = targetClanMembers.First();
                return (bool)(AwakenClans?.Call("AreMergeAllies", playerId, targetMemberId) ?? false);
            }
            catch (Exception ex)
            {
                if (Debug) PrintError($"Error checking merge ally status for player {playerId} and clan {clanName}: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Helpers

        private void CreateDetectionZone(RaidBubbleSetup raidBubbleSetup)
        {
            // Create a minimal raid bubble component just for detection
            GameObject gameObject = new GameObject("RaidDetectionZone");
            RaidBubbleComp detectionZone = gameObject.AddComponent<RaidBubbleComp>();

            // Set detection-only mode BEFORE initializing
            detectionZone.detectionOnlyMode = true;
            detectionZone.Initialize(raidBubbleSetup, new HashSet<ulong>(), new HashSet<ulong>());

            // Add to running bubbles so TC break detection can find it
            RaidBubblesRunning.Add(raidBubbleSetup.DefenderClanName, detectionZone);

            if (Debug)
                Puts($"[DETECTION ZONE] Created detection zone for {raidBubbleSetup.DefenderClanName} at {raidBubbleSetup.Position} with {raidBubbleSetup.Radius * 2}m detection radius");
        }

        private Vector3 CalculateClanBaseCenterPosition(string clanName, Vector3 playerPosition, float searchRadius)
        {
            List<Vector3> buildingPositions = new List<Vector3>();

            // Find all buildings owned by the clan within search radius using more efficient method
            var entities = BaseEntity.saveList.Where(e => e is BuildingBlock &&
                                                     Vector3.Distance(e.transform.position, playerPosition) <= searchRadius * 2).Cast<BuildingBlock>();
            foreach (var building in entities)
            {
                if (building == null) continue;

                float distance = Vector3.Distance(building.transform.position, playerPosition);
                if (distance <= searchRadius * 2) // Search in larger radius
                {
                    string ownerClan = GetPlayerClanName(building.OwnerID);
                    if (ownerClan == clanName)
                    {
                        buildingPositions.Add(building.transform.position);
                    }
                }
            }

            // If we found buildings, calculate center
            if (buildingPositions.Count > 0)
            {
                Vector3 center = Vector3.zero;
                foreach (var pos in buildingPositions)
                {
                    center += pos;
                }
                center /= buildingPositions.Count;
                center.y = TerrainMeta.HeightMap.GetHeight(center) + 5f; // Slightly above ground

                if (Debug) Puts($"[Base Center] Calculated center for {clanName}: {center} (from {buildingPositions.Count} buildings)");
                return center;
            }

            // Fallback to player position if no buildings found
            if (Debug) Puts($"[Base Center] No buildings found for {clanName}, using player position: {playerPosition}");
            return playerPosition;
        }

        #endregion

        #region Methods

        private static string RemoveTags(string message)
        {
            return Regex.Replace(message, "<.*?>", string.Empty);
        }

        private string GetFormattedTime(float time)
        {
            if (time <= 0) return "Expired";

            TimeSpan timeSpan = TimeSpan.FromSeconds(time);
            if (timeSpan.TotalHours >= 1)
                return $"{(int)timeSpan.TotalHours}h {timeSpan.Minutes}m";

            return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
        }

        private void RegisterAllPerms()
        {
            RegisterPerm(AdminPerm);
        }

        private void RegisterPerm(string perm)
        {
            if (!permission.PermissionExists(perm))
                permission.RegisterPermission(perm, this);
        }

        private bool HasPermission(BasePlayer player, string Perm)
        {
            if (permission.UserHasPermission(player.UserIDString, Perm))
                return true;
            return false;
        }

        private static bool IsVanihsed(BasePlayer player)
        {
            if (ins.Vanish != null && (bool)ins.Vanish.Call("IsInvisible", player)) return true;
            return false;
        }

        private static void SendGameTip(BasePlayer player, string messages)
        {
            try
            {
                player.SendConsoleCommand("gametip.showgametip", messages);
                ins.timer.Once(5, () => player?.SendConsoleCommand("gametip.hidegametip"));
            }
            catch { }
        }

        private void CM(BasePlayer player, string message)
        {
            player.ChatMessage(GetMessage("Title") + message);
        }

        private void CacheImages()
        {
            if (Debug) Puts("Starting image caching process...");

            foreach (var image in imageUrls)
            {
                if (_cachedImages.ContainsKey(image.Key))
                {
                    if (Debug) Puts($"Image {image.Key} already cached, skipping");
                    continue;
                }

                // Download and cache the image locally
                webrequest.Enqueue(image.Value, null, (code, response) =>
                {
                    if (code == 200 && !string.IsNullOrEmpty(response))
                    {
                        // Store the image data locally using Rust's image storage system
                        var imageId = FileStorage.server.Store(System.Text.Encoding.UTF8.GetBytes(response), FileStorage.Type.png, new NetworkableId(uint.MaxValue));
                        _cachedImages[image.Key] = imageId.ToString();

                        if (Debug) Puts($"Successfully cached image: {image.Key} -> {imageId}");
                    }
                    else
                    {
                        if (Debug) Puts($"Failed to cache image {image.Key}: HTTP {code}");
                        // Fallback to URL if caching fails
                        _cachedImages[image.Key] = image.Value;
                    }
                }, this);
            }

            if (Debug) Puts($"Initiated caching for {imageUrls.Count} images");
        }

        private static string GetImage(string name)
        {
            if (ins._cachedImages.ContainsKey(name))
                return ins._cachedImages[name];

            // Fallback to URL if not cached
            if (ins.imageUrls.ContainsKey(name))
                return ins.imageUrls[name];

            return string.Empty;
        }

        private static string GetFormatTimeInD2(float time)
        {
            TimeSpan timeSpan = TimeSpan.FromSeconds(time);
            int totalMinutes = (int)timeSpan.TotalMinutes;

            return $"{totalMinutes:D2}:{timeSpan.Seconds:D2}";
        }

        private static string GetFormatTime(float time)
        {
            TimeSpan dateDifference = TimeSpan.FromSeconds(time);
            int days = dateDifference.Days;
            int hours = dateDifference.Hours;
            int mins = dateDifference.Minutes;
            int secs = dateDifference.Seconds;

            if (days > 0)
                return $"{days} day{(days > 1 ? "s" : string.Empty)}, {hours} hour{(hours > 1 ? "s" : string.Empty)}";
            if (hours > 0)
                return $"{hours} hour{(hours > 1 ? "s" : string.Empty)}, {mins} minute{(mins > 1 ? "s" : string.Empty)}";
            return mins > 0 ? $"{mins} minute{(mins > 1 ? "s" : string.Empty)}, {secs} second{(secs > 1 ? "s" : string.Empty)}" : $"{secs} second{(secs > 1 ? "s" : string.Empty)}";
        }

        private static void Broadcast(string message)
        {
            string sorted = GetMessage("Title") + message;
            foreach (var player in BasePlayer.activePlayerList)
            {
                try
                {
                    player.ChatMessage(sorted);
                    SendGameTip(player, message);
                }
                catch { }
            }
        }

        public HashSet<ulong> GetClanMembers(string clanName)
        {
            HashSet<ulong> memberIds = new HashSet<ulong>();

            if (AwakenClans == null || string.IsNullOrEmpty(clanName))
                return memberIds;

            try
            {
                // Method 1: Try to find a player from this clan first, then use GetClanMembers API
                var allClans = AwakenClans.Call<List<object>>("GetAllClans");
                if (allClans != null)
                {
                    ulong firstMemberId = 0;

                    foreach (var clanObj in allClans)
                    {
                        // The GetAllClans API returns anonymous objects with specific properties
                        var clanType = clanObj.GetType();
                        var nameProperty = clanType.GetProperty("ClanName");
                        var membersProperty = clanType.GetProperty("ClanMembers");

                        if (nameProperty != null && membersProperty != null)
                        {
                            var clanNameValue = nameProperty.GetValue(clanObj)?.ToString();
                            if (clanNameValue != null && clanNameValue.Equals(clanName, StringComparison.OrdinalIgnoreCase))
                            {
                                var membersValue = membersProperty.GetValue(clanObj);
                                if (membersValue is Dictionary<string, string> members)
                                {
                                    foreach (var memberId in members.Keys)
                                    {
                                        if (ulong.TryParse(memberId, out ulong id))
                                        {
                                            memberIds.Add(id);
                                            if (firstMemberId == 0) firstMemberId = id;
                                        }
                                    }
                                }
                                break;
                            }
                        }
                    }

                    // Method 2: If we found members, try using the GetClanMembers API for verification
                    if (firstMemberId != 0)
                    {
                        try
                        {
                            var apiMembers = AwakenClans.Call("GetClanMembers", firstMemberId);
                            if (apiMembers is List<string> membersList)
                            {
                                var apiMembersSet = new HashSet<ulong>();
                                foreach (var memberIdStr in membersList)
                                {
                                    if (ulong.TryParse(memberIdStr, out ulong memberId))
                                    {
                                        apiMembersSet.Add(memberId);
                                    }
                                }

                                // Use API result if it has more or equal members (more reliable)
                                if (apiMembersSet.Count >= memberIds.Count)
                                {
                                    if (Debug) Puts($"[Clan Debug] Using GetClanMembers API result: {apiMembersSet.Count} members vs {memberIds.Count} from GetAllClans");
                                    memberIds = apiMembersSet;
                                }
                            }
                        }
                        catch (Exception apiEx)
                        {
                            if (Debug) Puts($"[Clan Debug] GetClanMembers API failed: {apiEx.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error getting clan members for {clanName}: {ex.Message}");
            }

            if (Debug) Puts($"[Clan Debug] Found {memberIds.Count} members for clan '{clanName}'");
            return memberIds;
        }

        private string GetPlayerClanTag(ulong playerId)
        {
            if (AwakenClans == null || playerId == 0)
                return string.Empty;

            try
            {
                var clanTag = AwakenClans.Call<string>("GetClanTag", playerId);
                return clanTag ?? string.Empty;
            }
            catch (Exception ex)
            {
                PrintError($"Error getting clan tag for player {playerId}: {ex.Message}");
                return string.Empty;
            }
        }

        private string GetClanNameByTag(string clanTag)
        {
            if (AwakenClans == null || string.IsNullOrEmpty(clanTag))
                return string.Empty;

            try
            {
                var allClans = AwakenClans.Call<List<object>>("GetAllClans");
                if (allClans != null)
                {
                    foreach (var clanObj in allClans)
                    {
                        var clanType = clanObj.GetType();
                        var tagProperty = clanType.GetProperty("ClanTag");
                        var nameProperty = clanType.GetProperty("ClanName");

                        if (tagProperty != null && nameProperty != null)
                        {
                            var clanTagValue = tagProperty.GetValue(clanObj)?.ToString();
                            if (clanTagValue != null && clanTagValue.Equals(clanTag, StringComparison.OrdinalIgnoreCase))
                            {
                                return nameProperty.GetValue(clanObj)?.ToString() ?? string.Empty;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error getting clan name for tag {clanTag}: {ex.Message}");
            }

            return string.Empty;
        }

        private string GetPlayerClanName(ulong playerId)
        {
            if (AwakenClans == null || playerId == 0)
            {
                if (Debug) Puts($"[Clan Debug] AwakenClans is null or playerId is 0: {playerId}");
                return string.Empty;
            }

            try
            {
                // Method 1: Try GetClanName API (direct clan name)
                var clanName = AwakenClans.Call<string>("GetClanName", playerId);
                if (!string.IsNullOrEmpty(clanName))
                {
                    if (Debug) Puts($"[Clan Debug] Found clan name for {playerId}: '{clanName}'");
                    return clanName;
                }

                // Method 2: Try GetClanOf API (Clans.cs compatible - returns clan tag)
                var clanTag = AwakenClans.Call<string>("GetClanOf", playerId);
                if (!string.IsNullOrEmpty(clanTag))
                {
                    if (Debug) Puts($"[Clan Debug] Found clan tag via GetClanOf for {playerId}: '{clanTag}'");

                    // Convert clan tag to clan name using GetClanNameByTag
                    var convertedName = GetClanNameByTag(clanTag);
                    if (!string.IsNullOrEmpty(convertedName))
                    {
                        if (Debug) Puts($"[Clan Debug] Converted tag '{clanTag}' to name '{convertedName}'");
                        return convertedName;
                    }
                    else
                    {
                        // If we can't get the name, use the tag as fallback
                        if (Debug) Puts($"[Clan Debug] Using clan tag as name: '{clanTag}'");
                        return clanTag;
                    }
                }

                // Method 3: Try GetClanTag API (fallback)
                var fallbackTag = AwakenClans.Call<string>("GetClanTag", playerId);
                if (!string.IsNullOrEmpty(fallbackTag))
                {
                    if (Debug) Puts($"[Clan Debug] Found clan tag via GetClanTag for {playerId}: '{fallbackTag}'");
                    return fallbackTag;
                }

                // Method 4: Try GetClan API with reflection (last resort)
                var clan = AwakenClans.Call("GetClan", playerId);
                if (clan != null)
                {
                    if (Debug) Puts($"[Clan Debug] GetClan returned object of type: {clan.GetType().Name}");

                    var clanType = clan.GetType();
                    var nameProperty = clanType.GetProperty("ClanName");
                    if (nameProperty != null)
                    {
                        var reflectionName = nameProperty.GetValue(clan)?.ToString();
                        if (Debug) Puts($"[Clan Debug] Found clan name via reflection: '{reflectionName}'");
                        return reflectionName ?? string.Empty;
                    }
                    else
                    {
                        if (Debug) Puts($"[Clan Debug] ClanName property not found on {clanType.Name}");
                    }
                }
                else
                {
                    if (Debug) Puts($"[Clan Debug] GetClan returned null for player {playerId}");
                }

                // Method 5: Fallback - try to extract clan tag from display name
                var player = BasePlayer.FindByID(playerId);
                if (player != null && !string.IsNullOrEmpty(player.displayName))
                {
                    var match = System.Text.RegularExpressions.Regex.Match(player.displayName, @"^\[([A-Z0-9]{2,6})\]");
                    if (match.Success)
                    {
                        var extractedTag = match.Groups[1].Value;
                        if (Debug) Puts($"[Clan Debug] Extracted clan tag from display name: '{extractedTag}'");

                        // Try to convert tag to name
                        var nameFromExtracted = GetClanNameByTag(extractedTag);
                        if (!string.IsNullOrEmpty(nameFromExtracted))
                        {
                            if (Debug) Puts($"[Clan Debug] Method 5 SUCCESS (extracted + converted) - returning '{nameFromExtracted}'");
                            return nameFromExtracted;
                        }
                        else
                        {
                            if (Debug) Puts($"[Clan Debug] Method 5 SUCCESS (extracted tag only) - returning '{extractedTag}'");
                            return extractedTag;
                        }
                    }
                }

                if (Debug) Puts($"[Clan Debug] All methods failed for player {playerId} - returning empty string");
                return string.Empty;
            }
            catch (Exception ex)
            {
                PrintError($"Error getting clan name for player {playerId}: {ex.Message}");
                if (Debug) Puts($"[Clan Debug] Exception details: {ex}");
                return string.Empty;
            }
        }

        private bool IsAllied(ulong playerId, ulong targetId)
        {
            if (playerId == targetId)
                return true;

            // Check Rust teams
            if (RelationshipManager.ServerInstance.playerToTeam.TryGetValue(playerId, out var team) && team.members.Contains(targetId))
                return true;

            // Check AwakenClans - only SameClan is available in the current API
            if (AwakenClans != null)
            {
                try
                {
                    // Check if same clan
                    var sameClan = AwakenClans.Call<bool>("SameClan", playerId, targetId);
                    if (sameClan) return true;

                    // Note: AreAllies and AreMergeAllies APIs don't exist in current AwakenClans
                    // Only same clan checking is supported
                }
                catch (Exception ex)
                {
                    PrintError($"Error checking alliance between {playerId} and {targetId}: {ex.Message}");
                }
            }

            return false;
        }



        #endregion

        #region Component

        private void StartRaidBubble(RaidBubbleSetup raidBubbleSetup, string AttackerClan, string DefenderClan)
        {
            Puts($"[START RAID BUBBLE] FORCING BUBBLE CREATION!");
            Puts($"[START RAID BUBBLE] AttackerClan: '{AttackerClan}', DefenderClan: '{DefenderClan}'");
            Puts($"[START RAID BUBBLE] Bubble position: {raidBubbleSetup.Position}, Radius: {raidBubbleSetup.Radius}");

            // Remove any existing bubble first
            if (RaidBubblesRunning.ContainsKey(DefenderClan))
            {
                Puts($"[START RAID BUBBLE] Removing existing bubble for {DefenderClan}");
                var existingRaid = RaidBubblesRunning[DefenderClan];
                RaidBubblesRunning.Remove(DefenderClan);
                if (existingRaid != null)
                {
                    UnityEngine.Object.Destroy(existingRaid.gameObject);
                }
            }

            try
            {
                Puts($"[START RAID BUBBLE] Creating GameObject and RaidBubbleComp");
                GameObject gameObject = new GameObject($"RaidBubble_{DefenderClan}");
                RaidBubbleComp raid = gameObject.AddComponent<RaidBubbleComp>();

                Puts($"[START RAID BUBBLE] Setting up raid bubble data");
                raidBubbleSetup.Active = true;
                raidBubbleSetup.AttackerClanName = AttackerClan;
                raidBubbleSetup.DefenderClanName = DefenderClan;

                Puts($"[START RAID BUBBLE] Getting clan members - Attacker: {AttackerClan}, Defender: {DefenderClan}");
                var attackerMembers = GetClanMembers(AttackerClan) ?? new HashSet<ulong>();
                var defenderMembers = GetClanMembers(DefenderClan) ?? new HashSet<ulong>();
                Puts($"[START RAID BUBBLE] Attacker members: {attackerMembers.Count}, Defender members: {defenderMembers.Count}");

                // Force set detection mode to false so it creates full bubble immediately
                raid.detectionOnlyMode = false;
                raid.actualRaidStarted = true;

                Puts($"[START RAID BUBBLE] Initializing raid component");
                raid.Initialize(raidBubbleSetup, attackerMembers, defenderMembers);

                StringBuilder builder = Facepunch.Pool.Get<StringBuilder>();

                try
                {
                    string message = $"{AttackerClan} team has started a raid on {DefenderClan} team.";
                    builder.Append(GetMessage("Title") + message);

                    Puts($"[START RAID BUBBLE] Broadcasting message: {builder.ToString()}");
                    Broadcast(builder.ToString());

                    Puts($"[START RAID BUBBLE] Adding to RaidBubblesRunning dictionary");
                    RaidBubblesRunning.Add(DefenderClan, raid);

                    Puts($"[START RAID BUBBLE] SUCCESSFULLY STARTED RAID BUBBLE!");
                    Puts($"[START RAID BUBBLE] DefenderClan: {DefenderClan}, AttackerClan: {AttackerClan}");
                    Puts($"[START RAID BUBBLE] Position: {raidBubbleSetup.Position}, Radius: {raidBubbleSetup.Radius}");
                    Puts($"[START RAID BUBBLE] Total active raids: {RaidBubblesRunning.Count}");

                    // Force trigger the bubble creation immediately
                    Puts($"[START RAID BUBBLE] Component created, bubble should spawn automatically via Start() method");
                }
                finally
                {
                    Facepunch.Pool.FreeUnmanaged(ref builder);
                }
            }
            catch (Exception ex)
            {
                Puts($"[START RAID BUBBLE ERROR] CRITICAL ERROR: {ex.Message}");
                Puts($"[START RAID BUBBLE ERROR] Stack trace: {ex.StackTrace}");

                // Try to create a simple bubble manually as last resort
                try
                {
                    Puts($"[START RAID BUBBLE ERROR] Attempting manual bubble creation as fallback");
                    CreateManualBubble(raidBubbleSetup.Position, raidBubbleSetup.Radius, DefenderClan);
                }
                catch (Exception ex2)
                {
                    Puts($"[START RAID BUBBLE ERROR] Manual bubble creation also failed: {ex2.Message}");
                }
            }
        }

        private void CreateManualBubble(Vector3 position, float radius, string clanName)
        {
            Puts($"[MANUAL BUBBLE] Creating manual bubble at {position} with radius {radius}");

            try
            {
                // Create purple sphere
                BaseEntity purpleSphere = GameManager.server.CreateEntity("assets/bundled/prefabs/modding/events/twitch/br_sphere_purple.prefab", position, Quaternion.identity, true);
                if (purpleSphere != null)
                {
                    var sphereComp = purpleSphere.GetComponent<SphereEntity>();
                    if (sphereComp != null)
                    {
                        sphereComp.currentRadius = radius * 2;
                        sphereComp.lerpSpeed = 0f;
                        sphereComp.lerpRadius = sphereComp.currentRadius;
                    }
                    purpleSphere.Spawn();
                    Puts($"[MANUAL BUBBLE] Purple sphere created successfully");
                }

                // Create visualization sphere
                BaseEntity vizSphere = GameManager.server.CreateEntity("assets/prefabs/visualization/sphere.prefab", position, Quaternion.identity, true);
                if (vizSphere != null)
                {
                    var sphereComp = vizSphere.GetComponent<SphereEntity>();
                    if (sphereComp != null)
                    {
                        sphereComp.currentRadius = radius * 2;
                        sphereComp.lerpSpeed = 0f;
                    }
                    vizSphere.Spawn();
                    Puts($"[MANUAL BUBBLE] Visualization sphere created successfully");
                }

                // Store in cache
                var spheres = new List<SphereEntity>();
                if (purpleSphere?.GetComponent<SphereEntity>() != null)
                    spheres.Add(purpleSphere.GetComponent<SphereEntity>());
                if (vizSphere?.GetComponent<SphereEntity>() != null)
                    spheres.Add(vizSphere.GetComponent<SphereEntity>());

                if (spheres.Count > 0)
                {
                    cachedSpheres[clanName] = spheres;
                    Puts($"[MANUAL BUBBLE] Manual bubble creation completed! {spheres.Count} spheres created.");
                }
            }
            catch (Exception ex)
            {
                Puts($"[MANUAL BUBBLE ERROR] Failed to create manual bubble: {ex.Message}");
            }
        }

        private class RaidBubbleComp : MonoBehaviour
        {
            private Coroutine uiUpdateCoroutine;
            private static RaidBubbleComp thisRaid;
            public RaidBubbleSetup raidBubbleSetup;
            private Configuration config;
            private List<SphereEntity> Spheres = new List<SphereEntity>();
            public HashSet<BasePlayer> InRaidBubble = new();
            public HashSet<BasePlayer> InHoldPlayers = new();
            private HashSet<ulong> Raider = new();
            private HashSet<ulong> Defender = new();
            public bool AttackerWon = false;
            public bool GracePeriod = false;

            public Vector3 RaidPosition;
            private float RaidRadius;
            private int RaidTimer;

            public int HoldTime;
            private int CheckPoint = 0;

            public BaseEntity visualDome; // Visual dome entity for 200m radius

            public int RaiderTime { get; set; }
            public int RaiderTotalTime { get; set; }

            private Dictionary<int, int> checkpoints;

            public bool isOvertime = false;
            private GameObject bigBubble;
            public bool actualRaidStarted = false; // Track if actual raid has begun
            public bool detectionOnlyMode = false; // Track if this is just a detection zone

            private float RaidBubbleRadius;

            public bool isEnding = false;

            // Raid Statistics
            public int AttackerKills { get; set; } = 0;
            public int DefenderKills { get; set; } = 0;
            public int AttackerDeaths { get; set; } = 0;
            public int DefenderDeaths { get; set; } = 0;
            public float RaidStartTime { get; set; }

            // Explosive tracking
            private Dictionary<ulong, int> PlayerRocketCount = new Dictionary<ulong, int>();
            private Dictionary<ulong, int> PlayerC4Count = new Dictionary<ulong, int>();

            // Pre-tracking counters (before 5 rocket threshold)
            private int PreTrackingRockets = 0;
            private int PreTrackingC4 = 0;

            // Public properties for external access
            public bool ExplosiveTrackingActive { get; set; } = false;
            public int TotalRockets { get; private set; } = 0;
            public int TotalC4 { get; private set; } = 0;
            public int AttackerRockets { get; private set; } = 0;
            public int AttackerC4 { get; private set; } = 0;
            public int DefenderRockets { get; private set; } = 0;
            public int DefenderC4 { get; private set; } = 0;

            // Public accessors for pre-tracking counters
            public int GetPreTrackingRockets() => PreTrackingRockets;
            public int GetPreTrackingC4() => PreTrackingC4;

            // Demo recording
            private bool isDemoRecording = false;
            private string currentDemoFileName = "";
            private float demoStartTime = 0f;

            public void Initialize(RaidBubbleSetup raidBubble, HashSet<ulong> Raiders, HashSet<ulong> Defenders)
            {
                ins.Puts($"[INITIALIZE] Starting initialization for {raidBubble.DefenderClanName}");
                ins.Puts($"[INITIALIZE] Position: {raidBubble.Position}, Radius: {raidBubble.Radius}");
                ins.Puts($"[INITIALIZE] Raiders count: {Raiders?.Count ?? 0}, Defenders count: {Defenders?.Count ?? 0}");

                raidBubbleSetup = raidBubble;
                RaidPosition = raidBubbleSetup.Position;
                RaidRadius = raidBubbleSetup.Radius;

                Raider = Raiders ?? new HashSet<ulong>();
                Defender = Defenders ?? new HashSet<ulong>();

                //configs
                config = ins.config;
                checkpoints = config.checkpoints;
                RaidTimer = config.RaidBubbleTimer;
                HoldTime = config.RaidBubbleHoldTimer;
                RaidBubbleRadius = config.RaidBubbleRadius;

                ins.Puts($"[INITIALIZE] Config loaded - RaidTimer: {RaidTimer}, HoldTime: {HoldTime}, RaidBubbleRadius: {RaidBubbleRadius}");

                // Initialize raid statistics
                RaidStartTime = Time.realtimeSinceStartup;
                AttackerKills = 0;
                DefenderKills = 0;
                AttackerDeaths = 0;
                DefenderDeaths = 0;
                TotalRockets = 0;
                TotalC4 = 0;
                AttackerRockets = 0;
                AttackerC4 = 0;
                DefenderRockets = 0;
                DefenderC4 = 0;

                // Initialize explosive tracking
                PlayerRocketCount.Clear();
                PlayerC4Count.Clear();
                ExplosiveTrackingActive = false;
                PreTrackingRockets = 0;
                PreTrackingC4 = 0;

                ins.Puts($"[INITIALIZE] Initialization completed for {raidBubble.DefenderClanName}");
            }

            #region Unity Methods

            private void Awake()
            {
                thisRaid = this;
            }

            private void Start()
            {
                StartCoroutine(RaidBubbleStart());
            }

            private void OnDestroy()
            {
                //Debug("OnDestroy method called");
                if (ins.RaidBubblesRunning.Remove(raidBubbleSetup.DefenderClanName))
                {
                    //Debug("Raid Bubble Removed");
                }

                if (ins.cachedSpheres.TryGetValue(raidBubbleSetup.DefenderClanName, out var value))
                {
                    ins.cachedSpheres.Remove(raidBubbleSetup.DefenderClanName);
                }

                // Cleanup visual dome
                if (visualDome != null && !visualDome.IsDestroyed)
                {
                    visualDome.Kill();
                    if (ins.Debug)
                        ins.Puts($"[VISUAL DOME] Destroyed visual dome for raid {raidBubbleSetup.DefenderClanName}");
                }

                // Cleanup big bubble trigger
                if (bigBubble != null)
                {
                    Destroy(bigBubble);
                    if (ins.Debug)
                        ins.Puts($"[BIG BUBBLE] Destroyed detection zone for raid {raidBubbleSetup.DefenderClanName}");
                }

                // Cleanup spheres
                foreach (var sphere in Spheres)
                {
                    if (sphere != null && !sphere.IsDestroyed)
                    {
                        sphere.Kill();
                    }
                }

                foreach (var player in InRaidBubble)
                {
                    DestoryUI(player);
                }

                StopAllCoroutines();

                // Free memory
                //globalUIContainer?.Clear();
                //globalUIContainer = null;
                InRaidBubble.Clear();
                InHoldPlayers.Clear();
                Raider.Clear();
                Defender.Clear();
                Spheres.Clear();
            }

            #endregion

            #region IEnumerator

            private IEnumerator RaidBubbleStart()
            {
                ins.Puts($"[RAID BUBBLE START] Starting for {raidBubbleSetup.DefenderClanName}, detectionOnlyMode: {detectionOnlyMode}");

                if (detectionOnlyMode)
                {
                    // Only create the big trigger for TC break detection - no UI, no raid bubble
                    ins.Puts($"[RAID BUBBLE START] Creating detection zone only for {raidBubbleSetup.DefenderClanName}");
                    yield return StartCoroutine(CreateBigBubbleTrigger());

                    ins.Puts($"[DETECTION ZONE] Detection zone created for {raidBubbleSetup.DefenderClanName} - waiting for TC break");
                }
                else
                {
                    // This is a full raid - create everything
                    ins.Puts($"[RAID BUBBLE START] Creating full raid bubble for {raidBubbleSetup.DefenderClanName}");

                    ins.Puts($"[RAID BUBBLE START] Step 1: Creating bubble trigger");
                    yield return StartCoroutine(CreateBubbleTrigger());

                    ins.Puts($"[RAID BUBBLE START] Step 2: Creating raid dome");
                    yield return StartCoroutine(CreateRaidDome());

                    ins.Puts($"[RAID BUBBLE START] Step 3: Creating big bubble trigger");
                    yield return StartCoroutine(CreateBigBubbleTrigger());

                    ins.Puts($"[RAID BUBBLE START] Step 4: Creating visual dome");
                    yield return StartCoroutine(CreateVisualDome());

                    ins.Puts($"[RAID BUBBLE START] Step 5: Updating UI for all players");
                    UpdateUIForAllPlayers();
                    uiUpdateCoroutine = StartCoroutine(ContinuousUIUpdate());

                    ins.Puts($"[RAID BUBBLE START] Step 6: Starting timer");
                    yield return StartCoroutine(StartTimer());

                    ins.Puts($"[RAID BUBBLE START] Completed all steps for {raidBubbleSetup.DefenderClanName}");
                }
            }

            private IEnumerator StartActualRaid()
            {
                // This gets called when TC is broken - creates the actual raid bubble
                ins.Puts($"[START ACTUAL RAID] Starting actual raid for {raidBubbleSetup.DefenderClanName}");
                ins.Puts($"[START ACTUAL RAID] Position: {raidBubbleSetup.Position}, Radius: {raidBubbleSetup.Radius}");

                ins.Puts($"[START ACTUAL RAID] Step 1: Creating bubble trigger");
                yield return StartCoroutine(CreateBubbleTrigger());
                ins.Puts($"[START ACTUAL RAID] Created bubble trigger for {raidBubbleSetup.DefenderClanName}");

                ins.Puts($"[START ACTUAL RAID] Step 2: Creating raid dome");
                yield return StartCoroutine(CreateRaidDome());
                ins.Puts($"[START ACTUAL RAID] Created raid dome for {raidBubbleSetup.DefenderClanName}");

                ins.Puts($"[START ACTUAL RAID] Step 3: Creating visual dome");
                yield return StartCoroutine(CreateVisualDome()); // Create 200m visual dome
                ins.Puts($"[START ACTUAL RAID] Created visual dome for {raidBubbleSetup.DefenderClanName}");

                ins.Puts($"[START ACTUAL RAID] Step 4: Starting UI updates");
                UpdateUIForAllPlayers();
                uiUpdateCoroutine = StartCoroutine(ContinuousUIUpdate());
                ins.Puts($"[START ACTUAL RAID] Started UI updates for {raidBubbleSetup.DefenderClanName}");

                ins.Puts($"[START ACTUAL RAID] Step 5: Starting timer");
                yield return StartCoroutine(StartTimer());
                ins.Puts($"[START ACTUAL RAID] Started timer for {raidBubbleSetup.DefenderClanName}");

                ins.Puts($"[START ACTUAL RAID] COMPLETED - Raid bubble should now be fully active!");
            }

            private void UpdateUIForAllPlayers()
            {
                // Only show UI if actual raid has started (not during detection phase)
                if (detectionOnlyMode || !actualRaidStarted) return;

                foreach (BasePlayer player in BasePlayer.activePlayerList)
                {
                    if (Vector3.Distance(player.transform.position, RaidPosition) <= ins.config.RaidBubbleRadius)
                    {
                        ShowFullUIForPlayer(player);
                    }
                }
            }

            private IEnumerator ContinuousUIUpdate()
            {
                while (true)
                {
                    UpdateUIForAllPlayers();
                    yield return new WaitForSeconds(0.5f);
                }
            }

            private void ShowFullUIForPlayer(BasePlayer player)
            {
                // Only show UI if actual raid has started (not during detection phase)
                if (detectionOnlyMode || !actualRaidStarted) return;

                CreateRaidBubbleUI(player);
                UpdateProgressBar(player);
                UpdateTimeBar(player);
                UpdateCheckPoint(player);
                UpdateTextBar(player);
            }

            private IEnumerator StartTimer()
            {
            StartNormalPhase:
                while (!isOvertime)
                {
                    RaidTimer--;
                    RaiderTotalTime++;
                    yield return new WaitForSeconds(1);

                    if (CheckForEnd())
                    {
                        AttackerWon = true;
                        ins.Puts($"[RAID END] Initial timer check - Attackers won! RaiderTime: {RaiderTime}, HoldTime: {HoldTime}");
                        yield return StartCoroutine(EndBubble());
                        break;
                    }

                    if (RaidTimer <= 0)
                    {
                        if (CheckForRaider())
                        {
                            isOvertime = true;
                            break;
                        }
                        else
                        {
                            yield return StartCoroutine(EndBubble());
                            break;
                        }
                    }

                    if (CheckForRaiderHold())
                    {
                        int checkpointReached = CheckForCheckPoint();
                        if (checkpointReached > CheckPoint)
                        {
                            CheckPoint = checkpointReached;
                            RaidTimer += checkpoints[CheckForCheckPointKey()];
                        }

                        // If ONLY attackers (no defenders), progress faster (2x speed)
                        int defenderCount = DefenderCount();
                        int raiderCount = RaiderCount();

                        if (defenderCount == 0 && raiderCount > 0)
                        {
                            RaiderTime += 2; // Double speed when only attackers
                            if (ins.Debug) ins.Puts($"[Raid Progress] Only attackers present - double speed! Defenders: {defenderCount}, Raiders: {raiderCount}, RaiderTime: {RaiderTime}");
                        }
                        else
                        {
                            RaiderTime++; // Normal speed when defenders are present
                            if (ins.Debug) ins.Puts($"[Raid Progress] Attackers vs defenders - normal speed. Defenders: {defenderCount}, Raiders: {raiderCount}, RaiderTime: {RaiderTime}");
                        }

                        // Check if raid should end after progress update
                        if (CheckForEnd())
                        {
                            AttackerWon = true;
                            ins.Puts($"[RAID END] Normal phase - Attackers won! RaiderTime: {RaiderTime}, HoldTime: {HoldTime}");
                            yield return StartCoroutine(EndBubble());
                            break;
                        }

                        foreach (var player in InRaidBubble)
                        {
                            try
                            {
                                UpdateCheckPoint(player);
                                UpdateTextBar(player);
                            }
                            catch { }
                        }
                    }
                    else if (CheckForDefenderHold())
                    {
                        int securedThreshold = CheckForCheckPointKey();
                        if (RaiderTime >= securedThreshold)
                            RaiderTime = Math.Max(RaiderTime - 1, securedThreshold);
                        else
                            RaiderTime = Math.Max(RaiderTime - 1, 0);

                        if (ins.Debug) ins.Puts($"[Raid Progress] Defenders holding - progress decreasing. RaiderTime: {RaiderTime}");
                    }
                    else if (RaiderandDefenderEqual())
                    {
                        // Equal numbers - progress pauses (no change to RaiderTime)
                        if (ins.Debug) ins.Puts($"[Raid Progress] Equal numbers - progress paused. RaiderTime: {RaiderTime}");
                    }

                    foreach (var player in InRaidBubble)
                    {
                        try
                        {
                            UpdateProgressBar(player);
                            UpdateTimeBar(player);
                            UpdateTextBar(player);
                        }
                        catch { }
                    }
                }

                // Overtime Phase
                while (isOvertime)
                {
                    RaiderTotalTime++;
                    yield return new WaitForSeconds(1);

                    if (CheckForEnd())
                    {
                        AttackerWon = true;
                        ins.Puts($"[RAID END] Overtime initial check - Attackers won! RaiderTime: {RaiderTime}, HoldTime: {HoldTime}");
                        yield return StartCoroutine(EndBubble());
                        break;
                    }

                    if (CheckForRaiderHold())
                    {
                        // If ONLY attackers (no defenders), progress faster (2x speed)
                        if (DefenderCount() == 0 && RaiderCount() > 0)
                        {
                            RaiderTime += 2; // Double speed when only attackers
                            if (ins.Debug) ins.Puts($"[Overtime Progress] Only attackers present - double speed! RaiderTime: {RaiderTime}");
                        }
                        else
                        {
                            RaiderTime++; // Normal speed when defenders are present
                            if (ins.Debug) ins.Puts($"[Overtime Progress] Attackers vs defenders - normal speed. RaiderTime: {RaiderTime}");
                        }

                        // Check if raid should end after progress update
                        if (CheckForEnd())
                        {
                            AttackerWon = true;
                            ins.Puts($"[RAID END] Overtime phase - Attackers won! RaiderTime: {RaiderTime}, HoldTime: {HoldTime}");
                            yield return StartCoroutine(EndBubble());
                            break;
                        }

                        int checkpointReached = CheckForCheckPoint();
                        if (checkpointReached > CheckPoint)
                        {
                            CheckPoint = checkpointReached;
                            RaidTimer += checkpoints[CheckForCheckPointKey()];
                            isOvertime = false;
                            foreach (var player in InRaidBubble)
                            {
                                try
                                {
                                    UpdateCheckPoint(player);
                                    UpdateTextBar(player);
                                }
                                catch { }
                            }
                            goto StartNormalPhase;
                        }
                    }
                    else if (CheckForDefenderHold())
                    {
                        int securedThreshold = CheckForCheckPointKey();
                        if (RaiderTime >= securedThreshold)
                            RaiderTime = Math.Max(RaiderTime - 1, securedThreshold);
                        else
                            RaiderTime = Math.Max(RaiderTime - 1, 0);
                        yield return StartCoroutine(EndBubble());
                        break;
                    }
                    else if (RaiderandDefenderEqual())
                    {
                        // do nothing special
                    }
                    else
                    {
                        yield return StartCoroutine(EndBubble());
                        break;
                    }

                    foreach (var player in InRaidBubble)
                    {
                        try
                        {
                            UpdateProgressBar(player);
                            UpdateTimeBar(player);
                            UpdateTextBar(player);
                        }
                        catch { }
                    }
                }

                yield return null;
            }


            private bool HandleRaiderOrDefenderHold()
            {
                if (CheckForRaiderHold())
                {
                    int checkpointReached = CheckForCheckPoint();
                    if (checkpointReached > CheckPoint)
                    {
                        CheckPoint = checkpointReached;
                        RaidTimer += checkpoints[CheckPoint];

                        /*foreach (var player in InRaidBubble)
                        {
                            UpdateCheckPoint(player);
                            UpdateTextBar(player);
                        }*/
                    }

                    // If ONLY attackers (no defenders), progress faster (2x speed)
                    if (DefenderCount() == 0 && RaiderCount() > 0)
                    {
                        RaiderTime += 2; // Double speed when only attackers
                        if (ins.Debug) ins.Puts($"[Handle Progress] Only attackers present - double speed! RaiderTime: {RaiderTime}");
                    }
                    else
                    {
                        RaiderTime++; // Normal speed when defenders are present
                        if (ins.Debug) ins.Puts($"[Handle Progress] Attackers vs defenders - normal speed. RaiderTime: {RaiderTime}");
                    }
                }
                else if (CheckForDefenderHold())
                {
                    int securedThreshold = (CheckPoint > 0 && checkpoints.ContainsKey(CheckPoint)) ? checkpoints[CheckPoint] : 0;
                    RaiderTime = Math.Max(RaiderTime - 1, securedThreshold);

                    if (ins.Debug) ins.Puts($"[Handle Progress] Defenders holding - progress decreasing. RaiderTime: {RaiderTime}");
                    return true;
                }

                return false;
            }

            public IEnumerator EndBubble()
            {
                if (isEnding) yield break;
                isEnding = true;
                isOvertime = false;

                if (uiUpdateCoroutine != null)
                {
                    StopCoroutine(uiUpdateCoroutine);
                    uiUpdateCoroutine = null;
                }

                foreach (BasePlayer player in BasePlayer.activePlayerList)
                {
                    CuiHelper.DestroyUi(player, RaidBubbleUI);
                }

                yield return StartCoroutine(DestroyBubbleTrigger());
                yield return StartCoroutine(DestroyRaidDome());
                yield return StartCoroutine(DestroyBigBubbleTrigger());
                yield return StartCoroutine(CloseAllUIs());
                yield return StartCoroutine(SendWinnerMsg());
                yield return StartCoroutine(GracePeriodStart());
            }


            private IEnumerator GracePeriodStart()
            {
                // Wait 1.5 minutes before starting grace period
                yield return new WaitForSeconds(90f);

                GracePeriod = true;

                // Stop demo recording when grace period starts
                StopDemoRecording();

                // Teleport enemy players out of the bubble before creating grace dome
                TeleportEnemyPlayersOut();

                yield return StartCoroutine(CreateGraceDome());

                // Broadcast grace period start
                Broadcast(GetMessage("GracePeriodStarted"));

                yield return new WaitForSeconds(config.RaidBubbleGraceTimer);
                GracePeriod = false;
                yield return StartCoroutine(DestroyGraceDome());

                // Broadcast grace period end
                Broadcast(GetMessage("GracePeriodEnded"));

                if (ins._savedData.RaidBubbles.ContainsKey(raidBubbleSetup.DefenderClanName))
                {
                    ins._savedData.RaidBubbles.Remove(raidBubbleSetup.DefenderClanName);
                    ins.SaveSavedData();
                }

                Destroy(gameObject);
            }

            private void TeleportEnemyPlayersOut()
            {
                foreach (var player in BasePlayer.activePlayerList)
                {
                    if (player == null || !player.IsConnected) continue;

                    // Check if player is within grace period radius
                    if (Vector3.Distance(player.transform.position, RaidPosition) <= config.RaidBubbleGraceRadius)
                    {
                        var playerClan = ins.GetPlayerClanName(player.userID);

                        // If player is not defender clan or merge ally, teleport them out
                        if (playerClan != raidBubbleSetup.DefenderClanName && !IsPlayerMergeAlly(player.userID))
                        {
                            TeleportPlayerOutOfBubble(player);
                        }
                    }
                }
            }

            private bool IsPlayerMergeAlly(ulong playerId)
            {
                // Check if player is merge ally with defender clan
                return ins.IsPlayerMergeAllyOfClan(playerId, raidBubbleSetup.DefenderClanName);
            }

            public void RecordExplosiveUsage(ulong playerId, string explosiveType)
            {
                try
                {
                    // Determine team membership based on clan, not HashSet membership
                    string playerClan = (ins.GetPlayerClanName(playerId) ?? "").Trim();
                    string defenderClan = raidBubbleSetup.DefenderClanName.Trim();
                    string attackerClan = raidBubbleSetup.AttackerClanName.Trim();

                    bool isDefender = false;
                    bool isAttacker = false;

                    if (!string.IsNullOrEmpty(playerClan))
                    {
                        // Check if player is defender or merge ally of defenders
                        isDefender = playerClan == defenderClan || ins.IsPlayerMergeAllyOfClan(playerId, defenderClan);

                        // Check if player is attacker or merge ally of attackers (and NOT a defender)
                        if (!isDefender)
                        {
                            isAttacker = playerClan == attackerClan || ins.IsPlayerMergeAllyOfClan(playerId, attackerClan);
                        }
                    }

                    if (!isAttacker && !isDefender)
                    {
                        ins.Puts($"[EXPLOSIVE TRACKING] Player {playerId} ({playerClan}) is neither attacker nor defender - ignoring {explosiveType}");
                        return; // Player not part of the raid
                    }

                    var player = BasePlayer.FindByID(playerId);
                    var playerName = player?.displayName ?? "Unknown";
                    var teamType = isAttacker ? "Attacker" : "Defender";
                    ins.Puts($"[EXPLOSIVE TRACKING] {playerName} ({teamType}, {playerClan}) used {explosiveType}");

                    if (explosiveType == "Rocket")
                    {
                        if (ExplosiveTrackingActive)
                        {
                            // Full tracking is active - count everything
                            TotalRockets++;

                            if (!PlayerRocketCount.ContainsKey(playerId))
                                PlayerRocketCount[playerId] = 0;
                            PlayerRocketCount[playerId]++;

                            if (isAttacker)
                                AttackerRockets++;
                            else if (isDefender)
                                DefenderRockets++;
                        }
                        else
                        {
                            // Pre-tracking phase - only count for threshold detection
                            PreTrackingRockets++;
                            TotalRockets++; // Always count total rockets

                            ins.Puts($"[Raid Tracking] Pre-tracking rocket #{PreTrackingRockets} detected. Threshold: {ins.config.RocketThresholdForTracking}");

                            // Check if we've reached the threshold to start full tracking
                            if (PreTrackingRockets >= ins.config.RocketThresholdForTracking)
                            {
                                ExplosiveTrackingActive = true;
                                ins.Puts($"[Raid Tracking] Explosive tracking activated after {PreTrackingRockets} rockets detected!");

                                // Start demo recording if enabled
                                if (ins.config.demoSettings.EnableDemoRecording)
                                {
                                    StartDemoRecording();
                                }

                                // Count this rocket in player stats since tracking is now active
                                if (!PlayerRocketCount.ContainsKey(playerId))
                                    PlayerRocketCount[playerId] = 0;
                                PlayerRocketCount[playerId]++;

                                if (isAttacker)
                                    AttackerRockets++;
                                else if (isDefender)
                                    DefenderRockets++;
                            }
                        }
                    }
                    else if (explosiveType == "C4")
                    {
                        if (ExplosiveTrackingActive)
                        {
                            // Full tracking is active - count everything
                            TotalC4++;

                            if (!PlayerC4Count.ContainsKey(playerId))
                                PlayerC4Count[playerId] = 0;
                            PlayerC4Count[playerId]++;

                            if (isAttacker)
                                AttackerC4++;
                            else if (isDefender)
                                DefenderC4++;
                        }
                        else
                        {
                            // Pre-tracking phase - only count for threshold detection
                            PreTrackingC4++;
                        }
                    }

                    // Additional logging for tracking confirmation
                    if (ExplosiveTrackingActive)
                    {
                        ins.Puts($"[EXPLOSIVE TRACKING] Total {explosiveType}s: {(explosiveType == "Rocket" ? TotalRockets : TotalC4)} (Attackers: {(explosiveType == "Rocket" ? AttackerRockets : AttackerC4)}, Defenders: {(explosiveType == "Rocket" ? DefenderRockets : DefenderC4)})");
                    }
                }
                catch (Exception ex)
                {
                    ins.PrintError($"Error recording explosive usage: {ex.Message}");
                }
            }

            private void TeleportPlayerOutOfBubble(BasePlayer player)
            {
                // Calculate teleport position outside the grace radius
                var direction = (player.transform.position - RaidPosition).normalized;
                var teleportPos = RaidPosition + direction * (config.RaidBubbleGraceRadius + 50f);
                teleportPos.y = TerrainMeta.HeightMap.GetHeight(teleportPos);

                player.Teleport(teleportPos);
                player.ChatMessage($"<color=#ffa500>You have been teleported out due to grace period starting!</color>");
            }

            #region Demo Recording

            public void StartDemoRecording()
            {
                if (!ins.config.demoSettings.EnableDemoRecording || isDemoRecording) return;

                try
                {
                    // Generate unique demo filename with timestamp and clan names
                    string timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                    string attackerClan = raidBubbleSetup.AttackerClanName?.Replace(" ", "_") ?? "Unknown";
                    string defenderClan = raidBubbleSetup.DefenderClanName?.Replace(" ", "_") ?? "Unknown";
                    currentDemoFileName = $"raid_{attackerClan}_vs_{defenderClan}_{timestamp}";

                    // Start demo recording at the center of the base
                    Vector3 demoPosition = CalculateBaseCenterPosition();

                    // Use Rust's demo recording system
                    ConsoleSystem.Run(ConsoleSystem.Option.Server, $"demo.record \"{currentDemoFileName}\"");

                    isDemoRecording = true;
                    demoStartTime = Time.realtimeSinceStartup;

                    ins.Puts($"[Demo Recording] Started recording: {currentDemoFileName} at position {demoPosition}");
                    BroadcastRaidBubble($"<color=#00FF00>Demo recording started!</color>");
                }
                catch (Exception ex)
                {
                    ins.PrintError($"Failed to start demo recording: {ex.Message}");
                }
            }

            public void StopDemoRecording()
            {
                if (!isDemoRecording) return;

                try
                {
                    // Stop demo recording
                    ConsoleSystem.Run(ConsoleSystem.Option.Server, "demo.stop");

                    float recordingDuration = Time.realtimeSinceStartup - demoStartTime;
                    isDemoRecording = false;

                    ins.Puts($"[Demo Recording] Stopped recording: {currentDemoFileName} (Duration: {recordingDuration:F1}s)");

                    // Upload to CDN
                    ins.timer.Once(2f, () => UploadDemoToCDN(currentDemoFileName, recordingDuration));
                }
                catch (Exception ex)
                {
                    ins.PrintError($"Failed to stop demo recording: {ex.Message}");
                }
            }

            private Vector3 CalculateBaseCenterPosition()
            {
                // Calculate center position of all clan buildings
                List<Vector3> buildingPositions = new List<Vector3>();

                // Find all buildings owned by the defender clan within a reasonable radius using more efficient method
                var entities = BaseEntity.saveList.Where(e => e is BuildingBlock &&
                                                         Vector3.Distance(e.transform.position, RaidPosition) <= 500f).Cast<BuildingBlock>();
                foreach (var building in entities)
                {
                    if (building == null) continue;

                    float distance = Vector3.Distance(building.transform.position, RaidPosition);
                    if (distance <= raidBubbleSetup.Radius * 2) // Search in larger radius
                    {
                        string ownerClan = ins.GetPlayerClanName(building.OwnerID);
                        if (ownerClan == raidBubbleSetup.DefenderClanName)
                        {
                            buildingPositions.Add(building.transform.position);
                        }
                    }
                }

                // If we found buildings, calculate center
                if (buildingPositions.Count > 0)
                {
                    Vector3 center = Vector3.zero;
                    foreach (var pos in buildingPositions)
                    {
                        center += pos;
                    }
                    center /= buildingPositions.Count;
                    center.y = TerrainMeta.HeightMap.GetHeight(center) + 10f; // Slightly above ground
                    return center;
                }

                // Fallback to raid position
                return RaidPosition;
            }

            private void UploadDemoToCDN(string demoFileName, float duration)
            {
                if (string.IsNullOrEmpty(ins.config.demoSettings.CDNUploadURL) ||
                    string.IsNullOrEmpty(ins.config.demoSettings.CDNApiKey))
                {
                    ins.Puts($"[Demo Upload] CDN settings not configured - demo saved locally: {demoFileName}");
                    return;
                }

                ins.timer.Once(1f, () =>
                {
                    try
                    {
                        // This would be implemented based on your CDN's API
                        // For now, just log the attempt
                        ins.Puts($"[Demo Upload] Uploading {demoFileName} to CDN...");
                        ins.Puts($"[Demo Upload] Duration: {duration:F1}s, Size: Calculating...");

                        // TODO: Implement actual CDN upload based on your CDN provider
                        // Example: Upload file to your CDN endpoint
                        // string cdnUrl = UploadFileToCDN(demoFileName);

                        ins.Puts($"[Demo Upload] Demo upload initiated for {demoFileName}");
                    }
                    catch (Exception ex)
                    {
                        ins.PrintError($"Failed to upload demo to CDN: {ex.Message}");
                    }
                });
            }

            #endregion

            private IEnumerator SendWinnerMsg()
            {
                StringBuilder builder = Facepunch.Pool.Get<StringBuilder>();

                try
                {
                    if (AttackerWon)
                    {
                        builder.Append(GetMessage("RaidBubbleSuccessfullRaid", raidBubbleSetup.AttackerClanName, raidBubbleSetup.DefenderClanName, GetFormatTime(RaiderTotalTime)));

                        string message = builder.ToString();
                        Broadcast(message);
                        ins.SendRaidEndedEmbed(this, true);
                    }
                    else
                    {
                        builder.Clear();
                        builder.Append(GetMessage("RaidBubbleDefenderRaid", raidBubbleSetup.DefenderClanName, raidBubbleSetup.AttackerClanName));

                        string message = builder.ToString();
                        Broadcast(message);
                        ins.SendRaidEndedEmbed(this, false);
                    }
                }
                finally
                {
                    Facepunch.Pool.FreeUnmanaged(ref builder);
                }

                yield return null;
            }

            public void TryForceStop()
            {
                StartCoroutine(ForceStop());
            }

            private IEnumerator ForceStop()
            {
                if (uiUpdateCoroutine != null)
                {
                    StopCoroutine(uiUpdateCoroutine);
                    uiUpdateCoroutine = null;
                }

                yield return DestroyBubbleTrigger();
                yield return DestroyRaidDome();
                yield return DestroyBigBubbleTrigger();
                yield return CloseAllUIs();

                Destroy(gameObject);
            }

            private IEnumerator CreateBubbleTrigger()
            {
                //Debug("CreateBubbleTrigger coroutine started");
                gameObject.transform.position = RaidPosition;
                gameObject.layer = (int)Rust.Layer.Reserved1;
                SphereCollider collider = gameObject.AddComponent<SphereCollider>();
                collider.radius = RaidRadius;
                collider.isTrigger = true;
                gameObject.AddComponent<TriggerBase>().interestLayers = (int)Rust.Layer.Player_Server;

                yield return null;
            }

            private IEnumerator DestroyBubbleTrigger() // Destroy the bubble trigger
            {
                //Debug("DestroyBubbleTrigger coroutine started");
                Destroy(gameObject.GetComponent<SphereCollider>());
                Destroy(gameObject.GetComponent<TriggerBase>());

                yield return null;
            }

            private IEnumerator CreateRaidDome()
            {
                ins.Puts($"[CREATE RAID DOME] Starting for {raidBubbleSetup.DefenderClanName} at position {raidBubbleSetup.Position}");
                ins.Puts($"[CREATE RAID DOME] Radius: {raidBubbleSetup.Radius}, Diameter: {raidBubbleSetup.Radius * 2}");

                try
                {
                    for (int i = 0; i < 1; i++)
                    {
                        ins.Puts($"[CREATE RAID DOME] Creating purple sphere entity");
                        BaseEntity entity = GameManager.server.CreateEntity("assets/bundled/prefabs/modding/events/twitch/br_sphere_purple.prefab", raidBubbleSetup.Position, new Quaternion(), true);

                        if (entity == null)
                        {
                            ins.Puts($"[CREATE RAID DOME ERROR] Failed to create purple sphere entity");
                            continue;
                        }

                        SphereEntity redSphere = entity.GetComponent<SphereEntity>();
                        if (redSphere == null)
                        {
                            ins.Puts($"[CREATE RAID DOME ERROR] Failed to get SphereEntity component from purple sphere");
                            entity.Kill();
                            continue;
                        }

                        redSphere.currentRadius = raidBubbleSetup.Radius * 2;
                        redSphere.lerpSpeed = 0f;
                        redSphere.lerpRadius = redSphere.currentRadius;
                        entity.Spawn();
                        ins.Puts($"[CREATE RAID DOME] Purple sphere spawned with radius {redSphere.currentRadius}");

                        ins.Puts($"[CREATE RAID DOME] Creating visualization sphere entity");
                        BaseEntity ent = GameManager.server.CreateEntity("assets/prefabs/visualization/sphere.prefab", raidBubbleSetup.Position, new Quaternion(), true);

                        if (ent == null)
                        {
                            ins.Puts($"[CREATE RAID DOME ERROR] Failed to create visualization sphere entity");
                            continue;
                        }

                        SphereEntity bubble = ent.GetComponent<SphereEntity>();
                        if (bubble == null)
                        {
                            ins.Puts($"[CREATE RAID DOME ERROR] Failed to get SphereEntity component from visualization sphere");
                            ent.Kill();
                            continue;
                        }

                        bubble.currentRadius = raidBubbleSetup.Radius * 2;
                        bubble.lerpSpeed = 0f;
                        ent.Spawn();
                        ins.Puts($"[CREATE RAID DOME] Visualization sphere spawned with radius {bubble.currentRadius}");

                        Spheres.Add(bubble);
                        Spheres.Add(redSphere);
                        ins.Puts($"[CREATE RAID DOME] Added spheres to collection. Total spheres: {Spheres.Count}");
                    }

                    ins.cachedSpheres.Add(raidBubbleSetup.DefenderClanName, Spheres);
                    ins.Puts($"[CREATE RAID DOME] Added spheres to cache for {raidBubbleSetup.DefenderClanName}");
                    ins.Puts($"[CREATE RAID DOME] Successfully completed dome creation");
                }
                catch (Exception ex)
                {
                    ins.Puts($"[CREATE RAID DOME ERROR] Exception: {ex.Message}");
                    ins.Puts($"[CREATE RAID DOME ERROR] Stack trace: {ex.StackTrace}");
                }

                yield return null;
            }

            private IEnumerator DestroyRaidDome()
            {
                foreach (var sphere in Spheres)
                {
                    sphere?.Kill();
                }
                ins.cachedSpheres.Remove(raidBubbleSetup.DefenderClanName);
                yield return null;
            }

            private IEnumerator CreateGraceDome()
            {
                for (int i = 0; i < 3; i++)
                {
                    BaseEntity entity = GameManager.server.CreateEntity("assets/bundled/prefabs/modding/events/twitch/br_sphere.prefab", raidBubbleSetup.Position, new Quaternion(), true);
                    SphereEntity redSphere = entity.GetComponent<SphereEntity>();
                    redSphere.currentRadius = config.RaidBubbleGraceRadius * 2;
                    redSphere.lerpSpeed = 0f;
                    redSphere.lerpRadius = redSphere.currentRadius;
                    entity.Spawn();
                    BaseEntity ent = GameManager.server.CreateEntity("assets/prefabs/visualization/sphere.prefab", raidBubbleSetup.Position, new Quaternion(), true);
                    SphereEntity bubble = ent.GetComponent<SphereEntity>();
                    bubble.currentRadius = config.RaidBubbleGraceRadius * 2;
                    bubble.lerpSpeed = 0f;
                    ent.Spawn();
                    Spheres.Add(bubble);
                    Spheres.Add(redSphere);
                }
                ins.cachedSpheres.Add(raidBubbleSetup.DefenderClanName, Spheres);
                yield return null;
            }

            private IEnumerator DestroyGraceDome()
            {
                foreach (var sphere in Spheres)
                {
                    sphere?.Kill();
                }
                ins.cachedSpheres.Remove(raidBubbleSetup.DefenderClanName);
                yield return null;
            }

            private IEnumerator CreateBigBubbleTrigger()
            {
                bigBubble = new GameObject();
                bigBubble.transform.position = RaidPosition;
                bigBubble.layer = (int)Rust.Layer.Reserved1;
                SphereCollider collider = bigBubble.AddComponent<SphereCollider>();
                // Use a larger radius for detection zone (2x the raid bubble radius)
                collider.radius = raidBubbleSetup.Radius * 2f;
                collider.isTrigger = true;
                bigBubble.AddComponent<TriggerBase>().interestLayers = (int)Rust.Layer.Player_Server;
                BigBubbleTrigger trigger = bigBubble.AddComponent<BigBubbleTrigger>();
                trigger.RaidBubbleComp = this;

                if (ins.Debug)
                    ins.Puts($"[BIG BUBBLE] Created detection zone with radius {collider.radius}m (2x raid bubble radius of {raidBubbleSetup.Radius}m)");

                yield return null;
            }

            private IEnumerator CreateVisualDome()
            {
                yield return new WaitForSeconds(0.2f);

                try
                {
                    // Create the visual dome using the purple sphere prefab
                    var domePrefab = "assets/bundled/prefabs/modding/events/twitch/br_sphere_purple.prefab";
                    visualDome = GameManager.server.CreateEntity(domePrefab, RaidPosition, Quaternion.identity, true);

                    if (visualDome != null)
                    {
                        // Scale the dome to 200m radius (400m diameter)
                        visualDome.transform.localScale = Vector3.one * 400f; // 200m radius = 400m diameter

                        // Make sure it's not solid (remove colliders if any)
                        var colliders = visualDome.GetComponentsInChildren<Collider>();
                        foreach (var collider in colliders)
                        {
                            if (collider != null)
                                UnityEngine.Object.DestroyImmediate(collider);
                        }

                        visualDome.Spawn();
                        visualDome.SetParent(null);
                        visualDome.SendNetworkUpdate();

                        if (ins.Debug)
                            ins.Puts($"[VISUAL DOME] Created 200m visual dome at {RaidPosition}");
                    }
                    else
                    {
                        ins.Puts($"[ERROR] Failed to create visual dome - prefab not found");
                    }
                }
                catch (System.Exception ex)
                {
                    ins.Puts($"[ERROR] Exception creating visual dome: {ex.Message}");
                }

                yield return null;
            }
            private IEnumerator DestroyBigBubbleTrigger()
            {
                Destroy(bigBubble.GetComponent<SphereCollider>());
                Destroy(bigBubble.GetComponent<TriggerBase>());
                Destroy(bigBubble.GetComponent<BigBubbleTrigger>());
                Destroy(bigBubble);

                yield return null;
            }

            private IEnumerator CloseAllUIs()
            {
                foreach (BasePlayer player in BasePlayer.activePlayerList)
                {
                    CuiHelper.DestroyUi(player, RaidBubbleUI);
                }
                yield return null;
            }
            #endregion

            #region Methods

            public void BroadcastRaidBubble(string message)
            {
                foreach (var player in InRaidBubble)
                {
                    try
                    {
                        ins.CM(player, message);
                        SendGameTip(player, message);
                    }
                    catch { }
                }
            }

            private bool IsDefendingClan(BasePlayer player)
            {
                try
                {
                    return ins.GetPlayerClanName(player.userID) == raidBubbleSetup.DefenderClanName;
                }
                catch { return false; }
            }

            private bool CheckForEnd()
            {
                var maxCheckpoint = checkpoints.Keys.Max();
                bool shouldEnd = RaiderTime >= maxCheckpoint;

                if (ins.Debug)
                {
                    ins.Puts($"[CheckForEnd] RaiderTime: {RaiderTime}, MaxCheckpoint: {maxCheckpoint}, ShouldEnd: {shouldEnd}");
                    ins.Puts($"[CheckForEnd] UI Progress: {(float)RaiderTime / maxCheckpoint * 100:F1}%");
                    if (shouldEnd)
                    {
                        ins.Puts($"[CheckForEnd] RAID SHOULD END NOW! UI is full - RaiderTime ({RaiderTime}) >= MaxCheckpoint ({maxCheckpoint})");
                    }
                    else
                    {
                        ins.Puts($"[CheckForEnd] Raid continues - need {maxCheckpoint - RaiderTime} more seconds to fill UI");
                    }
                }
                return shouldEnd;
            }

            public int RaiderCount()
            {
                // Only count raiders if actual raid has started
                if (!actualRaidStarted) return 0;

                try
                {
                    int totalRaiders = 0;
                    int excludedRaiders = 0;

                    foreach (var player in InHoldPlayers)
                    {
                        if (player == null) continue;

                        string playerClan = (ins.GetPlayerClanName(player.userID) ?? "").Trim();
                        string defenderClan = raidBubbleSetup.DefenderClanName.Trim();
                        string attackerClan = raidBubbleSetup.AttackerClanName.Trim();

                        if (string.IsNullOrEmpty(playerClan)) continue;

                        // Check if player is defender or merge ally of defenders
                        if (playerClan == defenderClan || ins.IsPlayerMergeAllyOfClan(player.userID, defenderClan))
                            continue;

                        // Check if player is attacker or merge ally of attackers
                        bool isAttacker = playerClan == attackerClan || ins.IsPlayerMergeAllyOfClan(player.userID, attackerClan);

                        if (isAttacker)
                        {
                            // Check exclusion conditions
                            bool isAlive = player.IsAlive();
                            bool isSleeping = player.IsSleeping();
                            bool hasWeapon = HasWeapon(player);
                            bool isVanished = IsVanihsed(player);

                            if (ins.Debug)
                            {
                                ins.Puts($"[RAIDER COUNT] {player.displayName} ({playerClan}): Alive={isAlive}, Sleeping={isSleeping}, HasWeapon={hasWeapon}, Vanished={isVanished}");
                            }

                            if (!isAlive || isSleeping || !hasWeapon || isVanished)
                            {
                                excludedRaiders++;
                            }
                            else
                            {
                                totalRaiders++;
                            }
                        }
                    }

                    if (ins.Debug && (totalRaiders > 0 || excludedRaiders > 0))
                    {
                        ins.Puts($"[RAIDER COUNT] Total valid raiders: {totalRaiders}, Excluded raiders: {excludedRaiders}");
                    }

                    return totalRaiders;
                }
                catch { return 0; }
            }

            public int DefenderCount()
            {
                // Only count defenders if actual raid has started
                if (!actualRaidStarted) return 0;

                try
                {
                    int totalDefenders = 0;
                    int excludedDefenders = 0;

                    foreach (var player in InHoldPlayers)
                    {
                        if (player == null) continue;

                        string playerClan = (ins.GetPlayerClanName(player.userID) ?? "").Trim();
                        string defenderClan = raidBubbleSetup.DefenderClanName.Trim();

                        // Check if player is defender clan or merge ally
                        bool isDefender = !string.IsNullOrEmpty(playerClan) &&
                                         (playerClan == defenderClan || ins.IsPlayerMergeAllyOfClan(player.userID, defenderClan));

                        if (isDefender)
                        {
                            // Check exclusion conditions
                            bool isAlive = player.IsAlive();
                            bool isSleeping = player.IsSleeping();
                            bool hasWeapon = HasWeapon(player);
                            bool isVanished = IsVanihsed(player);

                            if (ins.Debug)
                            {
                                ins.Puts($"[DEFENDER COUNT] {player.displayName} ({playerClan}): Alive={isAlive}, Sleeping={isSleeping}, HasWeapon={hasWeapon}, Vanished={isVanished}");
                            }

                            if (!isAlive || isSleeping || !hasWeapon || isVanished)
                            {
                                excludedDefenders++;
                            }
                            else
                            {
                                totalDefenders++;
                            }
                        }
                    }

                    if (ins.Debug && (totalDefenders > 0 || excludedDefenders > 0))
                    {
                        ins.Puts($"[DEFENDER COUNT] Total valid defenders: {totalDefenders}, Excluded defenders: {excludedDefenders}");
                    }

                    return totalDefenders;
                }
                catch { return 0; }
            }

            public int GetPlayerCount()
            {
                return InHoldPlayers?.Count ?? 0;
            }

            public List<BasePlayer> GetPlayersInBubble()
            {
                return InHoldPlayers?.ToList() ?? new List<BasePlayer>();
            }

            public bool CheckPlayerHasWeapon(BasePlayer player)
            {
                return HasWeapon(player);
            }

            public void TriggerActualRaid()
            {
                ins.Puts($"[TRIGGER ACTUAL RAID] Called for {raidBubbleSetup.DefenderClanName} - actualRaidStarted: {actualRaidStarted}, detectionOnlyMode: {detectionOnlyMode}");

                if (!actualRaidStarted && detectionOnlyMode)
                {
                    actualRaidStarted = true;
                    detectionOnlyMode = false; // Switch from detection mode to full raid mode

                    ins.Puts($"[RAID TRIGGERED] TC broken - starting actual raid for {raidBubbleSetup.DefenderClanName}");
                    ins.Puts($"[RAID TRIGGERED] Attacker: {raidBubbleSetup.AttackerClanName}, Defender: {raidBubbleSetup.DefenderClanName}");

                    StartCoroutine(StartActualRaid());
                }
                else
                {
                    ins.Puts($"[TRIGGER ACTUAL RAID] Conditions not met for {raidBubbleSetup.DefenderClanName} - actualRaidStarted: {actualRaidStarted}, detectionOnlyMode: {detectionOnlyMode}");
                }
            }



            private bool CheckForRaider()
            {
                return RaiderCount() > 0;
            }

            private bool RaiderandDefenderEqual()
            {
                int raiderCount = RaiderCount();
                int defenderCount = DefenderCount();
                return raiderCount > 0 && defenderCount > 0 && raiderCount == defenderCount;
            }


            private bool CheckForRaiderHold()
            {
                return RaiderCount() > DefenderCount();
            }

            private bool CheckForDefenderHold()
            {
                return DefenderCount() > RaiderCount();
            }

            private bool HasWeapon(BasePlayer player)
            {
                if (isOvertime) return true;

                try
                {
                    if (player == null || player.inventory == null)
                        return false;

                    List<Item> itemPool = Facepunch.Pool.Get<List<Item>>();

                    try
                    {
                        itemPool.AddRange(player.inventory.containerBelt?.itemList ?? Enumerable.Empty<Item>());
                        itemPool.AddRange(player.inventory.containerMain?.itemList ?? Enumerable.Empty<Item>());

                        foreach (var item in itemPool)
                        {
                            if (item?.info?.category == ItemCategory.Weapon)
                                return true;
                        }

                        // More lenient check for defenders - count tools and medical items as "weapons"
                        // This helps defenders who might be building/healing get counted
                        string playerClan = (ins.GetPlayerClanName(player.userID) ?? "").Trim();
                        string defenderClan = raidBubbleSetup.DefenderClanName.Trim();
                        bool isDefender = playerClan == defenderClan || ins.IsPlayerMergeAllyOfClan(player.userID, defenderClan);

                        if (isDefender)
                        {
                            foreach (var item in itemPool)
                            {
                                if (item?.info?.category == ItemCategory.Tool ||
                                    item?.info?.category == ItemCategory.Medical ||
                                    item?.info?.category == ItemCategory.Ammunition ||
                                    item?.info?.shortname?.Contains("explosive") == true)
                                {
                                    if (ins.Debug) ins.Puts($"[WEAPON CHECK] Defender {player.displayName} has qualifying item: {item.info.shortname}");
                                    return true;
                                }
                            }
                        }

                        return false;
                    }
                    finally
                    {
                        Facepunch.Pool.FreeUnmanaged(ref itemPool);
                    }
                }
                catch { return false; }
            }
            private int CheckForCheckPoint()
            {
                int checkpointIndex = 0;
                var sortedKeys = checkpoints.Keys.OrderBy(x => x).ToList();
                //    ins.Puts("Sorted Checkpoints: " + string.Join(", ", sortedKeys));

                foreach (var key in sortedKeys)
                {
                    //ins.Puts($"Checkpoint threshold {key}: RaiderTime = {RaiderTime}");
                    if (RaiderTime >= key)
                    {
                        checkpointIndex++;
                    }
                    else
                    {
                        break;
                    }
                }
                return checkpointIndex;
            }

            private int CheckForCheckPointKey()
            {
                int securedThreshold = 0;
                var sortedKeys = checkpoints.Keys.OrderBy(x => x).ToList();

                foreach (var key in sortedKeys)
                {
                    if (RaiderTime >= key)
                    {
                        securedThreshold = key;
                    }
                    else
                    {
                        break;
                    }
                }
                //ins.Puts($"Secured threshold: {securedThreshold} (RaiderTime: {RaiderTime})");
                return securedThreshold;
            }

            private static void Debug(string message)
            {
                if (!ins.Debug) return;
                ins.Puts(message);
            }

            public void HandleDeath(BasePlayer player)
            {
                if (InHoldPlayers.Contains(player))
                {
                    InHoldPlayers.Remove(player);
                }
                if (InRaidBubble.Contains(player))
                {
                    InRaidBubble.Remove(player);
                    DestoryUI(player);
                }
            }

            private bool IsPlayerInsideBubble(BasePlayer player)
            {
                return Vector3.Distance(player.transform.position, RaidPosition) <= RaidRadius;
            }

            private bool IsVanished(BasePlayer player)
            {
                return IsVanihsed(player) || player.IsSleeping();
            }

            #endregion

            #region TriggerBase

            private void OnTriggerEnter(Collider collider)
            {
                if (collider?.TryGetComponent<BasePlayer>(out var player) != true || !player.userID.IsSteamId())
                    return;

                if (!InHoldPlayers.Any(p => p.userID == player.userID))
                {
                    InHoldPlayers.Add(player);
                    //   Puts($"[Trigger] Added player {player.displayName} (ID: {player.userID}). Count: {InHoldPlayers.Count}");
                }
            }

            private void OnTriggerExit(Collider collider)
            {
                if (collider?.TryGetComponent<BasePlayer>(out var player) != true || !player.userID.IsSteamId())
                    return;

                var existing = InHoldPlayers.FirstOrDefault(p => p.userID == player.userID);
                if (existing != null)
                {
                    InHoldPlayers.Remove(existing);
                    //   Puts($"[Trigger] Removed player {player.displayName} (ID: {player.userID}). Count: {InHoldPlayers.Count}");
                }
            }


            private class BigBubbleTrigger : FacepunchBehaviour
            {
                public RaidBubbleComp RaidBubbleComp;
                private Collider triggerCollider;

                private void Awake()
                {
                    triggerCollider = GetComponent<Collider>();

                    if (triggerCollider == null)
                    {
                        enabled = false;
                        return;
                    }

                    triggerCollider.isTrigger = true;
                }

                private void OnTriggerEnter(Collider collider)
                {
                    if (collider?.TryGetComponent<BasePlayer>(out var player) != true || !player.userID.IsSteamId())
                        return;

                    // Only show UI if actual raid has started (not during detection phase)
                    if (RaidBubbleComp.detectionOnlyMode || !RaidBubbleComp.actualRaidStarted)
                        return;

                    if (!IsPlayerInUIBubble(player))
                        return;

                    RaidBubbleComp.CreateRaidBubbleUI(player);

                    if (IsPlayerInGameplayBubble(player))
                    {
                        if (!RaidBubbleComp.InRaidBubble.Contains(player))
                            RaidBubbleComp.InRaidBubble.Add(player);
                        RaidBubbleComp.AddPlayerToHold(player);
                    }
                }

                private void OnTriggerExit(Collider collider)
                {
                    if (collider?.TryGetComponent<BasePlayer>(out var player) != true || !player.userID.IsSteamId())
                        return;

                    if (IsPlayerInGameplayBubble(player))
                        return;

                    if (RaidBubbleComp.InRaidBubble.Contains(player))
                        RaidBubbleComp.InRaidBubble.Remove(player);

                    RaidBubbleComp.RemovePlayerFromHold(player);

                    try
                    {
                        RaidBubbleComp.DestoryUI(player);
                    }
                    catch { }
                }

                private bool IsPlayerInGameplayBubble(BasePlayer player)
                {
                    return Vector3.Distance(player.transform.position, RaidBubbleComp.RaidPosition) <= RaidBubbleComp.RaidRadius;
                }

                private bool IsPlayerInUIBubble(BasePlayer player)
                {
                    return Vector3.Distance(player.transform.position, RaidBubbleComp.RaidPosition) <= ins.config.RaidBubbleRadius;
                }
            }


            private void AddPlayerToHold(BasePlayer player)
            {
                if (!InHoldPlayers.Any(p => p.userID == player.userID))
                {
                    InHoldPlayers.Add(player);
                    //    Puts($"[BigBubble] Added player {player.displayName} (ID: {player.userID}). Count: {InHoldPlayers.Count}");
                }
            }

            private void RemovePlayerFromHold(BasePlayer player)
            {
                var existing = InHoldPlayers.FirstOrDefault(p => p.userID == player.userID);
                if (existing != null)
                {
                    InHoldPlayers.Remove(existing);
                    //    Puts($"[BigBubble] Removed player {player.displayName} (ID: {player.userID}). Count: {InHoldPlayers.Count}");
                }
            }


            #endregion

            #region UIs

            private void DestoryUI(BasePlayer player)
            {
                CuiHelper.DestroyUi(player, RaidBubbleUI);
            }

            private string RaidBubbleUI = $"RaidBubbleUI";

            private void CreateRaidBubbleUI(BasePlayer player)
            {
                CuiHelper.DestroyUi(player, RaidBubbleUI);

                var container = new CuiElementContainer();

                // Main UI Panel - adjusted height to accommodate top tracking bar
                UIBuilder.CreatePanel(ref container, "Overlay", RaidBubbleUI, "1 1 1 0", "0.5 1 0.5 1", "-200 -102.12 200 -40", RaidBubbleUI);

                // Top tracking bar for defenders (blue) and attackers (red)
                int defenderCount = DefenderCount();
                int attackerCount = RaiderCount();

                // Defender tracking bar (blue) - left side
                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "DefenderTracker", "0.2039216 0.6862745 0.9215686 0.8", "0.5 0.5 0.5 0.5", "-180 25 -10 30", "DefenderTracker");
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "DefenderCount", defenderCount.ToString(), "1 1 1 1", 8, "0.5 0.5 0.5 0.5", "-185 22 -5 33", TextAnchor.MiddleCenter);

                // Attacker tracking bar (red) - right side
                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "AttackerTracker", "0.8627451 0.1372549 0.1647059 0.8", "0.5 0.5 0.5 0.5", "10 25 180 30", "AttackerTracker");
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "AttackerCount", attackerCount.ToString(), "1 1 1 1", 8, "0.5 0.5 0.5 0.5", "5 22 185 33", TextAnchor.MiddleCenter);

                // Main progress bars - white base color
                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "Bar1", "1 1 1 0.80", "0.5 0.5 0.5 0.5", "-150 -3.25 -55 3.25", "Bar1");
                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "Bar2", "1 1 1 0.80", "0.5 0.5 0.5 0.5", "-50 -3.25 50 3.25", "Bar2");
                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "Bar3", "1 1 1 0.80", "0.5 0.5 0.5 0.5", "55 -3.25 150 3.25", "Bar3");

                // Checkpoints - adjusted positions for smaller bars
                UIBuilder.CreateImage(ref container, RaidBubbleUI, "CheckPoint", GetImage("RaidBubble.checkpoint"), CheckPoint > 0 ? "0.4392157 0 0.9921569 1" : "0.5019608 0.5019608 0.5019608 0.80", "0.5 0.5 0.5 0.5", "-56 -12.25 -47 -3.25");
                UIBuilder.CreateImage(ref container, RaidBubbleUI, "CheckPoint (1)", GetImage("RaidBubble.checkpoint"), CheckPoint > 1 ? "0.4392157 0 0.9921569 1" : "0.5019608 0.5019608 0.5019608 0.80", "0.5 0.5 0.5 0.5", "47 -12.25 56 -3.25");

                // Time Added Text (moved to center)
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "OverTime", "", "0.4392157 0 0.9921569 0.80", 9, "0.5 0.5 0.5 0.5", "-31.464 -17.658 31.464 0", TextAnchor.MiddleCenter);

                // Timer (moved to right)
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "Time", "00:00", "1 1 1 0.80", 9, "0.5 0.5 0.5 0.5", "120 -17.658 190 0", TextAnchor.MiddleRight);
                UIBuilder.CreateImage(ref container, RaidBubbleUI, "TimeIcon", GetImage("RaidBubble.timeicon"), "1 1 1 0.80", "0.5 0.5 0.5 0.5", "129 -13.329 138 -4.329");

                CuiHelper.AddUi(player, container);
            }

            private void UpdateProgressBar(BasePlayer player)
            {
                var container = new CuiElementContainer();

                // Update top tracking bars
                int defenderCount = DefenderCount();
                int attackerCount = RaiderCount();

                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "DefenderTracker", "0.2039216 0.6862745 0.9215686 0.8", "0.5 0.5 0.5 0.5", "-180 25 -10 30", "DefenderTracker");
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "DefenderCount", defenderCount.ToString(), "1 1 1 1", 8, "0.5 0.5 0.5 0.5", "-185 22 -5 33", TextAnchor.MiddleCenter, "DefenderCount");

                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "AttackerTracker", "0.8627451 0.1372549 0.1647059 0.8", "0.5 0.5 0.5 0.5", "10 25 180 30", "AttackerTracker");
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "AttackerCount", attackerCount.ToString(), "1 1 1 1", 8, "0.5 0.5 0.5 0.5", "5 22 185 33", TextAnchor.MiddleCenter, "AttackerCount");

                string barColor = "1 1 1 1";  // White base color
                string progressColor = "0.4392157 0 0.9921569 1";  // #7000fd purple progress
                float barWidth = 95f;  // Smaller bars
                float startX = -150f;  // Adjusted start position
                float gap = 5f;
                float progressIndicatorX = startX; // Initial X position of Progress1st

                List<int> sortedKeys = Facepunch.Pool.Get<List<int>>();
                sortedKeys.AddRange(checkpoints.Keys.ToList());

                for (int i = 0; i < sortedKeys.Count; i++)
                {
                    float barStart = startX + i * (barWidth + gap);
                    float barEnd = barStart + barWidth;

                    UIBuilder.CreatePanel(ref container, "RaidBubbleUI", $"Bar{i + 1}", barColor, "0.5 0.5 0.5 0.5",
                        $"{barStart} -3.25 {barEnd} 3.25", $"Bar{i + 1}");

                    int checkpointTime = sortedKeys[i];
                    float progressWidth = 0;

                    if (RaiderTime >= checkpointTime)
                    {
                        progressWidth = barWidth;
                    }
                    else if (i == 0 || RaiderTime > sortedKeys[i - 1])
                    {
                        int prevCheckpoint = (i == 0) ? 0 : sortedKeys[i - 1];
                        float progressFraction = (float)(RaiderTime - prevCheckpoint) / (checkpointTime - prevCheckpoint);
                        progressWidth = barWidth * progressFraction;
                    }

                    if (progressWidth > 0)
                    {
                        UIBuilder.CreatePanel(ref container, RaidBubbleUI, $"Bar{i + 1}Progress", progressColor, "0.5 0.5 0.5 0.5",
                            $"{barStart} -3.25 {barStart + progressWidth} 3.25");

                        progressIndicatorX = barStart + progressWidth; // Update indicator position
                    }
                }

                // Clamp Progress Indicator position within the last bar
                float maxProgressX = startX + ((sortedKeys.Count - 1) * (barWidth + gap)) + barWidth;
                progressIndicatorX = Math.Min(progressIndicatorX, maxProgressX);

                // Add Progress Indicator (Progress1st) at the updated position - adjusted for smaller bars
                UIBuilder.CreatePanel(ref container, RaidBubbleUI, "Progress1st", "0.0509804 0.7529413 0.8705883 0", "0.5 0.5 0.5 0.5",
                    $"{progressIndicatorX - 25} 3.25 {progressIndicatorX + 25} 20", "Progress1st");

                // Progress Indicator Images and Label
                int raiderCount = attackerCount; // Use the already declared attackerCount
                // defenderCount is already declared above

                if (CheckForRaiderHold())
                {
                    UIBuilder.CreateImage(ref container, "Progress1st", "Image_9958", GetImage("RaidBubble.circle"), "0.2039216 0.6862745 0.9215686 0.80", "0.5 0.5 0.5 0.5", "-7.5 -11.25 7.5 3.75");
                    UIBuilder.CreateImage(ref container, "Progress1st", "Image_2906", GetImage("RaidBubble.rightarrow"), "0.5019608 0.5019608 0.5019608 0.80", "0.5 0.5 0.5 0.5", "4.88 -4.151 10.88 1.849");
                    UIBuilder.CreateLabel(ref container, "Progress1st", "Label_7813", raiderCount.ToString(), "1 1 1 0.80", 9, "0.5 0.5 0.5 0.5", "-7.5 -7.504 7.5 5.202", TextAnchor.MiddleCenter);
                }
                else if (CheckForDefenderHold())
                {
                    UIBuilder.CreateImage(ref container, "Progress1st", "Image_9958", GetImage("RaidBubble.circle"), "0.8627451 0.1372549 0.1647059 0.80", "0.5 0.5 0.5 0.5", "-7.5 -11.25 7.5 3.75");
                    UIBuilder.CreateLabel(ref container, "Progress1st", "Label_7813", defenderCount.ToString(), "1 1 1 0.80", 9, "0.5 0.5 0.5 0.5", "-7.5 -7.504 7.5 5.202", TextAnchor.MiddleCenter);
                    UIBuilder.CreateImage(ref container, "Progress1st", "Image_2906 (1)", GetImage("RaidBubble.leftarrow"), "0.5019608 0.5019608 0.5019608 0.80", "0.5 0.5 0.5 0.5", "-10.88 -4.151 -4.88 1.849");
                }
                else if (raiderCount > 0 && defenderCount > 0 && defenderCount == raiderCount)
                {
                    UIBuilder.CreateImage(ref container, "Progress1st", "Image_9958", GetImage("RaidBubble.circle"), "0.4392157 0 0.9921569 0.80", "0.5 0.5 0.5 0.5", "-7.5 -11.25 7.5 3.75");
                    UIBuilder.CreateLabel(ref container, "Progress1st", "Label_7813", "=", "1 1 1 0.80", 9, "0.5 0.5 0.5 0.5", "-7.5 -7.504 7.5 5.202", TextAnchor.MiddleCenter);
                }
                else
                {
                    UIBuilder.CreateImage(ref container, "Progress1st", "Image_9958", GetImage("RaidBubble.circle"), "0.4392157 0 0.9921569 0.80", "0.5 0.5 0.5 0.5", "-7.5 -11.25 7.5 3.75");
                    UIBuilder.CreateLabel(ref container, "Progress1st", "Label_7813", "", "1 1 1 0.80", 9, "0.5 0.5 0.5 0.5", "-7.5 -7.504 7.5 5.202", TextAnchor.MiddleCenter);
                }

                CuiHelper.AddUi(player, container);
                Facepunch.Pool.FreeUnmanaged(ref sortedKeys);
            }

            private void UpdateTimeBar(BasePlayer player)
            {
                var container = new CuiElementContainer();
                // Timer (moved further to the right)
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "Time", GetFormatTimeInD2(RaidTimer), "1 1 1 0.80", 9, "0.5 0.5 0.5 0.5", "120 -17.658 190 0", TextAnchor.MiddleRight, "Time");
                CuiHelper.AddUi(player, container);
            }

            private void UpdateCheckPoint(BasePlayer player)
            {
                if (CheckPoint < 1) return;

                var container = new CuiElementContainer();
                // Checkpoints icons - adjusted for smaller bars
                UIBuilder.CreateImage(ref container, RaidBubbleUI, "CheckPoint", GetImage("RaidBubble.checkpoint"), CheckPoint > 0 ? "0.4392157 0 0.9921569 0.80" : "0.5019608 0.5019608 0.5019608 0.80", "0.5 0.5 0.5 0.5", "-56 -12.25 -47 -3.25");
                UIBuilder.CreateImage(ref container, RaidBubbleUI, "CheckPoint (1)", GetImage("RaidBubble.checkpoint"), CheckPoint > 1 ? "0.4392157 0 0.9921569 0.80" : "0.5019608 0.5019608 0.5019608 0.80", "0.5 0.5 0.5 0.5", "47 -12.25 56 -3.25");
                CuiHelper.AddUi(player, container);
            }

            private void UpdateTextBar(BasePlayer player)
            {
                var container = new CuiElementContainer();
                // Time Added Text (now in center)
                UIBuilder.CreateLabel(ref container, RaidBubbleUI, "OverTime", GetTextString(), "0.4392157 0 0.9921569 0.80", 9, "0.5 0.5 0.5 0.5", "-31.464 -17.658 31.464 0", TextAnchor.MiddleCenter, "OverTime");

                CuiHelper.AddUi(player, container);
            }

            private string GetTextString()
            {
                return isOvertime ? "OVERTIME" : CheckPoint switch
                {
                    1 => "+5 MINS",
                    2 => "+10 MINS",
                    3 => "+15 MINS",
                    _ => string.Empty
                };
            }

            #endregion
        }

        #endregion

        #region UIs

        private class UIBuilder
        {
            public static void CreatePanel(ref CuiElementContainer container, string parent, string name, string color, string anchor, string offset, string destroyUi = null)
            {
                var dimensions = ParseDimensions(anchor, offset);

                container.Add(
                    new CuiPanel
                    {
                        RectTransform = {
                    AnchorMin = dimensions[0],
                    AnchorMax = dimensions[1],
                    OffsetMin = dimensions[2],
                    OffsetMax = dimensions[3],
                        },
                        Image = {
                    Color = color,
                    //Material = "assets/icons/greyout.mat"
                        }
                    },
                    parent,
                    name,
                    destroyUi
                );
            }

            public static void CreateImage(ref CuiElementContainer container, string parent, string name, string image, string color, string anchor, string offset)
            {
                var dimensions = ParseDimensions(anchor, offset);

                uint _value;
                var isPng = uint.TryParse(image, out _value);

                container.Add(
                    new CuiElement
                    {
                        Parent = parent,
                        Name = name ?? CuiHelper.GetGuid(),
                        Components = {
                    new CuiRectTransformComponent {
                        AnchorMin = dimensions[0],
                        AnchorMax = dimensions[1],
                        OffsetMin = dimensions[2],
                        OffsetMax = dimensions[3],
                    },
                    new CuiRawImageComponent {
                        Png = isPng ? image : null,
                        Url = !isPng ? image : null,
                        Color = color
                    }
                        }
                    }
                );
            }

            public static void CreateLabel(ref CuiElementContainer container, string parent, string name, string text, string color, int fontSize, string anchor, string offset, TextAnchor align = TextAnchor.MiddleLeft, string destoryUI = null)
            {
                var dimensions = ParseDimensions(anchor, offset);

                container.Add(
                    new CuiLabel
                    {
                        RectTransform = {
                    AnchorMin = dimensions[0],
                    AnchorMax = dimensions[1],
                    OffsetMin = dimensions[2],
                    OffsetMax = dimensions[3],
                        },
                        Text = {
                        Text = text,
                        Color = color,
                        Align = align,
                        FontSize = fontSize
                        }
                    },
                    parent,
                    name,
                    destoryUI

                );
            }

            private static string[] ParseDimensions(string anchor, string offset)
            {
                var anchors = anchor.Split(' ');
                string anchorMin = string.Join(" ", anchors.Take(2)),
                    anchorMax = string.Join(" ", anchors.Skip(2));

                var offsets = offset.Split(' ');
                string offsetMin = string.Join(" ", offsets.Take(2)),
                    offsetMax = string.Join(" ", offsets.Skip(2));

                return new string[] {
                anchorMin,
                anchorMax,
                offsetMin,
                offsetMax
                };
            }
        }

        private static void ParseHexColor(string hexColor, int opacity)
        {
            if (hexColor.StartsWith("#"))
            {
                hexColor = hexColor.Substring(1);
            }
            if (hexColor.Length != 6)
            {
                throw new FormatException("Invalid hex color format.");
            }
            int r = Convert.ToInt32(hexColor.Substring(0, 2), 16);
            int g = Convert.ToInt32(hexColor.Substring(2, 2), 16);
            int b = Convert.ToInt32(hexColor.Substring(4, 2), 16);
            float rF = r / 255f;
            float gF = g / 255f;
            float bF = b / 255f;
            float opacityF = opacity / 255f;
            string color = $"{rF} {gF} {bF} {opacityF}";
        }

        #endregion

        #region Discord

        private static void SendDiscordNotification(string message)
        {
            if (string.IsNullOrEmpty(ins.config.discordSetting.WebhookURL)) return;

            var payload = new Dictionary<string, string>
            {
                { "content", message }
            };

            var jsonPayload = JsonConvert.SerializeObject(payload);

            ins.webrequest.EnqueuePost(ins.config.discordSetting.WebhookURL, jsonPayload, (code, response) =>
            {
                if (code != 204)
                {
                    ins.Puts($"Error Discord: {code} - {response}");
                }
            }, ins, new Dictionary<string, string> { { "Content-Type", "application/json" } });
        }

        private void SendRaidStartedEmbed(string attackerClan, string defenderClan, Vector3 position)
        {
            if (!config.discordSetting.UseRichEmbeds || string.IsNullOrEmpty(config.discordSetting.WebhookURL))
            {
                SendDiscordNotification(GetMessage("RaidBubbleStartDiscord", attackerClan, defenderClan));
                return;
            }

            var embed = new
            {
                title = "Raid Started",
                color = config.discordSetting.Colors.RaidStarted,
                fields = new[]
                {
                    new { name = "Attackers", value = $"**{attackerClan}**", inline = true },
                    new { name = "Defenders", value = $"**{defenderClan}**", inline = true },
                    new { name = "Location", value = GetGridPosition(position), inline = true }
                },
                thumbnail = new { url = "https://cdn.discordapp.com/attachments/1394159843534831778/1394545348801990667/US10X.png?ex=68773310&is=6875e190&hm=98bf3d12e6a39ccc745e59e643214b81c5c030e9e5bb524f476cb90e607b64d1&" },
                footer = new
                {
                    text = "Awaken Rust Servers",
                    icon_url = "https://cdn.discordapp.com/attachments/1394159843534831778/1394545299011276850/US10X.png?ex=68773304&is=6875e184&hm=7f978db21081243aa83404915c851051ac80d82c05a29184a77ac840916a0396&"
                },
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            };

            SendDiscordEmbed(embed);
        }

        private void SendRaidEndedEmbed(RaidBubbleComp raid, bool attackerWon)
        {
            if (!config.discordSetting.UseRichEmbeds || string.IsNullOrEmpty(config.discordSetting.WebhookURL))
            {
                if (attackerWon)
                    SendDiscordNotification(string.Format(config.discordSetting.WinMessage, raid.raidBubbleSetup.AttackerClanName, raid.raidBubbleSetup.DefenderClanName, GetFormatTime(raid.RaiderTotalTime)));
                else
                    SendDiscordNotification(string.Format(config.discordSetting.LoseMessage, raid.raidBubbleSetup.DefenderClanName, raid.raidBubbleSetup.AttackerClanName));
                return;
            }

            var raidDuration = Time.realtimeSinceStartup - raid.RaidStartTime;
            var formattedDuration = FormatDuration(raidDuration);

            // Use actual in-game counts, not total clan member counts
            var attackerCount = raid.RaiderCount();
            var defenderCount = raid.DefenderCount();

            // Also get clan member counts for context
            var attackerClanSize = GetClanMemberCount(raid.raidBubbleSetup.AttackerClanName);
            var defenderClanSize = GetClanMemberCount(raid.raidBubbleSetup.DefenderClanName);

            // Create two separate embeds as requested
            SendRaidInfoEmbed(raid, attackerWon, formattedDuration, attackerCount, defenderCount, attackerClanSize, defenderClanSize);
            timer.Once(0.5f, () => SendTeamStatsEmbed(raid, attackerCount, defenderCount));
        }

        private void SendRaidInfoEmbed(RaidBubbleComp raid, bool attackerWon, string formattedDuration, int attackerCount, int defenderCount, int attackerClanSize, int defenderClanSize)
        {
            var embed = new
            {
                title = attackerWon ? $"{raid.raidBubbleSetup.DefenderClanName.ToUpper()} has been raided" : $"{raid.raidBubbleSetup.DefenderClanName.ToUpper()} successfully defended",
                description = $"{raid.raidBubbleSetup.AttackerClanName} x {raid.raidBubbleSetup.DefenderClanName}\nActive: {attackerCount} vs {defenderCount} | Clan Size: {attackerClanSize} vs {defenderClanSize}",
                color = attackerWon ? config.discordSetting.Colors.RaidWon : config.discordSetting.Colors.RaidDefended,
                fields = new[]
                {
                    new {
                        name = attackerWon ? $"Raid time: {formattedDuration}" : $"Defense time: {formattedDuration}",
                        value = $"Total rockets: **{raid.TotalRockets}** | Total c4: **{raid.TotalC4}**",
                        inline = false
                    }
                },
                thumbnail = new { url = "https://cdn.discordapp.com/attachments/1394159843534831778/1394545348801990667/US10X.png?ex=68773310&is=6875e190&hm=98bf3d12e6a39ccc745e59e643214b81c5c030e9e5bb524f476cb90e607b64d1&" },
                footer = new
                {
                    text = "Awaken Rust Servers",
                    icon_url = "https://cdn.discordapp.com/attachments/1394159843534831778/1394545299011276850/US10X.png?ex=68773304&is=6875e184&hm=7f978db21081243aa83404915c851051ac80d82c05a29184a77ac840916a0396&"
                },
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            };

            SendDiscordEmbed(embed);
        }

        private void SendTeamStatsEmbed(RaidBubbleComp raid, int attackerCount, int defenderCount)
        {
            // Calculate total rockets and C4 for each team (including merge allies)
            var attackerTotalRockets = raid.AttackerRockets;
            var attackerTotalC4 = raid.AttackerC4;
            var defenderTotalRockets = raid.DefenderRockets;
            var defenderTotalC4 = raid.DefenderC4;

            // Calculate control time percentages
            var raidDuration = Time.realtimeSinceStartup - raid.RaidStartTime;
            var attackerControlTime = CalculateControlTime(raid.RaiderTime, raidDuration, true);
            var defenderControlTime = CalculateControlTime(raid.RaiderTime, raidDuration, false);

            var embed = new
            {
                title = $"Active Raiders ({attackerCount}) vs Defenders ({defenderCount})",
                color = 0xFF6B6B, // Red color for raiders
                fields = new[]
                {
                    new {
                        name = $"{raid.raidBubbleSetup.AttackerClanName} ({attackerCount})",
                        value = $"Control time: **{attackerControlTime}**\nRockets: **{attackerTotalRockets}** | c4: **{attackerTotalC4}**\nKills: **{raid.AttackerKills}** | Deaths: **{raid.AttackerDeaths}** | KDR: **{(raid.AttackerDeaths == 0 ? raid.AttackerKills : (float)raid.AttackerKills / raid.AttackerDeaths):F1}**",
                        inline = true
                    },
                    new {
                        name = $"{raid.raidBubbleSetup.DefenderClanName} ({defenderCount})",
                        value = $"Control time: **{defenderControlTime}**\nRockets: **{defenderTotalRockets}** | c4: **{defenderTotalC4}**\nKills: **{raid.DefenderKills}** | Deaths: **{raid.DefenderDeaths}** | KDR: **{(raid.DefenderDeaths == 0 ? raid.DefenderKills : (float)raid.DefenderKills / raid.DefenderDeaths):F1}**",
                        inline = true
                    }
                },
                footer = new
                {
                    text = "Awaken Rust Servers",
                    icon_url = "https://cdn.discordapp.com/attachments/1394159843534831778/1394545287464484966/MAIN.png?ex=68773301&is=6875e181&hm=d8fabb05f60d670e79530a22d606ce91ebfbdc19efcef83b7e6281e52bdebd0f&"
                },
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            };

            SendDiscordEmbed(embed);
        }

        private void SendDiscordEmbed(object embed)
        {
            var payload = new
            {
                embeds = new[] { embed },
                username = "Awaken Raid Bubble",
                avatar_url = "https://cdn.discordapp.com/attachments/1394159843534831778/1394545287464484966/MAIN.png?ex=68773301&is=6875e181&hm=d8fabb05f60d670e79530a22d606ce91ebfbdc19efcef83b7e6281e52bdebd0f&"
            };

            var json = JsonConvert.SerializeObject(payload);
            webrequest.EnqueuePost(config.discordSetting.WebhookURL, json, (code, response) =>
            {
                if (code != 200 && code != 204)
                    PrintError($"Discord webhook failed: {code} - {response}");
            }, this, new Dictionary<string, string> { ["Content-Type"] = "application/json" });
        }


        #region Utility Methods
        private string GetGridPosition(Vector3 position)
        {
            const float scale = 150f;
            float x = position.x + World.Size / 2f;
            float z = position.z + World.Size / 2f;
            int lat = (int)(x / scale);
            char latChar = (char)('A' + lat);
            int lon = (int)(World.Size / scale - z / scale);
            return latChar + lon.ToString();
        }

        private string FormatDuration(float seconds)
        {
            var timeSpan = TimeSpan.FromSeconds(seconds);
            if (timeSpan.TotalHours >= 1)
                return $"{(int)timeSpan.TotalHours}h {timeSpan.Minutes}m";
            return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
        }

        private int GetClanMemberCount(string clanName)
        {
            if (string.IsNullOrEmpty(clanName))
                return 0;

            try
            {
                var clanMembers = GetClanMembers(clanName);
                return clanMembers.Count;
            }
            catch (Exception ex)
            {
                PrintError($"Error getting clan member count for {clanName}: {ex.Message}");
                return 0;
            }
        }

        private string CalculateControlTime(int raiderTime, float totalDuration, bool isAttacker)
        {
            float percentage = isAttacker ?
                (raiderTime / totalDuration) * 100f :
                ((totalDuration - raiderTime) / totalDuration) * 100f;

            var duration = FormatDuration(isAttacker ? raiderTime : totalDuration - raiderTime);
            return $"{duration} ({percentage:F1}%)";
        }

        private string GetAttackerStatsText(RaidBubbleComp raid, string controlTime)
        {
            var kdr = raid.AttackerDeaths == 0 ? raid.AttackerKills : (float)raid.AttackerKills / raid.AttackerDeaths;
            return $"**{raid.raidBubbleSetup.AttackerClanName}** ({GetClanMemberCount(raid.raidBubbleSetup.AttackerClanName)})\n" +
                   $"**Control time:** {controlTime}\n" +
                   $"**Rockets:** {raid.AttackerRockets} | **c4:** {raid.AttackerC4}\n" +
                   $"**Kills:** {raid.AttackerKills} | **Deaths:** {raid.AttackerDeaths} | **KDR:** {kdr:F1}";
        }

        private string GetDefenderStatsText(RaidBubbleComp raid, string controlTime)
        {
            var kdr = raid.DefenderDeaths == 0 ? raid.DefenderKills : (float)raid.DefenderKills / raid.DefenderDeaths;
            return $"**{raid.raidBubbleSetup.DefenderClanName}** ({GetClanMemberCount(raid.raidBubbleSetup.DefenderClanName)})\n" +
                   $"**Control time:** {controlTime}\n" +
                   $"**Rockets:** {raid.DefenderRockets} | **c4:** {raid.DefenderC4}\n" +
                   $"**Kills:** {raid.DefenderKills} | **Deaths:** {raid.DefenderDeaths} | **KDR:** {kdr:F1}";
        }
        #endregion

        #endregion
    }
}