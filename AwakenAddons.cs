using Oxide.Core.Libraries.Covalence;
using System;
using UnityEngine;
using System.Linq;
using System.Collections.Generic;
using Newtonsoft.Json;
using System.Collections;

namespace Oxide.Plugins
{
    [Info("Awaken Addons", "Skelee", "1.1.0")]
    [Description("Adds items and entities to monuments.")]

    public class AwakenAddons : CovalencePlugin
    {
        #region Defines
        List<MonumentInfo> monuments = new List<MonumentInfo>();
        Dictionary<BaseEntity, EntInfo> monumentAddons = new Dictionary<BaseEntity, EntInfo>();
        #endregion

        #region Config
        static Configuration config;
        public class EntInfo
        {
            public MonumentInfo mInfo = null;
            public AddonInfo aInfo = null;
        }

        public class AddonInfo
        {
            public string prefab = string.Empty;
            public Vector3 position = default(Vector3);
            public float roation = 0f;
            public bool allowRespawn = true;
            public int respawnTime = 300;
        }

        public class Configuration
        {
            [JsonProperty(PropertyName = "Monuments")]
            public Dictionary<string, List<AddonInfo>> monumentAddons;
            public static Configuration DefaultConfig()
            {
                return new Configuration
                {
                    monumentAddons = new Dictionary<string, List<AddonInfo>>()
                    {
                        { "Power Plant", new List<AddonInfo>()
                            {
                                new AddonInfo
                                {
                                    prefab = "assets/bundled/prefabs/radtown/crate_elite.prefab",
                                    position = new Vector3(-33.8f, 12.3f, 11.4f),
                                    roation = 88.5f,
                                    allowRespawn = true,
                                    respawnTime = 240
                                }
                            }
                        }
                    }
                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = Configuration.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Hooks
        private void OnServerInitialized(bool initial)
        {
            monuments.AddRange(TerrainMeta.Path.Monuments.Where(m => !string.IsNullOrEmpty(m.displayPhrase.english)));
            if ((config?.monumentAddons.Count ?? 0) == 0) return;

            foreach (var (monumentName, addons) in config.monumentAddons)
                foreach (var monument in FindMonumentsOfType(monumentName))
                    foreach (var addon in addons)
                        ServerMgr.Instance.StartCoroutine(SpawnEntity(monument, addon));
        }

        private void Unload()
        {
            monumentAddons.Keys.ToList().ForEach(ent => ent.Kill());
            ServerMgr.Instance.StopAllCoroutines();
            monumentAddons.Clear();
            monuments.Clear();
        }

        private void OnEntityKill(BaseEntity? entity)
        {
            if (entity == null || !monumentAddons.Remove(entity, out var entInfo) || !entInfo.aInfo.allowRespawn) return;
            ServerMgr.Instance.StartCoroutine(SpawnEntity(entInfo.mInfo, entInfo.aInfo, entInfo.aInfo.respawnTime));
        }
        #endregion

        #region Functions
        private List<MonumentInfo> FindMonumentsOfType(string displayName) =>
            monuments.Where(info => info.displayPhrase.english.Equals(displayName)).ToList();

        private IEnumerator SpawnEntity(MonumentInfo info, AddonInfo aInfo, float respawnTime = 0f)
        {
            yield return new WaitForSeconds(respawnTime);
            var position = info.transform.TransformPoint(aInfo.position);
            var rotation = Quaternion.Euler(0, info.transform.rotation.eulerAngles.y - aInfo.roation, 0);

            var entity = GameManager.server.CreateEntity(aInfo.prefab, position, rotation);
            if (entity == null) yield break;

            entity.enableSaving = false;
            entity.Spawn();
            monumentAddons[entity] = new() { mInfo = info, aInfo = aInfo };
        }
        #endregion

        #region Commands
        [Command("getlocalpos")]
        private void GetLocalPosCommand(IPlayer iPlayer, string cmd, string[] args)
        {
            if (iPlayer.IsServer) return;
            if (iPlayer.Object is not BasePlayer player) return;

            var monument = TerrainMeta.Path.Monuments.FirstOrDefault(m => Vector3.Distance(m.transform.position, player.transform.position) < 150);
            if (monument == null) { player.ChatMessage("Could not find monument nearby."); return; }

            player.ChatMessage($"You are currently near {monument.displayPhrase.english}");

            var localPosition = monument.transform.InverseTransformPoint(player.transform.position);
            var localRotationAngle = player.HasParent() ? 180 - player.viewAngles.y : monument.transform.rotation.eulerAngles.y - player.viewAngles.y + 180;

            player.ChatMessage($"Local position: {localPosition}");
            player.ChatMessage($"Local rotation: {localRotationAngle}");
        }
        #endregion
    }
}









