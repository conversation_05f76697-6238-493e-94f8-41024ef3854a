﻿// Copyright © 2025 Tryware OÜ. All rights reserved.
// SUPPORT DISCORD: https://discord.gg/YBAj4UrSMw

using Facepunch;
using Newtonsoft.Json;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using Color = UnityEngine.Color;
using Random = System.Random;

namespace Oxide.Plugins
{
    [Info("Simple Radar", "Tryhard", "1.4.1")]
    public class SimpleRadar : RustPlugin
    {
        #region Fields

        //Stores all stashes on the server
        ListHashSet<StashContainer> stashes = new();
        //Stores all TCs on the server
        ListHashSet<BuildingPrivlidge> tcs = new();
        //Stores all randomly generated colors for each team on the server
        Dictionary<ulong, string> clanColors = new();
        //Stores the voice data of players
        Dictionary<ulong, TimeSince> voiceTransmitters;
        public static SimpleRadar instance;
        Coroutine c;
        private readonly Random _random = new Random();
        private readonly byte[] _buffer = new byte[3];
        public static object[] argArray4 = new object[4];
        public static object[] argArray5 = new object[5];

        #endregion

        #region Commands

          //Radar chat command
        [ChatCommand("radar")]
        private void RadarChatCommand(BasePlayer player, string command, string[] args)
        {
            if (HasPermission(player))
            {
                switch (args.Length)
                {
                    case 3:
                        StartRadar(player, args[0], args[1], args[2]);
                        return;
                    case 2:
                        StartRadar(player, args[0], args[1], "players");
                        return;
                    case 1:
                        if (args[0] == "help")
                        {
                            SendReply(player, "Use /radar <updaterate> <distance> <players/stashes/tcs/all>");
                            return;
                        }
                        StartRadar(player, args[0], "250", "players");
                        return;
                    case 0:
                        RadarBehaviour component = player.GetComponent<RadarBehaviour>();

                        if (component != null)
                        {
                            SendReply(player, "Radar toggled off!");
                            UnityEngine.Object.Destroy(component);
                        }
                        else
                        {
                            StartRadar(player, "1", "250", "players");
                        }
                        break;
                    default:
                        SendReply(player, "Use /radar <updaterate> <distance> <players/stashes/tcs/all>");
                        return;
                }
            }
            else
            {
                SendReply(player, $"No permission to use radar");
            }
        }

        //Radar console command
        [ConsoleCommand("radar")]
        private void RadarConsoleCommand(ConsoleSystem.Arg arg)
        {
            if (arg == null || arg.IsRcon) return;
            BasePlayer player = (BasePlayer)arg.Connection.player;
            if (player == null) return;
            var args = arg.Args;
            if (HasPermission(player))
            {
                switch (args.Length)
                {
                    case 3:
                        StartRadar(player, args[0], args[1], args[2]);
                        return;
                    case 2:
                        StartRadar(player, args[0], args[1], "players");
                        return;
                    case 1:
                        if (args[0] == "help")
                        {
                            PrintToConsole(player, "Use /radar <updaterate> <distance> <players/stashes/tcs/all>");
                            return;
                        }
                        StartRadar(player, args[0], "250", "players");
                        return;
                    case 0:
                        RadarBehaviour component = player.GetComponent<RadarBehaviour>();

                        if (component != null)
                        {
                            PrintToConsole(player, "Radar toggled off!");
                            UnityEngine.Object.Destroy(component);
                        }
                        else
                        {
                            StartRadar(player, "1", "250", "players");
                        }
                        break;
                    default:
                        PrintToConsole(player, "Use /radar <updaterate> <distance> <players/stashes/tcs/all>");
                        return;
                }
            }
            else
            {
                PrintToConsole(player, $"No permission to use radar");
            }
        }

        #endregion

        #region Hooks

        private void OnServerInitialized()
        {
            permission.RegisterPermission("simpleradar.use", this);

            argArray4[1] = Color.white;
            argArray5[1] = Color.white;
            argArray5[4] = 0.1f;
            
            instance = this;
            
            if (config.voiceIndicator)
            {
                voiceTransmitters = new Dictionary<ulong, TimeSince>();
            }
            else Unsubscribe(nameof(OnPlayerVoice));

            //Finds all stashes and adds them to the list
            foreach (var ent in BaseNetworkable.serverEntities)
            {
                if (ent == null) continue;
                var stash = ent as StashContainer;
                if (stash != null)
                {
                    if (!stashes.Contains(stash)) stashes.Add(stash);
                    continue;
                }

                var tc = ent as BuildingPrivlidge;
                if (tc != null)
                {
                    if (!tcs.Contains(tc)) tcs.Add(tc);
                    continue; //Don't need this continue here really but my OCD is would kill me
                }
            }

            //Using a coroutine to add delays to prevent generating the same color, can't use task.delay becasuse of oxide sandbox :sadge:
            c = ServerMgr.Instance.StartCoroutine(InitializeTeams());
        }
        
        //Removes the radarbehaviour from players on unload
        private void Unload()
        {
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player == null || !HasPermission(player)) continue;
                RadarBehaviour component = player.GetComponent<RadarBehaviour>();

                if (component != null)
                {
                    UnityEngine.Object.Destroy(component);
                }
            }

            instance = null;
            c = null;
        }
        
        //Removes the radarbehaviour from the player on disconnect
        private void OnPlayerDisconnected(BasePlayer player)
        {
            if (player == null) return;
            RadarBehaviour component = player.GetComponent<RadarBehaviour>();

            if (component != null)
            {
                UnityEngine.Object.Destroy(component);
            }
        }

        //Records all newly placed stashes
        private void OnEntitySpawned(StashContainer stash)
        {
            if (stash == null) return;
            if (!stashes.Contains(stash)) stashes.Add(stash);
        }
        
        //Records all newly placed TCs
        private void OnEntitySpawned(BuildingPrivlidge tc)
        {
            if (tc == null) return;
            if (!tcs.Contains(tc)) tcs.Add(tc);
        }

        //Removes all destroyed stashes
        private void OnEntityKill(StashContainer stash)
        {
            if (stash == null) return;
            if (stashes.Contains(stash)) stashes.Remove(stash);
        }

        //Removes all destroyed TCs
        private void OnEntityKill(BuildingPrivlidge tc)
        {
            if (tc == null) return;
            if (tcs.Contains(tc)) tcs.Remove(tc);
        }

        //Generates a random color and stores it for the freshly created team
        private void OnTeamCreated(BasePlayer player, RelationshipManager.PlayerTeam team)
        {
            if (team == null) return;
            clanColors.Add(team.teamID, GetRandomColor());
        }

        //Saves people who speak
        private void OnPlayerVoice(BasePlayer player, Byte[] data)
        {
            if (player == null) return;
            
            voiceTransmitters[player.userID] = 0;
        }

        #endregion

        #region Methods

        private void StartRadar(BasePlayer player, string rate, string distance, string mode)
        {
            //Checks if the player already has radar enabled and if he does it stops the radar
            RadarBehaviour component = player.GetComponent<RadarBehaviour>();

            if (component != null)
            {
                UnityEngine.Object.Destroy(component);
            }

            //Gets the float values of the distance and rate from the string values
            float frate;
            if (!float.TryParse(rate, out frate))
            {
                SendReply(player, "Invalid update rate | Use /radar <updaterate> <distance> <players/stashes/tcs/all>");
                return;
            }

            float fdistance;
            if (!float.TryParse(distance, out fdistance))
            {
                SendReply(player, "Invalid distance | Use /radar <updaterate> <distance> <players/stashes/tcs/all>");
                return;
            }

            //Creates a new radarbehaviour and adds it to the player
            var newcomponent = player.gameObject.AddComponent<RadarBehaviour>();
            newcomponent.rate = frate < config.minimumRefresh ? config.minimumRefresh : frate;
            newcomponent.dist = fdistance > config.maximumDistance ? config.maximumDistance : fdistance;
            newcomponent.player = player;
            newcomponent.mode = mode;
            newcomponent.Init();
        }

        private IEnumerator InitializeTeams()
        {
            //Finds all clans and generates a random color for them
            List<RelationshipManager.PlayerTeam> teams = Pool.Get<List<RelationshipManager.PlayerTeam>>();

            teams.AddRange(RelationshipManager.ServerInstance.teams.Values);

            foreach (var team in teams)
            {
                if (team == null || clanColors.ContainsKey(team.teamID))
                {
                    continue;
                }

                clanColors.Add(team.teamID, GetRandomColor());
                yield return CoroutineEx.waitForSeconds(0.1f);
            }

            Pool.FreeUnmanaged(ref teams);

            yield return CoroutineEx.waitForEndOfFrame;

            c = null;
        }

        //Random color generator
        private string GetRandomColor()
        {
            _random.NextBytes(_buffer);
            return "#" + BitConverter.ToString(_buffer).Replace("-", "");
        }

        private string GetClanColor(ulong id)
        {
            if (!clanColors.ContainsKey(id))
            {
                clanColors.Add(id, GetRandomColor());
            }
            return clanColors[id];
        }
        
        private bool HasPermission(BasePlayer player) => permission.UserHasPermission(player.UserIDString, "simpleradar.use") || player.Connection.authLevel > 0;

        #endregion

        #region Classes

        public class RadarBehaviour : FacepunchBehaviour
        {
            //Update rate for the invokerepeating
            public float rate;
            //Maximum visibility distance
            public float dist;
            //Player who ran the radar command
            public BasePlayer player;
            //Radar mode, either players/all/stashes
            public string mode;

            private StringBuilder sb;

            public void Init()
            {
                sb = Pool.Get<StringBuilder>();
                sb.Clear(); // FP doesn't clear strinbuilders after using them so we should clear it!

                bool isPlayer = false;
                
                //Starts radar with the correct mode
                switch (mode.ToLower()) // ToLower ass but don't care not called often enough
                {
                    case "all":
                        isPlayer = true;
                        InvokeRepeating(() =>
                        {
                            UpdatePlayerRadar();
                            UpdateStashRadar();
                            UpdateTCRadar();
                        }, rate, rate);
                        break;
                    case "players":
                        isPlayer = true;
                        InvokeRepeating(UpdatePlayerRadar, rate, rate);
                        break;
                    case "stashes":
                        InvokeRepeating(UpdateStashRadar, rate, rate);
                        break;
                    case "tcs":
                        InvokeRepeating(UpdateTCRadar, rate, rate);
                        break;
                    default:
                        //Handle invalid mode
                        OnDestroy();
                        instance.SendReply(player, "Use /radar <updaterate> <distance> <players/stashes/all>");
                        return;
                }

                sb.Clear();
                
                sb.Append("Radar enabled with ")
                  .Append(mode)
                  .Append(" mode, ")
                  .Append(rate)
                  .Append(" second refresh rate and ")
                  .Append(dist)
                  .Append(" meter radius!");
                
                if (dist > 250 && isPlayer)
                {
                    sb.Append(" NB: Your distance is over 250m, this has a larger performance impact on the server!");
                }

                //Send sucess message to the player
                instance.SendReply(player, sb.ToString());
            }

            private void UpdatePlayerRadar()
            {
                if (dist > 250)
                {
                    foreach (var user in BasePlayer.activePlayerList)
                    {
                        DrawRadarIfNeeded(user);
                    }
                }
                else
                {
                    //Gets all players from the network group
                    foreach (var connection in player.net.group.subscribers)
                    {
                        //Check if it's an active connection
                        if (!connection.active) continue;

                        //Get's the baseplayer from the connection
                        BasePlayer user = connection.player as BasePlayer;
                    
                        //Draws the radar text above the player
                        DrawRadarIfNeeded(user);
                    }
                }
            }

            private void DrawRadarIfNeeded(BasePlayer user)
            {
                //Makes sure the player isn't the radar user so it doesn't add a text on top of your own head
                if (user == null || user.userID == player.userID) return;

                if (config.hideAuth2 && user.Connection?.authLevel == 2 && player.Connection.authLevel == 1) return;

                //Calculates the distance and makes sure it's not bigger than the allowed distance
                var distance = Math.Round((user.transform.position - player.transform.position).magnitude);
                if (distance > dist) return;

                sb.Append("<size=15>")
                    .Append(user.displayName)
                    .Append(" | ");

                if (user.IsDead())
                {
                    sb.Append("<color=#ff0000>")
                        .Append(Math.Round(user.health))
                        .Append("</color>");
                }
                else if (user.health < 51)
                {
                    sb.Append("<color=#ffb300>")
                        .Append(Math.Round(user.health))
                        .Append("</color>");
                }
                else
                {
                    sb.Append("<color=#168503>")
                        .Append(Math.Round(user.health))
                        .Append("</color>");
                }

                sb.Append("HP <color=#002fff>")
                    .Append(distance)
                    .Append("</color>M</size>");

                //Checks if the player is in a team to know if it should add the team color idicator
                var team = user.Team;
                if (team != null)
                {
                    sb.Append($" | <color=");
                    sb.Append(instance.GetClanColor(team.teamID));
                    sb.Append(">T</color>");
                }

                if (config.voiceIndicator && instance.voiceTransmitters.TryGetValue(user.userID, out TimeSince ts))
                {
                    if (ts < config.indicatorDuration)
                    {
                        sb.Append(" | <color=#9ef507>S</color>");
                    }
                    else
                    {
                        instance.voiceTransmitters.Remove(player.userID);
                    }
                }

                argArray4[0] = rate;
                argArray4[2] = user.transform.position + new Vector3(0, 2, 0);
                argArray4[3] = sb.ToString();

                player.SendConsoleCommand("ddraw.text", argArray4);

                //Draws an arrow where the player is looking
                if (config.arrows)
                {
                    RaycastHit hit;
                    if (Physics.Raycast(user.eyes.HeadRay(), out hit, Mathf.Infinity))
                    {
                        argArray5[0] = rate;
                        argArray5[2] = user.eyes.position;
                        argArray5[3] = hit.point;

                        player.SendConsoleCommand("ddraw.arrow", argArray5);
                    }
                }

                sb.Clear();
            }

            private void UpdateStashRadar()
            {
                foreach (var stash in instance.stashes)
                {
                    if (stash == null)
                    {
                        continue;
                    }

                    var distance = Math.Round((stash.transform.position - player.transform.position).magnitude);
                    if (distance > dist) continue;

                    sb.Append("<size=13><color=#e642f5>Stash</color> | <color=#002fff>")
                      .Append(distance)
                      .Append("</color>M</size>");

                    if (stash.IsHidden())
                    {
                        sb.Append(" | <color=#e642f5>(H)</color>");
                    }
                    
                    argArray4[0] = rate;
                    argArray4[2] = stash.transform.position + new Vector3(0, 0.5f, 0);
                    argArray4[3] = sb.ToString();

                    player.SendConsoleCommand("ddraw.text", argArray4);

                    sb.Clear();
                }
            }

            private void UpdateTCRadar()
            {
                foreach (var tc in instance.tcs)
                {
                    if (tc == null)
                    {
                        continue;
                    }

                    var distance = Math.Round((tc.transform.position - player.transform.position).magnitude);
                    if (distance > dist) continue;

                    sb.Append("<size=14><color=#05f5e5>TC</color> | <color=#002fff>")
                      .Append(distance)
                      .Append("</color>M</size>");
                    
                    argArray4[0] = rate;
                    argArray4[2] = tc.transform.position + new Vector3(0, 0.5f, 0);
                    argArray4[3] = sb.ToString();

                    player.SendConsoleCommand("ddraw.text", argArray4);

                    sb.Clear();
                }
            }

            public void OnDestroy()
            {
                CancelInvoke(UpdatePlayerRadar);
                CancelInvoke(UpdateStashRadar);
                CancelInvoke(UpdateTCRadar);
                Pool.FreeUnmanaged(ref sb);
                Destroy(this);
            }
        }

        #endregion

        #region Config

        private static Configuration config;

        public class Configuration
        {
            [JsonProperty("Make players with moderatorid not see players with ownerid")]
            public bool hideAuth2 = true;

            [JsonProperty("Enable player vision arrows")]
            public bool arrows = true;

            [JsonProperty("Add a voice indicator for players who are tansmitting voice")]
            public bool voiceIndicator = false;

            [JsonProperty("Show voice indicator for x seconds after speaking")]
            public float indicatorDuration = 2f;

            [JsonProperty("Minimum allowed radar refresh rate")]
            public float minimumRefresh = 1f;

            [JsonProperty("Maximum radar distance")]
            public float maximumDistance = 250f;
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = new Configuration();
        protected override void SaveConfig() => Config.WriteObject(config);

        #endregion
    }
}