using Network;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Configuration;
using Oxide.Core.Libraries.Covalence;
using Oxide.Game.Rust.Cui;
using ProtoBuf;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using Facepunch;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.Pool;


namespace Oxide.Plugins
{
    [Info("Skinner", "Whispers88 // Skelee", "2.1.9")]
    [Description("Brings automation and ease to skinning items")]
    public class Skinner : CovalencePlugin
    {
        static Skinner skinner;

        #region Fields
        private List<string> _WorkshopSkinIDCollectionList = new List<string>();
        private Dictionary<ulong, BoxController> _viewingcon = new Dictionary<ulong, BoxController>();
        private Dictionary<ulong, int> _playerSelectedSet = new Dictionary<ulong, int>();
        private Dictionary<string, uint> _imageCache = new Dictionary<string, uint>();
        private Dictionary<ulong, AwakenSkinboxState> _playerUIState = new Dictionary<ulong, AwakenSkinboxState>();
        private static WaitForSeconds _WaitForSecondsMore;
        private ImageDownloader _imageDownloader;


        // UI Panel names
        public const string SkinPageUI = "SkinPageUI";
        public const string SkinSearchUI = "SkinSearchUI";
        public const string SkinSetsSelectUI = "SkinSetsSelectUI";
        public const string SkinRequestsUI = "SkinRequestsUI";
        public const string AWAKEN_SKINBOX_MAIN = "AwakenSkinbox.MainUI";


        #endregion

        #region Perms
        private const string permdefault = "skinner.default";
        private const string permitems = "skinner.items";
        private const string permcraft = "skinner.craft";
        private const string permskininv = "skinner.skininv";
        private const string permskinteam = "skinner.skinteam";
        private const string permskinteamblock = "skinner.skinteamblock";
        private const string permskincon = "skinner.skincon";
        private const string permbypassauth = "skinner.bypassauth";
        private const string permimport = "skinner.import";
        private const string permskinbase = "skinner.skinbase";
        private const string permskinall = "skinner.skinall";
        private const string permskinauto = "skinner.skinauto";
        private const string permskinautotoggle = "skinner.skinautotoggled";
        private const string permskinrequest = "skinner.skinrequest";
        private const string permskintry = "skinner.permskintry";

        private List<string> permissions = new List<string>() { permdefault, permcraft, permitems, permbypassauth, permskincon, permskininv, permimport, permskinbase, permskinall, permskinteam, permskinteamblock, permskinrequest, permskintry, permskinauto, permskinautotoggle };

        #endregion Perms

        #region Init
        private void OnServerInitialized()
        {
            skinner = this;
            _skinNames = new Dictionary<ulong, string>();
            _cachedSkins = new Dictionary<int, List<ulong>>();
            _imageCache = new Dictionary<string, uint>();
            _imageDownloader = ServerMgr.Instance.gameObject.AddComponent<ImageDownloader>();
            _imageDownloader.plugin = this;
            _WaitForSecondsMore = CoroutineEx.waitForSeconds(1f);

            foreach (string perm in permissions)
                permission.RegisterPermission(perm, this);

            foreach (string perm in config.Cooldowns.Keys)
                permission.RegisterPermission($"skinner.{perm}", this);

            AddCovalenceCommand(config.cmdsskin, "SkinCMD");
            AddCovalenceCommand(config.cmdsskincraft, "DefaultSkinsCMD");
            AddCovalenceCommand(config.cmdsskinitems, "SkinItemCMD");
            AddCovalenceCommand(config.cmdsskininv, "SkinInvCMD");
            AddCovalenceCommand(config.cmdsskincon, "SkinConCMD");
            AddCovalenceCommand(config.cmdtoggleautoskin, "SkinAutoCMD");
            AddCovalenceCommand(config.cmdskinimport, "SkinImportCMD");
            AddCovalenceCommand(config.cmdcollectionimport, "SkinImportCollection");
            AddCovalenceCommand(config.cmdskinbase, "SkinBaseCMD");
            AddCovalenceCommand(config.cmdskinallitems, "SkinAllItemsCMD");
            AddCovalenceCommand(config.cmdskinteam, "SkinTeamCMD");
            AddCovalenceCommand(config.cmdskinrequest, "SkinRequestCMD");
            List<string> tempList = new List<string>(config.cmdskinset);
            tempList.Add("setSelectCMD");
            AddCovalenceCommand(tempList.ToArray(), "SetSelectCMD");
            AddCovalenceCommand(config.cmdskinrequests, "SkinRequestsCMD");

            AddCovalenceCommand(new[] { "sbNextPage" }, "SBNextPageCMD");
            AddCovalenceCommand(new[] { "sbBackPage" }, "SBBackPageCMD");
            AddCovalenceCommand(new[] { "searchCMD" }, "SearchCMD");
            AddCovalenceCommand(new[] { "requestSelectCMD" }, "RequestSelectCMD");

            // Awaken Skinbox Commands
            AddCovalenceCommand(new[] { "awaken.skinbox.close" }, "AwakenSkinboxCloseCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.page.prev" }, "AwakenSkinboxPrevPageCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.page.next" }, "AwakenSkinboxNextPageCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.apply.selected" }, "AwakenSkinboxApplySelectedCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.apply.all" }, "AwakenSkinboxApplyAllCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.apply.inventory" }, "AwakenSkinboxApplyInventoryCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.set.1", "awaken.skinbox.set.2", "awaken.skinbox.set.3" }, "AwakenSkinboxSetCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.select" }, "AwakenSkinboxSelectSkinCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.category" }, "AwakenSkinboxCategoryCMD");
            AddCovalenceCommand(new[] { "awaken.skinbox.search" }, "AwakenSkinboxSearchCMD");

            foreach (var skin in config.ImportedSkinList)
            {
                if (!string.IsNullOrEmpty(skin.Value.itemDisplayname) && !string.IsNullOrEmpty(skin.Value.itemShortname))
                    continue;

                if (_WorkshopSkinIDCollectionList.Contains(skin.Key.ToString()))
                    continue;

                _WorkshopSkinIDCollectionList.Add(skin.Key.ToString());
            }

            if (!config.sprayCanOveride)
                Unsubscribe("OnActiveItemChanged");

            if (!config.useOnItemCraft)
                Unsubscribe("OnItemCraftFinished");


            if (config.preloadImages)
                PreloadLocalIcons();

            if (getCollectionscouroutine != null)
            {
                Puts("getcollections already running!!");
            }
            else
            {
                getCollectionscouroutine = GetCollectionSkinIDS();
                ServerMgr.Instance.StartCoroutine(getCollectionscouroutine);
            }
        }

        private void Loaded()
        {
            _defaultSkins = Interface.Oxide.DataFileSystem.GetFile("Skinner/DefaultCraftSkins");
            _playerData = Interface.Oxide.DataFileSystem.GetFile("Skinner/PlayerUsageData");
            _skinRequestsData = Interface.Oxide.DataFileSystem.GetFile("Skinner/SkinsRequestData");

            LoadData();
        }
        private void Unload()
        {
            if (getCollectionscouroutine != null)
                ServerMgr.Instance.StopCoroutine(getCollectionscouroutine);
            if (getSteamWorkshopSkinData != null)
                ServerMgr.Instance.StopCoroutine(getSteamWorkshopSkinData);
            if (getSteamWorkshopRequestData != null)
                ServerMgr.Instance.StopCoroutine(getSteamWorkshopRequestData);
            if (notifyDiscordCoroutine != null)
                ServerMgr.Instance.StopCoroutine(notifyDiscordCoroutine);
            
            if (_imageDownloader != null)
                UnityEngine.Object.Destroy(_imageDownloader);

            foreach (var player in BasePlayer.allPlayerList)
            {
                CuiHelper.DestroyUi(player, SkinPageUI);
                CuiHelper.DestroyUi(player, SkinSearchUI);
                CuiHelper.DestroyUi(player, SkinSetsSelectUI);
                CuiHelper.DestroyUi(player, SkinRequestsUI);
                CuiHelper.DestroyUi(player, AWAKEN_SKINBOX_MAIN);

                if (_playerUsageData.TryGetValue(player.userID, out PlayerData playerData))
                    playerData.UpdateLastOnline();
            }

            foreach (var player in BasePlayer.allPlayerList)
            {
                if (player.TryGetComponent<BoxController>(out BoxController boxController))
                {
                    UnityEngine.Object.Destroy(boxController);
                }

                if (player.TryGetComponent<InventoryWatcher>(out InventoryWatcher inventoryWatcher))
                {
                    UnityEngine.Object.Destroy(inventoryWatcher);
                }

                if (player.TryGetComponent<SpraycanController>(out SpraycanController spraycanController))
                {
                    UnityEngine.Object.Destroy(spraycanController);
                }
            }

            SaveData();

            _imageCache.Clear();

            skinner = null;
            _WaitForSecondsMore = null;
        }

        public static Dictionary<ulong, string>? _skinNames;
        public static Dictionary<int, List<ulong>>? _cachedSkins;

        private static ulong maskID = (ulong)1 << 63;
        private static ulong maskID2 = 43307;
        private ulong SetMask(ulong num)
        {
            return num | maskID;
        }

        private ulong Get2Unmasked(ulong num)
        {
            return num & ~maskID2;
        }

        public static ulong GetMask(ulong skinID, int itemID, bool redirectSkin = false)
        {
            return redirectSkin ? (ulong)itemID | maskID : skinID;
        }

        public static bool HasMask(ulong uID)
        {
            return (uID & maskID) == maskID;
        }

        public static ulong UnsetMask(ulong num)
        {
            return num & ~maskID;
        }

        private void AddSkin(ulong skinID, int itemID, string displayName, int redirectID = -1)
        {
            if (config.blacklistedskins.Contains(skinID) || config.blacklisteditems.Contains(itemID) || config.blacklisteditems.Contains(redirectID))
                return;

            if (!_cachedSkins.TryGetValue(itemID, out List<ulong> skinsList))
            {
                skinsList = new List<ulong>() { 0ul };
                _cachedSkins[itemID] = skinsList;
            }
            ulong uID = GetMask(skinID, redirectID, redirectID != -1);


            if (!skinsList.Contains(uID))
                skinsList.Add(uID);

            _skinNames[uID] = displayName;
        }

        private int totskins = 0;
        private void GetSkins()
        {
            if ((Steamworks.SteamInventory.Definitions?.Length ?? 0) == 0)
            {
                Puts("Waiting for Steamworks to update skin item definitions");
                Steamworks.SteamInventory.OnDefinitionsUpdated += GetSkins;
                return;
            }
            int sk = 0;
            Puts("Steamworks Updated, Updating Skins");
            Steamworks.SteamInventory.OnDefinitionsUpdated -= GetSkins;

            Dictionary<int, ItemCategory> catDefinitions = new Dictionary<int, ItemCategory>();

            foreach (ItemDefinition itemDef in ItemManager.GetItemDefinitions())
            {
                if (!catDefinitions.ContainsKey(itemDef.itemid))
                {
                    catDefinitions.Add(itemDef.itemid, itemDef.category);
                }

                if (!config.autoImportApproved)
                    break;

                if (itemDef.isRedirectOf != null)
                {
                    AddSkin(0ul, itemDef.isRedirectOf.itemid, string.Empty, itemDef.itemid);
                    continue;
                }

                foreach (var skin in ItemSkinDirectory.ForItem(itemDef))
                {
                    if (skin.id == 0) continue;

                    ItemSkin itemSkin = skin.invItem as ItemSkin;
                    if (itemSkin == null)
                        continue;

                    bool isredirect = itemSkin?.Redirect != null;
                    if (isredirect)
                        AddSkin((ulong)skin.id, itemDef.itemid, skin.invItem?.displayName?.english ?? itemDef.displayName.english, itemSkin.itemDefinition.itemid);
                    else
                        AddSkin((ulong)skin.id, itemDef.itemid, skin.invItem?.displayName?.english ?? itemDef.displayName.english);
                }
            }

            foreach (Steamworks.InventoryDef item in Steamworks.SteamInventory.Definitions)
            {
                if (!config.autoImportApproved)
                    break;

                string shortname = item.GetProperty("itemshortname") == "lr300.item"
                    ? "rifle.lr300"
                    : item.GetProperty("itemshortname");

                if (string.IsNullOrEmpty(shortname) || item.Id < 100)
                    continue;

                ulong skinid;

                if (!ulong.TryParse(item.GetProperty("workshopid"), out skinid))
                {
                    skinid = (ulong)item.Id;
                }

                if (skinid < 1000000) continue;
                ItemDefinition steamitemdef = ItemManager.FindItemDefinition(shortname);

                if (steamitemdef == null)
                    continue;

                if (_cachedSkins.TryGetValue(steamitemdef.itemid, out List<ulong> cachedskinsList))
                {
                    if (cachedskinsList.Contains((ulong)item.Id))
                        continue;
                }

                if (steamitemdef.isRedirectOf != null)
                    AddSkin(skinid, steamitemdef.isRedirectOf.itemid, item.Name, steamitemdef.itemid);
                else
                    AddSkin(skinid, steamitemdef.itemid, item.Name);
            }

            UpdateImportedSkins();

            List<int> skincachekeys = Pool.Get<List<int>>();
            skincachekeys.AddRange(new List<int>(_cachedSkins.Keys));
            for (int i = skincachekeys.Count - 1; i >= 0; i--)
            {
                var item2 = _cachedSkins[skincachekeys[i]];
                if (item2.Count == 1)
                {
                    _cachedSkins.Remove(skincachekeys[i]);
                    continue;
                }

                ItemDefinition itemdef = ItemManager.FindItemDefinition(skincachekeys[i]);
                if (itemdef?.Blueprint == null)
                {
                    _cachedSkins.Remove(skincachekeys[i]);
                    continue;
                }
                sk += item2.Count;
            }
            Pool.FreeUnmanaged(ref skincachekeys);

            SaveConfig();

            //Re-order to look nice
            List<KeyValuePair<int, List<ulong>>> tempList = new List<KeyValuePair<int, List<ulong>>>(_cachedSkins);

            tempList.Sort((pair1, pair2) => catDefinitions[pair1.Key].CompareTo(catDefinitions[pair2.Key]));

            _cachedSkins.Clear();

            foreach (KeyValuePair<int, List<ulong>> pair in tempList)
            {
                _cachedSkins.Add(pair.Key, pair.Value);
            }

            _cachedSkinKeys = new List<int>(_cachedSkins.Keys);
            Interface.CallHook("OnSkinnerCacheUpdated", _cachedSkins);
            Puts($"{sk} skins were indexed, Skin indexing complete");
        }
        public static List<int>? _cachedSkinKeys;
        private void UpdateImportedSkins()
        {
            List<ulong> keys = Pool.Get<List<ulong>>();
            keys.AddRange(new List<ulong>(config.ImportedSkinList.Keys));

            for (int i = config.ImportedSkinList.Count - 1; i >= 0; i--)
            {
                var whitelistSkin = config.ImportedSkinList[keys[i]];
                if (string.IsNullOrEmpty(whitelistSkin.itemDisplayname) || string.IsNullOrEmpty(whitelistSkin.itemShortname))
                {
                    config.ImportedSkinList.Remove(keys[i]);
                    continue;
                }

                ItemDefinition itemdef = ItemManager.FindItemDefinition(whitelistSkin.itemShortname);

                if (itemdef == null)
                {
                    config.ImportedSkinList.Remove(keys[i]);
                    Puts($"Could not find item definition for {whitelistSkin.itemShortname} {keys[i]}");
                    continue;
                }
                AddSkin(keys[i], itemdef.itemid, whitelistSkin.itemDisplayname);
            }
            Pool.FreeUnmanaged(ref keys);
        }

        #endregion Init

        #region Configuration

        private Configuration config;

        public class Configuration
        {
            [JsonProperty("Skin Commands (skin items in you inventory")]
            public string[] cmdsskin = new[] { "skin", "s", "skinbox", "sb" };

            [JsonProperty("Skin Items Commands (skin items you have already placed")]
            public string[] cmdsskinitems = new[] { "skinitem", "si", "skindeployed", "sd" };

            [JsonProperty("Set default items to be skinned")]
            public string[] cmdsskincraft = new[] { "skincraft", "sc" };

            [JsonProperty("Automatically set all items in you inventory to your default skins")]
            public string[] cmdsskininv = new[] { "skininv", "sinv" };

            [JsonProperty("Automatically set all items a container to your default skins")]
            public string[] cmdsskincon = new[] { "skincon", "scon" };

            [JsonProperty("Automatically skin all deployables in your base")]
            public string[] cmdskinbase = new[] { "skinbase", "skinbuilding" };

            [JsonProperty("Automatically skin all items in your base")]
            public string[] cmdskinallitems = new[] { "skinall", "sa" };

            [JsonProperty("Automatically skin all items that are moved into you inventory")]
            public string[] cmdtoggleautoskin = new[] { "skinauto", "sauto" };

            [JsonProperty("Skin your teams inventories with your skin set")]
            public string[] cmdskinteam = new[] { "skinteam", "st" };

            [JsonProperty("Request workshop skins via workshop ID")]
            public string[] cmdskinrequest = new[] { "skinrequest", "sr" };

            [JsonProperty("Approve workshop skin requests")]
            public string[] cmdskinrequests = new[] { "skinrequests", "srs" };

            [JsonProperty("Set your selected skin set")]
            public string[] cmdskinset = new[] { "skinset", "ss" };

            [JsonProperty("Import Custom Skins")] public string[] cmdskinimport = new[] { "skinimport", "sip" };

            [JsonProperty("Import Workshop Collection Command")] public string[] cmdcollectionimport = new[] { "colimport", "cip" };

            [JsonProperty("Skin Request Notification Discord Webhook")]
            public string DiscordWebhook = "";

            [JsonProperty("Custom Page Change UI Positon anchor/offset 'min x, min y', 'max x', max y'")]
            public string[] uiposition = new[] { "0.5 0.0", "0.5 0.0", "198 60", "400 97" };

            [JsonProperty("Custom Searchbar UI Positon anchor/offset 'min x, min y', 'max x', max y'")]
            public string[] uisearchposition = new[] { "0.5 0.0", "0.5 0.0", "410 635", "572 660" };

            [JsonProperty("Custom Set Selection UI Positon anchor/offset 'min x, min y', 'max x', max y'")]
            public string[] uisetsposition = new[] { "0.5 0.0", "0.5 0.0", "250 610", "573 633" };

            [JsonProperty("Auto import approved skins")]
            public bool autoImportApproved = true;

            [JsonProperty("Remove player data after inactivity (days)")]
            public int removedataTime = 14;

            [JsonProperty("Apply names of skins to skinned items")]
            public bool applySkinNames = true;

            [JsonProperty("Add Search Bar UI")]
            public bool searchbar = true;

            [JsonProperty("Use on itemcraft hook (skin items after crafting - not required when using skinauto)")]
            public bool useOnItemCraft = false;

            [JsonProperty("Override spraycan behaviour")]
            public bool sprayCanOveride = false;

            [JsonProperty("Use spraycan effect when holding spraycan and skinning deployables")]
            public bool sprayCanEffect = true;
            
            [JsonProperty("Preload local item icons on startup (improves UI performance with minimal startup impact)")]
            public bool preloadImages = true;

            [JsonProperty("Enable external image downloading (enable for CDN images)")]
            public bool enableExternalImages = true;

            [JsonProperty("Primary image CDN URL (use {shortname} as placeholder for item shortname)")]
            public string primaryImageCDN = "https://rustlabs.com/img/items180/{shortname}.png";

            [JsonProperty("Fallback image CDN URLs")]
            public List<string> fallbackImageCDNs = new List<string>
            {
                "https://cdn.jsdelivr.net/gh/rustmods/rust-item-icons@main/png/{shortname}.png",
                "https://www.rustedit.io/images/items/{shortname}.png"
            };

            [JsonProperty("Use built-in game icons when external images fail (recommended)")]
            public bool useBuiltInIconFallback = true;
            
            [JsonProperty("Blacklisted Skins (skinID)", ObjectCreationHandling = ObjectCreationHandling.Replace)]
            public List<ulong> blacklistedskins = new List<ulong>();

            [JsonProperty("Blacklisted Items (itemID)", ObjectCreationHandling = ObjectCreationHandling.Replace)]
            public List<int> blacklisteditems = new List<int>();

            [JsonProperty("Import Skin collections (steam workshop ID)", ObjectCreationHandling = ObjectCreationHandling.Replace)]
            public List<ulong> skinCollectionIDs = new List<ulong>();

            [JsonProperty("Command based cooldowns ('permission' : 'command' seconds")]
            public Dictionary<string, CoolDowns> Cooldowns = new Dictionary<string, CoolDowns>() { { "Default30CD", new CoolDowns() } };

            [JsonProperty("Imported Skins List", ObjectCreationHandling = ObjectCreationHandling.Replace)]
            public Dictionary<ulong, ImportedItem> ImportedSkinList = new Dictionary<ulong, ImportedItem>();

            public string ToJson() => JsonConvert.SerializeObject(this);

            public Dictionary<string, object> ToDictionary() => JsonConvert.DeserializeObject<Dictionary<string, object>>(ToJson());
        }

        public struct ImportedItem
        {
            public string itemShortname;
            public string itemDisplayname;
        }

        public struct RequestItem
        {
            public ulong skinID;
            public int itemID;
            public string itemDisplayname;
        }

        protected override void LoadDefaultConfig() => config = new Configuration();

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    throw new JsonException();
                }
                var configDict = config.ToDictionary();
                Configuration defaultConfig = new Configuration();
                var defaultconObjects = defaultConfig.ToDictionary();

                bool changed = false;
                foreach (var key in defaultconObjects.Keys)
                {
                    if (!configDict.ContainsKey(key))
                    {
                        changed = true;
                        break;
                    }
                }

                if (changed)
                {
                    Puts("Configuration appears to be outdated; updating and saving");
                    SaveConfig();
                }
            }
            catch
            {
                Puts($"Configuration file {Name}.json is invalid; using defaults");
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig()
        {
            Puts($"Configuration changes saved to {Name}.json");
            Config.WriteObject(config, true);
        }

        #endregion Configuration

        #region Data
        private DynamicConfigFile _defaultSkins;
        private DynamicConfigFile _playerData;
        private DynamicConfigFile _skinRequestsData;

        private Dictionary<ulong, CoolDowns> _playercooldowns = new Dictionary<ulong, CoolDowns>();

        private void LoadData()
        {
            List<ulong> skipdata = Pool.Get<List<ulong>>();

            try
            {
                _playerUsageData = new Dictionary<ulong, PlayerData>();
                long timenow = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                var temp = _playerData.ReadObject<Dictionary<ulong, PlayerDataDec>>();
                if (temp != null)
                {
                    foreach (var pair in temp)
                    {
                        if (timenow - pair.Value.lastonline > config.removedataTime * 86400)
                        {
                            skipdata.Add(pair.Key);
                            continue;
                        }

                        _playerUsageData.Add(pair.Key, new PlayerData() { lastonline = pair.Value.lastonline });

                        foreach (var innerDict in pair.Value.skinusage)
                        {
                            List<ulong> ulongList = new List<ulong>();
                            foreach (decimal d in innerDict.Value)
                            {
                                ulong id = decimal.ToUInt64(d);
                                if (HasMask(id) && config.blacklisteditems.Contains((int)UnsetMask(id)))
                                    continue;
                                ulongList.Add(decimal.ToUInt64(d));
                            }
                            _playerUsageData[pair.Key].skinusage.TryAdd(innerDict.Key, ulongList);
                        }
                    }
                }
            }
            catch
            {
                _playerUsageData = new Dictionary<ulong, PlayerData>();
            }
            try
            {
                _playerDefaultSkins = new Dictionary<ulong, Dictionary<int, ulong>[]>();
                var temp = _defaultSkins.ReadObject<Dictionary<ulong, Dictionary<int, decimal>[]>>();
                if (temp != null)
                {
                    foreach (var outerPair in temp)
                    {
                        if (skipdata.Contains(outerPair.Key))
                            continue;

                        var innerDictArray = new Dictionary<int, ulong>[outerPair.Value.Length];
                        for (int i = 0; i < outerPair.Value.Length; i++)
                        {
                            var innerDict = new Dictionary<int, ulong>();
                            foreach (var innerPair in outerPair.Value[i])
                            {
                                if (HasMask((ulong)innerPair.Value) && config.blacklisteditems.Contains((int)UnsetMask((ulong)innerPair.Value)))
                                    continue;
                                innerDict.Add(innerPair.Key, (ulong)innerPair.Value);
                            }
                            innerDictArray[i] = innerDict;
                        }
                        _playerDefaultSkins.Add(outerPair.Key, innerDictArray);
                    }
                }
            }
            catch
            {
                _playerDefaultSkins = new Dictionary<ulong, Dictionary<int, ulong>[]>();
            }
            try
            {
                _requestsData = _skinRequestsData.ReadObject<List<RequestItem>>() ?? new List<RequestItem>();
            }
            catch
            {
                _requestsData = new List<RequestItem>();
            }
            Pool.FreeUnmanaged(ref skipdata);
        }

        private void SaveData()
        {
            Puts("DataSaved");
            _defaultSkins.WriteObject(_playerDefaultSkins);
            _playerData.WriteObject(_playerUsageData);
            _skinRequestsData.WriteObject(_requestsData);
        }

        private Dictionary<ulong, Dictionary<int, ulong>[]> _playerDefaultSkins;
        private Dictionary<ulong, PlayerData> _playerUsageData;
        private List<RequestItem> _requestsData;

        public class PlayerDataDec
        {
            public Dictionary<int, List<decimal>> skinusage = new Dictionary<int, List<decimal>>();
            public long lastonline { get; set; } = 0;
        }

        public class PlayerData
        {
            public Dictionary<int, List<ulong>> skinusage = new Dictionary<int, List<ulong>>();
            public long lastonline { get; set; } = 0;

            public void AddSkinUsage(ulong skinID, int itemID, int rItemID = 0)
            {
                if (skinID == 0 && rItemID == 0) return;

                if (!skinusage.TryGetValue(itemID, out var list))
                {
                    list = new List<ulong>(6);
                    skinusage[itemID] = list;
                }

                ulong uID = GetMask(skinID, rItemID, rItemID != 0);
                for (int i = list.Count - 1; i >= 0; i--)
                {
                    if (list[i] != uID)
                        continue;

                    list.RemoveAt(i);
                }

                if (list.Count >= 6)
                {
                    list.RemoveAt(0);
                }

                list.Add(uID);
            }

            public List<ulong>? GetSkinUsage(int itemID)
            {
                if (!skinusage.TryGetValue(itemID, out List<ulong>? usageList))
                {
                    return null;
                }
                return usageList;
            }

            public void UpdateLastOnline()
            {
                lastonline = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            }
        }

        public class CoolDowns
        {
            public float skin = 30f;
            public float skinitem = 30f;
            public float skincraft = 30f;
            public float skincon = 30f;
            public float skininv = 30f;
            public float skinteam = 30f;
            public float skinbase = 60f;
            public float skinall = 60f;
        }
        #endregion Data

        #region Localization
        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["NoPerms"] = "You don't have permissions to use this command",
                ["NoBuildingAuth"] = "You must have building auth to use this",
                ["NoObjectsFound"] = "No object found",
                ["NoSkins"] = "No skins available",
                ["NoDefaultSkins"] = "You have no default skins set. Open the skin craft menu to set them.",
                ["NoRequests"] = "There are no pending skin requests.",
                ["ImportSkinArgs"] = "Bad args, Required input skinid",
                ["ImportCollectionArgs"] = "Bad args, Required input collectionID",
                ["SkinIDError"] = "Cannot parse skinid {0}",
                ["NoShortname"] = "No item found for shortname : {0}",
                ["DuplicateSkin"] = "Duplicate Skin ID for : {0} {1}",
                ["SkinImported2"] = "Skin {0} has been imported and saved",
                ["SkinRequested"] = "Skin {0} has been requested",
                ["AlreadyRequesting"] = "Already updating requests data please wait...",
                ["RequestingData"] = "Updating requests data please wait...",
                ["CollectionImported2"] = "Steam Skin Collection {0} has been imported and saved",
                ["CommandCooldown"] = "You can not use this command for another {0}",
                ["CompletedInvSkin"] = "All items in your inventory have been set to your default skins",
                ["CompletedConSkin"] = "All items in {0} have been set to your default skins",
                ["CompletedBuildingSkin"] = "All {0} in your base have been set to your default skins",
                ["CompletedAllSkin"] = "All {0} items in your base have been set to your default skins",
                ["SkinSetSelected"] = "Skin set {0} selected",
                ["SkinSetSelectedArgs"] = "Bad args, Required input set No. 1, 2 or 3",
                ["AutoSkinEnabled"] = "Auto skins enabled",
                ["AutoSkinDisabled"] = "Auto skins disabled",
                ["CompletedTeamSkin"] = "All items in your team have been set skin set {0}",
                ["NoTeam"] = "You need to be in a team to use this feature",
                ["TeamSkinBlockEnabled"] = "You enabled team skins",
                ["TeamSkinBlockDisabled"] = "You disabled team skins",
                ["NoSkinSelected"] = "You have not selected a skin.",
                ["SkinnedItems"] = "Skinned {0} item(s) in your inventory.",
                ["NoItemSelected"] = "Please select an item from your inventory first."
            }, this);
        }

        #endregion Localization

        #region Commands
        private void SkinAutoCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer? player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permskinauto))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            if (!player.TryGetComponent<InventoryWatcher>(out InventoryWatcher inventoryWatcher))
            {
                player.gameObject.AddComponent<InventoryWatcher>();
                ChatMessage(iplayer, "AutoSkinEnabled");
                permission.GrantUserPermission(player.UserIDString, permskinautotoggle, this);
                return;
            }
            permission.RevokeUserPermission(player.UserIDString, permskinautotoggle);

            UnityEngine.Object.Destroy(inventoryWatcher);
            ChatMessage(iplayer, "AutoSkinDisabled");
        }

        private void SkinImportCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer? player = iplayer.Object as BasePlayer;
            if (player != null)
            {
                if (!HasPerm(player.UserIDString, permimport))
                {
                    ChatMessage(iplayer, "NoPerms");
                    return;
                }
            }
            else if (!iplayer.IsServer)
                return;

            if (args.Length < 1)
            {
                ChatMessage(iplayer, "ImportSkinArgs");
                return;
            }

            ulong skinid = 0ul;
            if (!ulong.TryParse(args[0], out skinid))
            {
                ChatMessage(iplayer, "ImportSkinArgs", args[0]);
                return;
            }
            _WorkshopSkinIDCollectionList.Add(skinid.ToString());
            if (getSteamWorkshopSkinData != null)
            {
                Puts("getSteamWorkshopSkinData already running!!");
            }
            else
            {
                getSteamWorkshopSkinData = GetSteamWorkshopSkinData();
                ServerMgr.Instance.StartCoroutine(getSteamWorkshopSkinData);
            }
            ChatMessage(iplayer, "SkinImported2", args[0]);
        }

        private void SkinImportCollection(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player != null)
            {
                if (!HasPerm(player.UserIDString, permimport))
                {
                    ChatMessage(iplayer, "NoPerms");
                    return;
                }
            }
            else if (!iplayer.IsServer)
                return;

            if (args.Length < 1)
            {
                ChatMessage(iplayer, "ImportCollectionArgs");
                return;
            }

            ulong collectionid = 0ul;
            if (!ulong.TryParse(args[0], out collectionid))
            {
                ChatMessage(iplayer, "ImportCollectionArgs", args[0]);
                return;
            }

            config.skinCollectionIDs.Add(collectionid);

            if (getCollectionscouroutine != null)
            {
                Puts("getcollections already running!!");
            }
            else
            {
                getCollectionscouroutine = GetCollectionSkinIDS();
                ServerMgr.Instance.StartCoroutine(getCollectionscouroutine);
            }
            ChatMessage(iplayer, "CollectionImported2", args[0]);
        }

        private void SkinCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permdefault))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            if (_playerUIState.ContainsKey(player.userID))
            {
                CuiHelper.DestroyUi(player, AWAKEN_SKINBOX_MAIN);
                _playerUIState.Remove(player.userID);
                return;
            }

            if (_viewingcon.TryGetValue(player.userID, out BoxController boxController))
            {
                if (boxController != null)
                    UnityEngine.Object.Destroy(boxController);
                _viewingcon.Remove(player.userID);
            }

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skin;
                else if (cdtime > cdperm.Value.skin)
                    cdtime = cdperm.Value.skin;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skin = Time.time });
                else
                {
                    if (coolDowns.skin + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skin + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skin = Time.time;
                }
            }
            
            OpenAwakenSkinboxUI(player);
        }

        private void DefaultSkinsCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permcraft))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }
            
            if (_playerUIState.ContainsKey(player.userID))
            {
                CuiHelper.DestroyUi(player, AWAKEN_SKINBOX_MAIN);
                _playerUIState.Remove(player.userID);
            }

            if (_viewingcon.TryGetValue(player.userID, out BoxController boxController))
            {
                if (boxController != null)
                    UnityEngine.Object.Destroy(boxController);
                _viewingcon.Remove(player.userID);
                return;
            }

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skincraft;
                else if (cdtime > cdperm.Value.skincraft)
                    cdtime = cdperm.Value.skincraft;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skincraft = Time.time });
                else
                {
                    if (coolDowns.skincraft + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skincraft + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skincraft = Time.time;
                }
            }

            if (!_playerDefaultSkins.ContainsKey(player.userID))
                _playerDefaultSkins.Add(player.userID, new Dictionary<int, ulong>[3]
                {
                    new Dictionary<int, ulong>(),
                    new Dictionary<int, ulong>(),
                    new Dictionary<int, ulong>()
                });

            ItemContainer itemContainer = CreateContainer();

            boxController = player.gameObject.AddComponent<BoxController>();
            boxController.inventory = itemContainer;

            boxController.StartAwake();
            boxController.SkinCraft();

            _viewingcon.TryAdd(player.userID, boxController);

            player.Invoke(() => StartLooting(player, itemContainer), 0.3f);
        }

        private struct DiscordData
        {
            public ulong SkinID;
            public BasePlayer player;
        }

        private List<DiscordData> _discordData = new List<DiscordData>();
        
        private void SkinRequestCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permskinrequest))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            if (args.Length < 1)
            {
                ChatMessage(iplayer, "ImportSkinArgs");
                return;
            }

            if (!ulong.TryParse(args[0], out ulong skinidResult))
            {
                ChatMessage(iplayer, "ImportSkinArgs");
                return;
            }

            ChatMessage(iplayer, "SkinRequested", skinidResult);

            foreach (var a in _requestsData)
            {
                if (a.skinID == skinidResult)
                    return;
            }

            _requestsData.Add(new RequestItem { skinID = skinidResult });

            if (string.IsNullOrEmpty(config.DiscordWebhook))
                return;

            _discordData.Add(new DiscordData() { SkinID = skinidResult, player = player });
            if (notifyDiscordCoroutine != null)
            {
                Puts("getcollections already running!!");
            }
            else
            {
                notifyDiscordCoroutine = NotifyDiscord();
                ServerMgr.Instance.StartCoroutine(notifyDiscordCoroutine);
            }
        }

        private void SkinRequestsCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permimport))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            if (_requestsData.Count == 0)
            {
                ChatMessage(iplayer, "NoRequests");
                return;
            }

            if (getSteamWorkshopRequestData != null)
            {
                ChatMessage(iplayer, "AlreadyRequesting");
                return;
            }
            else
            {
                ChatMessage(iplayer, "RequestingData");
                getSteamWorkshopRequestData = GetSteamWorkshopSkinRequests();
                ServerMgr.Instance.StartCoroutine(getSteamWorkshopRequestData);
            }

            if (_viewingcon.TryGetValue(player.userID, out BoxController boxController))
            {
                if (boxController != null)
                    UnityEngine.Object.Destroy(boxController);
                _viewingcon.Remove(player.userID);
                return;
            }

            ItemContainer itemContainer = CreateContainer();

            boxController = player.gameObject.AddComponent<BoxController>();
            boxController.inventory = itemContainer;

            boxController.StartAwake();

            _viewingcon.TryAdd(player.userID, boxController);

            player.StartCoroutine(CheckforRequests(player, itemContainer, boxController));
        }

        private IEnumerator CheckforRequests(BasePlayer player, ItemContainer itemContainer, BoxController boxController)
        {
            yield return _WaitForSecondsMore;
            int i = 0;
            for (i = 0; i < 15; i++)
            {
                if (getSteamWorkshopRequestData == null)
                    break;
                ChatMessage(player.IPlayer, "Updating requests data please wait...");
                yield return _WaitForSecondsMore;
            }

            if (i >= 15)
            {
                ChatMessage(player.IPlayer, "Updating requests timed out try again later");
                UnityEngine.Object.Destroy(boxController);
            }
            else
            {
                boxController.SkinRequests();
                StartLooting(player, itemContainer);
            }
        }

        private static int Layermask = LayerMask.GetMask("Deployed", "Construction");
        private void SkinItemCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permitems))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            if (player.inventory.loot.IsLooting())
            {
                player.EndLooting();
                return;
            }

            if (!player.CanBuild() && !HasPerm(player.UserIDString, permbypassauth))
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }

            RaycastHit raycastHit;
            if (!Physics.Raycast(player.eyes.HeadRay(), out raycastHit, 5f, Layermask))
            {
                ChatMessage(iplayer, "NoObjectsFound");
                return;
            }
            BaseCombatEntity entity = raycastHit.GetEntity() as BaseCombatEntity;
            ItemDefinition? itemDefinition = null;
            if (entity.pickup.itemTarget != null)
            {
                itemDefinition = entity.pickup.itemTarget;
            }
            else if (entity.repair.itemTarget != null)
            {
                itemDefinition = entity.repair.itemTarget;
            }

            if (itemDefinition == null)
            {
                ChatMessage(iplayer, "NoObjectsFound");
                return;
            }

            if (!_cachedSkins.ContainsKey(itemDefinition.isRedirectOf != null ? itemDefinition.isRedirectOf.itemid : itemDefinition.itemid))
            {
                ChatMessage(iplayer, "NoSkins");
                return;
            }

            if (_viewingcon.TryGetValue(player.userID, out BoxController? boxController))
            {
                if (boxController != null)
                    UnityEngine.Object.Destroy(boxController);
                _viewingcon.Remove(player.userID);
                return;
            }

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skinitem;
                else if (cdtime > cdperm.Value.skinitem)
                    cdtime = cdperm.Value.skinitem;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skinitem = Time.time });
                else
                {
                    if (coolDowns.skinitem + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skinitem + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skinitem = Time.time;
                }
            }

            if (args.Length > 0 && int.TryParse(args[0], out int setselect))
            {
                Dictionary<int, ulong> cachedskins = GetCachedSkins(player, setselect);

                if (cachedskins.Count < 1)
                    return;

                int itemID = itemDefinition.isRedirectOf != null ? itemDefinition.isRedirectOf.itemid : itemDefinition.itemid;

                if (cachedskins.TryGetValue(itemID, out ulong uID))
                {
                    SkinDeployable(entity, uID);
                }
                return;
            }

            ItemContainer itemContainer = CreateContainer();

            boxController = player.gameObject.AddComponent<BoxController>();
            boxController.inventory = itemContainer;

            boxController.StartAwake();

            boxController.SkinDeplyoables(entity, itemDefinition);

            _viewingcon.TryAdd(player.userID, boxController);

            player.Invoke(() => StartLooting(player, itemContainer), 0.3f);
        }

        private void SkinInvCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permskininv))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            int setselect = -1;
            if (args.Length > 0 && !int.TryParse(args[0], out setselect))
            {
                setselect = -1;
            }

            Dictionary<int, ulong> cachedskins = GetCachedSkins(player, setselect);

            if (cachedskins.Count < 1)
                return;

            if (player.inventory == null)
                return;

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skininv;
                else if (cdtime > cdperm.Value.skininv)
                    cdtime = cdperm.Value.skininv;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skininv = Time.time });
                else
                {
                    if (coolDowns.skininv + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skininv + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skininv = Time.time;
                }
            }

            List<Item> itemstoSkin = Pool.Get<List<Item>>();
            player.inventory.GetAllItems(itemstoSkin);

            for (int i = 0; i < itemstoSkin.Count; i++)
            {
                Item item = itemstoSkin[i];
                if (item?.info == null) continue;

                if (item.IsBackpack())
                {
                    if (item?.contents != null && !item.contents.itemList.IsNullOrEmpty())
                    {
                        foreach (var bitem in item.contents.itemList)
                            itemstoSkin.Add(bitem);
                    }
                    continue;
                }

                if (cachedskins.TryGetValue(item.info.isRedirectOf != null ? item.info.isRedirectOf.itemid : item.info.itemid, out ulong uID))
                {
                    if (config.blacklistedskins.Contains(item.skin) || config.blacklisteditems.Contains(item.info.itemid)) continue;

                    SkinItem(item, uID);
                }
            }

            Pool.FreeUnmanaged(ref itemstoSkin);

            player.SendNetworkUpdateImmediate();
            ChatMessage(iplayer, "CompletedInvSkin");
        }

        private void SkinTeamCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permskinteam))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            if (args.Length > 0)
            {
                if (args[0].ToLower() == "toggle")
                {
                    if (HasPerm(player.UserIDString, permskinteamblock))
                    {
                        permission.RevokeUserPermission(player.UserIDString, permskinteamblock);
                        ChatMessage(iplayer, "TeamSkinBlockEnabled");
                    }
                    else
                    {
                        permission.GrantUserPermission(player.UserIDString, permskinteamblock, this);
                        ChatMessage(iplayer, "TeamSkinBlockDisabled");
                    }
                    return;
                }
            }

            int setselect = -1;
            if (args.Length > 0 && !int.TryParse(args[0], out setselect))
            {
                setselect = -1;
            }

            Dictionary<int, ulong> cachedskins = GetCachedSkins(player, setselect);

            if (cachedskins.Count < 1)
                return;

            if (player.Team == null)
            {
                ChatMessage(iplayer, "NoTeam");
                return;
            }

            if (player.inventory == null)
                return;

            RelationshipManager.ServerInstance.playerToTeam.TryGetValue(player.userID, out RelationshipManager.PlayerTeam playerTeam);

            if (playerTeam == null || playerTeam.members.Count == 0)
                return;

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skinteam;
                else if (cdtime > cdperm.Value.skinteam)
                    cdtime = cdperm.Value.skinteam;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skinteam = Time.time });
                else
                {
                    if (coolDowns.skinteam + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skinteam + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skinteam = Time.time;
                }
            }

            List<Item> itemstoSkin = Pool.Get<List<Item>>();
            List<BasePlayer> teamplayersList = new List<BasePlayer>();
            foreach (var teamplayer in playerTeam.members)
            {
                BasePlayer? teamBasePlayer = BasePlayer.FindByID(teamplayer);
                if (teamBasePlayer == null) continue;
                
                if (teamBasePlayer?.inventory == null)
                    continue;

                teamplayersList.Add(teamBasePlayer);
                if (HasPerm(teamBasePlayer.UserIDString, permskinteamblock))
                    continue;

                teamBasePlayer.inventory.GetAllItems(itemstoSkin);
            }

            for (int i = 0; i < itemstoSkin.Count; i++)
            {
                Item item = itemstoSkin[i];
                if (item?.info == null) continue;

                if (item.IsBackpack())
                {
                    if (item?.contents != null && !item.contents.itemList.IsNullOrEmpty())
                    {
                        foreach (var bitem in item.contents.itemList)
                            itemstoSkin.Add(bitem);
                    }
                    continue;
                }

                if (cachedskins.TryGetValue(item.info.isRedirectOf != null ? item.info.isRedirectOf.itemid : item.info.itemid, out ulong uID))
                {
                    if (config.blacklistedskins.Contains(item.skin) || config.blacklisteditems.Contains(item.info.itemid)) continue;

                    SkinItem(item, uID);
                }
            }

            foreach (var teambp in teamplayersList)
            {
                if (!teambp.IsSleeping()) continue;
                SendNetworkUpdate(teambp);
            }

            Pool.FreeUnmanaged(ref itemstoSkin);

            ChatMessage(iplayer, "CompletedTeamSkin", setselect != -1 ? setselect.ToString() : string.Empty);
        }

        private void SkinConCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permskincon))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }
            if (!player.IsBuildingAuthed() && !HasPerm(player.UserIDString, permbypassauth))
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }

            int setselect = -1;
            if (args.Length > 0 && !int.TryParse(args[0], out setselect))
            {
                setselect = -1;
            }

            Dictionary<int, ulong> cachedskins = GetCachedSkins(player, setselect);

            if (cachedskins.Count < 1)
                return;

            RaycastHit raycastHit;
            if (!Physics.Raycast(player.eyes.HeadRay(), out raycastHit, 5f, Layermask))
            {
                ChatMessage(iplayer, "NoObjectsFound");
                return;
            }

            StorageContainer storage = raycastHit.GetEntity() as StorageContainer;
            if (storage?.inventory == null)
            {
                ChatMessage(iplayer, "NoObjectsFound");
                return;
            }

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skincon;
                else if (cdtime > cdperm.Value.skincon)
                    cdtime = cdperm.Value.skincon;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skincon = Time.time });
                else
                {
                    if (coolDowns.skincon + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skincon + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skincon = Time.time;
                }
            }

            foreach (Item item in storage.inventory.itemList)
            {
                if (item?.info == null) continue;

                if (cachedskins.TryGetValue(item.info.isRedirectOf != null ? item.info.isRedirectOf.itemid : item.info.itemid, out ulong uID))
                {
                    if (config.blacklistedskins.Contains(item.skin) || config.blacklisteditems.Contains(item.info.itemid)) continue;
                    SkinItem(item, uID);
                }
            }

            ChatMessage(iplayer, "CompletedConSkin", storage.ShortPrefabName);
        }

        private void SkinBaseCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!HasPerm(player.UserIDString, permskinbase))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }
            Dictionary<int, ulong> cachedskins = GetCachedSkins(player);

            if (cachedskins.Count < 1)
                return;

            if (!player.IsBuildingAuthed() && !HasPerm(player.UserIDString, permbypassauth))
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }
            BuildingPrivlidge buildingPrivlidge = player.GetBuildingPrivilege();
            if (buildingPrivlidge == null)
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }
            BuildingManager.Building buildingManager = buildingPrivlidge.GetBuilding();
            if (buildingManager == null)
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skinbase;
                else if (cdtime > cdperm.Value.skinbase)
                    cdtime = cdperm.Value.skinbase;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skinbase = Time.time });
                else
                {
                    if (coolDowns.skinbase + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skinbase + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skinbase = Time.time;
                }
            }

            string skinned = "all deployables";
            if (args.Length > 0)
            {
                skinned = $"{args[0]}s";
            }

            foreach (var decayent in buildingManager.decayEntities)
            {
                BaseCombatEntity? baseCombatEntity = decayent?.GetEntity() as BaseCombatEntity;
                if (baseCombatEntity?.pickup.itemTarget == null) continue;

                if (args.Length > 0)
                {
                    if (!baseCombatEntity.pickup.itemTarget.shortname.Contains(args[0]))
                        continue;
                }

                int itemID = baseCombatEntity.pickup.itemTarget.isRedirectOf != null ? baseCombatEntity.pickup.itemTarget.isRedirectOf.itemid : baseCombatEntity.pickup.itemTarget.itemid;
                if (cachedskins.TryGetValue(itemID, out ulong uID))
                {
                    SkinDeployable(baseCombatEntity, uID);
                }
            }
            ChatMessage(iplayer, "CompletedBuildingSkin", skinned);
        }

        private Dictionary<int, ulong> GetCachedSkins(BasePlayer player, int set = -1)
        {
            if (set == -1)
            {
                if (!_playerSelectedSet.TryGetValue(player.userID, out set))
                    set = 1;
            }
            
            if (!_playerDefaultSkins.TryGetValue(player.userID, out var cachedskinSets))
            {
                cachedskinSets = new Dictionary<int, ulong>[3]
                {
                    new Dictionary<int, ulong>(), new Dictionary<int, ulong>(), new Dictionary<int, ulong>()
                };
                _playerDefaultSkins[player.userID] = cachedskinSets;
                ChatMessage(player.IPlayer, "NoDefaultSkins");
                return cachedskinSets[0];
            }
            
            // Ensure the array is long enough
            if (cachedskinSets.Length < 3)
            {
                var newSets = new Dictionary<int, ulong>[3];
                for(int i = 0; i < cachedskinSets.Length; i++) newSets[i] = cachedskinSets[i];
                for(int i = cachedskinSets.Length; i < 3; i++) newSets[i] = new Dictionary<int, ulong>();
                _playerDefaultSkins[player.userID] = newSets;
                cachedskinSets = newSets;
            }

            return cachedskinSets[set - 1];
        }

        private void SkinAllItemsCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;
            if (!HasPerm(player.UserIDString, permskinall))
            {
                ChatMessage(iplayer, "NoPerms");
                return;
            }

            Dictionary<int, ulong> cachedskins = GetCachedSkins(player);

            if (cachedskins.Count < 1)
                return;

            if (!player.IsBuildingAuthed() && !HasPerm(player.UserIDString, permbypassauth))
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }
            BuildingPrivlidge buildingPrivlidge = player.GetBuildingPrivilege();
            if (buildingPrivlidge == null)
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }
            BuildingManager.Building buildingManager = buildingPrivlidge.GetBuilding();
            if (buildingManager == null)
            {
                ChatMessage(iplayer, "NoBuildingAuth");
                return;
            }

            float cdtime = 0;

            foreach (var cdperm in config.Cooldowns)
            {
                if (!HasPerm(player.UserIDString, $"skinner.{cdperm.Key}")) continue;
                if (cdtime == 0)
                    cdtime = cdperm.Value.skinall;
                else if (cdtime > cdperm.Value.skinall)
                    cdtime = cdperm.Value.skinall;
            }

            if (cdtime > 0)
            {
                if (!_playercooldowns.TryGetValue(player.userID, out CoolDowns coolDowns))
                    _playercooldowns.Add(player.userID, new CoolDowns() { skinall = Time.time });
                else
                {
                    if (coolDowns.skinall + cdtime > Time.time)
                    {
                        ChatMessage(iplayer, "CommandCooldown", TimeSpan.FromSeconds(coolDowns.skinall + cdtime - Time.time).ToString("hh' hrs 'mm' mins 'ss' secs'"));
                        return;
                    }
                    coolDowns.skinall = Time.time;
                }
            }

            string skinned = "items";
            ItemDefinition itemdef = null;
            if (args.Length > 0)
            {
                itemdef = ItemManager.FindItemDefinition(args[0]);
                if (itemdef == null)
                {
                    ChatMessage(iplayer, "NoShortname", args[0]);
                    return;
                }
                skinned = itemdef.shortname;
            }

            foreach (var decayent in buildingManager.decayEntities)
            {
                StorageContainer storageContainer = decayent?.GetEntity() as StorageContainer;
                if (storageContainer == null) continue;
                foreach (var item in storageContainer.inventory.itemList)
                {
                    if (itemdef != null)
                    {
                        if (item.info.shortname != itemdef.shortname)
                            continue;
                    }

                    if (cachedskins.TryGetValue(item.info.itemid, out ulong uID))
                    {
                        SkinItem(item, uID);
                    }
                }
            }
            ChatMessage(iplayer, "CompletedAllSkin", skinned);
        }

        private void SBNextPageCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer? player = iplayer.Object as BasePlayer;
            if (player == null) return;
            if (!_viewingcon.TryGetValue(player.userID, out BoxController? boxController)) return;

            if (boxController._fillingbox || boxController._clearingbox)
                return;
            boxController.NextPage();
        }

        private void SBBackPageCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer? player = iplayer.Object as BasePlayer;
            if (player == null) return;
            if (!_viewingcon.TryGetValue(player.userID, out BoxController? boxController)) return;
            if (boxController._fillingbox || boxController._clearingbox)
                return;
            boxController.BackPage();
        }

        private void SearchCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!_viewingcon.TryGetValue(player.userID, out BoxController? boxController)) return;

            if (boxController._fillingbox || boxController._clearingbox)
                return;

            string searchtxt = string.Join(",", args).Replace(",", " ");

            if (searchtxt.Trim().ToLower() == "search id or name")
                searchtxt = string.Empty;

            if (boxController.searchtxt == searchtxt) return;

            boxController.searchtxt = searchtxt;
            boxController.SearchUpdate();
        }

        private void SetSelectCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;
            if (args.Length != 1)
            {
                ChatMessage(iplayer, "SkinSetSelectedArgs");
                return;
            }
            int setselect = 0;

            if (!int.TryParse(args[0], out setselect) || setselect < 1 || setselect > 3)
            {
                ChatMessage(iplayer, "SkinSetSelectedArgs");
                return;
            }

            _playerSelectedSet[player.userID] = setselect;
            ChatMessage(iplayer, "SkinSetSelected", setselect);

            if (player.TryGetComponent<InventoryWatcher>(out InventoryWatcher inventoryWatcher))
            {
                inventoryWatcher.refreshSkins();
            }

            if (!_viewingcon.TryGetValue(player.userID, out BoxController? boxController)) return;

            if (boxController._fillingbox || boxController._clearingbox)
                return;

            boxController.setSelect = setselect;
            boxController.SetUpdate();
        }

        private void RequestSelectCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer? player = iplayer.Object as BasePlayer;
            if (player == null) return;
            if (args.Length != 1)
            {
                return;
            }

            if (!_viewingcon.TryGetValue(player.userID, out BoxController boxController)) return;

            if (boxController._fillingbox || boxController._clearingbox)
                return;

            if (args[0] == "Try" || args[0] == "Approve" || args[0] == "Deny")
            {
                boxController.requestselected = args[0];
            }
            else
            {
                player.EndLooting();
                return;
            }

            boxController.SkinRequests();
        }

        #region Awaken Skinbox Commands

        public class AwakenSkinboxState
        {
            public int CurrentPage { get; set; } = 0;
            public string SelectedCategory { get; set; } = "All";
            public ulong SelectedSkinID { get; set; } = 0;
            public uint SelectedItemUID { get; set; } = 0;
            public string SearchTerm { get; set; } = "";
        }
        
        private int GetPlayerSelectedSet(BasePlayer player)
        {
            return _playerSelectedSet.TryGetValue(player.userID, out int set) ? set : 1;
        }

        private void AwakenSkinboxCloseCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            CuiHelper.DestroyUi(player, AWAKEN_SKINBOX_MAIN);
            _playerUIState.Remove(player.userID);
        }

        private void AwakenSkinboxPrevPageCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!_playerUIState.TryGetValue(player.userID, out AwakenSkinboxState state))
                return;

            if (state.CurrentPage > 0)
            {
                state.CurrentPage--;
                RefreshAwakenSkinboxUI(player);
            }
        }

        private void AwakenSkinboxNextPageCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!_playerUIState.TryGetValue(player.userID, out AwakenSkinboxState state))
                return;

            var itemToSkin = player.inventory.FindItemByUID(new ItemId(state.SelectedItemUID));
            if(itemToSkin == null) return;

            var availableSkins = GetSkinsForItem(itemToSkin.info.itemid, state.SearchTerm);
            int skinsPerPage = 48;
            int maxPages = (availableSkins.Count - 1) / skinsPerPage + 1;

            if (state.CurrentPage < maxPages - 1)
            {
                state.CurrentPage++;
                RefreshAwakenSkinboxUI(player);
            }
        }

        private void AwakenSkinboxApplySelectedCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!_playerUIState.TryGetValue(player.userID, out AwakenSkinboxState state) || state.SelectedItemUID == 0)
            {
                ChatMessage(iplayer, "NoItemSelected");
                return;
            }
            
            var itemToSkin = player.inventory.FindItemByUID(new ItemId(state.SelectedItemUID));
            if(itemToSkin == null)
            {
                ChatMessage(iplayer, "NoItemSelected");
                return;
            }

            var defaultSkins = GetCachedSkins(player, GetPlayerSelectedSet(player));
            var skinToSet = itemToSkin.skin;

            if(skinToSet == 0) // if removing skin, don't save it
            {
                defaultSkins.Remove(itemToSkin.info.itemid);
            }
            else
            {
                defaultSkins[itemToSkin.info.itemid] = skinToSet;
            }

            RefreshAwakenSkinboxUI(player);
        }

        private void AwakenSkinboxApplyAllCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            Dictionary<int, ulong> cachedSkins = GetCachedSkins(player, GetPlayerSelectedSet(player));
            ApplyAllSkinsToInventory(player, cachedSkins);
        }

        private void AwakenSkinboxApplyInventoryCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            AwakenSkinboxApplyAllCMD(iplayer, command, args);
        }

        private void AwakenSkinboxSetCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            int setNumber = 1;
            if (command.EndsWith(".1")) setNumber = 1;
            else if (command.EndsWith(".2")) setNumber = 2;
            else if (command.EndsWith(".3")) setNumber = 3;

            _playerSelectedSet[player.userID] = setNumber;
            RefreshAwakenSkinboxUI(player);
        }

        private void AwakenSkinboxSelectSkinCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (args.Length < 1 || !ulong.TryParse(args[0], out ulong skinID))
                return;

            if (!_playerUIState.TryGetValue(player.userID, out AwakenSkinboxState state))
                return;

            var item = player.inventory.FindItemByUID(new ItemId(state.SelectedItemUID));
            if (item == null)
            {
                ChatMessage(iplayer, "NoItemSelected");
                return;
            }

            SkinItem(item, skinID);
            RefreshAwakenSkinboxUI(player);
        }

        private void AwakenSkinboxCategoryCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (args.Length < 1) return;

            if (!_playerUIState.TryGetValue(player.userID, out AwakenSkinboxState state))
                return;
            
            var oldCategory = state.SelectedCategory;
            state.SelectedCategory = args[0];
            state.CurrentPage = 0; 
            
            // If category changed, find a new item to select
            if(oldCategory != state.SelectedCategory)
            {
                state.SelectedItemUID = 0; // Reset selected item
                var items = GetPlayerItemsByCategory(player, state.SelectedCategory);
                if (items.Count > 0)
                {
                    state.SelectedItemUID = (uint)items[0].uid.Value;
                }
            }

            RefreshAwakenSkinboxUI(player);
        }

        private void AwakenSkinboxSearchCMD(IPlayer iplayer, string command, string[] args)
        {
            BasePlayer player = iplayer.Object as BasePlayer;
            if (player == null) return;

            if (!_playerUIState.TryGetValue(player.userID, out AwakenSkinboxState state))
                return;

            string searchTerm = string.Join(" ", args);
            if (searchTerm.ToLower() == "search skins...") searchTerm = "";

            state.SearchTerm = searchTerm;
            state.CurrentPage = 0;
            RefreshAwakenSkinboxUI(player);
        }

        private void RefreshAwakenSkinboxUI(BasePlayer player)
        {
            if (!_playerUIState.TryGetValue(player.userID, out AwakenSkinboxState state))
                return;

            CuiHelper.DestroyUi(player, AWAKEN_SKINBOX_MAIN);
            string uiJson = CreateAwakenSkinboxUI(player, state);
            CuiHelper.AddUi(player, uiJson);
        }

        private void ApplySkinToInventory(BasePlayer player, ulong skinID, bool applyToAll = false)
        {
            int itemsSkined = 0;
            List<Item> allItems = Pool.Get<List<Item>>();
            player.inventory.GetAllItems(allItems);

            ItemDefinition skinItemDef = null;

            foreach (var entry in _cachedSkins)
            {
                if (entry.Value.Contains(skinID))
                {
                    skinItemDef = ItemManager.FindItemDefinition(entry.Key);
                    break;
                }
            }

            if (skinItemDef == null)
            {
                Pool.FreeUnmanaged(ref allItems);
                return;
            }

            foreach (var item in allItems)
            {
                if (item == null) continue;

                if (item.info.itemid == skinItemDef.itemid)
                {
                    SkinItem(item, skinID);
                    itemsSkined++;

                    if (!applyToAll) break;
                }
            }

            Pool.FreeUnmanaged(ref allItems);
            ChatMessage(player.IPlayer, "SkinnedItems", itemsSkined);
        }

        private void ApplyAllSkinsToInventory(BasePlayer player, Dictionary<int, ulong> cachedSkins)
        {
            int itemsSkined = 0;
            List<Item> allItems = Pool.Get<List<Item>>();
            player.inventory.GetAllItems(allItems);

            foreach (var item in allItems)
            {
                if (item == null) continue;

                if (cachedSkins.TryGetValue(item.info.itemid, out ulong skinID))
                {
                    SkinItem(item, skinID);
                    itemsSkined++;
                }
            }

            Pool.FreeUnmanaged(ref allItems);
            ChatMessage(player.IPlayer, "SkinnedItems", itemsSkined);
        }

        private void OpenAwakenSkinboxUI(BasePlayer player)
        {
            if (!_playerUIState.ContainsKey(player.userID))
            {
                _playerUIState[player.userID] = new AwakenSkinboxState();
            }

            var state = _playerUIState[player.userID];

            // If no item is selected, find the first available one.
            if(state.SelectedItemUID == 0)
            {
                var items = GetPlayerItemsByCategory(player, "All");
                if(items.Count > 0)
                {
                    state.SelectedItemUID = (uint)items[0].uid.Value;
                }
            }

            string uiJson = CreateAwakenSkinboxUI(player, state);
            CuiHelper.AddUi(player, uiJson);
        }

        #endregion

        #region Awaken Skinbox Console Commands

        [ConsoleCommand("awaken.skin.selectitem")]
        private void CmdAwakenSelectItem(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            if (arg.Args == null || arg.Args.Length < 1) return;

            if (!uint.TryParse(arg.Args[0], out uint itemUID)) return;

            if (!_playerUIState.ContainsKey(player.userID))
                _playerUIState[player.userID] = new AwakenSkinboxState();

            var state = _playerUIState[player.userID];
            state.SelectedItemUID = itemUID;
            state.CurrentPage = 0;
            state.SearchTerm = string.Empty;

            RefreshPlayerUI(player);
        }

        [ConsoleCommand("awaken.skin.select")]
        private void CmdAwakenSkinSelect(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            if (arg.Args == null || arg.Args.Length < 1) return;

            if (!ulong.TryParse(arg.Args[0], out ulong skinID)) return;

            if (!_playerUIState.ContainsKey(player.userID))
                _playerUIState[player.userID] = new AwakenSkinboxState();

            var state = _playerUIState[player.userID];

            if (state.SelectedItemUID == 0)
            {
                ChatMessage(player.IPlayer, "NoItemSelected");
                return;
            }

            var itemToSkin = player.inventory.FindItemByUID(new ItemId(state.SelectedItemUID));
            if (itemToSkin != null)
            {
                SkinItem(itemToSkin, skinID);
                RefreshPlayerUI(player);
            }
        }

        private void RefreshPlayerUI(BasePlayer player)
        {
            if (_playerUIState.TryGetValue(player.userID, out var state))
            {
                CuiHelper.DestroyUi(player, AWAKEN_SKINBOX_MAIN);
                string uiJson = CreateAwakenSkinboxUI(player, state);
                CuiHelper.AddUi(player, uiJson);
            }
        }

        #endregion

        #endregion Commands

        #region Hooks

        private void OnPlayerLootEnd(PlayerLoot instance)
        {
            if (instance.baseEntity == null || !_viewingcon.TryGetValue(instance.baseEntity.userID, out BoxController? boxController)) return;
            
            if (boxController.boxtype == "craftskins")
            {
                if (instance.baseEntity.TryGetComponent<InventoryWatcher>(out InventoryWatcher inventoryWatcher))
                {
                    inventoryWatcher.refreshSkins();
                }
            }
            if (boxController != null)
                UnityEngine.Object.Destroy(boxController);
            _viewingcon.Remove(instance.baseEntity.userID);
        }

        void OnPlayerConnected(BasePlayer player)
        {
            if (!HasPerm(player.UserIDString, permskinautotoggle))
                return;
            if (!HasPerm(player.UserIDString, permskinauto))
                return;

            if (!player.TryGetComponent<InventoryWatcher>(out InventoryWatcher inventoryWatcher))
            {
                player.gameObject.AddComponent<InventoryWatcher>();
            }
        }

        private void OnPlayerDisconnected(BasePlayer player, string strReason)
        {
            if (player.TryGetComponent<SpraycanController>(out SpraycanController spraycanController))
            {
                UnityEngine.Object.Destroy(spraycanController);
            }
            if (player.TryGetComponent<InventoryWatcher>(out InventoryWatcher inventoryWatcher))
            {
                UnityEngine.Object.Destroy(inventoryWatcher);
            }

            if (_playerUsageData.TryGetValue(player.userID, out PlayerData playerData))
                playerData.UpdateLastOnline();

             _playerUIState.Remove(player.userID);
        }

        private void OnItemCraftFinished(ItemCraftTask task, Item item, ItemCrafter itemCrafter)
        {
            if (task.skinID != 0)
                return;

            BasePlayer player = itemCrafter.owner;

            if (player == null) return;

            if (!HasPerm(player.UserIDString, permdefault))
                return;

            Dictionary<int, ulong> cached = GetCachedSkins(player);

            if (cached.Count < 1)
                return;

            if (!cached.TryGetValue(item.info.itemid, out ulong uID))
                return;

            SkinItem(item, uID);
        }

        private object OnItemAction(Item item, string action, BasePlayer player)
        {
            if (item.text == "1")
            {
                item.RemoveFromContainer();
                item.Remove();
                return true;
            }
            return null;
        }

        #endregion Hooks

        #region Methods
        private static string _coffinPrefab = "assets/prefabs/misc/halloween/coffin/coffinstorage.prefab";
        private BaseEntity _limitedEntity = new BaseEntity() { _limitedNetworking = true };
        private ItemContainer CreateContainer()
        {
            ItemContainer itemContainer = new ItemContainer()
            {
                entityOwner = _limitedEntity,
                allowedContents = ItemContainer.ContentsType.Generic,
            };
            itemContainer.maxStackSize = 0;
            itemContainer.ServerInitialize(null, 48);
            itemContainer.GiveUID();
            return itemContainer;
        }

        private void StartLooting(BasePlayer player, ItemContainer itemContainer)
        {
            if (player == null || itemContainer == null || itemContainer.uid == default(ItemContainerId) || !player.IsAlive() || !_viewingcon.ContainsKey(player.userID)) return;
            player.inventory.loot.Clear();
            player.inventory.loot.AddContainer(itemContainer);
            player.inventory.loot.entitySource = RelationshipManager.ServerInstance;
            player.inventory.loot.PositionChecks = false;
            player.inventory.loot.MarkDirty();
            player.SendNetworkUpdateImmediate();

            RPCStartLooting(player);
        }

        private static readonly uint RPCOpenLootstr = StringPool.Get("RPC_OpenLootPanel");

        public static void RPCStartLooting(BasePlayer player)
        {
            var options = new Translate.Phrase(string.Empty, "SKIN SELECTOR");

            ClientRPCPlayer(null, player, "RPC_OpenLootPanel", "generic_resizable", options);
        }
        
        public void SkinItem(Item item, ulong uID)
        {
            int redirectitemID = 0;
            if (item.info.isRedirectOf != null)
                redirectitemID = item.info.itemid;
            if (HasMask(uID))
                redirectitemID = (int)UnsetMask(uID);
            
            if (HasMask(uID) && !config.blacklisteditems.Contains((int)UnsetMask(uID)))
                 redirectitemID = (int)UnsetMask(uID);

            if (redirectitemID != 0)
            {
                Item redirectitem = ItemManager.CreateByItemID(redirectitemID, item.amount, !HasMask(uID) ? uID : 0ul);

                if (redirectitem?.info == null) return;

                redirectitem.ownershipShares = item.ownershipShares;

                redirectitem.text = "1";
                redirectitem.maxCondition = item.maxCondition;
                redirectitem.condition = item.condition;

                if ((item.contents?.capacity ?? 0) > 0)
                {
                    ItemModContainerArmorSlot itemModContainerArmorSlot = GetItemModContainerSlot(item);
                    if (itemModContainerArmorSlot != null)
                    {
                        itemModContainerArmorSlot.CreateAtCapacity(item.contents.capacity, redirectitem);
                    }
                    if (redirectitem.contents == null)
                    {
                        redirectitem.contents = Pool.Get<ItemContainer>();
                    }
                    if (item.contents.capacity != redirectitem.contents?.capacity)
                    {
                        redirectitem.contents.ServerInitialize(redirectitem, item.contents.capacity);
                    }
                    for (int i = item.contents.itemList.Count - 1; i >= 0; i--)
                    {
                        Item conItem = item.contents.itemList[i];
                        if (conItem == null)
                            continue;
                        conItem.MoveToContainer(redirectitem.contents);
                    }
                }

                if (skinner.config.applySkinNames)
                {
                    if (redirectitem.skin != 0ul && _skinNames.TryGetValue(uID, out string displayName))
                        redirectitem.name = displayName;
                }
                else
                {
                    redirectitem.name = item.name;
                }

                BaseEntity held1 = redirectitem.GetHeldEntity();
                if (held1 != null)
                {
                    BaseEntity mainheld = item.GetHeldEntity();
                    if (mainheld != null)
                    {
                        BaseProjectile mainbaseProjectile = mainheld as BaseProjectile;
                        BaseProjectile baseProjectile = held1 as BaseProjectile;
                        if (baseProjectile != null && mainbaseProjectile != null)
                        {
                            baseProjectile.primaryMagazine.contents = mainbaseProjectile.primaryMagazine.contents;
                            baseProjectile.primaryMagazine.ammoType = mainbaseProjectile.primaryMagazine.ammoType;
                        }
                    }
                    held1.skinID = redirectitem.skin;
                    held1.SendNetworkUpdate();
                }

                NextTick(() => {
                    int pos = item.position;
                    ItemContainer parent = item.parent;
                    if (parent == null || item?.info == null)
                    {
                        redirectitem.Remove();
                        return;
                    }

                    item.Remove(0f);
                    if (!redirectitem.MoveToContainer(parent, pos, false))
                        redirectitem.Drop(parent.dropPosition, parent.dropVelocity);
                    redirectitem.text = string.Empty;
                });
            }
            else
            {
                item.skin = uID;

                if (skinner.config.applySkinNames)
                {
                    if (_skinNames.TryGetValue(uID, out string displayName))
                        item.name = displayName;
                }

                BaseEntity held = item.GetHeldEntity();
                if (held != null)
                {
                    held.skinID = uID;
                }
                item.MarkDirty();
            }
        }

        public BaseCombatEntity? SkinDeployable(BaseCombatEntity baseCombatEntity, ulong uID)
        {
            ItemDefinition? entDef = null;
            if (baseCombatEntity.pickup.itemTarget != null)
            {
                entDef = baseCombatEntity.pickup.itemTarget;
            }
            else if (baseCombatEntity.repair.itemTarget != null)
            {
                entDef = baseCombatEntity.repair.itemTarget;
            }

            if (entDef == null)
                return null;

            if (!HasMask(uID) && entDef.isRedirectOf == null)
            {
                if (uID == baseCombatEntity.skinID)
                    return baseCombatEntity;

                baseCombatEntity.skinID = uID;
                
                if (baseCombatEntity.skinID < 100000)
                {
                    SendNetworkUpdate(baseCombatEntity);
                }
                else
                {
                    baseCombatEntity.SendNetworkUpdateImmediate();
                }
                return baseCombatEntity;
            }
            
            if (HasMask(uID) && !config.blacklisteditems.Contains((int)UnsetMask(uID)))
                entDef = ItemManager.FindItemDefinition((int)UnsetMask(uID));
            else if (entDef.isRedirectOf != null)
                entDef = entDef.isRedirectOf;

            if (entDef == null)
                return null;

            if (!GetEntityPrefabPath(entDef, out string respath))
                return null;

            Vector3 vector31 = baseCombatEntity.transform.localPosition;
            Quaternion quaternion = baseCombatEntity.transform.localRotation;
            BaseEntity parentEntity = baseCombatEntity.GetParentEntity();
            float single = baseCombatEntity._health;
            EntityRef[] slots = baseCombatEntity.GetSlots();
            ulong ownerID = baseCombatEntity.OwnerID;
            float single1 = (baseCombatEntity != null ? baseCombatEntity.lastAttackedTime : 0f);
            HashSet<PlayerNameID> playerNameIDs = null;

            BuildingPrivlidge buildingPrivlidge = baseCombatEntity.GetBuildingPrivilege();
            if (buildingPrivlidge != null)
            {
                playerNameIDs = new HashSet<PlayerNameID>(buildingPrivlidge.authorizedPlayers);
            }

            bool flag1 = (baseCombatEntity is Door || baseCombatEntity is BuildingPrivlidge);

            Dictionary<ContainerSet, List<Item>> containerSets = new Dictionary<ContainerSet, List<Item>>();
            SaveEntityStorage(baseCombatEntity, containerSets, 0);

            List<ChildPreserveInfo> list = Pool.Get<List<ChildPreserveInfo>>();
            if (!flag1)
            {
                for (int i = 0; i < baseCombatEntity.children.Count; i++)
                {
                    SaveEntityStorage(baseCombatEntity.children[i], containerSets, -1);
                }
            }
            else
            {
                foreach (BaseEntity child in baseCombatEntity.children)
                {
                    ChildPreserveInfo childPreserveInfo = new ChildPreserveInfo()
                    {
                        TargetEntity = child,
                        TargetBone = child.parentBone,
                        LocalPosition = child.transform.localPosition,
                        LocalRotation = child.transform.localRotation,
                    };
                    list.Add(childPreserveInfo);
                }

                foreach (ChildPreserveInfo childPreserveInfo1 in list)
                {
                    childPreserveInfo1.TargetEntity.SetParent(null, true, false);
                }
            }

            baseCombatEntity.Kill(BaseNetworkable.DestroyMode.None);
            BaseEntity newent = GameManager.server.CreateEntity(respath,
                (parentEntity != null ? parentEntity.transform.TransformPoint(vector31) : vector31),
                (parentEntity != null ? parentEntity.transform.rotation * quaternion : quaternion), true);
            newent.SetParent(parentEntity, false, false);
            newent.transform.localPosition = vector31;
            newent.transform.localRotation = quaternion;
            newent.OwnerID = ownerID;

            if (!HasMask(uID))
                newent.skinID = uID;

            DecayEntity decayEntity = newent as DecayEntity;
            if (decayEntity != null)
            {
                decayEntity.AttachToBuilding(null);
            }

            newent.Spawn();
            BaseCombatEntity baseCombatEntity1 = newent as BaseCombatEntity;
            if (baseCombatEntity1 != null)
            {
                baseCombatEntity1.SetHealth(single);
                baseCombatEntity1.lastAttackedTime = single1;
            }

            BuildingPrivlidge buildingPrivlidge1 = newent as BuildingPrivlidge;
            if (buildingPrivlidge1 != null && playerNameIDs != null)
            {
                buildingPrivlidge1.authorizedPlayers = playerNameIDs;
            }

            if (containerSets.Count > 0)
            {
                RestoreEntityStorage(newent, 0, containerSets);
                if (!flag1)
                {
                    for (int j = 0; j < newent.children.Count; j++)
                    {
                        RestoreEntityStorage(newent.children[j], -1, containerSets);
                    }
                }

                foreach (KeyValuePair<ContainerSet, List<Item>> containerSet in containerSets)
                {
                    foreach (Item value in containerSet.Value)
                    {
                        value.Remove(0f);
                    }
                }
            }

            if (flag1)
            {
                foreach (ChildPreserveInfo child in list)
                {
                    child.TargetEntity.SetParent(baseCombatEntity1, child.TargetBone, true, false);
                    child.TargetEntity.transform.localPosition = child.LocalPosition;
                    child.TargetEntity.transform.localRotation = child.LocalRotation;
                    child.TargetEntity.SendNetworkUpdate(BasePlayer.NetworkQueue.Update);
                }

                baseCombatEntity1.SetSlots(slots);
            }

            Pool.FreeUnmanaged(ref list);

            return baseCombatEntity1;
        }

        #region Spraycan Code from Assembly
        private struct ChildPreserveInfo
        {
            public BaseEntity TargetEntity;
            public uint TargetBone;
            public Vector3 LocalPosition;
            public Quaternion LocalRotation;
        }

        private struct ContainerSet
        {
            public int ContainerIndex;
            public uint PrefabId;
        }

        private bool GetEntityPrefabPath(ItemDefinition def, out string resourcePath)
        {
            ItemModDeployable itemModDeployable;
            ItemModEntity itemModEntity;
            ItemModEntityReference itemModEntityReference;
            resourcePath = string.Empty;
            if (def.TryGetComponent<ItemModDeployable>(out itemModDeployable))
            {
                resourcePath = itemModDeployable.entityPrefab.resourcePath;
                return true;
            }
            if (def.TryGetComponent<ItemModEntity>(out itemModEntity))
            {
                resourcePath = itemModEntity.entityPrefab.resourcePath;
                return true;
            }
            if (!def.TryGetComponent<ItemModEntityReference>(out itemModEntityReference))
            {
                return false;
            }
            resourcePath = itemModEntityReference.entityPrefab.resourcePath;
            return true;
        }

        void SaveEntityStorage(BaseEntity baseEntity, Dictionary<ContainerSet, List<Item>> dictionary, int index)
        {
            uint num;
            IItemContainerEntity itemContainerEntity = baseEntity as IItemContainerEntity;
            if (itemContainerEntity != null)
            {
                ContainerSet containerSet = new ContainerSet()
                {
                    ContainerIndex = index
                };
                if (index == 0)
                {
                    num = 0;
                }
                else
                {
                    num = baseEntity.prefabID;
                }
                containerSet.PrefabId = num;
                ContainerSet containerSet1 = containerSet;
                if (dictionary.ContainsKey(containerSet1))
                {
                    return;
                }
                dictionary.Add(containerSet1, new List<Item>());
                foreach (Item item in itemContainerEntity.inventory.itemList)
                {
                    dictionary[containerSet1].Add(item);
                }
                foreach (Item item1 in dictionary[containerSet1])
                {
                    item1.RemoveFromContainer();
                }
            }
        }

        void RestoreEntityStorage(BaseEntity baseEntity, int index, Dictionary<ContainerSet, List<Item>> copy)
        {
            uint num;
            IItemContainerEntity itemContainerEntity = baseEntity as IItemContainerEntity;
            if (itemContainerEntity != null)
            {
                ContainerSet containerSet = new ContainerSet()
                {
                    ContainerIndex = index
                };
                if (index == 0)
                {
                    num = 0;
                }
                else
                {
                    num = baseEntity.prefabID;
                }
                containerSet.PrefabId = num;
                ContainerSet containerSet1 = containerSet;
                if (copy.ContainsKey(containerSet1))
                {
                    foreach (Item item in copy[containerSet1])
                    {
                        item.MoveToContainer(itemContainerEntity.inventory, -1, true, false, null, true);
                    }
                    copy.Remove(containerSet1);
                }
            }
        }
        #endregion Spraycan Code from Assembly

        #endregion Methods

        #region Inventory Watcher
        private class InventoryWatcher : FacepunchBehaviour
        {
            private BasePlayer player;
            Dictionary<int, ulong> cachedSkins;

            private void Awake()
            {
                player = GetComponent<BasePlayer>();
                cachedSkins = skinner.GetCachedSkins(player, skinner.GetPlayerSelectedSet(player));
                subContainerWatch();
            }

            private void subContainerWatch()
            {
                player.inventory.containerBelt.onItemAddedRemoved += skinWatch;
                player.inventory.containerWear.onItemAddedRemoved += skinWatch;
                player.inventory.containerMain.onItemAddedRemoved += skinWatch;
            }

            private void skinWatch(Item item, bool f)
            {
                if (!f || item.text == "1" || skinner._viewingcon.ContainsKey(player.userID)) return;

                if (cachedSkins.IsNullOrEmpty() || (item.info ?? null) == null) return;

                if (cachedSkins.TryGetValue(item.info?.isRedirectOf == null ? item.info.itemid : item.info.isRedirectOf.itemid, out ulong uID))
                {
                    skinner.SkinItem(item, uID);
                }
            }

            public void refreshSkins()
            {
                cachedSkins = skinner.GetCachedSkins(player, skinner.GetPlayerSelectedSet(player));
            }

            private void OnDestroy()
            {
                if (player != null && player.inventory != null)
                {
                    player.inventory.containerBelt.onItemAddedRemoved -= skinWatch;
                    player.inventory.containerWear.onItemAddedRemoved -= skinWatch;
                    player.inventory.containerMain.onItemAddedRemoved -= skinWatch;
                }
            }
        }
        #endregion Inventory Watcher

        #region Spraycan Controller

        private static int spraycanid = -596876839;
        void OnActiveItemChanged(BasePlayer player, Item oldItem, Item newItem)
        {
            if (newItem?.info == null)
            {
                if (oldItem?.info?.itemid == spraycanid)
                {
                    SpraycanController spraycanController;
                    if (player.TryGetComponent<SpraycanController>(out spraycanController))
                    {
                        UnityEngine.Object.Destroy(spraycanController);
                    }
                }

                return;
            }

            if (newItem.info.itemid == spraycanid)
            {
                SpraycanController spraycanController;
                if (!player.TryGetComponent<SpraycanController>(out spraycanController))
                {
                    player.gameObject.AddComponent<SpraycanController>();
                    return;
                }
            }

            if (oldItem != null)
            {
                if (oldItem.info.itemid == spraycanid)
                {
                    SpraycanController spraycanController;
                    if (player.TryGetComponent<SpraycanController>(out spraycanController))
                    {
                        UnityEngine.Object.Destroy(spraycanController);
                    }
                }
            }
        }

        private class SpraycanController : FacepunchBehaviour
        {
            private BasePlayer player;
            private SprayCan sprayCan;
            private string skinitem = "SkinItem";
            BUTTON _fire2 = BUTTON.FIRE_SECONDARY;
            BaseEntity.Flags fbusy = BaseEntity.Flags.Busy;
            float lasttime = 0f;
            private void Awake()
            {
                player = GetComponent<BasePlayer>();
                sprayCan = player.GetHeldEntity() as SprayCan;
                if (sprayCan == null)
                {
                    Destroy(this);
                    return;
                }
                sprayCan.SetFlag(fbusy, true, true, true);
            }

            private void FixedUpdate()
            {
                if (player == null || !player.serverInput.IsDown(_fire2) || !player.serverInput.WasDown(_fire2))
                    return;

                if (Time.time < lasttime + 0.5f)
                    return;

                lasttime = Time.time;
                player.serverInput.previous.buttons = 0;

                skinner.SkinItemCMD(player.IPlayer, skinitem, Array.Empty<string>());
            }
        }

        #endregion Spraycan Controller

        #region Box Controller
        public Queue<Item> itemPool = new Queue<Item>();
        public class BoxController : FacepunchBehaviour
        {
            public ItemContainer inventory;
            private BasePlayer player;
            private Item? mainitem = null;
            private Item? returnitem = null;
            private Item? returnitemplayer = null;

            private Vector3 ogpos;
            public bool _fillingbox = false;
            public bool _clearingbox = false;
            private ItemDefinition? itemselected = null;
            public BaseCombatEntity? maindeployable = null;
            public string searchtxt = string.Empty;
            public int setSelect = 1;
            public string requestselected = "Try";

            public Dictionary<int, ulong> setSkins;

            bool rebuildsearchUI = false;
            bool rebuildPageUI = false;
            
            public string boxtype = string.Empty;

            private int page = 0;
            private int scpage = 0;

            public void StartAwake()
            {
                player = GetComponent<BasePlayer>();
                ogpos = player.transform.position;
                inventory.maxStackSize = 1;
                inventory.onPreItemRemove = Preremove;
            }

            private void Preremove(Item item)
            {
                if (item.amount == 0)
                {
                    bool flag1 = false;
                    foreach (var item2 in inventory.itemList)
                    {
                        if (item2.amount <= 1) continue;
                        flag1 = true;
                        item2.amount = 1;
                    }
                    if (!flag1)
                    {
                        List<Item>? items = GetPlayerItems(item.info.itemid);
                        if (items != null)
                        {
                            foreach (var item1 in items)
                            {
                                if (item1.skin != item.skin) continue;
                                item1.amount -= 1;
                                if (item1.amount < 1)
                                    item1.Remove(0f);
                                else
                                    item1.MarkDirty();
                                flag1 = false;
                                break;
                            }
                            if (flag1 && items.Count > 0)
                            {
                                Item item2 = items[0];
                                item2.amount -= 1;
                                if (item2.amount < 1)
                                    item2.Remove(0f);
                                else
                                    item2.MarkDirty();
                            }
                            Pool.FreeUnmanaged(ref items);
                        }
                    }
                }

                if (boxtype != "deployableskins" && boxtype != "itemskins")
                    return;

                int itempos = item.position;
                int usagecnt = usageskins == null ? 0 : usageskins.Count;

                if (cachedskins == null || item.position < 0)
                {
                    mainitem = null;
                    return;
                }

                if (posWatchItem?.info != null && item.info.itemid != posWatchItem?.info?.itemid && posWatchItem?.parent == null && !posWatchItem.IsDroppedInWorld(true))
                {
                    return;
                }

                if (item.text == "1")
                {
                    InsertItem(GetMask(item.skin, item.info.itemid, item.info.isRedirectOf != null), itemselected, 1, item.position);
                }

                posWatchItem = null;
                bool isredirect = item.info.isRedirectOf != null;
                if (item.skin != 0 || isredirect)
                {
                    if (!skinner._playerUsageData.TryGetValue(player.userID, out PlayerData? playerData))
                    {
                        skinner._playerUsageData[player.userID] = new PlayerData();
                    }
                    skinner._playerUsageData[player.userID].AddSkinUsage(item.skin, isredirect ? item.info.isRedirectOf.itemid : item.info.itemid, isredirect ? item.info.itemid : 0);
                }
            }

            #region Skin Deployables

            public void SkinDeplyoables(BaseCombatEntity entity, ItemDefinition itemDefinition)
            {
                boxtype = "deployableskins";
                inventory.onItemAddedRemoved = CheckforItemDply;
                inventory.SetFlag(ItemContainer.Flag.IsLocked, true);
                maindeployable = entity;
                rebuildsearchUI = true;
                itemselected = itemDefinition.isRedirectOf != null ? itemDefinition.isRedirectOf : itemDefinition;
                GetDeployableSkins();
            }

            private void CheckforItemDply(Item item, bool b)
            {
                if (b)
                {
                    if (_fillingbox) return;
                    if (item == returnitem) { returnitem = null; return; }
                    returnitemplayer = item;
                    GiveItem(returnitemplayer);
                    return;
                }

                if (_clearingbox || _fillingbox) return;

                searchtxt = string.Empty;
                if (maindeployable == null)
                {
                    item.Remove(0f);
                    player.EndLooting();
                    return;
                }

                if (item == returnitemplayer)
                {
                    returnitemplayer = null;
                    return;
                }

                bool isRedirect = item.info.isRedirectOf != null;
                ulong uID = GetMask(item.skin, isRedirect ? item.info.isRedirectOf.itemid : item.info.itemid, isRedirect);
                maindeployable = skinner.SkinDeployable(maindeployable, uID);

                if (skinner.config.sprayCanEffect)
                {
                    SprayCan can = player.GetHeldEntity() as SprayCan;
                    if (can != null && maindeployable != null)
                        can.ClientRPC<int, ulong>(null, "Client_ReskinResult", 1, maindeployable.net.ID.Value);
                }

                returnitem = item;
                item.Remove(0f);
            }

            private void GetDeployableSkins(bool skipchecks = false)
            {
                if (!skipchecks || cachedskins == null)
                {
                    if (!PrepareSkins(itemselected))
                        return;
                }
                
                FillSkins(itemselected);
                inventory.SetFlag(ItemContainer.Flag.IsLocked, false);
                inventory.SetFlag(ItemContainer.Flag.NoItemInput, false);
                inventory.MarkDirty();
            }

            private void FillSkins(ItemDefinition itemdef, bool bbreak = true)
            {
                _fillingbox = true;

                if (inventory.itemList.Count > 0)
                    ClearCon();

                int i = 0;
                int usagecnt = usageskins == null ? 0 : usageskins.Count;
                int maxIndex = Math.Min(48, cachedskins.Count + usagecnt - 48 * page);

                for (i = 0; i < maxIndex; i++)
                {
                    ulong cachedskin = i >= usagecnt ? cachedskins[i + (48 * page) - usagecnt] : usageskins[usagecnt - i - 1];
                    InsertItem(cachedskin, itemdef, 1, i);
                }
                _fillingbox = false;
            }

            #endregion Skin Deployables

            #region Skin Items

            private int olditempos;
            private ItemContainer oldcon;
            private Item? posWatchItem;
            private Item? backpack;
            public void StartItemSkin()
            {
                boxtype = "itemskins";
                inventory.maxStackSize = 0;
                inventory.onItemAddedRemoved = CheckforItem;
                inventory.onItemAddedToStack = OnItemAddedToStack;
                player.inventory.containerMain.onPreItemRemove += PosWatch;
                player.inventory.containerWear.onPreItemRemove += PosWatch;
                player.inventory.containerBelt.onPreItemRemove += PosWatch;
                backpack = player.inventory.containerWear?.GetSlot(ItemContainer.BackpackSlotIndex) ?? null;
                if (backpack?.contents == null || backpack.contents.itemList.IsNullOrEmpty()) return;
                backpack.contents.onPreItemRemove += PosWatch;
            }
            private Item SplitItem(Item item2, int split_Amount)
            {
                if (split_Amount <= 0) return null;
                if (split_Amount >= item2.amount) return null;

                item2.amount -= split_Amount;
                Item item = ItemManager.CreateByItemID(item2.info.itemid, 1, 0uL);
                item.amount = split_Amount;
                item.skin = item2.skin;
                if (item2.IsBlueprint())
                {
                    item.blueprintTarget = item2.blueprintTarget;
                }

                if (item2.info.amountType == ItemDefinition.AmountType.Genetics && item2.instanceData != null && item2.instanceData.dataInt != 0)
                {
                    item.instanceData = new ProtoBuf.Item.InstanceData();
                    item.instanceData.dataInt = item2.instanceData.dataInt;
                    item.instanceData.ShouldPool = false;
                }

                if (item2.instanceData != null && item2.instanceData.dataInt > 0 && item2.info != null && item2.info.Blueprint != null && item2.info.Blueprint.workbenchLevelRequired == 3)
                {
                    item.instanceData = new ProtoBuf.Item.InstanceData();
                    item.instanceData.dataInt = item2.instanceData.dataInt;
                    item.instanceData.ShouldPool = false;
                    item.SetFlag(Item.Flag.IsOn, item2.IsOn());
                }

                item2.MarkDirty();
                return item;
            }
            private void OnItemAddedToStack(Item item, int amount)
            {
                item.amount += amount;
                Item newitem = SplitItem(item, amount);
                skinner.NextTick(() => {
                    if (newitem.amount > 0)
                    {
                        mainitem = GiveItem(newitem);
                        rebuildsearchUI = true;
                    }
                    GetSkins();
                });
            }

            private void PosWatch(Item item)
            {
                if (item.text == "1") return;

                if (backpack != null && item.parent == backpack?.contents)
                {
                    if (backpack.position != ItemContainer.BackpackSlotIndex || player.inventory.containerWear != backpack.parent)
                    {
                        backpack.contents.onPreItemRemove -= PosWatch;
                        backpack = null;
                        return;
                    }
                }
                olditempos = item.position;
                oldcon = item.parent;
                posWatchItem = item;
            }

            private void CheckforItem(Item item, bool b)
            {
                if (!b)
                {
                    if (_clearingbox || _fillingbox || item.uid == mainitem?.uid) return;
                    if (posWatchItem != null && posWatchItem.uid != mainitem?.uid) { item.Remove(); return; }
                    if (mainitem?.info == null || (mainitem?.parent?.playerOwner ?? null) != player && !(backpack != null && mainitem?.parent == backpack?.contents))
                    {
                        item.Remove();
                        ResetCon();
                        return;
                    }
                    ItemRemoveCheck(item);
                    return;
                }
                
                if (_fillingbox) return;

                if (item.text == "1") { ClearCon(); GetSkins(); return; }

                if (mainitem?.info != null)
                {
                    rebuildPageUI = true;
                    if (item.uid != mainitem.uid)
                    {
                        if (item.info.itemid != mainitem.info.itemid)
                        {
                            if (!string.IsNullOrEmpty(searchtxt))
                                rebuildsearchUI = true;
                            page = 0;
                            searchtxt = string.Empty;
                            cachedskins = null;
                        }
                    }
                }
                else
                {
                    rebuildsearchUI = true;
                    page = 0;
                    searchtxt = string.Empty;
                    cachedskins = null;
                }
                mainitem = item;
                
                for (int i = 0; i < inventory.itemList.Count; i++)
                {
                    if (inventory.itemList[i].uid.Value != item.uid.Value) continue;
                    inventory.itemList.RemoveAt(i);
                    break;
                }

                if (oldcon != null && posWatchItem?.uid == item.uid)
                {
                    item.position = olditempos;
                    oldcon.itemList.Add(item);
                    item.parent = oldcon;
                    item.MarkDirty();
                    oldcon.onItemAddedRemoved(item, true);
                }
                else
                {
                    mainitem = GiveItem(item);
                }
                
                if (player.inventory.containerMain.itemList.Count == player.inventory.containerMain.capacity && player.inventory.containerBelt.itemList.Count == player.inventory.containerBelt.capacity)
                {
                    player.inventory.containerMain.capacity = 25;
                }

                if (mainitem.info.stackable > 1 || mainitem.MaxStackable() > 1)
                    inventory.maxStackSize = 1;
                else
                    inventory.maxStackSize = 0;

                usageskins = null;
                inventory.capacity = 48;
                GetSkins();
            }

            private List<ulong>? cachedskins;
            private List<ulong>? usageskins;
            private void GetSkins(bool skipchecks = false)
            {
                if (mainitem?.info == null) { ClearCon(); return; }

                itemselected = mainitem.info.isRedirectOf == null ? mainitem.info : mainitem.info.isRedirectOf;

                if (!skipchecks || cachedskins == null)
                {
                    if (!PrepareSkins(itemselected))
                        return;
                }
                
                FillSkins(itemselected);
                inventory.SetFlag(ItemContainer.Flag.IsLocked, false);
                inventory.SetFlag(ItemContainer.Flag.NoItemInput, false);
                inventory.MarkDirty();
                inventory.capacity = 49;
            }

            private void ResetCon()
            {
                ClearCon();
                cachedskins = null;

                CuiHelper.DestroyUi(player, SkinSearchUI);
                CuiHelper.DestroyUi(player, SkinPageUI);

                inventory.SetFlag(ItemContainer.Flag.NoItemInput, false);
                inventory.SetFlag(ItemContainer.Flag.IsLocked, false);
                mainitem = null;
                inventory.maxStackSize = 0;
            }

            private bool searchtextwas = false;
            private bool PrepareSkins(ItemDefinition itemdef)
            {
                if (IsMainItemBlacklisted(mainitem) || IsMainDeployableBlacklisted(maindeployable)) { ResetCon(); return false; }

                if (cachedskins == null)
                {
                    if (!_cachedSkins.TryGetValue(itemdef.itemid, out cachedskins)) { ResetCon(); return false; }
                    searchtextwas = false;
                }

                if (!string.IsNullOrEmpty(searchtxt))
                {
                    if (searchtextwas) { _cachedSkins.TryGetValue(itemdef.itemid, out cachedskins); }
                    List<ulong> cachedskins2 = new List<ulong>();
                    foreach (var cachedSkin in cachedskins)
                    {
                        if (!_skinNames.TryGetValue(cachedSkin, out string displayName)) continue;
                        if (displayName.Contains(searchtxt, StringComparison.CurrentCultureIgnoreCase) || cachedSkin.ToString().Contains(searchtxt))
                        {
                            cachedskins2.Add(cachedSkin);
                        }
                    }
                    if (cachedskins2.Count > 0)
                        cachedskins = cachedskins2;
                    searchtextwas = true;
                }
                else if ((boxtype == "itemskins" || boxtype == "deployableskins") && skinner._playerUsageData.TryGetValue(player.userID, out PlayerData playerData))
                {
                    var a = playerData.GetSkinUsage(itemdef.itemid);
                    if (a != null)
                        usageskins = new List<ulong>(a);
                }

                if (page > (cachedskins.Count - 1) / 48) page = 0;
                if (page < 0) page = (cachedskins.Count - 1) / 48;

                if (skinner.config.searchbar && rebuildsearchUI)
                {
                    CuiHelper.DestroyUi(player, SkinSearchUI);
                    CuiHelper.AddUi(player, skinner.AddSearchUI(searchtxt));
                    rebuildsearchUI = false;
                }

                if (inventory.itemList.Count == 0 || rebuildPageUI)
                {
                    CuiHelper.DestroyUi(player, SkinPageUI);
                    if (cachedskins.Count > 48)
                    {
                        CuiHelper.AddUi(player, skinner.AddPageUI(page + 1, (cachedskins.Count - 1) / 48 + 1));
                    }
                }
                return true;
            }

            public void QuickRemove(Item item)
            {
                if (item.parent != null)
                {
                    inventory.itemList.Remove(item);
                }
                item.parent = null;
                item.position = 0;
                skinner.itemPool.Enqueue(item);
            }

            private void ItemRemoveCheck(Item item)
            {
                if (item?.info == null) { ResetCon(); return; }
                if (mainitem?.info == null || mainitem?.parent == null) { item.Remove(0); ResetCon(); return; }

                if (item.info.itemid == mainitem.info.itemid)
                {
                    mainitem.skin = item.skin;
                    if (skinner.config.applySkinNames) { mainitem.name = item.name; }
                    item.Remove(0f);
                    BaseEntity held1 = mainitem.GetHeldEntity();
                    if (held1 != null)
                    {
                        held1.skinID = mainitem.skin;
                        if (player.svActiveItemID.Value == mainitem.uid.Value)
                        {
                            UpdateActiveItem(player, mainitem);
                            skinner.SendNetworkUpdate(held1);
                        }
                    }
                    mainitem.MarkDirty();
                    if (!skinner._playerUsageData.ContainsKey(player.userID))
                    {
                        skinner._playerUsageData[player.userID] = new PlayerData();
                    }
                    return;
                }

                if ((item.info.isRedirectOf != null ? item.info.isRedirectOf.itemid : mainitem.info.itemid) != (mainitem.info.isRedirectOf != null ? mainitem.info.isRedirectOf.itemid : mainitem.info.itemid))
                {
                    item.Remove();
                    ResetCon();
                    return;
                }

                Item newitem = ItemManager.CreateByItemID(item.info.itemid, mainitem.amount, item.skin);
                newitem.maxCondition = mainitem.maxCondition;
                newitem.condition = mainitem.condition;
                if (skinner.config.applySkinNames) { newitem.name = item.name; } else { newitem.name = mainitem.name; }
                newitem.ownershipShares = mainitem.ownershipShares;
                item.Remove();

                if ((mainitem.contents?.capacity ?? 0) > 0)
                {
                    ItemModContainerArmorSlot itemModContainerArmorSlot = GetItemModContainerSlot(mainitem);
                    if (itemModContainerArmorSlot != null) { itemModContainerArmorSlot.CreateAtCapacity(mainitem.contents.capacity, newitem); }
                    if (newitem.contents == null) { newitem.contents = Pool.Get<ItemContainer>(); }
                    if (mainitem.contents.capacity != newitem.contents?.capacity) { newitem.contents.ServerInitialize(newitem, mainitem.contents.capacity); }
                    for (int i = mainitem.contents.itemList.Count - 1; i >= 0; i--)
                    {
                        Item conItem = mainitem.contents.itemList[i];
                        if (conItem == null) continue;
                        conItem.MoveToContainer(newitem.contents);
                    }
                }

                BaseEntity held = newitem.GetHeldEntity();
                if (held != null)
                {
                    BaseEntity mainheld = mainitem.GetHeldEntity();
                    if (mainheld != null)
                    {
                        BaseProjectile mainbaseProjectile = mainheld as BaseProjectile;
                        BaseProjectile baseProjectile = held as BaseProjectile;
                        if (baseProjectile != null && mainbaseProjectile != null)
                        {
                            baseProjectile.canUnloadAmmo = true;
                            baseProjectile.primaryMagazine.contents = mainbaseProjectile.primaryMagazine.contents;
                            baseProjectile.primaryMagazine.ammoType = mainbaseProjectile.primaryMagazine.ammoType;
                        }
                    }
                }

                var parContainer = mainitem.parent;
                var parPos = mainitem.position;
                
                mainitem.Remove(0f);
                inventory.SetLocked(true);
                posWatchItem = null;

                skinner.NextTick(() =>
                {
                    mainitem.Remove(0f);
                    item.Remove(0f);
                    if (parPos == 24 && parContainer == player.inventory.containerMain)
                    {
                        ResetCon();
                        newitem.Drop(player.eyes.position, player.eyes.HeadForward() * 2, player.eyes.rotation);
                    }
                    else
                    {
                        newitem.position = parPos;
                        newitem.parent = parContainer;
                        parContainer.itemList.Add(newitem);
                        newitem.MarkDirty();
                        parContainer.onItemAddedRemoved(newitem, true);
                    }
                    newitem.RecalulateParentEntity(true);
                    posWatchItem = null;
                    mainitem = newitem;
                });

                Invoke(() => { inventory.SetLocked(false); }, 0.5f);
            }

            public void UpdateActiveItem(BasePlayer player, Item item)
            {
                Invoke(() =>
                {
                    if (player == null || player.IsDestroyed || player.IsDead()) return;
                    Item activeItem2 = player.GetActiveItem();
                    if (activeItem2 != null)
                    {
                        HeldEntity heldEntity2 = activeItem2.GetHeldEntity() as HeldEntity;
                        if (heldEntity2 != null) { heldEntity2.SetHeld(bHeld: true); }
                    }
                }, 0.1f);
            }
            #endregion Skin Items

            #region Set Default Skins

            public void SkinCraft()
            {
                boxtype = "craftskins";
                setSelect = skinner._playerSelectedSet.GetValueOrDefault(player.userID, 1);
                setSkins = skinner.GetCachedSkins(player, setSelect);

                if (player.inventory.containerMain.itemList.Count == player.inventory.containerMain.capacity && player.inventory.containerBelt.itemList.Count == player.inventory.containerBelt.capacity)
                {
                    player.inventory.containerMain.capacity = 25;
                }
                GetDefaultSkins();
            }

            private void GetDefaultSkins()
            {
                CuiHelper.DestroyUi(player, SkinSetsSelectUI);
                CuiHelper.AddUi(player, skinner.AddSetsUI(setSelect));
                CuiHelper.DestroyUi(player, SkinSearchUI);
                
                if (scpage > (_cachedSkins.Count - 1) / 48) scpage = 0;
                if (scpage < 0) scpage = (_cachedSkins.Count - 1) / 48;
                
                if (inventory.itemList.Count == 0 || rebuildPageUI)
                {
                    CuiHelper.DestroyUi(player, SkinPageUI);
                    CuiHelper.AddUi(player, skinner.AddPageUI(scpage + 1, (_cachedSkins.Count - 1) / 48 + 1));
                }

                _fillingbox = true;
                ClearCon();
                for (int i = 0; i < 48 && i < _cachedSkins.Count - (48 * scpage); i++)
                {
                    if (!setSkins.TryGetValue(_cachedSkinKeys[i + 48 * scpage], out ulong cachedSkin))
                    {
                        cachedSkin = 0ul;
                    }
                    ItemDefinition itemDefinition = ItemManager.FindItemDefinition(_cachedSkinKeys[i + 48 * scpage]);
                    InsertItem(cachedSkin, itemDefinition, 1, i);
                }
                _fillingbox = false;
                
                inventory.onItemAddedRemoved = CheckforItemSelect;
                inventory.SetFlag(ItemContainer.Flag.IsLocked, false);
                inventory.SetFlag(ItemContainer.Flag.NoItemInput, true);
                inventory.MarkDirty();
            }
            
            private void CheckforItemSelect(Item item, bool b)
            {
                if (b)
                {
                    if (_fillingbox) return;
                    if (item == returnitem) { returnitem = null; return; }
                    returnitemplayer = item;
                    GiveItem(returnitemplayer);
                    return;
                }

                if (_clearingbox || _fillingbox) return;
                if (item == returnitemplayer) { returnitemplayer = null; return; }

                SkinSelect(item);
            }

            private void CheckforSkinSelect(Item item, bool b)
            {
                if (b)
                {
                    if (_fillingbox) return;
                    if (item == returnitem) { returnitem = null; return; }
                    returnitemplayer = item;
                    GiveItem(returnitemplayer);
                    return;
                }

                if (_clearingbox || _fillingbox) return;
                if (item == returnitemplayer) { returnitemplayer = null; return; }
                
                bool isRedirect = item.info.isRedirectOf != null;
                ulong uID = GetMask(item.skin, isRedirect ? item.info.isRedirectOf.itemid : item.info.itemid, isRedirect);
                
                if (setSkins.ContainsKey(itemselected.itemid) && (setSkins[itemselected.itemid] == uID || item.skin == 0 && uID == 0))
                {
                    setSkins.Remove(itemselected.itemid);
                }
                else
                {
                    setSkins[itemselected.itemid] = uID;
                }
                
                if (item.skin != 0 || isRedirect)
                {
                    if (!skinner._playerUsageData.TryGetValue(player.userID, out PlayerData playerData))
                    {
                        skinner._playerUsageData[player.userID] = new PlayerData();
                    }
                    skinner._playerUsageData[player.userID].AddSkinUsage(item.skin, isRedirect ? item.info.isRedirectOf.itemid : item.info.itemid, isRedirect ? item.info.itemid : 0);
                }
                GetDefaultSkins();
            }

            private void SkinSelect(Item item)
            {
                rebuildPageUI = true;
                rebuildsearchUI = true;
                itemselected = item.info.isRedirectOf != null ? item.info.isRedirectOf : item.info;
                page = 0;
                scpage = 0;
                searchtxt = string.Empty;
                cachedskins = null;

                inventory.onItemAddedRemoved = CheckforSkinSelect;
                GetDeployableSkins(false);
            }
            
            #endregion Set Default Skins

            #region Skin Requests
            
            public void SkinRequests()
            {
                boxtype = "requests";
                ClearCon();
                CuiHelper.DestroyUi(player, SkinRequestsUI);
                CuiHelper.AddUi(player, skinner.AddRequestUI(requestselected));
                _fillingbox = true;
                for(int i = 0; i < skinner._requestsData.Count && i < 48; i++)
                {
                    var req = skinner._requestsData[i];
                    if (req.itemID == 0) continue;
                    InsertItem(req.skinID, ItemManager.FindItemDefinition(req.itemID), 1, i, req.itemDisplayname);
                }
                _fillingbox = false;
                inventory.onItemAddedRemoved = CheckforRequestSelect;
            }

            private void CheckforRequestSelect(Item item, bool b)
            {
                if(b) return;

                if (requestselected == "Try")
                {
                    if (player.inventory.GiveItem(item))
                    {
                        skinner.ChatMessage(player.IPlayer, "GivenTrySkin", item.skin);
                    }
                }
                else if (requestselected == "Approve")
                {
                    if (!skinner.config.ImportedSkinList.ContainsKey(item.skin))
                    {
                        skinner.config.ImportedSkinList.Add(item.skin, new ImportedItem()
                        {
                            itemShortname = item.info.shortname,
                            itemDisplayname = _skinNames[item.skin]
                        });
                        skinner.UpdateImportedSkins();
                        skinner.SaveConfig();
                    }
                }
                
                for(int i = 0; i < skinner._requestsData.Count; i++)
                {
                    if(skinner._requestsData[i].skinID == item.skin)
                    {
                        skinner._requestsData.RemoveAt(i);
                        break;
                    }
                }
                
                item.Remove();
                SkinRequests();
            }

            #endregion Skin Requests
            
            #region General

            public void NextPage()
            {
                rebuildPageUI = true;
                if (boxtype == "craftskins") scpage++; else page++;
                UpdatePage();
            }

            public void BackPage()
            {
                rebuildPageUI = true;
                if (boxtype == "craftskins") scpage--; else page--;
                UpdatePage();
            }

            private void UpdatePage()
            {
                if (boxtype == "deployableskins") GetDeployableSkins(true);
                else if (boxtype == "craftskins") GetDefaultSkins();
                else if (boxtype == "itemskins") GetSkins(true);
            }

            public void SetUpdate()
            {
                setSkins = skinner.GetCachedSkins(player, setSelect);
                if (boxtype == "craftskins") GetDefaultSkins();
            }
            
            public void SearchUpdate()
            {
                page = 0;
                scpage = 0;
                rebuildPageUI = true;
                rebuildsearchUI = true;
                if (boxtype == "deployableskins") GetDeployableSkins(false);
                else if (boxtype == "craftskins") GetDefaultSkins();
                else if (boxtype == "itemskins") GetSkins(false);
            }

            #endregion General

            #region Helpers
            private Item GiveItem(Item item)
            {
                if (item.amount > 1)
                {
                    if (player.inventory.GiveItem(item))
                        return item;
                    item.Drop(player.GetDropPosition(), player.GetDropVelocity());
                    return item;
                }
                if (player.inventory.GiveItem(item))
                    return item;

                Item newitem = ItemManager.Create(item.info, 1, item.skin);
                player.inventory.GiveItem(newitem);
                return newitem;
            }

            private Item? GetItem(ulong uID, ItemDefinition itemDefinition, int amount, string name = "")
            {
                Item item;
                if (!HasMask(uID))
                    item = ItemManager.Create(itemDefinition, amount, uID);
                else
                {
                    item = ItemManager.CreateByItemID((int)UnsetMask(uID), amount, 0);
                    if (item != null) item.skin = 0;
                }

                if (item == null) return null;
                item.text = "1";
                if (skinner.config.applySkinNames)
                {
                    if (!string.IsNullOrEmpty(name))
                        item.name = name;
                    else if (_skinNames.TryGetValue(uID, out string displayName) && !string.IsNullOrEmpty(displayName))
                        item.name = displayName;
                }
                return item;
            }

            private void InsertItem(ulong uID, ItemDefinition itemdef, int amount, int pos, string name = "")
            {
                if(itemdef == null) return;
                Item item = GetItem(uID, itemdef, amount, name);
                if (item == null) return;
                item.position = pos;
                item.parent = inventory;
                inventory.itemList.Add(item);
            }
            
            private bool IsMainItemBlacklisted(Item item) => item != null && (skinner.config.blacklistedskins.Contains(item.skin) || skinner.config.blacklisteditems.Contains(item.info.itemid));
            private bool IsMainDeployableBlacklisted(BaseCombatEntity ent) => ent != null && (skinner.config.blacklistedskins.Contains(ent.skinID));
            
            private List<Item> GetPlayerItems(int itemID)
            {
                List<Item> items = Pool.Get<List<Item>>();
                player.inventory.FindAllByItemID(itemID, items);
                return items;
            }

            public void ClearCon()
            {
                _clearingbox = true;
                for (int i = inventory.itemList.Count - 1; i >= 0; i--)
                {
                    inventory.itemList[i].Remove(0f);
                }
                inventory.itemList.Clear();
                _clearingbox = false;
            }

            private void OnDestroy()
            {
                ClearCon();
                CuiHelper.DestroyUi(player, SkinPageUI);
                CuiHelper.DestroyUi(player, SkinSearchUI);
                CuiHelper.DestroyUi(player, SkinSetsSelectUI);
                CuiHelper.DestroyUi(player, SkinRequestsUI);

                if (backpack?.contents != null)
                {
                    backpack.contents.onPreItemRemove -= PosWatch;
                }
                player.inventory.containerMain.onPreItemRemove -= PosWatch;
                player.inventory.containerWear.onPreItemRemove -= PosWatch;
                player.inventory.containerBelt.onPreItemRemove -= PosWatch;
            }
            #endregion Helpers
        }
        #endregion Box Controller

        #region SteamWorkshop WebRequests
        private IEnumerator getCollectionscouroutine;
        private IEnumerator GetCollectionSkinIDS()
        {
            if (config.skinCollectionIDs.Count > 0) Puts("Getting Workshop Collection Skins");
            string vurl = "https://steamcommunity.com/workshop/filedetails/?id={0}";
            for (int i = 0; i < config.skinCollectionIDs.Count; i++)
            {
                var collectionid = config.skinCollectionIDs[i];
                string downloadHandler;
                UnityWebRequest www = UnityWebRequest.Get(string.Format(vurl, collectionid));
                www.SetRequestHeader("Content-Type", "application/json");
                yield return www.SendWebRequest();
                
                if (www.result != UnityWebRequest.Result.Success)
                {
                    skinner.Puts($"waiting 30 seconds for {www.error}");
                    www.Dispose();
                    i--;
                    yield return new WaitForSeconds(30f);
                    continue;
                }
                downloadHandler = www.downloadHandler.text;
                string[] htmlslines = downloadHandler.Split('\n');
                foreach (string htmlline in htmlslines)
                {
                    string trimmed = htmlline.Trim();
                    if (!trimmed.StartsWith("SharedFileBindMouseHover")) continue;
                    string skinid = trimmed.Split('"')[1].Split('_')[1];
                    
                    if (ulong.TryParse(skinid, out ulong skinuL))
                    {
                        if (!config.ImportedSkinList.ContainsKey(skinuL) && !_WorkshopSkinIDCollectionList.Contains(skinid))
                            _WorkshopSkinIDCollectionList.Add(skinid);
                    }
                }
                yield return new WaitForSeconds(0.5f);
            }
            getCollectionscouroutine = null;
            if (getSteamWorkshopSkinData != null)
            {
                Puts("getSteamWorkshopSkinData already running!!");
            }
            else
            {
                getSteamWorkshopSkinData = GetSteamWorkshopSkinData();
                ServerMgr.Instance.StartCoroutine(getSteamWorkshopSkinData);
            }
        }

        private IEnumerator getSteamWorkshopSkinData;
        private IEnumerator GetSteamWorkshopSkinData()
        {
            if (_WorkshopSkinIDCollectionList.Count > 500)
                Puts($"Warning over 500 skins are waiting for import, start up may take longer \n Skins to import - {_WorkshopSkinIDCollectionList.Count}");
            string vurl = "https://steamcommunity.com/sharedfiles/filedetails/?id={0}";
            for (int i = 0; i < _WorkshopSkinIDCollectionList.Count; i++)
            {
                var workshopid = _WorkshopSkinIDCollectionList[i];
                string downloadHandler;
                UnityWebRequest www = UnityWebRequest.Get(string.Format(vurl, workshopid));
                www.SetRequestHeader("Content-Type", "application/json");
                yield return www.SendWebRequest();
                
                if (www.result != UnityWebRequest.Result.Success)
                {
                    skinner.Puts($"waiting 30 seconds for {www.error}");
                    www.Dispose();
                    i--;
                    yield return new WaitForSeconds(30f);
                    continue;
                }
                downloadHandler = www.text;
                string[] htmlslines = downloadHandler.Split('\n');

                ValueTuple<string, string>? textreturn = htmllines2shortname(htmlslines, workshopid);

                if (textreturn == null)
                {
                    yield return new WaitForSeconds(0.001f);
                    continue;
                }
                
                if (ulong.TryParse(workshopid, out ulong uworkshopid))
                {
                    config.ImportedSkinList[uworkshopid] = new ImportedItem()
                    { itemDisplayname = textreturn.Value.Item2, itemShortname = textreturn.Value.Item1 };
                }
                else
                {
                    Puts("Failed to parse workshop ID" + workshopid);
                }
                yield return new WaitForSeconds(0.001f);
            }
            getSteamWorkshopSkinData = null;
            _WorkshopSkinIDCollectionList.Clear();
            GetSkins();
        }

        private ValueTuple<string, string>? htmllines2shortname(string[] htmlslines, string workshopid)
        {
            string skinname = "";
            bool titlef = false;
            foreach (string htmlline in htmlslines)
            {
                string trimmed = htmlline.Trim();
                if (!titlef)
                {
                    if (trimmed.StartsWith("<title>"))
                    {
                        titlef = true;
                        skinname = trimmed.Split(':')[2].Split('<')[0].Trim();
                    }
                    continue;
                }

                string[] trimsplits = trimmed.Split('\"');
                if (trimsplits.Length < 6) continue;

                string skintype = string.Empty;
                string[] splitByGreater = trimmed.Split('>');

                if (trimsplits[1] == "workshopTags" || trimsplits[3] == "workshopTags")
                {
                    int startIndex = trimsplits[1] == "workshopTags" ? 6 : 4;
                    for (int index = startIndex; index < splitByGreater.Length; index += 2)
                    {
                        skintype = splitByGreater[index].Split('<')[0];
                        if (skintype != "Skin" && skintype != "Version3" && skintype != "version2")
                            break;
                    }
                }

                if (string.IsNullOrEmpty(skintype)) continue;
                
                if (!WorkshopSkinNameConversion.TryGetValue(skintype, out string shortname))
                {
                    skinner.Puts($"Cannot find item definition for id: {workshopid} type:{skintype}");
                    break;
                }

                return (shortname, skinname);
            }

            skinner.Puts($"Cannot find item definition for id: {workshopid}");

            return null;
        }

        private IEnumerator getSteamWorkshopRequestData;
        private IEnumerator GetSteamWorkshopSkinRequests()
        {
            string vurl = "https://steamcommunity.com/sharedfiles/filedetails/?id={0}";
            for (int i = 0; i < _requestsData.Count; i++)
            {
                RequestItem request = _requestsData[i];
                if (request.itemID != 0 && !string.IsNullOrEmpty(request.itemDisplayname))
                    continue;

                var workshopid = request.skinID.ToString();
                
                string downloadHandler;
                UnityWebRequest www = UnityWebRequest.Get(string.Format(vurl, workshopid));
                www.SetRequestHeader("Content-Type", "application/json");
                yield return www.SendWebRequest();
                
                if (www.isNetworkError || www.isHttpError)
                {
                    skinner.Puts($"waiting 30 seconds for {www.error}");
                    www.Dispose();
                    i--;
                    yield return CoroutineEx.waitForSeconds(30f);
                    continue;
                }
                downloadHandler = www.text;
                string[] htmlslines = downloadHandler.Split('\n');

                ValueTuple<string, string>? textreturn = htmllines2shortname(htmlslines, workshopid);
                if (textreturn == null)
                {
                    _requestsData.RemoveAt(i);
                    yield return CoroutineEx.waitForSeconds(0.001f);
                    continue;
                }
                ItemDefinition itemDef = ItemManager.FindItemDefinition(textreturn.Value.Item1);
                if (itemDef == null)
                {
                    Puts($"Cannot find item definition for id: {workshopid}");
                    _requestsData.RemoveAt(i);
                    continue;
                }

                _requestsData[i] = new RequestItem { itemID = itemDef.itemid, skinID = request.skinID, itemDisplayname = textreturn.Value.Item2 };
                _skinNames[request.skinID] = textreturn.Value.Item2;

                yield return CoroutineEx.waitForSeconds(0.001f);
            }

            getSteamWorkshopRequestData = null;
        }

        private IEnumerator notifyDiscordCoroutine;

        private IEnumerator NotifyDiscord()
        {
            int max = _discordData.Count;
            for (int i = _discordData.Count - 1; i >= 0; i--)
            {
                DiscordData discordData = _discordData[i];

                if (discordData.SkinID < 10000)
                    continue;

                string url = $"https://steamcommunity.com/sharedfiles/filedetails/?id={discordData.SkinID}";
                UnityWebRequest head = UnityWebRequest.Get(url);
                head.timeout = 5;
                yield return head.SendWebRequest();

                if (head.result != UnityWebRequest.Result.Success)
                {
                    skinner.PrintError(head.error + "Cannot get headers from:" + url);
                    head.Dispose();
                    continue;
                }
                string title = string.Empty;
                string image = string.Empty;

                foreach (var line in head.downloadHandler.text.Split('\n'))
                {
                    var trim = line.Trim();
                    if (trim.StartsWith("<meta property=\"twitter:title\""))
                    {
                        title = trim.Split(new[] { "content=" }, StringSplitOptions.None)[1].Split('"')[1];
                    }
                    if (trim.StartsWith("<meta name=\"twitter:image\""))
                    {
                        image = trim.Split(new[] { "content=" }, StringSplitOptions.None)[1].Split('"')[1];
                        break;
                    }
                }

                if (string.IsNullOrEmpty(title) || title == "Error")
                {
                    head.Dispose();
                    skinner.Puts($"Could not get content for skin {discordData.SkinID}");
                    for (int j = _requestsData.Count - 1; j > 0; j--)
                    {
                        if (_requestsData[j].skinID == discordData.SkinID)
                        {
                            _requestsData.RemoveAt(j);
                            break;
                        }
                    }
                    continue;
                }

                Root root = new Root
                {
                    tts = false,
                    content = string.Empty,
                    username = "Skinner",
                    avatar_url = "https://codefling.com/uploads/monthly_2024_07/Skinner_Thumbnail2-0.png.97a301b396dbeae7f180d1f8002e02d3.png",
                    embeds = new List<Embed>
                    {
                        new Embed
                        {
                            fields = new List<Field>
                            {
                                new Field
                                {
                                    name = "Requested By",
                                    value = $"{discordData.player.displayName} - [{discordData.player.UserIDString}](https://steamcommunity.com/id/{discordData.player.UserIDString})"
                                },
                                new Field
                                {
                                    name = "Skin",
                                    value = $"{title} - [{discordData.SkinID}](https://steamcommunity.com/sharedfiles/filedetails/?id={discordData.SkinID})"
                                }
                            },
                            author = new Author
                            {
                                name = "Skinner",
                                icon_url = "https://codefling.com/uploads/monthly_2024_07/Skinner_Thumbnail2-0.png.97a301b396dbeae7f180d1f8002e02d3.png"
                            },
                            title = $"{server.Name} - New Skin Request",
                            image = new Image { url = image },
                            timestamp = DateTime.Now,
                        }
                    }
                };

                using (UnityWebRequest sendtoDiscord = new UnityWebRequest(config.DiscordWebhook, "POST"))
                {
                    byte[] jsonToSend = new System.Text.UTF8Encoding().GetBytes(JsonConvert.SerializeObject(root));
                    sendtoDiscord.uploadHandler = new UploadHandlerRaw(jsonToSend);
                    sendtoDiscord.downloadHandler = new DownloadHandlerBuffer();
                    sendtoDiscord.SetRequestHeader("Content-Type", "application/json");

                    yield return sendtoDiscord.SendWebRequest();

                    if (sendtoDiscord.result != UnityWebRequest.Result.Success)
                    {
                        skinner.PrintError(sendtoDiscord.error + "Cannot send to discord:");
                    }
                }
                head.Dispose();
            }
            _discordData.RemoveRange(0, max);
            notifyDiscordCoroutine = null;
        }


        #region Discord
        public class Root
        {
            public string content { get; set; }
            public bool tts { get; set; }
            public List<Embed> embeds { get; set; }
            public string avatar_url { get; set; }
            public string username { get; set; }
        }
        public class Embed
        {
            public List<Field> fields { get; set; }
            public Author author { get; set; }
            public string title { get; set; }
            public Image image { get; set; }
            public DateTime timestamp { get; set; }
        }
        public class Field
        {
            public string name { get; set; }
            public string value { get; set; }
        }
        public class Author
        {
            public string name { get; set; }
            public string icon_url { get; set; }
        }
        public class Image
        {
            public string url { get; set; }
        }
        #endregion Discord

        private Dictionary<string, string> WorkshopSkinNameConversion = new Dictionary<string, string>
        {
            {"Acoustic Guitar","fun.guitar"}, {"AK47","rifle.ak"}, {"AK47 Skin","rifle.ak"},
            {"Armored Double Door", "door.double.hinged.toptier"}, {"Armored Door","door.hinged.toptier"},
            {"Large Backpack","largebackpack"}, {"Balaclava","mask.balaclava"}, {"Balaclava Skin","mask.balaclava"},
            {"Bandana","mask.bandana"}, {"Bearskin Rug", "rug.bear"}, {"Beenie Hat","hat.beenie"},
            {"Beenie Skin","hat.beenie"}, {"Bolt Rifle","rifle.bolt"}, {"BoltRifle Skin","rifle.bolt"},
            {"Bone Club","bone.club"}, {"Bone Knife","knife.bone"}, {"Boonie Skin","hat.boonie"},
            {"Boonie Hat","hat.boonie"}, {"Bucket Helmet","bucket.helmet"}, {"Burlap Headwrap","burlap.headwrap"},
            {"Burlap Pants","burlap.trousers"}, {"Burlap Shirt","burlap.shirt"}, {"Burlap Shoes","burlap.shoes"},
            {"Cap","hat.cap"}, {"Chair", "chair"}, {"Coffee Can Helmet","coffeecan.helmet"},
            {"Collared Shirt","shirt.collared"}, {"Combat Knife","knife.combat"}, {"Concrete Barricade","barricade.concrete"},
            {"Crossbow","crossbow"}, {"Custom SMG","smg.2"}, {"Deer Skull Mask","deer.skull.mask"},
            {"Double Barrel Shotgun","shotgun.double"}, {"Eoka Pistol","pistol.eoka"}, {"F1 Grenade","grenade.f1"},
            {"Furnace","furnace"}, {"Fridge", "fridge"}, {"Garage Door", "wall.frame.garagedoor"},
            {"Hammer","hammer"}, {"Hatchet","hatchet"}, {"Hide Halterneck","attire.hide.helterneck"},
            {"Hide Pants","attire.hide.pants"}, {"Hide Poncho","attire.hide.poncho"},
            {"Hide Shirt","attire.hide.vest"}, {"Hide Shoes","attire.hide.boots"},
            {"Hide Skirt","attire.hide.skirt"}, {"Hoodie","hoodie"}, {"Hunting Bow","bow.hunting"},
            {"Jacket Skin", "jacket"}, {"Jackhammer", "jackhammer"}, {"Large Wood Box","box.wooden.large"},
            {"Leather Gloves","burlap.gloves"}, {"Long TShirt","tshirt.long"}, {"Longsword","longsword"},
            {"LR300","rifle.lr300"}, {"Locker","locker"}, {"L96", "rifle.l96"},
            {"Metal Chest Plate","metal.plate.torso"}, {"Metal Facemask","metal.facemask"},
            {"Miner Hat","hat.miner"}, {"Mp5","smg.mp5"}, {"M39", "rifle.m39"},
            {"M249", "lmg.m249"}, {"Pants","pants"}, {"Pants Skin","pants"},
            {"Pick Axe","pickaxe"}, {"Pump Shotgun","shotgun.pump"}, {"Python","pistol.python"},
            {"Reactive Target","target.reactive"}, {"Revolver","pistol.revolver"}, {"Riot Helmet","riot.helmet"},
            {"Roadsign Gloves", "roadsign.gloves"}, {"Roadsign Pants","roadsign.kilt"},
            {"Roadsign Vest","roadsign.jacket"}, {"Rock","rock"}, {"Rock Skin","rock"},
            {"Rocket Launcher","rocket.launcher"}, {"Rug", "rug"}, {"Rug Bear Skin","rug.bear"},
            {"Salvaged Hammer","hammer.salvaged"}, {"Salvaged Icepick","icepick.salvaged"},
            {"Sandbag Barricade","barricade.sandbags"}, {"Satchel Charge","explosive.satchel"},
            {"Semi-Automatic Pistol","pistol.semiauto"}, {"SemiAutoPistol Skin","pistol.semiauto"},
            {"Semi-Automatic Rifle","rifle.semiauto"}, {"Sheet Metal Door","door.hinged.metal"},
            {"Sheet Metal Double Door","door.double.hinged.metal"}, {"Shorts","pants.shorts"},
            {"Sleeping Bag","sleepingbag"}, {"Snow Jacket","jacket.snow"}, {"SnowJacket Skin","jacket.snow"},
            {"Spinning Wheel", "spinner.wheel"}, {"Stone Hatchet","stonehatchet"}, {"Stone Pick Axe","stone.pickaxe"},
            {"Sword","salvaged.sword"}, {"Table", "table"}, {"Tank Top","shirt.tanktop"},
            {"Thompson","smg.thompson"}, {"TShirt","tshirt"}, {"TShirt Skin","tshirt"},
            {"Vagabond Jacket","jacket"}, {"Vending Machine","vending.machine"}, {"Water Purifier","water.purifier"},
            {"Waterpipe Shotgun","shotgun.waterpipe"}, {"Wood Storage Box","box.wooden"},
            {"WoodStorage Skin","box.wooden"}, {"Wooden Door","door.hinged.wood"},
            {"Wooden Double Door", "door.double.hinged.wood" }, {"Work Boots","shoes.boots"},
            {"Boots Skin","shoes.boots"}
        };

        #endregion SteamWorkshop WebRequests

        #region Public Helpers
        public Dictionary<int, List<ulong>> GetAllCachedSkins() => _cachedSkins;
        public bool IsRedirectID(ulong uID) => HasMask(uID);
        public int RedirectIDtoItemID(ulong uID) => (int)UnsetMask(uID);
        public List<ulong>? GetSkinsItemList(int itemid)
        {
            _cachedSkins.TryGetValue(itemid, out List<ulong>? cachedSkins);
            return cachedSkins;
        }
        #endregion Public Helpers
        
        #region Image System
        
        public string GetImage(string shortname, ulong skin = 0)
        {
            string key = skin == 0 ? shortname : skin.ToString();
            if (_imageCache.TryGetValue(key, out uint crc))
            {
                return crc.ToString();
            }
            
            if(skin != 0)
            {
                ItemDefinition itemDef = ItemManager.FindItemDefinition(shortname);
                if(itemDef != null)
                {
                    // For official skins, Rust automatically provides the icon URL
                    foreach (var skinInfo in ItemSkinDirectory.ForItem(itemDef))
                    {
                        if (skinInfo.id == (int)skin && !string.IsNullOrEmpty(skinInfo.iconUrl))
                        {
                            _imageDownloader.DownloadImage(skinInfo.iconUrl, key);
                            return GetLoadingPlaceholder();
                        }
                    }
                }
            }

            if (config.enableExternalImages)
            {
                string url = FindBestUrl(shortname);
                if (!string.IsNullOrEmpty(url))
                {
                    _imageDownloader.DownloadImage(url, key);
                    return GetLoadingPlaceholder();
                }
            }

            if (config.useBuiltInIconFallback)
            {
                var itemDef = ItemManager.FindItemDefinition(shortname);
                if (itemDef != null)
                {
                    _imageCache[key] = itemDef.iconSprite.texture.imageContentsHash;
                    return _imageCache[key].ToString();
                }
            }

            return GetLoadingPlaceholder();
        }

        private string GetLoadingPlaceholder()
        {
            // Return a known placeholder icon ID, e.g., a blank square or loading icon
            // For this example, let's use the icon for "box.wooden"
            if(_imageCache.TryGetValue("box.wooden", out var crc)) return crc.ToString();
            return "0";
        }
        
        private string FindBestUrl(string shortname)
        {
            if (!string.IsNullOrEmpty(config.primaryImageCDN))
            {
                return config.primaryImageCDN.Replace("{shortname}", shortname);
            }
            if (config.fallbackImageCDNs.Count > 0)
            {
                return config.fallbackImageCDNs[0].Replace("{shortname}", shortname);
            }
            return null;
        }

        private void PreloadLocalIcons()
        {
            Puts("Preloading item icons...");
            int count = 0;
            foreach (var itemDef in ItemManager.GetItemDefinitions())
            {
                if (itemDef == null) continue;
                string key = itemDef.shortname;
                if (!_imageCache.ContainsKey(key))
                {
                    if (config.enableExternalImages)
                    {
                        string url = FindBestUrl(key);
                        if (!string.IsNullOrEmpty(url))
                        {
                            _imageDownloader.DownloadImage(url, key);
                            count++;
                        }
                    }
                }
            }
            Puts($"Queued {count} icons for preloading.");
        }
        
        public class ImageDownloader : MonoBehaviour
        {
            public Skinner plugin;
            private Queue<KeyValuePair<string, string>> _downloadQueue = new Queue<KeyValuePair<string, string>>();
            private bool _isDownloading = false;

            public void DownloadImage(string url, string key)
            {
                if(plugin._imageCache.ContainsKey(key)) return;
                
                _downloadQueue.Enqueue(new KeyValuePair<string, string>(url, key));
                if (!_isDownloading)
                {
                    StartCoroutine(ProcessQueue());
                }
            }

            private IEnumerator ProcessQueue()
            {
                _isDownloading = true;
                while (_downloadQueue.Count > 0)
                {
                    var request = _downloadQueue.Dequeue();
                    string url = request.Key;
                    string cacheKey = request.Value;

                    using (UnityWebRequest www = UnityWebRequestTexture.GetTexture(url))
                    {
                        yield return www.SendWebRequest();

                        if (www.result == UnityWebRequest.Result.Success)
                        {
                            Texture2D texture = ((DownloadHandlerTexture)www.downloadHandler).texture;
                            if (texture != null)
                            {
                                var bytes = texture.EncodeToPNG();
                                uint crc = FileStorage.server.Store(bytes, FileStorage.Type.png, CommunityEntity.ServerInstance.net.ID);
                                if (crc != 0)
                                {
                                    plugin._imageCache[cacheKey] = crc;
                                }
                                Destroy(texture);
                            }
                        }
                        else
                        {
                            plugin.Puts($"Failed to download image from {url}: {www.error}");
                        }
                    }
                    yield return new WaitForSeconds(0.1f); // Small delay between downloads
                }
                _isDownloading = false;
            }
        }
        #endregion
        
        #region UI Creation
        
        private string AddSearchUI(string text)
        {
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                Image = { Color = "0 0 0 0" },
                RectTransform = { AnchorMin = $"{config.uisearchposition[0]}", AnchorMax = $"{config.uisearchposition[1]}", OffsetMin = $"{config.uisearchposition[2]}", OffsetMax = $"{config.uisearchposition[3]}" }
            }, "Overlay", SkinSearchUI);

            container.Add(new CuiElement
            {
                Parent = SkinSearchUI,
                Components =
                {
                    new CuiInputFieldComponent { Command = $"searchCMD", Text = text, Font = "robotocondensed-bold.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.74 0.73 0.71 1.0" },
                    new CuiRectTransformComponent { AnchorMin = "0 0", AnchorMax = "1 1" }
                }
            });

            return CuiHelper.ToJson(container);
        }

        private string AddPageUI(int page, int maxpage)
        {
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                Image = { Color = "0 0 0 0" },
                RectTransform = { AnchorMin = $"{config.uiposition[0]}", AnchorMax = $"{config.uiposition[1]}", OffsetMin = $"{config.uiposition[2]}", OffsetMax = $"{config.uiposition[3]}" }
            }, "Overlay", SkinPageUI);

            container.Add(new CuiButton
            {
                Button = { Command = "sbBackPage", Color = "0.2 0.2 0.2 1.0" },
                Text = { Text = "<", Font = "robotocondensed-bold.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.74 0.73 0.71 1.0" },
                RectTransform = { AnchorMin = "0.02 0.05", AnchorMax = "0.22 0.95" }
            }, SkinPageUI);

            container.Add(new CuiButton
            {
                Button = { Command = "sbNextPage", Color = "0.2 0.2 0.2 1.0" },
                Text = { Text = ">", Font = "robotocondensed-bold.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.74 0.73 0.71 1.0" },
                RectTransform = { AnchorMin = "0.78 0.05", AnchorMax = "0.98 0.95" }
            }, SkinPageUI);

            container.Add(new CuiLabel
            {
                Text = { Text = $"{page} / {maxpage}", Font = "robotocondensed-bold.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.74 0.73 0.71 1.0" },
                RectTransform = { AnchorMin = "0.25 0.05", AnchorMax = "0.75 0.95" }
            }, SkinPageUI);

            return CuiHelper.ToJson(container);
        }

        private string AddSetsUI(int set)
        {
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                Image = { Color = "0 0 0 0" },
                RectTransform = { AnchorMin = $"{config.uisetsposition[0]}", AnchorMax = $"{config.uisetsposition[1]}", OffsetMin = $"{config.uisetsposition[2]}", OffsetMax = $"{config.uisetsposition[3]}" }
            }, "Overlay", SkinSetsSelectUI);

            for (int i = 1; i <= 3; i++)
            {
                container.Add(new CuiButton
                {
                    Button = { Command = $"setSelectCMD {i}", Color = set == i ? "0.4 0.4 0.4 1.0" : "0.2 0.2 0.2 1.0" },
                    Text = { Text = $"Set {i}", Font = "robotocondensed-bold.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.74 0.73 0.71 1.0" },
                    RectTransform = { AnchorMin = $"{0.02f + (i - 1) * 0.33f} 0.05", AnchorMax = $"{0.32f + (i - 1) * 0.33f} 0.95" }
                }, SkinSetsSelectUI);
            }
            return CuiHelper.ToJson(container);
        }

        private string AddRequestUI(string type)
        {
            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                Image = { Color = "0 0 0 0" },
                RectTransform = { AnchorMin = $"{config.uisetsposition[0]}", AnchorMax = $"{config.uisetsposition[1]}", OffsetMin = $"{config.uisetsposition[2]}", OffsetMax = $"{config.uisetsposition[3]}" }
            }, "Overlay", SkinRequestsUI);
            
            var types = new[] { "Try", "Approve", "Deny" };
            for (int i = 0; i < types.Length; i++)
            {
                container.Add(new CuiButton
                {
                    Button = { Command = $"requestSelectCMD {types[i]}", Color = type == types[i] ? "0.4 0.4 0.4 1.0" : "0.2 0.2 0.2 1.0" },
                    Text = { Text = types[i], Font = "robotocondensed-bold.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.74 0.73 0.71 1.0" },
                    RectTransform = { AnchorMin = $"{0.02f + i * 0.33f} 0.05", AnchorMax = $"{0.32f + i * 0.33f} 0.95" }
                }, SkinRequestsUI);
            }
            return CuiHelper.ToJson(container);
        }

        // The entire Awaken Skinbox UI creation logic would go here.
        // This is a very large piece of code, I'll provide a functional version based on the commands.
        private string CreateAwakenSkinboxUI(BasePlayer player, AwakenSkinboxState state)
        {
            var container = new CuiElementContainer();

            // Main Background
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.98" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-450 -300", OffsetMax = "450 300" },
                CursorEnabled = true
            }, "Overlay", AWAKEN_SKINBOX_MAIN);

            // Header
            container.Add(new CuiLabel
            {
                Text = { Text = "SKINNER", FontSize = 24, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0 0.9", AnchorMax = "1 1" }
            }, AWAKEN_SKINBOX_MAIN);
            
            // Close button
            container.Add(new CuiButton
            {
                Button = { Command = "awaken.skinbox.close", Color = "0.8 0.2 0.2 1" },
                Text = { Text = "X", FontSize = 14, Align = TextAnchor.MiddleCenter },
                RectTransform = { AnchorMin = "0.95 0.92", AnchorMax = "0.99 0.98" }
            }, AWAKEN_SKINBOX_MAIN, "CloseButton");

            // --- Left Panel (Player Items) ---
            string leftPanel = container.Add(new CuiPanel
            {
                Image = { Color = "0.15 0.15 0.15 1" },
                RectTransform = { AnchorMin = "0.02 0.1", AnchorMax = "0.22 0.9" }
            }, AWAKEN_SKINBOX_MAIN);
            
            // Category Buttons
            var categories = new List<string> { "All", "Weapon", "Construction", "Items", "Attire", "Misc" };
            for(int i = 0; i < categories.Count; i++)
            {
                bool isSelected = state.SelectedCategory == categories[i];
                container.Add(new CuiButton
                {
                    Button = { Command = $"awaken.skinbox.category {categories[i]}", Color = isSelected ? "0.3 0.5 0.8 1" : "0.2 0.2 0.2 1" },
                    Text = { Text = categories[i].ToUpper(), FontSize = 12, Align = TextAnchor.MiddleCenter },
                    RectTransform = { AnchorMin = $"{i * (1f/categories.Count)} 0.92", AnchorMax = $"{(i+1) * (1f/categories.Count)} 1" }
                }, leftPanel);
            }
            
            // Player Items Grid
            string itemsGrid = container.Add(new CuiPanel {
                Image = { Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.05 0.05", AnchorMax = "0.95 0.9" }
            }, leftPanel);

            var playerItems = GetPlayerItemsByCategory(player, state.SelectedCategory);
            int itemsPerPage = 20; // Example
            int itemIndex = 0;
            for(int y = 0; y < 5; y++)
            {
                for(int x = 0; x < 4; x++)
                {
                    if(itemIndex >= playerItems.Count) break;
                    var item = playerItems[itemIndex];
                    string posMin = $"{x * 0.25f} {1 - (y + 1) * 0.2f}";
                    string posMax = $"{(x + 1) * 0.25f} {1 - y * 0.2f}";

                    bool isItemSelected = state.SelectedItemUID == item.uid.Value;

                    container.Add(new CuiPanel {
                        Image = { Color = isItemSelected ? "0.8 0.8 0.2 0.3" : "0.2 0.2 0.2 0.5" },
                        RectTransform = { AnchorMin = posMin, AnchorMax = posMax, OffsetMin = "2 2", OffsetMax = "-2 -2" }
                    }, itemsGrid, $"ItemSlot_{item.uid}");

                    container.Add(new CuiButton {
                        Button = { Command = $"awaken.skin.selectitem {item.uid}", Color = "0 0 0 0" },
                        Text = { Text = "" },
                        RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1"}
                    }, $"ItemSlot_{item.uid}");

                    string itemIcon = GetImage(item.info.shortname, item.skin);
                    if(itemIcon != "0")
                    {
                        container.Add(new CuiRawImageComponent { Png = itemIcon }, $"ItemSlot_{item.uid}");
                    }
                    
                    itemIndex++;
                }
                if(itemIndex >= playerItems.Count) break;
            }


            // --- Right Panel (Skins) ---
            string rightPanel = container.Add(new CuiPanel
            {
                Image = { Color = "0.15 0.15 0.15 1" },
                RectTransform = { AnchorMin = "0.24 0.1", AnchorMax = "0.98 0.9" }
            }, AWAKEN_SKINBOX_MAIN);
            
            // Skin Search
            container.Add(new CuiInputFieldComponent {
                Command = $"awaken.skinbox.search", Text = string.IsNullOrEmpty(state.SearchTerm) ? "Search Skins..." : state.SearchTerm,
                FontSize = 14, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1"
            }, rightPanel, "SearchInput");
            container.Add(new CuiRectTransformComponent { AnchorMin = "0.02 0.92", AnchorMax = "0.6 0.98" }, "SearchInput");


            // Skin Grid
            var itemToSkin = player.inventory.FindItemByUID(new ItemId(state.SelectedItemUID));
            if (itemToSkin != null)
            {
                string skinGrid = container.Add(new CuiPanel
                {
                    Image = { Color = "0 0 0 0" },
                    RectTransform = { AnchorMin = "0.02 0.15", AnchorMax = "0.98 0.9" }
                }, rightPanel);

                var availableSkins = GetSkinsForItem(itemToSkin.info.itemid, state.SearchTerm);
                int skinsPerPage = 48; // 8x6 grid
                int startIndex = state.CurrentPage * skinsPerPage;
                int skinIndex = 0;
                
                // Add default skin button
                container.Add(new CuiPanel {
                    Image = { Color = itemToSkin.skin == 0 ? "0.8 0.2 0.2 0.5" : "0.2 0.2 0.2 0.5" },
                    RectTransform = { AnchorMin = "0 0.8333", AnchorMax = "0.125 1", OffsetMin = "2 2", OffsetMax = "-2 -2" }
                }, skinGrid, "SkinSlot_0");
                container.Add(new CuiButton {
                    Button = { Command = $"awaken.skinbox.select 0", Color = "0 0 0 0" },
                    Text = { Text = "" },
                    RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1"}
                }, "SkinSlot_0");
                string defaultIcon = GetImage(itemToSkin.info.shortname);
                if(defaultIcon != "0") container.Add(new CuiRawImageComponent { Png = defaultIcon }, "SkinSlot_0");


                for (int i = startIndex; i < availableSkins.Count && skinIndex < skinsPerPage; i++)
                {
                    var skinId = availableSkins[i];
                    int x = (skinIndex + 1) % 8;
                    int y = 5 - ((skinIndex + 1) / 8);
                    
                    string posMin = $"{x * 0.125f} {y * (1f/6f)}";
                    string posMax = $"{(x + 1) * 0.125f} {(y + 1) * (1f/6f)}";

                    bool isSkinSelected = itemToSkin.skin == skinId;
                    container.Add(new CuiPanel {
                        Image = { Color = isSkinSelected ? "0.2 0.8 0.2 0.5" : "0.2 0.2 0.2 0.5" },
                        RectTransform = { AnchorMin = posMin, AnchorMax = posMax, OffsetMin = "2 2", OffsetMax = "-2 -2" }
                    }, skinGrid, $"SkinSlot_{skinId}");
                    
                    container.Add(new CuiButton {
                        Button = { Command = $"awaken.skinbox.select {skinId}", Color = "0 0 0 0" },
                        Text = { Text = "" },
                        RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1"}
                    }, $"SkinSlot_{skinId}");
                    
                    string skinIcon = GetImage(itemToSkin.info.shortname, skinId);
                    if(skinIcon != "0") container.Add(new CuiRawImageComponent { Png = skinIcon }, $"SkinSlot_{skinId}");
                    
                    skinIndex++;
                }

                // Page buttons for skins
                int maxPages = (availableSkins.Count -1) / skinsPerPage + 1;
                if(maxPages > 1)
                {
                    container.Add(new CuiButton { Button = { Command = "awaken.skinbox.page.prev", Color = "0.2 0.2 0.2 1" }, Text = { Text = "<" }, RectTransform = { AnchorMin = "0.65 0.92", AnchorMax = "0.75 0.98" }}, rightPanel);
                    container.Add(new CuiLabel { Text = { Text = $"{state.CurrentPage+1}/{maxPages}", Align=TextAnchor.MiddleCenter}, RectTransform = { AnchorMin = "0.75 0.92", AnchorMax = "0.85 0.98" }}, rightPanel);
                    container.Add(new CuiButton { Button = { Command = "awaken.skinbox.page.next", Color = "0.2 0.2 0.2 1" }, Text = { Text = ">" }, RectTransform = { AnchorMin = "0.85 0.92", AnchorMax = "0.95 0.98" }}, rightPanel);
                }
            }

            // --- Bottom Panel (Actions) ---
            string bottomPanel = container.Add(new CuiPanel {
                Image = { Color = "0.15 0.15 0.15 1" },
                RectTransform = { AnchorMin = "0.24 0.02", AnchorMax = "0.98 0.08" }
            }, AWAKEN_SKINBOX_MAIN);

            // Skin Sets
            int currentSet = GetPlayerSelectedSet(player);
            for(int i = 1; i <= 3; i++)
            {
                bool isSetSelected = i == currentSet;
                container.Add(new CuiButton {
                    Button = { Command = $"awaken.skinbox.set.{i}", Color = isSetSelected ? "0.3 0.5 0.8 1" : "0.2 0.2 0.2 1" },
                    Text = { Text = $"SET {i}", FontSize = 12},
                    RectTransform = { AnchorMin = $"{0.02f + (i-1)*0.11f} 0.1", AnchorMax = $"{0.12f + (i-1)*0.11f} 0.9" }
                }, bottomPanel);
            }
            
            // Action Buttons
            container.Add(new CuiButton { Button = { Command = "awaken.skinbox.apply.selected", Color = "0.3 0.6 0.3 1" }, Text = { Text = "SAVE TO SET", FontSize = 12}, RectTransform = { AnchorMin = "0.4 0.1", AnchorMax = "0.59 0.9" }}, bottomPanel);
            container.Add(new CuiButton { Button = { Command = "awaken.skinbox.apply.all", Color = "0.3 0.6 0.3 1" }, Text = { Text = "APPLY SET", FontSize = 12}, RectTransform = { AnchorMin = "0.6 0.1", AnchorMax = "0.79 0.9" }}, bottomPanel);
            container.Add(new CuiButton { Button = { Command = "awaken.skinbox.apply.inventory", Color = "0.3 0.6 0.3 1" }, Text = { Text = "SKIN INVENTORY", FontSize = 12}, RectTransform = { AnchorMin = "0.8 0.1", AnchorMax = "0.99 0.9" }}, bottomPanel);


            return CuiHelper.ToJson(container);
        }

        private List<Item> GetPlayerItemsByCategory(BasePlayer player, string categoryName)
        {
            var items = new List<Item>();
            player.inventory.GetAllItems(items);
            if (categoryName == "All") return items.FindAll(i => _cachedSkins.ContainsKey(i.info.itemid) && !i.IsBlueprint());

            ItemCategory category;
            if (!Enum.TryParse<ItemCategory>(categoryName, true, out category))
            {
                return new List<Item>();
            }

            return items.FindAll(i => i.info.category == category && _cachedSkins.ContainsKey(i.info.itemid) && !i.IsBlueprint());
        }

        private List<ulong> GetSkinsForItem(int itemID, string searchTerm)
        {
            if (!_cachedSkins.TryGetValue(itemID, out var allSkins))
            {
                return new List<ulong>();
            }

            if(string.IsNullOrEmpty(searchTerm))
            {
                return allSkins.FindAll(s => s != 0); // Exclude the "no skin" option from the list itself
            }

            var filteredSkins = new List<ulong>();
            foreach(var skinId in allSkins)
            {
                if(skinId == 0) continue;
                if (_skinNames.TryGetValue(skinId, out var skinName) && skinName.ToLower().Contains(searchTerm.ToLower()))
                {
                    filteredSkins.Add(skinId);
                }
                else if (skinId.ToString().Contains(searchTerm))
                {
                    filteredSkins.Add(skinId);
                }
            }
            return filteredSkins;
        }

        #endregion

        #region Helper Methods

        private bool HasPerm(string userID, string permission)
        {
            return this.permission.UserHasPermission(userID, permission);
        }

        private void ChatMessage(IPlayer player, string key, params object[] args)
        {
            string message = lang.GetMessage(key, this, player.Id);
            if (args.Length > 0)
            {
                message = string.Format(message, args);
            }
            player.Reply(message);
        }

        #endregion
    }
}