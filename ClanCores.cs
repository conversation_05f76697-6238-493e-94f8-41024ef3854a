using Oxide.Game.Rust.Cui;
using System;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Oxide.Core;
using System.IO;
using Newtonsoft.Json;
using Oxide.Core.Plugins;
using System.Collections;
using System.Reflection;
using Network;
using System.Text;
using UnityEngine.UI;
using Newtonsoft.Json.Linq;

namespace Oxide.Plugins
{
    [Info("ClanCores", "Amino // Skelee", "2.1.3")]
    [Description("Clan Cores")]
    public class ClanCores : RustPlugin
    {
        [PluginReference] private Plugin OasisClans, ImageLibrary;

        #region [ CONFIG ]
        public class PointsConfig
        {
            [JsonProperty(PropertyName = "Commands")]
            public List<string> Commands { get; set; } = new List<string>();
            [JsonProperty(PropertyName = "Role the last wipe winners get")]
            public string WinnerGroup { get; set; } = "CoreRank";
            [JsonProperty(PropertyName = "Role the current leaders get (empty = disabled)")]
            public string CurrentGroup { get; set; } = "CurrentLeader";
            [JsonProperty(PropertyName = "Lose points while no clan TC is set")]
            public bool LosePointsWithNoTC { get; set; } = true;
            [JsonProperty(PropertyName = "Force all clans into ClanCores")]
            public bool ForceIntoCores { get; set; } = false;
            [JsonProperty(PropertyName = "Send patrol and heli spawned messages")]
            public bool SendSpawnedMessages { get; set; } = true;
            [JsonProperty(PropertyName = "Heli and APC option (true = last hit is killer | false = most damage is killer)")]
            public bool Killer { get; set; } = true;
            [JsonProperty(PropertyName = "Points Gained For Actions")]
            public PointsGain PointsGainClass { get; set; } = new PointsGain();
            [JsonProperty(PropertyName = "Points Lost For Actions")]
            public PointsLoss PointsLossClass { get; set; } = new PointsLoss();
            [JsonProperty(PropertyName = "Points Needed To Show TC Location")]
            public int PointsNeededLoco { get; set; } = 1000;
            [JsonProperty(PropertyName = "Show TC Location")]
            public bool ShowTCLoco { get; set; } = false;
            [JsonProperty(PropertyName = "Send global chat message on clan raided")]
            public bool ClanRaidedGlobalMessage { get; set; } = true;
            [JsonProperty(PropertyName = "TC UI Position OffsetMin")]
            public string OffsetMin = "235 620";
            [JsonProperty(PropertyName = "TC UI Position OffsetMax")]
            public string OffsetMax = "435 643";
            [JsonProperty(PropertyName = "UI Images")]
            public Dictionary<string, string> UIImages { get; set; } = new Dictionary<string, string>();
            public UIElements UIColors { get; set; } = new UIElements();
            public static PointsConfig DefaultConfig()
            {
                return new PointsConfig
                {
                    Commands = new List<string>() { "points", "cores" },
                    UIImages = {
                        { "UIBanner", "https://i.ibb.co/zPYpwzf/CoresBnr.png" },
                        { "UIBigBanner", "https://i.ibb.co/GQcsRn7L/clancores-banner-oasis.png" },
                        { "UIEmptyBanner", "https://i.ibb.co/QD8mTfG/Empty-Cores-Banner.png" },
                        { "UICrownIcon", "https://i.ibb.co/qsC2LPy/Crown-Icon.png" }
                    },
                    PointsGainClass = new PointsGain {
                        KillPoints = 1,
                        HeadshotPoints = .5,
                        EventPoints = 200,
                        RoamEventPoints = 250,
                        MazeEventPoints = 300,
                        ControlEventPoints = 275,
                        SulfurEventPoints = 225,
                        KOTHEventPoints = 200
                    },
                    PointsLossClass = new PointsLoss { DeathPointsLoss = 1, TCPointsLoss = 50, TimesPointsLossMinutes = 1, TimedPointsLoss = 10, TimesPointsLossPeriod = 480 }
                };
            }
        }

        public class UIElements
        {
            public string BlurBackgroundColor { get; set; } = "0 0 0 .4";
            public string MainPanelColor { get; set; } = "0 0 0 .5";
            public string TitlePanelColor { get; set; } = "0 0 0 0";
            public string BottomPanelColor { get; set; } = "0 0 0 0";
            public string FirstPlaceColor { get; set; } = "1 0.78 0 .6";
            public string SecondPlaceColor { get; set; } = "0.81 0.81 0.81 .6";
            public string ThirdPlaceColor { get; set; } = "0.64 0.31 0 .6";
            public string FourthPlaceColor { get; set; } = "0.19 0.19 0.19 .6";
            public string FifthPlaceColor { get; set; } = "0.19 0.19 0.19 .6";

        }

        public class PointsGain
        {
            [JsonProperty(PropertyName = "Points Per Kill")]
            public double KillPoints { get; set; }
            [JsonProperty(PropertyName = "Points Per Locked Crate")]
            public double LoackedCratesPoints { get; set; }
            [JsonProperty(PropertyName = "Extra Points For Headshot Kill")]
            public double HeadshotPoints { get; set; }
            [JsonProperty(PropertyName = "Event Won (Legacy - use specific event points below)")]
            public double EventPoints { get; set; }
            [JsonProperty(PropertyName = "Points for killing heli")]
            public double HeliPoints { get; set; }
            [JsonProperty(PropertyName = "Points for killing brad")]
            public double BradPoints { get; set; }
            [JsonProperty(PropertyName = "Points for winning Roam Event")]
            public double RoamEventPoints { get; set; }
            [JsonProperty(PropertyName = "Points for winning Maze Event")]
            public double MazeEventPoints { get; set; }
            [JsonProperty(PropertyName = "Points for winning Control Event")]
            public double ControlEventPoints { get; set; }
            [JsonProperty(PropertyName = "Points for winning Sulfur Event")]
            public double SulfurEventPoints { get; set; }
            [JsonProperty(PropertyName = "Points for winning KOTH Event")]
            public double KOTHEventPoints { get; set; }
        }

        public class PointsLoss
        {
            [JsonProperty(PropertyName = "Points Lost Per Death")]
            public double DeathPointsLoss { get; set; }
            [JsonProperty(PropertyName = "Points Lost When TC Raided (Percent) (Gives this to the raiding clan)")]
            public double TCPointsLoss { get; set; }
            [JsonProperty(PropertyName = "Points Lost When TC Raided OFFLINE (Percent) (Gives this to the raiding clan)")]
            public double TCPointsLossOffline { get; set; }
            [JsonProperty(PropertyName = "Lose Points Every X Minutes With No TC After Grace Period (Minutes) (AKA Point decay)")]
            public double TimesPointsLossMinutes { get; set; }
            [JsonProperty(PropertyName = "Points Lost Per X Time With Raided TC (Percent)")]
            public double TimedPointsLoss { get; set; }
            [JsonProperty(PropertyName = "Grace Period Before Point Decay After Raided (Minutes)")]
            public long TimesPointsLossPeriod { get; set; }
        }

        private static PointsConfig _config;
        private UIElements _uiColors = new UIElements();
        protected override void LoadDefaultConfig() => _config = PointsConfig.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(_config);
        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                _config = Config.ReadObject<PointsConfig>();
                if (_config == null) LoadDefaultConfig();
                _uiColors = _config.UIColors;
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }
        #endregion

        #region [ DATA ]
        public Dictionary<ulong, int> _pages = new Dictionary<ulong, int>();
        public Dictionary<ulong, DateTime> _cooldowns = new Dictionary<ulong, DateTime>();
        public List<ClanPoints> _staticClanList = new List<ClanPoints>();
        public Dictionary<ulong, BD> _bulletData = new Dictionary<ulong, BD>();
        public bool usingWC = false;
        public ActionInfo _actionInfo = new ActionInfo();
        public ClanPoints _wipeData = new ClanPoints();
        public string LastLeader = string.Empty;
        public bool isWipe = false;

        public class BD
        {
            public int Headshots { get; set; } = 0;
            public int Bullets { get; set; } = 0;
        }

        public class ActionInfo
        {
            public List<HackableLockedCrate> _hackedCrates = new List<HackableLockedCrate>();
            public Dictionary<PatrolHelicopter, EntityInfo> _patrolDamage = new Dictionary<PatrolHelicopter, EntityInfo>();
            public Dictionary<BradleyAPC, EntityInfo> _apcDamage = new Dictionary<BradleyAPC, EntityInfo>();
        }

        public class DamageData
        {
            public string ClanName { get; set; }
            public int BulletsFired { get; set; }
            public int Headshots { get; set; }
            public List<ulong> ClanMembers { get; set; } = new List<ulong>();
        }

        public class EntityInfo
        {
            public Dictionary<BasePlayer, double> PlayerInfo { get; set; } = new Dictionary<BasePlayer, double>();
            public ulong LastHitter { get; set; }
            public bool Dead { get; set; }
            public double LastHealth { get; set; }
        }

        public class UIInfo
        {
            public string Min = string.Empty;
            public string Max = string.Empty;
            public string Color = string.Empty;
            public int TextSize = 25;
            public string BoxColor = string.Empty;
        }

        public class ClanPoints
        {
            public string Name { get; set; } = string.Empty;
            public double Points { get; set; } = 0;
            public double TotalPointsLost { get; set; } = 0;
            public int Kills { get; set; } = 0;
            public int Deaths { get; set; } = 0;
            public int HeliKills { get; set; } = 0;
            public int BradKills { get; set; } = 0;
            public int BulletsFired { get; set; }
            public int Headshots { get; set; }
            public int LockedCrates { get; set; } = 0;
            public int TookTC { get; set; } = 0;
            public int LostTC { get; set; } = 0;
            public int EventWins { get; set; } = 0;
            public long LastTimePointsLost { get; set; } = 0;
            public Dictionary<ulong, string> Members { get; set; } = new Dictionary<ulong, string>();
            public Dictionary<ulong, string> PastMembers { get; set; } = new Dictionary<ulong, string>();
            public ulong TC { get; set; } = 0;
            public string TCLoco { get; set; } = String.Empty;

            public ClanPoints Clone()
            {
                return new ClanPoints
                {
                    Name = this.Name,
                    Points = this.Points,
                    TotalPointsLost = this.TotalPointsLost,
                    Kills = this.Kills,
                    Deaths = this.Deaths,
                    HeliKills = this.HeliKills,
                    BradKills = this.BradKills,
                    BulletsFired = this.BulletsFired,
                    Headshots = this.Headshots,
                    LockedCrates = this.LockedCrates,
                    TookTC = this.TookTC,
                    LostTC = this.LostTC,
                    EventWins = this.EventWins,
                    LastTimePointsLost = this.LastTimePointsLost,
                    Members = new Dictionary<ulong, string>(this.Members),
                    PastMembers = new Dictionary<ulong, string>(this.PastMembers),
                    TC = this.TC,
                    TCLoco = this.TCLoco
                };
            }
        }
        #endregion

        #region [ LANG ]
        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["HeadshotKill"] = "<color=#e573ff>[ CLAN CORES ]</color> You gained <color=#e573ff>{0}</color> points for getting a headshot kill!",
                ["RegularKill"] = "<color=#e573ff>[ CLAN CORES ]</color> You gained <color=#e573ff>{0}</color> points for getting a kill!",
                ["Raided"] = "<color=#e573ff>[ CLAN CORES ]</color> You gained <color=#e573ff>{0}</color> points for raiding <color=#e573ff>{1}</color> clan!",
                ["LockedCrate"] = "<color=#e573ff>[ CLAN CORES ]</color> You gained <color=#e573ff>{0}</color> points for being the first to loot this locked crate!",
                ["HeliKill"] = "<color=#e573ff>[ CLAN CORES ]</color> You gained <color=#e573ff>{0}</color> points for dealing the most damage to heli!",
                ["BradKill"] = "<color=#e573ff>[ CLAN CORES ]</color> You gained <color=#e573ff>{0}</color> points for dealing the most damage to brad!",
                ["RoamEventWin"] = "<color=#e573ff>[ CLAN CORES ]</color> Your clan won the Roam Event and gained <color=#e573ff>{0}</color> points!",
                ["MazeEventWin"] = "<color=#e573ff>[ CLAN CORES ]</color> Your clan won the Maze Event and gained <color=#e573ff>{0}</color> points!",
                ["ControlEventWin"] = "<color=#e573ff>[ CLAN CORES ]</color> Your clan won the Control Event and gained <color=#e573ff>{0}</color> points!",
                ["SulfurEventWin"] = "<color=#e573ff>[ CLAN CORES ]</color> Your clan won the Sulfur Event and gained <color=#e573ff>{0}</color> points!",
                ["KOTHEventWin"] = "<color=#e573ff>[ CLAN CORES ]</color> Your clan won the KOTH Event and gained <color=#e573ff>{0}</color> points!",
                ["HeliSpawned"] = "<color=#e573ff>[ CLAN CORES ]</color> Patrol helicopter spawned! Kill it for <color=#e573ff>{0}</color> for clan points!",
                ["BradSpawned"] = "<color=#e573ff>[ CLAN CORES ]</color> Brad spawned! Kill it for <color=#e573ff>{0}</color> for clan points!",
                ["TCRemoved"] = "<color=#e573ff>[ CLAN CORES ]</color> Your TC has been removed and you've lost <color=#e573ff>{0}</color> points!",
                ["TCRaided"] = "<color=#e573ff>[ CLAN CORES ]</color> Your TC has been raided and you've lost <color=#e573ff>{0}</color> points!",
                ["NotValid"] = "<color=#e573ff>[ CLAN CORES ]</color> You just missed out on points! Create your clan and set your clan TC to gain points!",
                ["NotTCOwner"] = "NOT TC OWNER",
                ["SetClanTC"] = "SET CLAN TC",
                ["ThisIsClanTC"] = "THIS IS CLAN TC",
                ["TCAlreadySet"] = "TC ALREADY SET",
                ["CoreTC"] = "THIS IS CORE TC",
                ["NoClan"] = "No clan...",
                ["LabelPosition"] = "#",
                ["LabelClan"] = "CLAN",
                ["LabelPoints"] = "POINTS",
                ["LabelKills"] = "KILLS",
                ["LabelDeaths"] = "DEATHS",
                ["LabelKDR"] = "KDR",
                ["ClanLocation"] = "Clan base: {0}",
                ["NoTC"] = "No Clan TC",
                ["StatsRank"] = "Rank: #{0}",
                ["StatsPoints"] = "Points: {0}",
                ["StatsPointsLost"] = "Points Lost: {0}",
                ["StatsRaids"] = "Raids: {0}",
                ["StatsRaided"] = "Raided: {0}",
                ["StatsHelis"] = "Raided: {0}",
                ["StatsKills"] = "Kills: {0}",
                ["StatsDeaths"] = "Deaths: {0}",
                ["StatsKDR"] = "KDR: {0}",
                ["StatsShots"] = "Shots Fired: {0}",
                ["StatsHeadshots"] = "Headshots: {0}",
                ["StatsHSAccuracy"] = "HS Accuracy: {0}%",
                ["NoClanData"] = "NO CLAN DATA",
                ["GlobalRaid"] = "<color=#e573ff>[ CLAN CORES ]</color>: <color=#e573ff>{0}</color> has raided <color=#e573ff>{1}</color> clan for <color=#e573ff>{2}</color> points!"
            }, this, "en");
        }

        private string Lang(string key, string id = null, params object[] args) => string.Format(lang.GetMessage(key, this, id), args);
        #endregion

        #region [ COMMANDS ]
        [ChatCommand("removepoints")]
        private void CMDRemovePoints(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;

            if (!permission.UserHasPermission(player.UserIDString, "clancores.admin"))
            {
                SendReply(player, "You need 'clancores.admin' permission to use this command");
                return;
            }

            if (args.Length == 0)
            {
                SendReply(player, $"You didn't provide me a clan name to remove points from");
                return;
            }

            if (args.Length == 1)
            {
                SendReply(player, $"You didn't provide me an amount of points to remove from {args[0]} clan");
                return;
            }

            ClanPoints clanInfo = GetClan(args[0]);
            if (clanInfo == null)
            {
                SendReply(player, $"Could not find a clan with the name {args[0]}");
                return;
            }

            if (!int.TryParse(args[1], out int points))
            {
                SendReply(player, $"{args[1]} is not a valid int");
                return;
            }

            clanInfo.Points -= points;
            SendReply(player, $"Successfully removed {args[1]} points from {args[0]} clan!");
        }

        [ChatCommand("givepoints")]
        private void CMDGivePoints(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;

            if (!permission.UserHasPermission(player.UserIDString, "clancores.admin"))
            {
                SendReply(player, "You need 'clancores.admin' permission to use this command");
                return;
            }

            if (args.Length == 0)
            {
                SendReply(player, $"You didn't provide me a clan name to give points to");
                return;
            }

            if (args.Length == 1)
            {
                SendReply(player, $"You didn't provide me an amount of points to give {args[0]} clan");
                return;
            }

            ClanPoints clanInfo = GetClan(args[0]);
            if (clanInfo == null)
            {
                SendReply(player, $"Could not find a clan with the name {args[0]}");
                return;
            }

            if (!int.TryParse(args[1], out int points))
            {
                SendReply(player, $"{args[1]} is not a valid int");
                return;
            }

            clanInfo.Points += points;
            SendReply(player, $"Successfully gave {args[0]} clan {args[1]} points!");
        }

        private void CMDOpenClanUI(BasePlayer player, string command, string[] args)
        {
            if (player == null) return;
            MainUI(player);
        }

        [ConsoleCommand("clancores.scan")]
        void ScanClans(ConsoleSystem.Arg arg)
        {
            if (arg.Connection != null && arg.Connection.authLevel < 2) return;

            PrintToConsole("=== Manual Clan Scan ===");
            CheckAllClans();
            PrintToConsole($"Scan complete. Total clans: {_staticClanList.Count}");

            foreach (var clan in _staticClanList)
            {
                PrintToConsole($"- {clan.Name}: {clan.Members.Count} members, {clan.Points} points");
            }
        }

        [ConsoleCommand("clancores.debug")]
        private void CMDDebugClan(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null || !player.IsAdmin) return;

            PrintToConsole(player, "=== ClanCores Debug Info ===");

            // Test OasisClans connection
            if (OasisClans == null)
            {
                PrintToConsole(player, "ERROR: OasisClans plugin not found!");
                return;
            }

            PrintToConsole(player, "✅ OasisClans plugin found");

            // Test all clan detection methods
            PrintToConsole(player, "=== Testing Clan Detection Methods ===");

            // Method 1: GetClanTag
            var clanTag = OasisClans.Call("GetClanTag", player.userID);
            PrintToConsole(player, $"1. GetClanTag: {(string.IsNullOrEmpty(clanTag?.ToString()) ? "❌ NULL/Empty" : $"✅ '{clanTag}'")}");

            // Method 2: Display name parsing
            string parsedTag = null;
            if (!string.IsNullOrEmpty(player.displayName) && player.displayName.StartsWith("[") && player.displayName.Contains("]"))
            {
                int endIndex = player.displayName.IndexOf("]");
                if (endIndex > 1)
                {
                    parsedTag = player.displayName.Substring(1, endIndex - 1);
                }
            }
            PrintToConsole(player, $"2. Display name parsing: {(string.IsNullOrEmpty(parsedTag) ? "❌ No tag found" : $"✅ '{parsedTag}'")}");

            // Method 3: GetClan API
            var clanObj = OasisClans.Call("GetClan", player.userID);
            PrintToConsole(player, $"3. GetClan API: {(clanObj == null ? "❌ NULL" : "✅ Object found")}");

            // Final result from GetClanName
            string finalClanName = GetClanName(player);
            PrintToConsole(player, $"Final GetClanName result: {(string.IsNullOrEmpty(finalClanName) ? "❌ NULL" : $"✅ '{finalClanName}'")}");

            // Test clan lookup in ClanCores
            if (!string.IsNullOrEmpty(finalClanName))
            {
                var clanInfo = GetClan(finalClanName);
                PrintToConsole(player, $"ClanPoints lookup: {(clanInfo != null ? $"✅ Found (Points: {clanInfo.Points}, Members: {clanInfo.Members.Count})" : "❌ Not found")}");
            }

            // Test GetAllClans
            var allClans = OasisClans.Call("GetAllClans");
            if (allClans is List<object> clanList)
            {
                PrintToConsole(player, $"GetAllClans: ✅ Found {clanList.Count} clans");
            }
            else
            {
                PrintToConsole(player, $"GetAllClans: ❌ {(allClans == null ? "NULL" : "Invalid type")}");
            }

            PrintToConsole(player, $"Total ClanCores clans: {_staticClanList.Count}");
        }

        [ConsoleCommand("cl_main")]
        private void CMDCLMain(ConsoleSystem.Arg arg)
        {
            var player = arg.Player();
            if (player == null) return;

            if (!CheckCooldown(player.userID)) return;

            switch (arg.Args[0])
            {
                case "close":
                    CuiHelper.DestroyUi(player, "CLMainPanel");
                    break;
                case "page":
                    if (arg.Args[1] == "add") _pages[player.userID] += 1;
                    else _pages[player.userID] -= 1;

                    UIAllStatsInd(player);
                    UpdatePageLabel(player);
                    break;
                case "settc":
                    SetClanTC(player, arg.Args);
                    break;
                case "closelastwipe":
                    CuiHelper.DestroyUi(player, "CLWipeLeaderboard");
                    break;
                case "lastwipe":
                    LastWipeLeaderboard(player);
                    break;
                case "display":
                    _pages[player.userID] = 0;
                    int clanInt = int.Parse(arg.Args[1]);
                    UIClanDisplay(player, _staticClanList[clanInt], clanInt);
                    break;
                case "closedisplay":
                    _pages[player.userID] = 0;
                    CuiHelper.DestroyUi(player, "CLClanDisplay");
                    break;
                case "memberspage":
                    clanInt = int.Parse(arg.Args[2]);
                    if (arg.Args[1] == "add") _pages[player.userID] += 1;
                    else _pages[player.userID] -= 1;

                    UIClanMembersDisplay(player, clanInt);
                    break;
                default:
                    CuiHelper.DestroyUi(player, "CLMainPanel");
                    break;
            }
        }
        #endregion

        #region [ HOOKS ]
        void OnServerInitialized(bool initial)
        {
            var isUsingWC = Interface.Call("IsUsingPlugin", "ClanCores");
            if (isUsingWC != null && (isUsingWC is bool)) usingWC = (bool)isUsingWC;
            else usingWC = false;

            HandleCommands();
            LoadClans();
            ImportImages();
            RunTimer();
            TimerHandler(true);

            foreach (var clan in _staticClanList.Where(x => x.TC != 0))
            {
                BuildingPrivlidge entity = BaseNetworkable.serverEntities.Find(new NetworkableId(clan.TC)) as BuildingPrivlidge;
                if (entity == null) clan.TC = 0;
            }

            if (isWipe)
            {
                if (_wipeData.Members.Count > 0) foreach (var winner in _wipeData.Members) permission.RemoveUserGroup($"{winner.Key}", _config.WinnerGroup);

                _wipeData = new ClanPoints();
                if (_staticClanList.Count > 0)
                {
                    var clanInfo = _staticClanList.OrderByDescending(x => x.Points).FirstOrDefault();
                    if (clanInfo != null)
                    {
                        _wipeData = clanInfo.Clone();
                        foreach (var winner in clanInfo.Members) permission.AddUserGroup($"{winner.Key}", _config.WinnerGroup);
                        foreach (var winner in clanInfo.Members) permission.RemoveUserGroup($"{winner.Key}", _config.CurrentGroup);
                    }
                }

                _staticClanList.Clear();
            }

            StartCheckAllClans();
        }

        void StartCheckAllClans()
        {
            timer.Once(300f, CheckAllClans);
        }

        void OnPlayerConnected(BasePlayer player)
        {
            string clanName = GetClanName(player);
            if (string.IsNullOrEmpty(clanName)) return;

            var clanInfo = GetClan(clanName);
            if (clanInfo.Members.ContainsKey(player.userID))
            {
                clanInfo.Members[player.userID] = player.displayName;
            }
            else clanInfo.Members.Add(player.userID, player.displayName);
        }

        void CheckAllClans()
        {
            try
            {
                PrintToConsole("[ClanCores] === NEW APPROACH: Direct API Integration ===");

                // Clear existing data
                _staticClanList.Clear();

                if (OasisClans == null)
                {
                    PrintToConsole("[ClanCores] ❌ OasisClans plugin not found");
                    return;
                }

                // Method 1: Try to get clan data directly from OasisClans internal data
                try
                {
                    // Access the clan cache directly from OasisClans
                    var clanCacheField = OasisClans.GetType().GetField("clanCache", BindingFlags.NonPublic | BindingFlags.Instance);
                    if (clanCacheField != null)
                    {
                        var clanCache = clanCacheField.GetValue(OasisClans);
                        if (clanCache != null)
                        {
                            PrintToConsole($"[ClanCores] Found clanCache field: {clanCache.GetType().Name}");

                            // Try to iterate through the cache
                            if (clanCache is IEnumerable enumerable)
                            {
                                int clanCount = 0;
                                foreach (var clanObj in enumerable)
                                {
                                    if (clanObj == null) continue;

                                    try
                                    {
                                        var clanType = clanObj.GetType();
                                        var clanNameProp = clanType.GetProperty("ClanName");
                                        var clanMembersProp = clanType.GetProperty("ClanMemebers"); // Note: typo in original

                                        if (clanNameProp != null && clanMembersProp != null)
                                        {
                                            string clanName = clanNameProp.GetValue(clanObj)?.ToString();
                                            var members = clanMembersProp.GetValue(clanObj) as Dictionary<string, string>;

                                            if (!string.IsNullOrEmpty(clanName) && members != null)
                                            {
                                                var newClan = new ClanPoints()
                                                {
                                                    Name = clanName,
                                                    Members = new Dictionary<ulong, string>(),
                                                    Points = 0,
                                                    Kills = 0,
                                                    Deaths = 0,
                                                    EventWins = 0,
                                                    TC = 0,
                                                    TCLoco = "",
                                                    LastTimePointsLost = 0,
                                                    TookTC = 0,
                                                    LostTC = 0,
                                                    HeliKills = 0,
                                                    BradKills = 0,
                                                    LockedCrates = 0,
                                                    PastMembers = new Dictionary<ulong, string>()
                                                };

                                                // Convert string IDs to ulong
                                                foreach (var member in members)
                                                {
                                                    if (ulong.TryParse(member.Key, out ulong userId))
                                                    {
                                                        newClan.Members[userId] = member.Value;
                                                    }
                                                }

                                                _staticClanList.Add(newClan);
                                                clanCount++;
                                                PrintToConsole($"[ClanCores] ✅ Added clan '{clanName}' with {newClan.Members.Count} members");
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        PrintToConsole($"[ClanCores] Error processing clan: {ex.Message}");
                                    }
                                }

                                PrintToConsole($"[ClanCores] Direct cache access: Found {clanCount} clans");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    PrintToConsole($"[ClanCores] Direct cache access failed: {ex.Message}");
                }

                PrintToConsole($"[ClanCores] === SYNC COMPLETE: {_staticClanList.Count} clans loaded ===");
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] CRITICAL ERROR in CheckAllClans: {ex.Message}");
                PrintError($"[ClanCores] Stack trace: {ex.StackTrace}");
            }
        }

        void ScanActivePlayerClans()
        {
            try
            {
                PrintToConsole("[ClanCores] === SCANNING ACTIVE PLAYERS ===");

                // Clear the list first
                _staticClanList.Clear();
                PrintToConsole($"[ClanCores] Cleared existing clan list. Starting fresh scan...");

                int playersScanned = 0;
                int clansFound = 0;

                foreach (BasePlayer player in BasePlayer.activePlayerList)
                {
                    if (player == null) continue;
                    playersScanned++;

                    string clanName = GetClanName(player);
                    PrintToConsole($"[ClanCores] Player {player.displayName} ({player.userID}) - GetClanName result: '{clanName ?? "NULL"}'");

                    if (string.IsNullOrEmpty(clanName))
                    {
                        continue;
                    }

                    // Check if clan already exists in our list
                    var existingClan = _staticClanList.FirstOrDefault(x => x.Name.Equals(clanName, StringComparison.OrdinalIgnoreCase));

                    if (existingClan == null)
                    {
                        // Create new clan entry
                        var newClan = new ClanPoints()
                        {
                            Name = clanName,
                            Members = new Dictionary<ulong, string>(),
                            Points = 0,
                            Kills = 0,
                            Deaths = 0,
                            EventWins = 0,
                            TC = 0,
                            TCLoco = "",
                            LastTimePointsLost = 0,
                            TookTC = 0,
                            LostTC = 0,
                            HeliKills = 0,
                            BradKills = 0,
                            LockedCrates = 0,
                            PastMembers = new Dictionary<ulong, string>()
                        };

                        // Add this player to the clan
                        newClan.Members[player.userID] = player.displayName;

                        _staticClanList.Add(newClan);
                        clansFound++;
                        PrintToConsole($"[ClanCores] ✅ CREATED NEW CLAN: '{clanName}' with member {player.displayName}");
                        PrintToConsole($"[ClanCores] ✅ Clan list now has {_staticClanList.Count} clans");
                    }
                    else
                    {
                        // Add player to existing clan
                        existingClan.Members[player.userID] = player.displayName;
                        PrintToConsole($"[ClanCores] ✅ ADDED {player.displayName} to existing clan: {clanName}");
                    }
                }

                PrintToConsole($"[ClanCores] === SCAN COMPLETE ===");
                PrintToConsole($"[ClanCores] Players scanned: {playersScanned}");
                PrintToConsole($"[ClanCores] New clans found: {clansFound}");
                PrintToConsole($"[ClanCores] Total clans in list: {_staticClanList.Count}");

                // List all clans for verification
                foreach (var clan in _staticClanList)
                {
                    PrintToConsole($"[ClanCores] - Clan '{clan.Name}': {clan.Members.Count} members");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] CRITICAL ERROR in ScanActivePlayerClans: {ex.Message}");
                PrintError($"[ClanCores] Stack trace: {ex.StackTrace}");
            }
        }

        private void Unload()
        {
            foreach (BasePlayer player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "CLMainPanel");
                CuiHelper.DestroyUi(player, "CLTcUI");
                CuiHelper.DestroyUi(player, "CLClanDisplay");
                CuiHelper.DestroyUi(player, "CLWipeLeaderboard");
            }

            SaveClanPointData();
            _config = null;
        }

        public static Dictionary<string, string> ConvertToDictionary(UIElements uiElements)
        {
            var dictionary = new Dictionary<string, string>();
            foreach (PropertyInfo property in typeof(UIElements).GetProperties())
            {
                string key = property.Name;
                string value = property.GetValue(uiElements)?.ToString();
                dictionary[key] = value;
            }
            return dictionary;
        }

        void OnWCRequestedUIPanel(BasePlayer player, string panelName, string neededPlugin)
        {
            if (!neededPlugin.Equals("ClanCores", StringComparison.OrdinalIgnoreCase)) return;
            usingWC = true;
            MainUI(player);
        }

        void OnWCRequestColors(string pluginName)
        {
            if (!pluginName.Equals("ClanCores", StringComparison.OrdinalIgnoreCase)) return;
            Interface.CallHook("WCSendColors", ConvertToDictionary(_uiColors), pluginName);
        }

        void OnWCSentThemeColors(List<string> pluginNames, Dictionary<string, string> themeColors)
        {
            if (!pluginNames.Contains("ClanCores")) return;

            _config.UIColors.MainPanelColor = themeColors["BackgroundColor"];
            SaveConfig();
        }

        void OnPluginLoaded(Plugin plugin)
        {
            if (plugin.Name == "WelcomeController")
            {
                var isUsingWC = Interface.Call("IsUsingPlugin", "ClanCores");
                if (isUsingWC != null && (isUsingWC is bool)) usingWC = (bool)isUsingWC;
                else usingWC = false;
            }
            else if (plugin.Name == "OasisClans")
            {
                PrintToConsole("[ClanCores] OasisClans plugin loaded - syncing clan data");
                timer.Once(3f, () => CheckAllClans());
            }
        }

        void OnPluginUnloaded(Plugin plugin)
        {
            if (plugin.Name == "WelcomeController")
            {
                usingWC = false;
                HandleCommands();
            }
        }

        void OnNewSave(string filename) => isWipe = true;

        void OnWeaponFired(BaseProjectile projectile, BasePlayer player, ItemModProjectile mod, ProtoBuf.ProjectileShoot projectiles)
        {
            if (player == null || player.IsNpc) return;

            if (!_bulletData.ContainsKey(player.userID)) _bulletData[player.userID] = new BD();

            _bulletData[player.userID].Bullets++;
        }

        object OnEntityTakeDamage(BasePlayer player, HitInfo info)
        {
            if (info == null || info.InitiatorPlayer == null || player == null) return null;
            if (info.InitiatorPlayer.IsNpc || player.IsNpc) return null;

            if (!info.isHeadshot) return null;

            if (!_bulletData.ContainsKey(player.userID)) _bulletData[player.userID] = new BD();

            _bulletData[player.userID].Headshots++;
            return null;
        }

        void OnPlayerDeath(BasePlayer player, HitInfo info)
        {
            if (info == null || info.InitiatorPlayer == null || player == null || player == info.InitiatorPlayer) return;
            if (info.InitiatorPlayer.IsNpc || player.IsNpc) return;

            string clanName = GetClanName(info.InitiatorPlayer);
            ClanPoints clanInfo = GetClan(clanName);
            if (clanInfo == null) return;

            if (IsInSameClan(player, clanInfo)) return;

            string cName = GetClanName(player);
            ClanPoints deathClan = GetClan(cName);
            if (deathClan != null)
            {
                if (_config.LosePointsWithNoTC || ValidTC(deathClan))
                {
                    AlterPoints(deathClan, _config.PointsLossClass.DeathPointsLoss, false);
                    deathClan.Deaths++;
                }
            }

            if (ValidTC(clanInfo))
            {
                double points = _config.PointsGainClass.KillPoints;
                if (info.isHeadshot)
                {
                    SendReply(info.InitiatorPlayer, Lang("HeadshotKill", player.UserIDString, _config.PointsGainClass.KillPoints + _config.PointsGainClass.HeadshotPoints));
                    points += _config.PointsGainClass.HeadshotPoints;
                }
                else SendReply(info.InitiatorPlayer, Lang("RegularKill", player.UserIDString, _config.PointsGainClass.KillPoints));

                AlterPoints(clanInfo, points, true);
                clanInfo.Kills++;
            }

            return;
        }

        void OnEntityKill(BuildingPrivlidge entity)
        {
            if (entity == null) return;

            var clanPos = _staticClanList.FindIndex(x => entity.net.ID == new NetworkableId(x.TC));
            if (clanPos == -1) return;

            var clanInfo = _staticClanList[clanPos];

            List<BasePlayer> clanMembers = new List<BasePlayer>();
            foreach (var member in clanInfo.Members)
            {
                BasePlayer player = BasePlayer.FindByID(member.Key);
                if (player != null) clanMembers.Add(player);
            }

            double pointsLost;
            if (clanMembers.All(x => !x.IsConnected)) pointsLost = clanInfo.Points * (_config.PointsLossClass.TCPointsLossOffline / 100);
            else pointsLost = clanInfo.Points * (_config.PointsLossClass.TCPointsLoss / 100);

            foreach (var member in clanMembers) SendReply(member, Lang("TCRemoved", member.UserIDString, pointsLost));

            AlterPoints(clanInfo, pointsLost, false);
            clanInfo.TC = 0;
            clanInfo.TCLoco = String.Empty;
            clanInfo.LastTimePointsLost = DateTimeOffset.Now.ToUnixTimeSeconds() + (_config.PointsLossClass.TimesPointsLossPeriod * 60);
            clanInfo.LostTC++;
            return;
        }

        void OnEntityDeath(BuildingPrivlidge entity, HitInfo info)
        {
            if (entity == null) return;

            var clanPos = _staticClanList.FindIndex(x => entity.net.ID == new NetworkableId(x.TC));
            if (clanPos == -1) return;

            var clanInfo = _staticClanList[clanPos];

            List<BasePlayer> clanMembers = new List<BasePlayer>();
            foreach (var member in clanInfo.Members)
            {
                BasePlayer player = BasePlayer.FindByID(member.Key);
                if (player != null) clanMembers.Add(player);
            }

            double pointsLost;
            if (clanMembers.All(x => !x.IsConnected)) pointsLost = clanInfo.Points * (_config.PointsLossClass.TCPointsLossOffline / 100);
            else pointsLost = clanInfo.Points * (_config.PointsLossClass.TCPointsLoss / 100);

            foreach (var member in clanMembers) SendReply(member, Lang("TCRemoved", member.UserIDString, pointsLost));

            // Remove points
            AlterPoints(clanInfo, pointsLost, false);
            clanInfo.TC = 0;
            clanInfo.TCLoco = String.Empty;
            clanInfo.LastTimePointsLost = DateTimeOffset.Now.ToUnixTimeSeconds() + (_config.PointsLossClass.TimesPointsLossPeriod * 60);
            clanInfo.LostTC++;

            if (info == null || info.InitiatorPlayer == null) return;

            // Add points
            string clanName = GetClanName(info.InitiatorPlayer);
            ClanPoints raiderClanInfo = GetClan(clanName);

            if (raiderClanInfo == null || !ValidTC(raiderClanInfo)) return;

            raiderClanInfo.TookTC++;
            AlterPoints(raiderClanInfo, pointsLost, true);

            SendReply(info.InitiatorPlayer, Lang("Raided", info.InitiatorPlayer.UserIDString, $"{pointsLost}", clanInfo.Name));
            if (_config.ClanRaidedGlobalMessage)
            {
                Server.Broadcast(Lang("GlobalRaid", null, clanName, raiderClanInfo.Name, pointsLost));
            }
        }

        void OnEntitySpawned(PatrolHelicopter heli)
        {
            if (_config.SendSpawnedMessages) Server.Broadcast(Lang("HeliSpawned", null, _config.PointsGainClass.HeliPoints));
        }

        void OnEntitySpawned(BradleyAPC brad)
        {
            if (_config.SendSpawnedMessages) Server.Broadcast(Lang("BradSpawned", null, _config.PointsGainClass.BradPoints));
            brad.health = 500;
            brad.SendNetworkUpdateImmediate();
        }

        void OnEntityTakeDamage(PatrolHelicopter heli, HitInfo info)
        {
            if (heli == null || info == null || info.InitiatorPlayer == null) return;

            EntityInfo heliInfo = new EntityInfo();
            if (_actionInfo._patrolDamage.TryGetValue(heli, out EntityInfo heliInf)) heliInfo = heliInf;
            else heliInfo.LastHealth = heli.health;

            if (heliInfo == null || heliInfo.Dead) return;

            heliInfo.LastHitter = info.InitiatorPlayer.userID;

            if (heliInfo.LastHealth < heli.health)
            {
                HandleHeliDeath(heli);
                return;
            }

            if (info.InitiatorPlayer == null) return;
            double playerDamage = info.damageTypes.Total();
            if (heliInfo.PlayerInfo.TryGetValue(info.InitiatorPlayer, out double totalDamage)) playerDamage += totalDamage;

            heliInfo.PlayerInfo[info.InitiatorPlayer] = playerDamage;
            _actionInfo._patrolDamage[heli] = heliInfo;
            return;
        }

        void OnEntityDeath(PatrolHelicopter heli, HitInfo info)
        {
            if (heli == null) return;

            HandleHeliDeath(heli);
        }

        object OnEntityTakeDamage(BradleyAPC brad, HitInfo info)
        {
            if (brad == null) return null;

            EntityInfo bradInfo = new EntityInfo();
            if (_actionInfo._apcDamage.TryGetValue(brad, out EntityInfo heliInf)) bradInfo = heliInf;

            if (bradInfo == null || bradInfo.Dead) return null;

            if (info.InitiatorPlayer == null) return null;
            double playerDamage = info.damageTypes.Total();
            if (bradInfo.PlayerInfo.TryGetValue(info.InitiatorPlayer, out double totalDamage)) playerDamage += totalDamage;

            bradInfo.PlayerInfo[info.InitiatorPlayer] = playerDamage;
            _actionInfo._apcDamage[brad] = bradInfo;

            return null;
        }

        void OnEntityDeath(BradleyAPC brad, HitInfo info)
        {
            if (brad == null) return;

            EntityInfo bradInfo = new EntityInfo();
            if (_actionInfo._apcDamage.TryGetValue(brad, out EntityInfo heliInf)) bradInfo = heliInf;

            BasePlayer player = null;

            bradInfo.Dead = true;
            if (_config.Killer) player = info.InitiatorPlayer;
            else player = bradInfo.PlayerInfo.FirstOrDefault(x => x.Value == bradInfo.PlayerInfo.Values.Max()).Key;

            var clanInfo = _staticClanList.FirstOrDefault(x => x.Members.ContainsKey(player.userID) && ValidTC(x));
            if (clanInfo == null || clanInfo.TC == 0)
            {
                SendReply(player, Lang("NotValid", player.UserIDString));
                return;
            }

            clanInfo.BradKills++;
            AlterPoints(clanInfo, _config.PointsGainClass.BradPoints, true);
            SendReply(player, Lang("BradKill", player.UserIDString, _config.PointsGainClass.BradPoints));

            _actionInfo._apcDamage.Remove(brad);
        }

        void OnLootEntityEnd(BasePlayer player, HackableLockedCrate crate)
        {
            if (!_actionInfo._hackedCrates.Contains(crate))
            {
                _actionInfo._hackedCrates.Add(crate);
                var clanName = GetClanName(player);
                if (clanName != null)
                {
                    var pointData = GetClan(clanName);
                    if (pointData == null || !ValidTC(pointData))
                    {
                        SendReply(player, Lang("NotValid", player.UserIDString));
                        return;
                    }

                    pointData.LockedCrates++;
                    AlterPoints(pointData, _config.PointsGainClass.LoackedCratesPoints, true);
                    SendReply(player, Lang("LockedCrate", player.UserIDString, _config.PointsGainClass.LoackedCratesPoints));
                }
                else
                {
                    SendReply(player, Lang("NotValid", player.UserIDString));
                    return;
                }
            }
        }

        void OnLootEntity(BasePlayer player, BuildingPrivlidge entity)
        {
            if (player == null || entity == null) return;
            UICreateTCUI(player, entity);
        }

        private void OnLootEntityEnd(BasePlayer player, BuildingPrivlidge entity)
        {
            if (player == null) return;
            CuiHelper.DestroyUi(player, "CLTcUI");
        }

        void OnKOTHWinnerAnnounce(List<ulong> players, DateTime time)
        {
            if (players == null || players.Count == 0) return;
            var thePlayer = players.First();

            BasePlayer player = BasePlayer.FindByID(thePlayer);
            var clanName = GetClanName(player);
            if (clanName == null || player == null) return;

            var clanInfo = GetClan(clanName);
            HandleEventWinner(clanInfo, "KOTH");
        }

        void OnROAMWinnerAnnounce(List<ulong> players, DateTime time)
        {
            if (players == null || players.Count == 0) return;
            var thePlayer = players.First();

            BasePlayer player = BasePlayer.FindByID(thePlayer);
            var clanName = GetClanName(player);
            if (clanName == null || player == null) return;

            var clanInfo = GetClan(clanName);
            HandleEventWinner(clanInfo, "Roam");
        }

        void OnMazeWinnerAnnounce(List<ulong> players, DateTime time)
        {
            if (players == null || players.Count == 0) return;
            var thePlayer = players.First();

            BasePlayer player = BasePlayer.FindByID(thePlayer);
            var clanName = GetClanName(player);
            if (clanName == null || player == null) return;

            var clanInfo = GetClan(clanName);
            HandleEventWinner(clanInfo, "Maze");
        }

        void OnControlWinnerAnnounce(List<ulong> players, DateTime time)
        {
            if (players == null || players.Count == 0) return;
            var thePlayer = players.First();

            BasePlayer player = BasePlayer.FindByID(thePlayer);
            var clanName = GetClanName(player);
            if (clanName == null || player == null) return;

            var clanInfo = GetClan(clanName);
            HandleEventWinner(clanInfo, "Control");
        }

        void OnSulfurWinnerAnnounce(List<ulong> players, DateTime time)
        {
            if (players == null || players.Count == 0) return;
            var thePlayer = players.First();

            BasePlayer player = BasePlayer.FindByID(thePlayer);
            var clanName = GetClanName(player);
            if (clanName == null || player == null) return;

            var clanInfo = GetClan(clanName);
            HandleEventWinner(clanInfo, "Sulfur");
        }

        void OnClanDisbanded(string clanName, List<string> memberUserIDs)
        {
            try
            {
                var clanInfo = _staticClanList.FirstOrDefault(x => x.Name.Equals(clanName, StringComparison.OrdinalIgnoreCase));
                if (clanInfo == null) return;

                List<ulong> LastClanMembers = new List<ulong>();
                LastClanMembers.AddRange(clanInfo.Members.Keys);
                LastClanMembers.AddRange(clanInfo.PastMembers.Keys);
                foreach (var member in LastClanMembers)
                {
                    permission.RemoveUserGroup($"{member}", _config.CurrentGroup);
                }

                _staticClanList.Remove(clanInfo);
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] Error in OnClanDisbanded for clan {clanName}: {ex.Message}");
            }
        }

        void OnClanMemberGone(string userID, string clanName)
        {
            try
            {
                ClanPoints clanInfo = GetClan(clanName);
                if (clanInfo == null) return;

                if (ulong.TryParse(userID, out ulong steamId))
                {
                    clanInfo.Members.Remove(steamId);
                }
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] Error in OnClanMemberGone for user {userID}: {ex.Message}");
            }
        }

        void OnClanMemberJoined(string userID, string clanName)
        {
            try
            {
                ClanPoints clanInfo = GetClan(clanName);
                if (clanInfo == null) return;

                if (ulong.TryParse(userID, out ulong steamId))
                {
                    BasePlayer player = BasePlayer.FindByID(steamId);
                    clanInfo.Members[steamId] = player?.displayName ?? "N/A";
                }
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] Error in OnClanMemberJoined for user {userID}: {ex.Message}");
            }
        }

        void OnClanCreate(string clanName)
        {
            try
            {
                if (!_staticClanList.Any(x => x.Name.Equals(clanName, StringComparison.OrdinalIgnoreCase)))
                {
                    List<string> ClaMembers = GetClanMembers(clanName);
                    Dictionary<ulong, string> CMembers = new Dictionary<ulong, string>();

                    foreach (var member in ClaMembers)
                    {
                        ulong steamId;
                        if (ulong.TryParse(member, out steamId))
                        {
                            BasePlayer player = BasePlayer.FindByID(steamId);
                            if (player != null)
                            {
                                CMembers.Add(steamId, player.displayName);
                            }
                        }
                    }

                    _staticClanList.Add(new ClanPoints()
                    {
                        Name = clanName,
                        Members = CMembers
                    });
                }
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] Error in OnClanCreate for clan {clanName}: {ex.Message}");
            }
        }
        #endregion

        #region [ METHODS ]
        void HandleHeliDeath(PatrolHelicopter heli)
        {
            if (!_actionInfo._patrolDamage.TryGetValue(heli, out EntityInfo heliInfo)) return;

            BasePlayer killer = null;
            heliInfo.Dead = true;

            if (_config.Killer) killer = BasePlayer.FindByID(heliInfo.LastHitter);
            else killer = heliInfo.PlayerInfo.FirstOrDefault(x => x.Value == heliInfo.PlayerInfo.Values.Max()).Key;

            if (killer == null) return;

            string clanName = GetClanName(killer);
            var clanInfo = GetClan(clanName);

            if (clanInfo == null || !ValidTC(clanInfo))
            {
                SendReply(killer, Lang("NotValid", killer.UserIDString));
                return;
            }

            clanInfo.HeliKills++;
            AlterPoints(clanInfo, _config.PointsGainClass.HeliPoints, true);

            SendReply(killer, Lang("HeliKill", killer.UserIDString, _config.PointsGainClass.HeliPoints));
        }


        private double CalculateHS(double shots, double headshots)
        {
            double hs = 0;

            if (headshots == 0) hs = 0;
            else hs = Math.Round((headshots / shots) * 100, 2);

            return hs;
        }

        private void HandleEventWinner(ClanPoints clanInfo, string eventType = "")
        {
            if (clanInfo == null) return;

            clanInfo.EventWins++;

            double pointsToAdd = _config.PointsGainClass.EventPoints; // Default fallback

            switch (eventType.ToLower())
            {
                case "roam":
                    pointsToAdd = _config.PointsGainClass.RoamEventPoints;
                    break;
                case "maze":
                    pointsToAdd = _config.PointsGainClass.MazeEventPoints;
                    break;
                case "control":
                    pointsToAdd = _config.PointsGainClass.ControlEventPoints;
                    break;
                case "sulfur":
                    pointsToAdd = _config.PointsGainClass.SulfurEventPoints;
                    break;
                case "koth":
                    pointsToAdd = _config.PointsGainClass.KOTHEventPoints;
                    break;
                default:
                    pointsToAdd = _config.PointsGainClass.EventPoints;
                    break;
            }

            AlterPoints(clanInfo, pointsToAdd, true);

            // Send message to clan members
            string messageKey = $"{eventType}EventWin";
            foreach (var member in clanInfo.Members)
            {
                BasePlayer player = BasePlayer.FindByID(member.Key);
                if (player != null && player.IsConnected)
                {
                    SendReply(player, Lang(messageKey, player.UserIDString, pointsToAdd));
                }
            }

            // Log the event win for debugging
            Puts($"[ClanCores] Clan '{clanInfo.Name}' won {eventType} event and gained {pointsToAdd} points");
        }

        private void SaveClanPointData()
        {
            Interface.GetMod().DataFileSystem.WriteObject($"ClanCores{Path.DirectorySeparatorChar}PointData", _staticClanList);
            Interface.GetMod().DataFileSystem.WriteObject($"ClanCores{Path.DirectorySeparatorChar}WipeWinners", _wipeData);
        }

        void OnServerSave() => SaveClanPointData();

        void RunTimer()
        {
            timer.Every(300, () => TimerHandler());
        }

        void TimerHandler(bool firstLoad = false)
        {
            // Bullet add
            foreach (var user in _bulletData)
            {
                BasePlayer player = BasePlayer.FindByID(user.Key);
                if (player == null) continue;
                string clanName = GetClanName(player);
                ClanPoints clanInfo = GetClan(clanName);

                if (clanInfo == null) continue;
                clanInfo.BulletsFired += user.Value.Bullets;
                clanInfo.Headshots += user.Value.Headshots;
            }

            _bulletData.Clear();

            // Point Decay
            if (_config.LosePointsWithNoTC)
            {
                var currentTime = DateTimeOffset.Now.ToUnixTimeSeconds();
                var neededTime = currentTime - (_config.PointsLossClass.TimesPointsLossMinutes * 60);

                List<ClanPoints> clansWithNoTCs = _staticClanList.Where(x => x.TC == 0 && x.LastTimePointsLost != 0 && x.LastTimePointsLost <= neededTime).ToList();
                foreach (var clan in clansWithNoTCs)
                {
                    var pointsLost = clan.Points * (_config.PointsLossClass.TimedPointsLoss / 100);
                    AlterPoints(clan, pointsLost, false);
                    if (clan.Points < 1)
                    {
                        clan.Points = 0;
                        clan.LastTimePointsLost = 0;
                    }
                    else clan.LastTimePointsLost = currentTime;
                }
            }

            // Top Clans
            _staticClanList = _staticClanList.OrderByDescending(x => x.Points).ToList();

            if (!string.IsNullOrEmpty(_config.CurrentGroup))
            {
                ClanPoints clanInfo = _staticClanList.FirstOrDefault();
                ClanPoints lastClanInfo = _staticClanList.FirstOrDefault(x => x.Name == LastLeader);
                if (clanInfo != null && LastLeader != clanInfo.Name)
                {
                    if (lastClanInfo != null)
                    {
                        List<ulong> LastClanMembers = new List<ulong>();
                        LastClanMembers.AddRange(lastClanInfo.Members.Keys);
                        LastClanMembers.AddRange(lastClanInfo.PastMembers.Keys);
                        foreach (var member in LastClanMembers)
                        {
                            permission.RemoveUserGroup($"{member}", _config.CurrentGroup);
                        }
                    }

                    foreach (var member in clanInfo.Members)
                    {
                        permission.AddUserGroup($"{member.Key}", _config.CurrentGroup);
                    }

                    LastLeader = clanInfo.Name;
                }
            }

            if (firstLoad) SaveClanPointData();
        }

        bool CheckCooldown(ulong steamID)
        {
            if (_cooldowns.ContainsKey(steamID))
            {
                if (_cooldowns[steamID].Subtract(DateTime.Now).TotalSeconds >= 0) return false;
                else
                {
                    _cooldowns[steamID] = DateTime.Now.AddSeconds(0.5f);
                    return true;
                }
            }
            else _cooldowns[steamID] = DateTime.Now.AddSeconds(0.5f);
            return true;
        }

        string SetGrid(Vector3 pos)
        {
            var worldSize = ConVar.Server.worldsize;

            char gridLetter = 'A';
            char extraLetter = 'A';

            var trueGrid = Mathf.Floor((pos.x + (worldSize / 2)) / 146.3f);
            var xPos = trueGrid % 26;
            var zPos = (Mathf.Floor(worldSize / 146.3f)) - Mathf.Floor((pos.z + (worldSize / 2)) / 146.3f) - 1;

            bool needsExtraLetter = trueGrid > 25;
            if (needsExtraLetter) extraLetter = (char)(extraLetter + (xPos / 26));

            gridLetter = (char)(gridLetter + xPos);
            string stringPos = needsExtraLetter ? $"{extraLetter}{gridLetter}{zPos}" : $"{gridLetter}{zPos}";

            return stringPos;
        }

        void SetClanTC(BasePlayer player, string[] args)
        {
            ClanPoints clanInfo = GetClan(args[1]);

            if (clanInfo == null) return;
            ulong TCId = ulong.Parse(args[2]);

            BuildingPrivlidge tc = BaseNetworkable.serverEntities.Find(new NetworkableId(TCId)) as BuildingPrivlidge;
            if (tc == null) return;

            clanInfo.LastTimePointsLost = 0;
            clanInfo.TC = TCId;
            clanInfo.TCLoco = SetGrid(tc.Transform.position);
            UICreateTCUI(player, tc);
        }

        bool ValidTC(ClanPoints clanInfo)
        {
            if (clanInfo.TC == 0) return false;
            else return true;
        }

        void AlterPoints(ClanPoints clanInfo, double pointsChange, bool adding)
        {
            if (adding) clanInfo.Points += pointsChange;
            else
            {
                clanInfo.Points -= pointsChange;
                clanInfo.TotalPointsLost += pointsChange;
                clanInfo.Points = Math.Max(0, clanInfo.Points);
            }

            clanInfo.Points = Math.Round(clanInfo.Points);
            clanInfo.TotalPointsLost = Math.Round(clanInfo.TotalPointsLost);
        }

        bool IsInSameClan(BasePlayer player, ClanPoints clanInfo)
        {
            if (clanInfo.Members.ContainsKey(player.userID)) return true;
            if (clanInfo.PastMembers.ContainsKey(player.userID)) return true;
            return false;
        }

        ClanPoints GetClan(string clanName)
        {
            var clanPoints = _staticClanList.FindIndex(x => x.Name.Equals(clanName, StringComparison.OrdinalIgnoreCase));
            if (clanPoints == -1) return null;
            return _staticClanList[clanPoints];
        }

        public object GetClanInfo(string clanName)
        {
            try
            {
                if (OasisClans == null) return null;

                // Use GetAllClans to find clan by name
                var allClans = OasisClans.Call("GetAllClans");
                if (allClans == null) return null;

                // Find clan by name in the list
                if (allClans is List<object> clanList)
                {
                    foreach (var clan in clanList)
                    {
                        var clanType = clan.GetType();
                        var nameProperty = clanType.GetProperty("ClanName");
                        if (nameProperty != null)
                        {
                            var name = nameProperty.GetValue(clan)?.ToString();
                            if (name != null && name.Equals(clanName, StringComparison.OrdinalIgnoreCase))
                            {
                                return clan;
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] Error getting clan info for {clanName}: {ex.Message}");
                return null;
            }
        }

        public List<string> GetClanMembers(string clanName)
        {
            try
            {
                var clanInfo = GetClanInfo(clanName);
                if (clanInfo == null) return new List<string>();

                // Use reflection to get clan members
                var clanType = clanInfo.GetType();

                // Try "ClanMembers" first (from GetAllClans anonymous object)
                var membersProperty = clanType.GetProperty("ClanMembers");
                if (membersProperty != null)
                {
                    var membersDict = membersProperty.GetValue(clanInfo);
                    if (membersDict is Dictionary<string, string> members)
                    {
                        return members.Keys.ToList();
                    }
                }

                // Fallback to "ClanMemebers" (typo in OasisClans ClanInfo class)
                membersProperty = clanType.GetProperty("ClanMemebers");
                if (membersProperty != null)
                {
                    var membersDict = membersProperty.GetValue(clanInfo);
                    if (membersDict is Dictionary<string, string> members)
                    {
                        return members.Keys.ToList();
                    }
                }

                return new List<string>();
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] Error getting clan members for {clanName}: {ex.Message}");
                return new List<string>();
            }
        }

        int GetDecendedClan(string clanName)
        {
            var orderedList = _staticClanList.OrderByDescending(x => x.Points).ToList();
            int clanPosition = orderedList.FindIndex(x => x.Name.Equals(clanName, StringComparison.OrdinalIgnoreCase));
            return clanPosition;
        }

        string GetClanName(BasePlayer player)
        {
            try
            {
                if (player == null) return null;

                // Method 1: Check if player is in our internal clan list first
                var playerClan = _staticClanList.FirstOrDefault(x => x.Members.ContainsKey(player.userID));
                if (playerClan != null)
                {
                    return playerClan.Name;
                }

                // Method 2: Try GetClan API to get the actual clan name
                if (OasisClans != null)
                {
                    var clanObj = OasisClans.Call("GetClan", player.userID);
                    if (clanObj != null)
                    {
                        var clanType = clanObj.GetType();
                        var clanNameProperty = clanType.GetProperty("ClanName");
                        if (clanNameProperty != null)
                        {
                            var clanName = clanNameProperty.GetValue(clanObj)?.ToString();
                            if (!string.IsNullOrEmpty(clanName))
                            {
                                return clanName;
                            }
                        }

                        var clanTagProperty = clanType.GetProperty("ClanTag");
                        if (clanTagProperty != null)
                        {
                            var clanTag = clanTagProperty.GetValue(clanObj)?.ToString();
                            if (!string.IsNullOrEmpty(clanTag))
                            {
                                return clanTag;
                            }
                        }
                    }
                }

                // Method 3: Try GetClanTag API as backup
                if (OasisClans != null)
                {
                    var clanTag = OasisClans.Call("GetClanTag", player.userID);
                    if (clanTag != null && !string.IsNullOrEmpty(clanTag.ToString()))
                    {
                        return clanTag.ToString();
                    }
                }

                // Method 4: Parse from player display name if it has clan tag format
                if (!string.IsNullOrEmpty(player.displayName))
                {
                    var displayName = player.displayName;
                    // Look for clan tag pattern like [TAG] PlayerName
                    if (displayName.StartsWith("[") && displayName.Contains("]"))
                    {
                        int endIndex = displayName.IndexOf("]");
                        if (endIndex > 1)
                        {
                            string tag = displayName.Substring(1, endIndex - 1);
                            if (!string.IsNullOrEmpty(tag) && tag.Length >= 2 && tag.Length <= 10)
                            {
                                return tag;
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                PrintError($"[ClanCores] Error getting clan name for player {player.displayName}: {ex.Message}");
                return null;
            }
        }

        private void LoadClans()
        {
            _staticClanList = Interface.GetMod().DataFileSystem.ReadObject<List<ClanPoints>>($"ClanCores{Path.DirectorySeparatorChar}PointData");
            _wipeData = Interface.GetMod().DataFileSystem.ReadObject<ClanPoints>($"ClanCores{Path.DirectorySeparatorChar}WipeWinners");
        }

        public List<ClanPoints> GetTopClans(int amount)
        {
            var topClans = _staticClanList.Take(amount).ToList();

            return topClans;
        }

        private void HandleCommands()
        {
            if (!usingWC) foreach (var command in _config.Commands) cmd.AddChatCommand(command, this, CMDOpenClanUI);

            permission.RegisterPermission("clancores.admin", this);
        }

        private double CalculateKDR(double kills, double deaths)
        {
            double kdr = 0;

            if (deaths == 0) kdr = kills;
            else if (kills == 0 && deaths != 0) kdr = 0;
            else kdr = Math.Round((kills / deaths), 2);

            return kdr;
        }

        private string GetImage(string imageName)
        {
            if (ImageLibrary == null)
            {
                PrintError("Could not load images due to no Image Library");
                return null;
            }

            return ImageLibrary?.Call<string>("GetImage", imageName, 0UL, false);
        }

        void ImportImages()
        {
            var images = new Dictionary<string, string>()
            {
                 { "UIBanner", _config.UIImages["UIBanner"] },
                 { "UIBigBanner", _config.UIImages["UIBigBanner"] },
                 { "UIEmptyBanner", _config.UIImages["UIEmptyBanner"] },
                 { "UICrownIcon", _config.UIImages["UICrownIcon"] }
            };

            ImageLibrary?.Call("ImportImageList", "ClanCores", images, 0UL, true, null);
        }
        #endregion

        #region [ UI ]
        void UICreateTCUI(BasePlayer player, BuildingPrivlidge entity)
        {
            // Always destroy existing UI first
            CuiHelper.DestroyUi(player, "CLTcUI");

            string clanName = GetClanName(player);
            ClanPoints clanInfo = GetClan(clanName);

            var container = new CuiElementContainer();
            var panel = CreatePanel(ref container, ".5 0", ".5 0", "0.89 0.89 0.89 .2", "Overlay", "CLTcUI", offsetMin: _config.OffsetMin, offsetMax: _config.OffsetMax);

            if (clanInfo == null)
            {
                CreateSimpleLabel(ref container, "0 0", "1 1", "1 1 1 .8", Lang("NoClanData", player.UserIDString), 15, TextAnchor.MiddleCenter, panel);
                CuiHelper.AddUi(player, container);
                return;
            }

            ulong tcID = ulong.Parse($"{entity.net.ID}");
            var tcClanInfo = _staticClanList.FirstOrDefault(x => x.TC == tcID);

            if (entity.OwnerID != player.userID) CreateSimpleLabel(ref container, "0 0", "1 1", "1 1 1 .8", Lang("NotTCOwner", player.UserIDString), 15, TextAnchor.MiddleCenter, panel);
            else if (clanInfo.TC == 0) CreateButton(ref container, "0 0", "1 1", "0 0 0 0", "1 1 1 .8", Lang("SetClanTC", player.UserIDString), 17, $"cl_main settc {clanInfo.Name} {entity.net.ID}", panel);
            else if (clanInfo.TC == tcID) CreateSimpleLabel(ref container, "0 0", "1 1", "1 1 1 .8", Lang("ThisIsClanTC", player.UserIDString), 17, TextAnchor.MiddleCenter, panel);
            else if (clanInfo.TC != tcID) CreateSimpleLabel(ref container, "0 0", "1 1", "1 1 1 .8", Lang("TCAlreadySet", player.UserIDString), 17, TextAnchor.MiddleCenter, panel);
            else if (tcClanInfo != null && clanInfo.Name != tcClanInfo.Name) CreateLabel(ref container, "0 0", "1 1", "0 0 0 0", "1 1 1 .8", Lang("CoreTC", player.UserIDString), 17, TextAnchor.MiddleCenter, panel);

            CuiHelper.AddUi(player, container);
        }

        void UIClanMembersDisplay(BasePlayer player, int clanInt, CuiElementContainer container = null)
        {
            bool containerWasNull = container == null;
            if (containerWasNull) container = new CuiElementContainer();

            ClanPoints clanInfo = _staticClanList[clanInt];

            var membersPanel = CreatePanel(ref container, "0 0", "1 1", "0 0 0 0", "CLClanMembersPanel", "CLClanMembersPanelOverlay");

            int page = GetPage(player.userID);
            var maxPage = (clanInfo.Members.Count - 1) / 10;

            if (page > maxPage)
            {
                page = 0;
                _pages[player.userID] = 0;
            }

            if (page < 0)
            {
                page = maxPage;
                _pages[player.userID] = maxPage;
            }

            int i = 0;
            foreach (var member in clanInfo.Members.Skip(page * 10).Take(10))
            {
                CreateImagePanel(ref container, $"{.008 + (i * .0992)} .25", $"{.097 + (i * .0992)} .95", GetImage($"{member.Key}"), membersPanel);
                CreateSimpleLabel(ref container, $"{.008 + (i * .0992)} 0", $"{.097 + (i * .0992)} .25", "1 1 1 1", member.Value == null ? "N/A" : member.Value.Contains(']') ? member.Value.Split(']')[1] : member.Value, 10, TextAnchor.MiddleCenter, membersPanel);
                i++;
            }

            if (containerWasNull)
            {
                CuiHelper.DestroyUi(player, "CLClanMembersPanelOverlay");
                CuiHelper.AddUi(player, container);
            }
        }

        void UIClanDisplay(BasePlayer player, ClanPoints clanInfo, int clanInt)
        {
            var container = new CuiElementContainer();

            CreatePanel(ref container, "0 0", "1 1", "0 0 0 .3", "CLMainOverlay", "CLClanDisplay", true);
            CreatePanel(ref container, "0 0", "1 1", "0 0 0 .8", "CLClanDisplay");
            var panel = CreatePanel(ref container, ".1 .2", ".9 .8", "0 0 0 0", "CLClanDisplay", "CLClanDisplayPanel");

            var topLabel = CreatePanel(ref container, "0 .8", "1 1", "1 1 1 0", panel);
            CreateImagePanel(ref container, "0 0", ".997 1", GetImage("UIEmptyBanner"), topLabel);
            CreateSimpleLabel(ref container, "0 0", "1 1", "1 1 1 1", clanInfo.Name.ToUpper(), 55, TextAnchor.MiddleCenter, topLabel);
            CreateButton(ref container, ".9 0", "1 1", "0 0 0 0", "1 1 1 1", "X", 40, "cl_main closedisplay", topLabel);

            CreatePanel(ref container, ".06 .55", ".94 .78", "1 1 1 .3", panel, "CLClanMembersPanel");
            UIClanMembersDisplay(player, clanInt, container);

            bool needsPages = clanInfo.Members.Count > 10;
            int page = GetPage(player.userID);

            CreateButton(ref container, "0 .55", ".05 .781", "1 1 1 .35", "1 1 1 1", needsPages ? "<" : " ", 20, needsPages ? $"cl_main memberspage minus {clanInt}" : " ", "CLClanDisplayPanel");
            CreateButton(ref container, ".95 .55", ".998 .781", "1 1 1 .35", "1 1 1 1", needsPages ? ">" : " ", 20, needsPages ? $"cl_main memberspage add {clanInt}" : " ", "CLClanDisplayPanel");

            CreateLabel(ref container, "0 .47", "1 .53", "1 1 1 .35", "1 1 1 1", "STATS", 18, TextAnchor.MiddleCenter, panel);

            CreateLabel(ref container, "0 .38", ".2425 .45", "1 1 1 .3", "1 1 1 1", Lang("StatsRank", player.UserIDString, clanInt + 1), 17, TextAnchor.MiddleCenter, panel);
            CreateLabel(ref container, ".2525 .38", ".495 .45", "1 1 1 .3", "1 1 1 1", Lang("StatsRaids", player.UserIDString, clanInfo.TookTC), 17, TextAnchor.MiddleCenter, panel);

            CreateLabel(ref container, "0 .29", ".2425 .36", "1 1 1 .3", "1 1 1 1", Lang("StatsPoints", player.UserIDString, clanInfo.Points), 17, TextAnchor.MiddleCenter, panel);
            CreateLabel(ref container, ".2525 .29", ".495 .36", "1 1 1 .3", "1 1 1 1", Lang("StatsRaided", player.UserIDString, clanInfo.LostTC), 17, TextAnchor.MiddleCenter, panel);

            CreateLabel(ref container, "0 .2", ".2425 .27", "1 1 1 .3", "1 1 1 1", Lang("StatsPointsLost", player.UserIDString, clanInfo.TotalPointsLost), 17, TextAnchor.MiddleCenter, panel);
            CreateLabel(ref container, ".2525 .2", ".495 .27", "1 1 1 .3", "1 1 1 1", Lang("StatsHelis", player.UserIDString, clanInfo.HeliKills), 17, TextAnchor.MiddleCenter, panel);

            CreateLabel(ref container, ".505 .38", ".7575 .45", "1 1 1 .3", "1 1 1 1", Lang("StatsKills", player.UserIDString, clanInfo.Kills), 17, TextAnchor.MiddleCenter, panel);
            CreateLabel(ref container, ".7675 .38", "1 .45", "1 1 1 .3", "1 1 1 1", Lang("StatsShots", player.UserIDString, clanInfo.BulletsFired), 17, TextAnchor.MiddleCenter, panel);

            CreateLabel(ref container, ".505 .29", ".7575 .36", "1 1 1 .3", "1 1 1 1", Lang("StatsDeaths", player.UserIDString, clanInfo.Deaths), 17, TextAnchor.MiddleCenter, panel);
            CreateLabel(ref container, ".7675 .29", "1 .36", "1 1 1 .3", "1 1 1 1", Lang("StatsHeadshots", player.UserIDString, clanInfo.Headshots), 17, TextAnchor.MiddleCenter, panel);

            CreateLabel(ref container, ".505 .2", ".7575 .27", "1 1 1 .3", "1 1 1 1", Lang("StatsKDR", player.UserIDString, CalculateKDR(clanInfo.Kills, clanInfo.Deaths)), 17, TextAnchor.MiddleCenter, panel);
            CreateLabel(ref container, ".7675 .2", "1 .27", "1 1 1 .3", "1 1 1 1", Lang("StatsHSAccuracy", player.UserIDString, CalculateHS(clanInfo.BulletsFired, clanInfo.Headshots)), 17, TextAnchor.MiddleCenter, panel);

            CreateLabel(ref container, "0 0", "1 .18", "1 1 1 .3", "1 1 1 1", _config.ShowTCLoco && _config.PointsNeededLoco <= clanInfo.Points ? clanInfo.TC == 0 ? Lang("NoTC", player.UserIDString) : Lang("ClanLocation", player.UserIDString, clanInfo.TCLoco) : Lang("ClanLocation", player.UserIDString, "??"), 40, TextAnchor.MiddleCenter, panel);

            CuiHelper.DestroyUi(player, "CLClanDisplay");
            CuiHelper.AddUi(player, container);
        }

        void LastWipeLeaderboard(BasePlayer player)
        {
            var container = new CuiElementContainer();

            CreatePanel(ref container, "0 0", "1 1", "0 0 0 .3", "CLMainOverlay", "CLWipeLeaderboard", true);
            CreatePanel(ref container, "0 0", "1 1", "0 0 0 .8", "CLWipeLeaderboard");
            var panel = CreatePanel(ref container, ".1 .2", ".9 .8", "0 0 0 0", "CLWipeLeaderboard", "CLWipeLeaderboardPanel");

            var topLabel = CreatePanel(ref container, "0 .8", "1 1", "1 1 1 0", panel);
            CreateImagePanel(ref container, "0 0", ".998 1", GetImage("UIBanner"), topLabel);
            CreateButton(ref container, ".9 0", "1 1", "0 0 0 0", "1 1 1 1", "X", 40, "cl_main closelastwipe", topLabel);

            CreateLabel(ref container, "0 .62", "1 .78", "1 1 1 .3", "1 1 1 1", "CURRENT TOP CLAN", 25, TextAnchor.MiddleCenter, panel);
            var lbPnl1 = CreatePanel(ref container, "0 .51", "1 .61", "1 1 1 .25", panel);
            var vaPanel1 = CreatePanel(ref container, "0 .4", "1 .5", "1 1 1 .25", panel);
            UIWriteTitles(lbPnl1, ref container);
            UIWriteValues(vaPanel1, ref container, _staticClanList.Count > 0 ? GetTopClans(1).First() : new ClanPoints());

            CreateLabel(ref container, "0 .22", "1 .38", "1 1 1 .3", "1 1 1 1", "LAST WIPE WINNER", 25, TextAnchor.MiddleCenter, panel);
            var lbPnl2 = CreatePanel(ref container, "0 .11", "1 .21", "1 1 1 .25", panel);
            var vaPanel2 = CreatePanel(ref container, "0 0", "1 .1", "1 1 1 .25", panel);
            UIWriteTitles(lbPnl2, ref container);
            UIWriteValues(vaPanel2, ref container, _wipeData);

            CuiHelper.DestroyUi(player, "CLWipeLeaderboard");
            CuiHelper.AddUi(player, container);
        }

        void MainUI(BasePlayer player)
        {
            var container = new CuiElementContainer();

            CreatePanel(ref container, "0 0", "1 1", usingWC ? "0 0 0 0" : _uiColors.BlurBackgroundColor, usingWC ? "WCSourcePanel" : "Overlay", "CLMainPanel", true, true);
            CreatePanel(ref container, usingWC ? "0 0" : ".15 .1", usingWC ? ".995 1" : ".85 .9", "0 0 0 0", "CLMainPanel", "CLMainOverlay");

            UITopPanel(player, container);
            UICurrentLeaders(player, container);
            UIAllStats(player, container);
            UIClanStats(player, container);
            UIPageButtons(player, container);

            CuiHelper.DestroyUi(player, "CLMainPanel");
            CuiHelper.AddUi(player, container);
        }

        void UITopPanel(BasePlayer player, CuiElementContainer container)
        {
            // _uiColors.TitlePanelColor
            var topPanel = CreatePanel(ref container, "0 .89", "1 1", "0 0 0 0", "CLMainOverlay");

            CreateImagePanel(ref container, "0 0", ".998 .99", GetImage("UIBigBanner"), topPanel);
            if (!usingWC) CreateButton(ref container, ".915 0", "1 1", "0 0 0 0", "1 1 1 1", "X", 50, "cl_main close", topPanel);
            CreateImageButton(ref container, ".015 .2", ".06 .8", $"cl_main lastwipe", GetImage("UICrownIcon"), topPanel);
        }

        void UIAllStats(BasePlayer player, CuiElementContainer container = null)
        {
            bool containerWasNull = container == null;
            if (containerWasNull) container = new CuiElementContainer();

            var panel = CreatePanel(ref container, "0 .2", "1 .719", _uiColors.MainPanelColor, "CLMainOverlay", "CLAllStatsPanel");
            var labelPanel = CreatePanel(ref container, "0 .9", ".999 .999", "0 0 0 .6", panel);
            UIWriteTitles(labelPanel, ref container);
            UIAllStatsInd(player, container);

            if (containerWasNull)
            {
                CuiHelper.DestroyUi(player, "CLAllStatsPanel");
                CuiHelper.AddUi(player, container);
            }
        }

        int GetPage(ulong steamId)
        {
            if (!_pages.ContainsKey(steamId)) _pages[steamId] = 0;

            return _pages[steamId];
        }

        void UIAllStatsInd(BasePlayer player, CuiElementContainer container = null)
        {
            bool containerWasNull = container == null;
            if (containerWasNull) container = new CuiElementContainer();

            var statsPanel = CreatePanel(ref container, "0 0", ".998 .89", "0 0 0 0", "CLAllStatsPanel", "CLAllStatsPanelOverlay");

            int page = GetPage(player.userID);
            var maxPage = (_staticClanList.Count - 1) / 9;

            if (page > maxPage)
            {
                page = 0;
                _pages[player.userID] = 0;
            }

            if (page < 0)
            {
                page = maxPage;
                _pages[player.userID] = maxPage;
            }

            int i = 0;
            foreach (var clan in _staticClanList.Skip(9 * page).Take(9))
            {
                var statPanel = CreatePanel(ref container, $"0 {.895 - (.11 * i)}", $".999 {.995 - (.11 * i)}", $"0 0 0 {(i % 2 == 0 ? ".5" : ".4")}", statsPanel);
                UIWriteValues(statPanel, ref container, clan, page, i);
                CreateButton(ref container, "0 0", "1 1", "0 0 0 0", "0 0 0 0", " ", 15, $"cl_main display {9 * page + i}", statPanel);
                i++;
            }

            if (containerWasNull)
            {
                CuiHelper.DestroyUi(player, "CLAllStatsPanelOverlay");
                CuiHelper.AddUi(player, container);
            }
        }

        void UICurrentLeaders(BasePlayer player, CuiElementContainer container = null)
        {
            bool containerWasNull = container == null;
            if (containerWasNull) container = new CuiElementContainer();

            CreatePanel(ref container, "0 .725", "1 .885", _uiColors.MainPanelColor, "CLMainOverlay", "CLCurrentLeadersPanel");

            List<ClanPoints> topClans = GetTopClans(5);

            int i = 0;
            foreach (var clan in topClans)
            {
                UIInfo pos = GetUIPositions(i);
                var panel = CreatePanel(ref container, pos.Min, pos.Max, "0 0 0 0", "CLCurrentLeadersPanel");
                CreateSimpleLabel(ref container, "0 .75", "1 1", i <= 2 ? pos.Color : "1 1 1 1", $"#{i + 1}", pos.TextSize, TextAnchor.MiddleCenter, panel);

                var smallPanel = CreatePanel(ref container, "0 0", "1 .7", pos.BoxColor, panel);
                CreatePanel(ref container, "0 .96", ".997 1", pos.Color, smallPanel);

                CreateSimpleLabel(ref container, "0 .52", "1 1", "1 1 1 1", clan.Name.ToUpper(), 15, TextAnchor.LowerCenter, smallPanel);
                CreateSimpleLabel(ref container, "0 0", "1 .46", "1 1 1 1", $"{clan.Points} po.", 15, TextAnchor.UpperCenter, smallPanel);
                //CreateLabel(ref container, ".")

                i++;
            }

            if (containerWasNull)
            {
                CuiHelper.DestroyUi(player, "CLCurrentLeadersPanel");
                CuiHelper.AddUi(player, container);
            }
        }

        void UIClanStats(BasePlayer player, CuiElementContainer container = null)
        {
            bool containerWasNull = container == null;
            if (containerWasNull) container = new CuiElementContainer();

            CreatePanel(ref container, "0 .055", "1 .194", _uiColors.MainPanelColor, "CLMainOverlay", "CLClanStatsPanel");

            var labelPanel = CreatePanel(ref container, ".004 .575", ".995 .95", "0 0 0 .45", "CLClanStatsPanel");
            UIWriteTitles(labelPanel, ref container);

            ClanPoints clanPoints = new ClanPoints();
            int clanIndex = 0;
            string clanName = GetClanName(player);
            if (!string.IsNullOrEmpty(clanName))
            {
                clanIndex = _staticClanList.FindIndex(x => x.Name == clanName);
                if (clanIndex != -1) clanPoints = _staticClanList[clanIndex];
            }

            var statsPanel = CreatePanel(ref container, ".004 .05", ".995 .525", "0 0 0 .35", "CLClanStatsPanel");
            UIWriteValues(statsPanel, ref container, clanPoints, 0, clanIndex);

            if (containerWasNull)
            {
                CuiHelper.DestroyUi(player, "CLClanStatsPanel");
                CuiHelper.AddUi(player, container);
            }
        }

        void UpdatePageLabel(BasePlayer player)
        {
            var container = new CuiElementContainer();

            int page = GetPage(player.userID);
            var maxPage = (_staticClanList.Count - 1) / 10;
            CreateLabel(ref container, ".355 0", ".645 .995", "0 0 0 .5", "1 1 1 1", $"{page + 1} / {maxPage + 1}", 15, TextAnchor.MiddleCenter, "CLButtonsPanel", "CLPageLabel");

            CuiHelper.DestroyUi(player, "CLPageLabel");
            CuiHelper.AddUi(player, container);
        }

        void UIPageButtons(BasePlayer player, CuiElementContainer container = null)
        {
            bool containerWasNull = container == null;
            if (containerWasNull) container = new CuiElementContainer();

            var panel = CreatePanel(ref container, "0 0", "1 .049", "0 0 0 0", "CLMainOverlay", "CLButtonsPanel");

            int page = GetPage(player.userID);
            var maxPage = (_staticClanList.Count - 1) / 9;

            CreateButton(ref container, "0 0", ".35 .995", "0 0 0 .5", "1 1 1 1", "<", 15, maxPage > 0 ? $"cl_main page minus" : "", panel);
            CreateLabel(ref container, ".355 0", ".645 .995", "0 0 0 .5", "1 1 1 1", $"{page + 1} / {maxPage + 1}", 15, TextAnchor.MiddleCenter, "CLButtonsPanel", "CLPageLabel");
            CreateButton(ref container, ".65 0", ".997 .995", "0 0 0 .5", "1 1 1 1", ">", 15, maxPage > 0 ? $"cl_main page add" : "", panel);

            if (containerWasNull)
            {
                CuiHelper.DestroyUi(player, "CLButtonsPanel");
                CuiHelper.AddUi(player, container);
            }
        }

        void UIWriteValues(string panel, ref CuiElementContainer container, ClanPoints clanInfo, int page = 0, int idx = 0)
        {
            string[] values = { $"#{page * 10 + idx + 1}", String.IsNullOrEmpty(clanInfo.Name) ? "N/A" : clanInfo.Name.ToUpper(), $"{clanInfo.Points}", $"{clanInfo.Kills}", $"{clanInfo.Deaths}", $"{CalculateKDR(clanInfo.Kills, clanInfo.Deaths)}" };

            decimal panelLength = decimal.Divide(1, values.Length);

            for (int i = 0; i < values.Length; i++)
            {
                CreateSimpleLabel(ref container, $"{0 + (panelLength * i)} 0", $"{panelLength + (panelLength * i)} 1", "1 1 1 1", values[i], 15, TextAnchor.MiddleCenter, panel);
            }
        }

        void UIWriteTitles(string panel, ref CuiElementContainer container)
        {
            string[] titles = { "#", "CLAN", "POINTS", "KILLS", "DEATHS", "KDR" };

            decimal panelLength = decimal.Divide(1, titles.Length);

            for (int i = 0; i < titles.Length; i++)
            {
                CreateSimpleLabel(ref container, $"{0 + (panelLength * i)} 0", $"{panelLength + (panelLength * i)} 1", "1 1 1 1", titles[i], 15, TextAnchor.MiddleCenter, panel);
            }
        }

        UIInfo GetUIPositions(int i)
        {
            UIInfo pos = new UIInfo();
            switch (i)
            {
                case 0:
                    pos.Min = ".425 .05";
                    pos.Max = ".575 .95";
                    pos.Color = _uiColors.FirstPlaceColor;
                    pos.TextSize = 17;
                    pos.BoxColor = "0 0 0 .55";
                    break;
                case 1:
                    pos.Min = ".27 .05";
                    pos.Max = ".42 .9";
                    pos.Color = _uiColors.SecondPlaceColor;
                    pos.TextSize = 17;
                    pos.BoxColor = "0 0 0 .5";
                    break;
                case 2:
                    pos.Min = ".58 .05";
                    pos.Max = ".73 .85";
                    pos.Color = _uiColors.ThirdPlaceColor;
                    pos.TextSize = 16;
                    pos.BoxColor = "0 0 0 .45";
                    break;
                case 3:
                    pos.Min = ".115 .05";
                    pos.Max = ".265 .8";
                    pos.Color = _uiColors.FourthPlaceColor;
                    pos.TextSize = 15;
                    pos.BoxColor = "0 0 0 .4";
                    break;
                case 4:
                    pos.Min = ".735 .05";
                    pos.Max = ".885 .75";
                    pos.Color = _uiColors.FifthPlaceColor;
                    pos.TextSize = 15;
                    pos.BoxColor = "0 0 0 .4";
                    break;
            }

            return pos;
        }
        #endregion

        #region [ UI METHODS ]
        private static string CreatePanel(ref CuiElementContainer container, string anchorMin, string anchorMax, string panelColor, string parent = "Overlay", string panelName = null, bool blur = false, bool isMainPanel = false, string offsetMin = null, string offsetMax = null, float fadeInTime = 0f)
        {
            CuiPanel panel = new CuiPanel
            {
                RectTransform =
            {
                AnchorMin = anchorMin,
                AnchorMax = anchorMax
            },
                Image = { Color = panelColor }
            };

            if (offsetMax != null) panel.RectTransform.OffsetMax = offsetMax;
            if (offsetMax != null) panel.RectTransform.OffsetMin = offsetMin;
            if (fadeInTime != 0) panel.Image.FadeIn = fadeInTime;

            if (blur) panel.Image.Material = "assets/content/ui/uibackgroundblur.mat";
            if (isMainPanel) panel.CursorEnabled = true;
            return container.Add(panel, parent, panelName);
        }

        private static void CreateImagePanel(ref CuiElementContainer container, string anchorMin, string anchorMax, string panelImage, string parent = "Overlay", string panelName = null)
        {
            container.Add(new CuiElement
            {
                Parent = parent,
                Name = panelName,
                Components =
                {
                    new CuiRectTransformComponent
                    {
                        AnchorMin = anchorMin,
                        AnchorMax = anchorMax

                    },
                    new CuiRawImageComponent {Png = panelImage},
                }
            });
        }

        private static void CreateImageButton(ref CuiElementContainer container, string anchorMin, string anchorMax, string command, string panelImage, string parent = "Overlay", string panelName = null)
        {
            container.Add(new CuiElement
            {
                Parent = parent,
                Name = panelName,
                Components =
                {
                    new CuiRectTransformComponent
                    {
                        AnchorMin = anchorMin,
                        AnchorMax = anchorMax

                    },
                    new CuiRawImageComponent {Png = panelImage},
                }
            });

            container.Add(new CuiButton
            {
                RectTransform =
                {
                    AnchorMin = anchorMin,
                    AnchorMax = anchorMax
                },
                Button = { Color = "0 0 0 0", Command = $"{command}" }
            }, parent);
        }

        private static void CreateButton(ref CuiElementContainer container, string anchorMin, string anchorMax, string buttonColor, string textColor, string buttonText, int fontSize, string buttonCommand, string parent = "Overlay", TextAnchor labelAnchor = TextAnchor.MiddleCenter)
        {
            container.Add(new CuiButton
            {
                RectTransform =
                {
                    AnchorMin = anchorMin,
                    AnchorMax = anchorMax
                },
                Button = { Color = buttonColor, Command = $"{buttonCommand}" },
                Text = { Align = labelAnchor, Color = textColor, FontSize = fontSize, Text = buttonText },
            }, parent);
            return;
        }

        private static void CreateSimpleLabel(ref CuiElementContainer container, string anchorMin, string anchorMax, string textColor, string labelText, int fontSize, TextAnchor alignment, string parent = "Overlay")
        {
            container.Add(new CuiLabel
            {
                RectTransform =
                {
                    AnchorMin = anchorMin, AnchorMax = anchorMax
                },
                Text =
                {
                    Color = textColor,
                    Text = labelText,
                    Align = alignment,
                    FontSize = fontSize,
                    Font = "robotocondensed-bold.ttf"
                },
            }, parent);
        }

        private static string CreateLabel(ref CuiElementContainer container, string anchorMin, string anchorMax, string backgroundColor, string textColor, string labelText, int fontSize, TextAnchor alignment, string parent = "Overlay", string labelName = null)
        {
            var panel = container.Add(new CuiPanel
            {
                RectTransform =
                {
                    AnchorMin = anchorMin, AnchorMax = anchorMax
                },
                Image = { Color = backgroundColor }
            }, parent, labelName);

            container.Add(new CuiLabel
            {
                Text =
                {
                    Color = textColor,
                    Text = labelText,
                    Align = alignment,
                    FontSize = fontSize,
                    Font = "robotocondensed-bold.ttf"
                },
            }, panel);
            return panel;
        }
        #endregion

        #region API
        /// <summary>
        /// Award points to a clan for winning an event
        /// </summary>
        /// <param name="clanName">Name of the clan</param>
        /// <param name="eventType">Type of event (roam, maze, control, sulfur, koth)</param>
        /// <param name="customPoints">Optional custom points amount (uses config if null)</param>
        /// <returns>True if points were awarded successfully</returns>
        [HookMethod("API_AwardEventPoints")]
        public bool API_AwardEventPoints(string clanName, string eventType, double? customPoints = null)
        {
            if (string.IsNullOrEmpty(clanName) || string.IsNullOrEmpty(eventType))
            {
                PrintWarning($"[ClanCores API] Invalid parameters: clanName='{clanName}', eventType='{eventType}'");
                return false;
            }

            var clanInfo = GetClan(clanName);
            if (clanInfo == null)
            {
                PrintWarning($"[ClanCores API] Clan '{clanName}' not found");
                return false;
            }

            double pointsToAdd;
            if (customPoints.HasValue)
            {
                pointsToAdd = customPoints.Value;
            }
            else
            {
                pointsToAdd = GetEventPoints(eventType);
            }

            clanInfo.EventWins++;
            AlterPoints(clanInfo, pointsToAdd, true);

            // Send message to clan members
            string messageKey = $"{eventType}EventWin";
            foreach (var member in clanInfo.Members)
            {
                BasePlayer player = BasePlayer.FindByID(member.Key);
                if (player != null && player.IsConnected)
                {
                    SendReply(player, Lang(messageKey, player.UserIDString, pointsToAdd));
                }
            }

            Puts($"[ClanCores API] Clan '{clanName}' awarded {pointsToAdd} points for {eventType} event win");
            return true;
        }

        /// <summary>
        /// Award points to a clan by player ID
        /// </summary>
        /// <param name="playerId">Steam ID of a player in the clan</param>
        /// <param name="eventType">Type of event (roam, maze, control, sulfur, koth)</param>
        /// <param name="customPoints">Optional custom points amount (uses config if null)</param>
        /// <returns>True if points were awarded successfully</returns>
        [HookMethod("API_AwardEventPointsByPlayer")]
        public bool API_AwardEventPointsByPlayer(ulong playerId, string eventType, double? customPoints = null)
        {
            if (playerId == 0 || string.IsNullOrEmpty(eventType))
            {
                PrintWarning($"[ClanCores API] Invalid parameters: playerId='{playerId}', eventType='{eventType}'");
                return false;
            }

            BasePlayer player = BasePlayer.FindByID(playerId);
            if (player == null)
            {
                PrintWarning($"[ClanCores API] Player with ID '{playerId}' not found");
                return false;
            }

            string clanName = GetClanName(player);
            if (string.IsNullOrEmpty(clanName))
            {
                PrintWarning($"[ClanCores API] Player '{player.displayName}' is not in a clan");
                return false;
            }

            return API_AwardEventPoints(clanName, eventType, customPoints);
        }

        /// <summary>
        /// Award points to multiple clans by player IDs
        /// </summary>
        /// <param name="playerIds">List of Steam IDs</param>
        /// <param name="eventType">Type of event (roam, maze, control, sulfur, koth)</param>
        /// <param name="customPoints">Optional custom points amount (uses config if null)</param>
        /// <returns>Dictionary of clan names and whether points were awarded successfully</returns>
        [HookMethod("API_AwardEventPointsByPlayers")]
        public Dictionary<string, bool> API_AwardEventPointsByPlayers(List<ulong> playerIds, string eventType, double? customPoints = null)
        {
            var results = new Dictionary<string, bool>();

            if (playerIds == null || playerIds.Count == 0)
            {
                PrintWarning("[ClanCores API] No player IDs provided");
                return results;
            }

            var processedClans = new HashSet<string>();

            foreach (ulong playerId in playerIds)
            {
                BasePlayer player = BasePlayer.FindByID(playerId);
                if (player == null) continue;

                string clanName = GetClanName(player);
                if (string.IsNullOrEmpty(clanName)) continue;

                // Only award points once per clan
                if (processedClans.Contains(clanName)) continue;
                processedClans.Add(clanName);

                bool success = API_AwardEventPoints(clanName, eventType, customPoints);
                results[clanName] = success;
            }

            return results;
        }

        /// <summary>
        /// Get the configured points for an event type
        /// </summary>
        /// <param name="eventType">Type of event (roam, maze, control, sulfur, koth)</param>
        /// <returns>Points amount for the event type</returns>
        [HookMethod("API_GetEventPoints")]
        public double API_GetEventPoints(string eventType)
        {
            return GetEventPoints(eventType);
        }

        /// <summary>
        /// Get clan information by clan name
        /// </summary>
        /// <param name="clanName">Name of the clan</param>
        /// <returns>Dictionary with clan information or null if not found</returns>
        [HookMethod("API_GetClanInfo")]
        public Dictionary<string, object> API_GetClanInfo(string clanName)
        {
            var clanInfo = GetClan(clanName);
            if (clanInfo == null) return null;

            return new Dictionary<string, object>
            {
                ["Name"] = clanInfo.Name,
                ["Points"] = clanInfo.Points,
                ["TotalPointsLost"] = clanInfo.TotalPointsLost,
                ["Kills"] = clanInfo.Kills,
                ["Deaths"] = clanInfo.Deaths,
                ["EventWins"] = clanInfo.EventWins,
                ["HeliKills"] = clanInfo.HeliKills,
                ["BradKills"] = clanInfo.BradKills,
                ["LockedCrates"] = clanInfo.LockedCrates,
                ["TookTC"] = clanInfo.TookTC,
                ["LostTC"] = clanInfo.LostTC,
                ["HasTC"] = ValidTC(clanInfo),
                ["MemberCount"] = clanInfo.Members.Count
            };
        }

        /// <summary>
        /// Get clan information by player ID
        /// </summary>
        /// <param name="playerId">Steam ID of the player</param>
        /// <returns>Dictionary with clan information or null if player not in clan</returns>
        [HookMethod("API_GetClanInfoByPlayer")]
        public Dictionary<string, object> API_GetClanInfoByPlayer(ulong playerId)
        {
            BasePlayer player = BasePlayer.FindByID(playerId);
            if (player == null) return null;

            string clanName = GetClanName(player);
            if (string.IsNullOrEmpty(clanName)) return null;

            return API_GetClanInfo(clanName);
        }

        /// <summary>
        /// Check if a player is in a clan and has a valid TC
        /// </summary>
        /// <param name="playerId">Steam ID of the player</param>
        /// <returns>True if player is in a clan with a valid TC</returns>
        [HookMethod("API_IsPlayerEligibleForPoints")]
        public bool API_IsPlayerEligibleForPoints(ulong playerId)
        {
            BasePlayer player = BasePlayer.FindByID(playerId);
            if (player == null) return false;

            string clanName = GetClanName(player);
            if (string.IsNullOrEmpty(clanName)) return false;

            var clanInfo = GetClan(clanName);
            return clanInfo != null && ValidTC(clanInfo);
        }

        /// <summary>
        /// Get all clans with their basic information
        /// </summary>
        /// <returns>List of clan information dictionaries</returns>
        [HookMethod("API_GetAllClans")]
        public List<Dictionary<string, object>> API_GetAllClans()
        {
            var result = new List<Dictionary<string, object>>();

            foreach (var clan in _staticClanList)
            {
                result.Add(new Dictionary<string, object>
                {
                    ["Name"] = clan.Name,
                    ["Points"] = clan.Points,
                    ["EventWins"] = clan.EventWins,
                    ["MemberCount"] = clan.Members.Count,
                    ["HasTC"] = ValidTC(clan)
                });
            }

            return result.OrderByDescending(c => (double)c["Points"]).ToList();
        }

        /// <summary>
        /// Helper method to get event points based on type
        /// </summary>
        private double GetEventPoints(string eventType)
        {
            switch (eventType.ToLower())
            {
                case "roam":
                    return _config.PointsGainClass.RoamEventPoints;
                case "maze":
                    return _config.PointsGainClass.MazeEventPoints;
                case "control":
                    return _config.PointsGainClass.ControlEventPoints;
                case "sulfur":
                    return _config.PointsGainClass.SulfurEventPoints;
                case "koth":
                    return _config.PointsGainClass.KOTHEventPoints;
                default:
                    return _config.PointsGainClass.EventPoints;
            }
        }
        #endregion
    }
}
 