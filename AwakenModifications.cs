// Reference: 0Harmony
using HarmonyLib;
using Network;
using Newtonsoft.Json;
using Oxide.Core.Libraries.Covalence;
using Rust.Ai;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using UnityEngine;
using UnityEngine.AI;

namespace Oxide.Plugins
{
    [Info("Awaken Modifications", "Skelee", "1.4.1")]
    [Description("All modifications for Awaken Servers - Updated for May 2025 Rust API with configurable remove command limits and improved Harmony patches.")]
    public class AwakenModifications : CovalencePlugin
    {
        #region Defines
        private static AwakenModifications? Instance;
        private static Harmony? _harmony;
        public Dictionary<string, float> npcCooldowns = new();
        public Dictionary<string, float> removeCooldowns = new();
        public Dictionary<string, int> removeEntityCounts = new(); // Track entities removed per player
        public Translate.Phrase inventoryFullPhrase = new("inventoryfull", "Your inventory is full, purchase canceled.");
        private Dictionary<int, int> defaultBlueprints = new(); // Store original workbench requirements
        public class RespawnItem
        {
            public string shortname = string.Empty;
            public int amount = 0;
            public ulong skin = 0;
        }
        #endregion

        #region Config
        public static Configuration? config;
        public class Configuration
        {
            [JsonProperty(PropertyName = "Map Name")] public string mapName;
            [JsonProperty(PropertyName = "Use Recycler Speed")] public bool useRecyclerSpeed;
            [JsonProperty(PropertyName = "Recycler Speed")] public float recyclerSpeed;
            [JsonProperty(PropertyName = "Airdrop Airplane Time (Secs)")] public float planeTime;
            [JsonProperty(PropertyName = "Airdrop Drag (Less Drag = Faster)")] public float dragTime;
            [JsonProperty(PropertyName = "Use No Puzzle Reset")] public bool useNoPuzzleReset;
            [JsonProperty(PropertyName = "Use No Heat and Cold")] public bool useNoHeatCold;
            [JsonProperty(PropertyName = "Use NPC Vehicle Shop Cooldowns")] public bool useVehicleShopCooldowns;
            [JsonProperty(PropertyName = "Use Quick Backpack Despawns")] public bool useQuickBackpackDespawns;
            [JsonProperty(PropertyName = "Quick Despawn Time (Secs)")] public float quickDespawnTime;
            [JsonProperty(PropertyName = "Normal Despawn Time (Secs)")] public float normalDespawnTime;
            [JsonProperty(PropertyName = "Use Instant Vending Buy")] public bool useInstantVendingBuy;
            [JsonProperty(PropertyName = "Use Vending Drop")] public bool useVendingDrop;
            [JsonProperty(PropertyName = "Use Always In Stock")] public bool useAlwaysInStock;
            [JsonProperty(PropertyName = "Use Custom Rocket Damage")] public bool useCustomRocketDamage;
            [JsonProperty(PropertyName = "Use No Weapon Drop")] public bool useNoWeaponDrop;
            [JsonProperty(PropertyName = "Use Optimized Airdrop")] public bool useOptimizedAirdrop;
            [JsonProperty(PropertyName = "Use Always Bonus")] public bool useAlwaysBonus;
            [JsonProperty(PropertyName = "Use Remove Command")] public bool useRemoveCommand;
            [JsonProperty(PropertyName = "Remove Command Max Entities")] public int removeCommandMaxEntities;
            [JsonProperty(PropertyName = "Remove Command Cooldown (Minutes)")] public int removeCommandCooldownMinutes;
            [JsonProperty(PropertyName = "Use Radiation Adjustment")] public bool useRadiationAdjustment;
            [JsonProperty(PropertyName = "Use No Workbench")] public bool useNoWorkbench;
            [JsonProperty(PropertyName = "Use No Animal Movement")] public bool useNoAnimalMovement;
            [JsonProperty(PropertyName = "Use No Terrain Violation")] public bool useNoTerrainViolation;
            [JsonProperty(PropertyName = "Use Modified SAM Site Range")] public bool useSamSiteRange;
            [JsonProperty(PropertyName = "Use Custom Respawn Items")] public bool useCustomRespawnItems;
            [JsonProperty(PropertyName = "Custom Respawn Items")] public List<RespawnItem> customRespawnItems;
            [JsonProperty(PropertyName = "Use Fast Despawn")] public bool useFastDespawn;
            [JsonProperty(PropertyName = "Fast Despawn List")] public List<string> fastDespawnList;
            [JsonProperty(PropertyName = "Use Full Stack Recycle")] public bool useFullStackRecycle;
            [JsonProperty(PropertyName = "Use Save Announcer")] public bool useSaveAnnouncer;
            [JsonProperty(PropertyName = "Use Metabolism Adjustment")] public bool useMetabolismAdjustment;
            [JsonProperty(PropertyName = "Starting Health")] public float startingHealth;
            [JsonProperty(PropertyName = "Starting Thirst")] public float startingThirst;
            [JsonProperty(PropertyName = "Starting Hunger")] public float startingHunger;
            [JsonProperty(PropertyName = "Use Fast Barrels")] public bool useFastBarrels;
            [JsonProperty(PropertyName = "Use Custom Heli Fuel")] public bool useCustomHeliFuel;
            [JsonProperty(PropertyName = "Heli Fuel Amount")] public int fuelAmount;
            [JsonProperty(PropertyName = "Use Discord Logging")] public bool useDiscordLogging;
            [JsonProperty(PropertyName = "Discord Webhook URL")] public string discordWebhook;

            public static Configuration DefaultConfig()
            {
                return new Configuration
                {
                    mapName = "Awaken Rust Maps",
                    useRecyclerSpeed = true, // Enabled - 1.5x faster recycling
                    recyclerSpeed = 1.5f,
                    planeTime = 400f, // Airplane speed - LOWER = FASTER plane (default: 270f, faster: 400f+)
                    dragTime = 0.6f, // Crate fall speed - LOWER = FASTER falling
                    useNoPuzzleReset = true,
                    useNoHeatCold = true,
                    useVehicleShopCooldowns = true,
                    useQuickBackpackDespawns = true,
                    quickDespawnTime = 30f,
                    normalDespawnTime = 1200f,
                    useInstantVendingBuy = true,
                    useVendingDrop = true,
                    useAlwaysInStock = true,
                    useCustomRocketDamage = true,
                    useNoWeaponDrop = false,
                    useOptimizedAirdrop = true,
                    useAlwaysBonus = false,
                    useRemoveCommand = false,
                    removeCommandMaxEntities = 5,
                    removeCommandCooldownMinutes = 15,
                    useRadiationAdjustment = false,
                    useNoWorkbench = true,
                    useNoAnimalMovement = true,
                    useNoTerrainViolation = true,
                    useSamSiteRange = true,
                    useCustomRespawnItems = false,
                    customRespawnItems = new List<RespawnItem>
                    {
                        new RespawnItem
                        {
                            shortname = "icepick.salvaged",
                            amount = 1
                        },
                        new RespawnItem
                        {
                            shortname = "axe.salvaged",
                            amount = 1
                        },
                        new RespawnItem
                        {
                            shortname = "blueberries",
                            amount = 3
                        }
                    },
                    useFastDespawn = true,
                    fastDespawnList = new List<string>
                    {
                        "rock",
                        "torch"
                    },
                    useFullStackRecycle = true, // Enabled - recycle full stacks at once
                    useSaveAnnouncer = true,
                    useMetabolismAdjustment = false,
                    startingHealth = 80,
                    startingHunger = 500,
                    startingThirst = 250,
                    useFastBarrels = false,
                    useCustomHeliFuel = false,
                    fuelAmount = 250,
                    useDiscordLogging = false,
                    discordWebhook = "REPLACE_WITH_YOUR_DISCORD_WEBHOOK_URL"
                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = Configuration.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Hooks
       void Loaded()
        {
            if (_harmony == null) _harmony = new Harmony("com.Skelee.AwakenModifications");
            Instance = this;

            #region No Heat and Cold
            if (config.useNoHeatCold)
            {
                foreach (BasePlayer player in BasePlayer.activePlayerList)
                {
                    player.metabolism.temperature.min = 20;
                    player.metabolism.temperature.max = 20;
                    player.metabolism.isDirty = true;
                    player.metabolism.SendChangesToClient();
                }
            }
            #endregion

            #region Recycler Speed
            if (config.useRecyclerSpeed)
            {
                try
                {
                    // Use a simpler approach - patch the StartRecycling method directly
                    var startRecyclingMethod = AccessTools.Method(typeof(Recycler), "StartRecycling");
                    if (startRecyclingMethod != null)
                    {
                        _harmony.Patch(startRecyclingMethod,
                            prefix: new HarmonyMethod(typeof(RecyclerSpeedPatch), "StartRecyclingPrefix"),
                            postfix: new HarmonyMethod(typeof(RecyclerSpeedPatch), "StartRecyclingPostfix"));
                        Puts("[Awaken Modifications] Recycler Speed patch applied successfully to StartRecycling.");
                    }
                    else
                    {
                        PrintWarning("[Awaken Modifications] StartRecycling method not found, trying alternative approaches.");
                    }

                    // Also try to patch RecycleThink for ongoing recycling
                    var recycleThinkMethod = AccessTools.Method(typeof(Recycler), "RecycleThink");
                    if (recycleThinkMethod != null)
                    {
                        _harmony.Patch(recycleThinkMethod, prefix: new HarmonyMethod(typeof(RecyclerSpeedPatch), "RecycleThinkPrefix"));
                        Puts("[Awaken Modifications] Recycler Speed patch applied successfully to RecycleThink.");
                    }
                }
                catch (Exception ex)
                {
                    PrintError($"[Awaken Modifications] Recycler Speed patch failed: {ex.Message}");
                }
            }
            else
            {
                Puts("[Awaken Modifications] Recycler Speed modification is disabled - using normal recycle speeds.");
            }
            #endregion

            #region Puzzle Reset
            if (config.useNoPuzzleReset) _harmony.Patch(AccessTools.Method(typeof(PuzzleReset), "Start"), new HarmonyMethod(typeof(PuzzleResetRemovall), "Prefix"));
            #endregion

            #region Custom Map Name
            _harmony.Patch(AccessTools.Method(typeof(ServerMgr), "UpdateServerInformation"), transpiler: new HarmonyMethod(typeof(CustomMapName), "Transpiler"));
            #endregion

            #region Optimized Airdrop
            if (config.useOptimizedAirdrop) _harmony.Patch(AccessTools.Method(typeof(SupplySignal), "Explode"), transpiler: new HarmonyMethod(typeof(OptimizedAirdrop), "Transpiler"));
            #endregion

            #region Always Bonus
            if (config.useAlwaysBonus)
            {
                _harmony.Patch(AccessTools.Method(typeof(OreResourceEntity), "OnAttacked"), transpiler: new HarmonyMethod(typeof(AlwaysBonus), "Transpiler"));
                _harmony.Patch(AccessTools.Method(typeof(TreeEntity), "DidHitMarker"), new HarmonyMethod(typeof(AlwaysBonusTree), "Prefix"));
            }
            #endregion

            #region No Give Notices
            _harmony.Patch(AccessTools.Method(typeof(ConVar.Chat), "Broadcast"), new HarmonyMethod(typeof(NoGiveNotices), "Prefix"));
            #endregion

            #region No Active Item Drop
            if (config.useNoWeaponDrop) _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "ShouldDropActiveItem"), new HarmonyMethod(typeof(NoWeaponDrop), "Prefix"));
            #endregion

            #region Custom Icon
            _harmony.Patch(AccessTools.Method(typeof(ConsoleNetwork), "BroadcastToAllClients"), new HarmonyMethod(typeof(CustomIcon), "Prefix"));
            _harmony.Patch(AccessTools.Method(typeof(ConsoleNetwork), "SendClientCommand", new[] { typeof(Connection), typeof(string), typeof(object[]) }), new HarmonyMethod(typeof(CustomIcon), "Prefix"));
            _harmony.Patch(AccessTools.Method(typeof(ConsoleNetwork), "SendClientCommand", new[] { typeof(List<Connection>), typeof(string), typeof(object[]) }), new HarmonyMethod(typeof(CustomIcon), "Prefix"));
            #endregion

            #region Instant Vending Buy
            if (config.useInstantVendingBuy) _harmony.Patch(AccessTools.Method(typeof(VendingMachine), "BuyItem"), new HarmonyMethod(typeof(InstantVending), "Prefix"));
            #endregion

            #region Instant Vending Buy
            if (config.useVendingDrop) _harmony.Patch(AccessTools.Method(typeof(VendingMachine), "ServerInit"), transpiler: new HarmonyMethod(typeof(VendingDrop), "Transpiler"));
            #endregion

            #region Better Say
            _harmony.Patch(AccessTools.Method(typeof(ConVar.Chat), "Broadcast"), transpiler: new HarmonyMethod(typeof(BetterSay), "Transpiler"));
            #endregion

            #region No Workbench
            // No Workbench is now handled in OnServerInitialized using the proven approach
            #endregion

            #region Remove Command
            if (config.useRemoveCommand) AddCovalenceCommand("remove", "RemoveCMD");
            #endregion

            #region Block Commands
            AddCovalenceCommand("block", "BlockCMD");
            AddCovalenceCommand("blocked", "BlockCMD");
            AddCovalenceCommand("blockdebug", "BlockDebugCMD");
            #endregion

            #region Airdrop Modify
            try
            {
                var updateDropPositionMethod = AccessTools.Method(typeof(CargoPlane), "UpdateDropPosition");
                if (updateDropPositionMethod != null)
                {
                    _harmony.Patch(updateDropPositionMethod, postfix: new HarmonyMethod(typeof(AirdropModify), "Postfix"));
                }
                else
                {
                    PrintWarning("[Awaken Modifications] CargoPlane.UpdateDropPosition method not found.");
                }

                // Removed CargoPlane.Update patch - it was causing severe FPS drops
                // Airdrop crate speed is now handled efficiently in OnEntitySpawned
            }
            catch (Exception ex)
            {
                PrintError($"[Awaken Modifications] Airdrop speed patches failed: {ex.Message}");
            }
            #endregion

            #region No Animal Movement
            if (config.useNoAnimalMovement)
            {
                _harmony.Patch(AccessTools.Method(typeof(BaseNpc), "ServerInit"), transpiler: new HarmonyMethod(typeof(RemoveTickAi), "Transpiler"));
                _harmony.Patch(AccessTools.Method(typeof(BaseAnimalNPC), "ServerInit"), transpiler: new HarmonyMethod(typeof(RemoveAiThinkManager), "Transpiler"));
            }
            #endregion

            #region Quick Backpack Despawns
            if (config.useQuickBackpackDespawns)
            {
                _harmony.Patch(AccessTools.Method(typeof(DroppedItemContainer), "CalculateRemovalTime"), new HarmonyMethod(typeof(QuickBackpackDespawn), "Prefix"));
                _harmony.Patch(AccessTools.Method(typeof(DroppedItemContainer), "PlayerStoppedLooting"), transpiler: new HarmonyMethod(typeof(StopReset), "Transpiler"));
            }
            #endregion

            #region Always In Stock Vendings
            if (config.useAlwaysInStock) _harmony.Patch(AccessTools.Method(typeof(VendingMachine), "DoTransaction"), transpiler: new HarmonyMethod(typeof(AlwaysInStock), "Transpiler"));
            #endregion

            #region No Terrain Violation
            if (config.useNoTerrainViolation) _harmony.Patch(AccessTools.Method(typeof(AntiHack), "AddViolation"), new HarmonyMethod(typeof(NoTerrainViolation), "Prefix"));
            #endregion

            #region Sam Site Range
            if (config.useSamSiteRange) _harmony.Patch(AccessTools.Method(typeof(SamSite), "ServerInit"), new HarmonyMethod(typeof(SamSiteRange), "Prefix"));
            #endregion

            #region Custom Respawn Items
            if (config.useCustomRespawnItems) _harmony.Patch(AccessTools.Method(typeof(PlayerInventory), "GiveDefaultItems"), transpiler: new HarmonyMethod(typeof(CustomItems), "Transpiler"));
            #endregion

            #region Full Stack Recycle
            if (config.useFullStackRecycle)
            {
                try
                {
                    var method = AccessTools.Method(typeof(Recycler), "RecycleThink");
                    if (method != null)
                    {
                        _harmony.Patch(method, prefix: new HarmonyMethod(typeof(FullStackRecyclePatch), "RecycleThinkPrefix"));
                        Puts("[Awaken Modifications] Full Stack Recycle patch applied successfully.");
                    }
                    else
                    {
                        PrintWarning("[Awaken Modifications] Full Stack Recycle patch failed - RecycleThink method not found.");
                    }
                }
                catch (Exception ex)
                {
                    PrintError($"[Awaken Modifications] Full Stack Recycle patch failed: {ex.Message}");
                }
            }
            #endregion

            #region Save Announcer
            if (config.useSaveAnnouncer) _harmony.Patch(AccessTools.Method(typeof(SaveRestore), "DoAutomatedSave"), transpiler: new HarmonyMethod(typeof(SaveAnnouncer), "Transpiler"));
            #endregion

            #region Metabolism Adjustment
            _harmony.Patch(AccessTools.Method(typeof(BasePlayer), "RespawnAt"), transpiler: new HarmonyMethod(typeof(MetabolismAdjustment), "Transpiler"));
            #endregion

            #region Fast Barrels
            if (config.useFastBarrels)
            {
                try
                {
                    // Be more specific about the method signature to avoid ambiguity
                    var method = AccessTools.Method(typeof(LootContainer), "OnAttacked", new Type[] { typeof(HitInfo) });
                    if (method != null)
                    {
                        _harmony.Patch(method, transpiler: new HarmonyMethod(typeof(FastBarrels), "Transpiler"));
                        Puts("[Awaken Modifications] Fast Barrels patch applied successfully.");
                    }
                    else
                    {
                        PrintWarning("[Awaken Modifications] Fast Barrels patch failed - LootContainer.OnAttacked(HitInfo) method not found.");
                    }
                }
                catch (Exception ex)
                {
                    PrintError($"[Awaken Modifications] Fast Barrels patch failed: {ex.Message}");
                }
            }
            #endregion

            #region Entity Spawned Hook Management
            // We need OnEntitySpawned for helicopter fuel OR airdrop speed
            if (!config.useCustomHeliFuel && config?.dragTime == 0.6f) // Only unsubscribe if both features are disabled
            {
                Unsubscribe("OnEntitySpawned");
            }
            #endregion
        }

        private void Unload()
        {
            try
            {
                _harmony?.UnpatchAll("com.Skelee.AwakenModifications");
                npcCooldowns.Clear();
                removeCooldowns.Clear();
                removeEntityCounts.Clear();
                if (config?.useNoHeatCold == true) UpdatePlayerTemperatures(-100f, 100f);

                // Restore original workbench requirements if No Workbench was enabled
                if (config?.useNoWorkbench == true && defaultBlueprints?.Count > 0)
                {
                    foreach (ItemBlueprint bp in ItemManager.GetBlueprints())
                    {
                        if (defaultBlueprints.ContainsKey(bp.targetItem.itemid))
                            bp.workbenchLevelRequired = defaultBlueprints[bp.targetItem.itemid];
                    }
                    Puts("[Awaken Modifications] Restored original workbench requirements.");
                }

                // Clean up all active remove tools
                foreach (var player in BasePlayer.activePlayerList)
                {
                    var removeTool = player?.GetComponent<RemoveToolHandler>();
                    if (removeTool != null)
                    {
                        removeTool.DeactivateTool();
                    }
                }

                // Clean up instance reference
                Instance = null;

                Puts("[Awaken Modifications] Plugin unloaded successfully.");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error during unload: {ex.Message}");
            }
        }

        private void OnServerInitialized()
        {
            try
            {
                // No Workbench implementation using the proven approach from NoWorkbench.cs
                if (config?.useNoWorkbench == true)
                {
                    try
                    {
                        // Store original workbench requirements for restoration on unload
                        defaultBlueprints = ItemManager.GetBlueprints().ToDictionary(x => x.targetItem.itemid, y => y.workbenchLevelRequired);

                        // Set all blueprint workbench requirements to 0 (no workbench needed)
                        foreach (ItemBlueprint bp in ItemManager.GetBlueprints())
                            bp.workbenchLevelRequired = 0;

                        // Apply to all currently connected players
                        foreach (BasePlayer player in BasePlayer.activePlayerList)
                            SetPlayerNoWorkbench(player);

                        Puts("[Awaken Modifications] No Workbench system enabled - all blueprints set to require no workbench.");
                    }
                    catch (Exception ex)
                    {
                        PrintError($"[Awaken Modifications] No Workbench initialization failed: {ex.Message}");
                    }
                }

                if (config?.useRadiationAdjustment == true)
                {
                    timer.Once(10f, () =>
                    {
                        try
                        {
                            var launchSite = TerrainMeta.Path.Monuments.FirstOrDefault(info => info.displayPhrase.english == "Launch Site");

                            // Use a more efficient approach to find radiation triggers
                            // Instead of FindObjectsOfType, we'll search through existing entities
                            var radiationTriggers = new List<TriggerRadiation>();

                            // Search through all entities in the world more efficiently
                            // TriggerRadiation inherits from Trigger, so we search for those and filter
                            foreach (var entity in BaseNetworkable.serverEntities)
                            {
                                try
                                {
                                    // Use reflection to safely check if this is a TriggerRadiation
                                    if (entity != null && entity.GetType().Name == "TriggerRadiation")
                                    {
                                        // Use reflection to safely cast
                                        var radiation = entity.GetComponent<TriggerRadiation>();
                                        if (radiation != null)
                                            radiationTriggers.Add(radiation);
                                    }
                                }
                                catch (Exception)
                                {
                                    // Skip entities that can't be processed
                                    continue;
                                }
                            }

                            int processedCount = 0;
                            int destroyedCount = 0;
                            int modifiedCount = 0;

                            foreach (var radiation in radiationTriggers)
                            {
                                if (radiation == null || radiation.gameObject == null) continue;

                                processedCount++;

                                if (launchSite != null && Vector3.Distance(launchSite.transform.position, radiation.transform.position) > 300 ||
                                    radiation.transform.position.y <= 50)
                                {
                                    UnityEngine.Object.Destroy(radiation);
                                    destroyedCount++;
                                }
                                else
                                {
                                    radiation.RadiationAmountOverride = Vector3.Distance(radiation.transform.position, launchSite.transform.position) > 200 ? 19f : 80f;
                                    modifiedCount++;
                                }
                            }

                            Puts($"[Awaken Modifications] Radiation adjustment completed. Processed: {processedCount}, Destroyed: {destroyedCount}, Modified: {modifiedCount}");
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Modifications] Error during radiation adjustment: {ex.Message}");
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnServerInitialized: {ex.Message}");
            }
        }

        private void OnPlayerConnected(BasePlayer player)
        {
            try
            {
                if (config?.useNoWorkbench == true)
                {
                    SetPlayerNoWorkbench(player);
                }
                if (config?.useNoHeatCold == true) UpdatePlayerTemperature(player, 20f, 20f);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnPlayerConnected: {ex.Message}");
            }
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            try
            {
                // Clean up remove tool component when player disconnects
                var removeTool = player?.GetComponent<RemoveToolHandler>();
                if (removeTool != null)
                {
                    removeTool.DeactivateTool();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnPlayerDisconnected: {ex.Message}");
            }
        }

        private void OnPlayerInput(BasePlayer player, InputState input)
        {
            try
            {
                if (player == null || input == null) return;

                // Check if player has the remove tool component
                var removeTool = player.GetComponent<RemoveToolHandler>();
                if (removeTool != null)
                {
                    removeTool.HandlePlayerInput(input);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnPlayerInput: {ex.Message}");
            }
        }

        private void OnActiveItemChanged(BasePlayer player, Item oldItem, ItemId newItemId)
        {
            try
            {
                if (player == null) return;

                // Check if player has the remove tool component and they selected an item
                var removeTool = player.GetComponent<RemoveToolHandler>();
                if (removeTool != null && newItemId != new ItemId())
                {
                    // Player selected an item, disable the remove tool
                    removeTool.DeactivateTool();
                    player.ChatMessage("<color=#ff9900>Removal tool disabled - item selected.</color>");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnActiveItemChanged: {ex.Message}");
            }
        }

        private void OnEntitySpawned(BaseNetworkable entity)
        {
            try
            {
                // Handle helicopter fuel (only if enabled)
                if (config?.useCustomHeliFuel == true && entity is BaseVehicle vehicle && vehicle.ShortPrefabName.Contains("minicopter") && vehicle.creatorEntity != null)
                {
                    NextTick(() =>
                    {
                        try
                        {
                            var fuelsystem = vehicle.GetComponent<EntityFuelSystem>();
                            if (fuelsystem != null)
                            {
                                var fuelItem = fuelsystem.GetFuelItem();
                                if (fuelItem != null && fuelItem.amount != config?.fuelAmount)
                                {
                                    fuelItem.amount = config?.fuelAmount ?? 250;
                                    fuelItem.MarkDirty();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Modifications] Error in helicopter fuel modification: {ex.Message}");
                        }
                    });
                }

                // Handle airdrop speed (immediate, no NextTick to avoid lag)
                if (entity is SupplyDrop supplyDrop)
                {
                    var rigidbody = supplyDrop.GetComponent<Rigidbody>();
                    if (rigidbody != null)
                    {
                        // Apply custom drag to make airdrops fall faster
                        rigidbody.drag = config?.dragTime ?? 0.6f;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnEntitySpawned: {ex.Message}");
            }
        }

        private object? OnNpcConversationStart(NPCTalking npcTalking, BasePlayer player, ConversationData conversationData)
        {
            if (config?.useVehicleShopCooldowns != true) return null;

            if (npcCooldowns.TryGetValue(npcTalking.UserIDString, out var time) && Time.time - time < 30)
            {
                player.ChatMessage("This NPC has recently sold a helicopter, the NPC needs 30 second(s) to restock.");
                return true;
            }

            npcCooldowns.Remove(npcTalking.UserIDString);
            return null;
        }

        private object? OnNpcConversationRespond(NPCTalking npcTalking, BasePlayer player, ConversationData conversationData, ConversationData.ResponseNode responseNode)
        {
            try
            {
                if (config?.useVehicleShopCooldowns != true) return null;
                if (responseNode.actionString == "buyminicopter" || responseNode.actionString == "buytransport")
                {
                    npcCooldowns.TryAdd(npcTalking.UserIDString, Time.time);
                    var otherPlayers = npcTalking.conversingPlayers.Where(p => p != player).ToList();
                    foreach (var p in otherPlayers)
                        npcTalking.ForceEndConversation(p);
                    return null;
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnNpcConversationRespond: {ex.Message}");
                return null;
            }
        }

        private object? OnEntityTakeDamage(BasePlayer victim, HitInfo info)
        {
            try
            {
                if (config?.useCustomRocketDamage != true || victim == null || info?.InitiatorPlayer?.GetActiveItem()?.GetHeldEntity() is not BaseProjectile weapon ||
                    !weapon.primaryMagazine.ammoType.itemid.Equals(-1841918730)) return null;

                info.damageTypes.Clear();
                var wear = victim.inventory.containerWear;
                bool hasHeavyArmor = wear.itemList.Any(item => item.info.shortname == "heavy.plate.pants" ||
                                                              item.info.shortname == "heavy.plate.jacket" ||
                                                              item.info.shortname == "heavy.plate.helmet");
                bool hasRegularArmor = wear.itemList.Any(item => armor.Contains(item.info.shortname));
                float damage = hasHeavyArmor ? 40f : hasRegularArmor ? 80f : 100f;
                victim.Hurt(damage);
                return null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in OnEntityTakeDamage: {ex.Message}");
                return null;
            }
        }

        // New hook for the May 2025 Rust API to handle server saves
        private void OnServerSave()
        {
            try
            {
                // Clean up expired cooldowns to prevent memory leaks
                CleanupExpiredCooldowns();

                // Log cleanup for debugging
                if (BasePlayer.activePlayerList.Any(p => p.IsAdmin))
                {
                    Puts("[Awaken Modifications] Cleaned up expired cooldowns during server save.");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error during OnServerSave: {ex.Message}");
            }
        }

        // New method to clean up expired cooldowns
        private void CleanupExpiredCooldowns()
        {
            try
            {
                int npcRemoved = CleanupCooldownDictionary(npcCooldowns, 30);
                int removeRemoved = CleanupCooldownDictionary(removeCooldowns, config?.removeCommandCooldownMinutes * 60 ?? 900);
                int entityCountsRemoved = CleanupEntityCountDictionary();

                int totalRemoved = npcRemoved + removeRemoved + entityCountsRemoved;
                if (totalRemoved > 0)
                {
                    Debug.Log($"[Awaken Modifications] Cleaned up {totalRemoved} expired cooldowns " +
                              $"(NPC: {npcRemoved}, Remove: {removeRemoved}, EntityCounts: {entityCountsRemoved})");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error cleaning up cooldowns: {ex.Message}");
            }
        }

        // Helper method to clean up a specific cooldown dictionary
        private int CleanupCooldownDictionary(Dictionary<string, float> cooldowns, int cooldownTime)
        {
            if (cooldowns == null || cooldowns.Count == 0) return 0;

            var keysToRemove = cooldowns
                .Where(kvp => Time.time - kvp.Value >= cooldownTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in keysToRemove)
                cooldowns.Remove(key);

            return keysToRemove.Count;
        }

        // Helper method to clean up entity count dictionary when cooldowns expire
        private int CleanupEntityCountDictionary()
        {
            if (removeEntityCounts == null || removeEntityCounts.Count == 0) return 0;

            var keysToRemove = removeEntityCounts.Keys
                .Where(key => !removeCooldowns.ContainsKey(key))
                .ToList();

            foreach (var key in keysToRemove)
                removeEntityCounts.Remove(key);

            return keysToRemove.Count;
        }

        // Helper method to use the new thread-safe network update method introduced in 2025
        private void SendNetworkUpdateSafe(BaseNetworkable entity)
        {
            try
            {
                // This method uses reflection to call the new method if it exists
                // This approach allows the plugin to work with both old and new Rust versions
                var method = entity.GetType().GetMethod("SendNetworkUpdateSafe");
                if (method != null)
                    method.Invoke(entity, null);
                else
                    entity.SendNetworkUpdate();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in SendNetworkUpdateSafe: {ex.Message}");
                // Fallback to regular network update
                try
                {
                    entity.SendNetworkUpdate();
                }
                catch (Exception fallbackEx)
                {
                    Debug.LogError($"[Awaken Modifications] Fallback network update also failed: {fallbackEx.Message}");
                }
            }
        }
        #endregion

        #region Harmony
        private static class PuzzleResetRemovall { internal static bool Prefix() => false; }

        private static class RecyclerSpeedPatch
        {
            internal static bool StartRecyclingPrefix(Recycler __instance)
            {
                try
                {
                    if (config?.useRecyclerSpeed != true) return true; // Let original method run

                    // Let the original method run first to set up proper state
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] RecyclerSpeedPatch.StartRecyclingPrefix error: {ex.Message}");
                    return true; // Let original method run on error
                }
            }

            internal static void StartRecyclingPostfix(Recycler __instance)
            {
                try
                {
                    if (config?.useRecyclerSpeed != true) return;

                    // Cancel the original invoke and set up our own with modified timing
                    __instance.CancelInvoke(__instance.RecycleThink);

                    // Calculate the modified recycle time
                    float originalTime = 5f; // Default Rust recycle time
                    float modifiedTime = originalTime / (config?.recyclerSpeed ?? 1.5f);

                    // Start our modified recycling process
                    __instance.InvokeRepeating(__instance.RecycleThink, modifiedTime, modifiedTime);

                    Debug.Log($"[Awaken Modifications] Recycler speed modified: {originalTime}s -> {modifiedTime}s (speed: {config?.recyclerSpeed ?? 1.5f}x)");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] RecyclerSpeedPatch.StartRecyclingPostfix error: {ex.Message}");
                }
            }

            internal static bool RecycleThinkPrefix(Recycler __instance)
            {
                try
                {
                    if (config?.useRecyclerSpeed != true) return true; // Let original method run

                    // Let the original RecycleThink run, but we've already modified the timing in StartRecycling
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] RecyclerSpeedPatch.RecycleThinkPrefix error: {ex.Message}");
                    return true; // Let original method run on error
                }
            }
        }

        private static class CustomMapName
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                var mapNameLine = list.FindLastIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "stok");
                if (mapNameLine != -1 && mapNameLine >= 2)
                {
                    list[mapNameLine - 2] = new CodeInstruction(OpCodes.Ldstr, config?.mapName ?? "Awaken Rust Maps");
                }
                return list;
            }
        }

        private static class OptimizedAirdrop
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                var killTimeLine = list.FindLastIndex(i => i.opcode == OpCodes.Ldc_R4 && (float)i.operand == 210);
                if (killTimeLine != -1)
                {
                    list[killTimeLine].operand = 5f;
                }
                return list;
            }
        }

        private static class AlwaysBonus
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                var instanceLine = list.FindLastIndex(i => i.opcode == OpCodes.Isinst);
                if (instanceLine != -1)
                {
                    list[instanceLine].operand = typeof(BaseMelee);
                }
                return list;
            }
        }

        private static class AlwaysBonusTree { internal static bool Prefix(ref bool __result) => (__result = true) == true && false; }

        private static class NoGiveNotices
        {
            internal static bool Prefix(string message, string username) =>
                !(username == "SERVER" && (message.Contains("gave") || message.Contains("restarting")));
        }

        private static class BetterSay
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                if (list.Count == 0) return list;

                try
                {
                    var methodInfo = typeof(BetterSay).GetMethod(nameof(BetterSay.SendMessage), BindingFlags.Static | BindingFlags.NonPublic);
                    if (methodInfo == null) return list;

                    // Create a simple replacement that calls our custom method
                    return new List<CodeInstruction>
                    {
                        new CodeInstruction(OpCodes.Ldarg_0),
                        new CodeInstruction(OpCodes.Call, methodInfo),
                        new CodeInstruction(OpCodes.Ret)
                    };
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in BetterSay transpiler: {ex.Message}");
                    return list;
                }
            }

            internal static void SendMessage(string message) => ConsoleNetwork.BroadcastToAllClients("chat.add2", new object[] { 2, 76561199184470208, $"<color=white><size=15>{message}</size></color>", "<size=15>Awaken</size>", "#E9EAE6" });
        }

        private static class NoWeaponDrop { internal static bool Prefix() => false; }

        private static class CustomIcon
        {
            internal static bool Prefix(string strCommand, params object[] args)
            {
                if (args?.Length > 1 && (strCommand == "chat.add" || strCommand == "chat.add2") && args[1].ToString() == "0")
                {
                    args[1] = "76561199184470208";
                    return true;
                }
                return true;
            }
        }

        private static class InstantVending
        {
            internal static bool Prefix(VendingMachine __instance, BaseEntity.RPCMessage rpc)
            {
                try
                {
                    // Enhanced null safety for May 2025 API
                    if (__instance == null || rpc.player == null || __instance.IsDestroyed) return false;

                    if (!__instance.OccupiedCheck(rpc.player)) return false;

                    // Enhanced read safety for May 2025 API
                    if (rpc.read == null) return false;

                    int num = rpc.read.Int32();
                    int num1 = rpc.read.Int32();

                    if (__instance.IsVending())
                    {
                        rpc.player.ShowToast(GameTip.Styles.Blue_Normal, VendingMachine.WaitForVendingMessage);
                        return false;
                    }

                    // Enhanced inventory check for May 2025 API
                    if (rpc.player.inventory?.containerMain?.IsFull() == true &&
                        rpc.player.inventory?.containerBelt?.IsFull() == true)
                    {
                        rpc.player.ShowToast(GameTip.Styles.Blue_Normal, Instance?.inventoryFullPhrase ?? new Translate.Phrase("inventoryfull", "Your inventory is full, purchase canceled."));
                        return false;
                    }

                    // Use safer RPC method for May 2025 API
                    __instance.ClientRPC(null, "CLIENT_StartVendingSounds", num);
                    __instance.DoTransaction(rpc.player, num, num1);
                    return false;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] InstantVending error: {ex.Message}");
                    return true; // Allow original method to run if our patch fails
                }
            }
        }

        private static class VendingDrop
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                int line = list.FindLastIndex(i => i.opcode == OpCodes.Ldc_I4 && (int)i.operand == 256);
                if (line == -1) return list;

                var methodInfo = typeof(VendingDrop).GetMethod(nameof(VendingDrop.AllowDrop), BindingFlags.Static | BindingFlags.NonPublic);
                list.InsertRange(line - 1, new List<CodeInstruction>()
                {
                    new CodeInstruction(OpCodes.Ldarg_0, methodInfo),
                    new CodeInstruction(OpCodes.Callvirt, methodInfo)
                });
                return list;
            }

            internal static void AllowDrop(VendingMachine machine) => machine.dropsLoot = true;
        }



        private static class AirdropModify
        {
            internal static void Postfix(CargoPlane __instance)
            {
                try
                {
                    if (__instance == null) return;

                    // Calculate the distance the plane needs to travel
                    float distance = Vector3.Distance(__instance.startPos, __instance.endPos);

                    // Use planeTime as speed - lower planeTime = faster plane
                    float speed = config?.planeTime ?? 270f;

                    // Calculate time based on distance and speed
                    float newSecondsToTake = distance / speed;

                    // Apply the new timing
                    __instance.secondsToTake = newSecondsToTake;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] AirdropModify.Postfix error: {ex.Message}");
                }
            }
        }

        private static class ChangeAirdropSpeed
        {
            internal static void Postfix(CargoPlane __instance)
            {
                // Remove the expensive FindObjectsOfType call - we'll handle this in OnEntitySpawned instead
                // This method was causing severe FPS drops
            }
        }

        private static class RemoveTickAi
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Callvirt && i.operand is MethodBase { Name: "set_IsOnOffmeshLinkAndReachedNewCoord" }) is var lineAboveTick && lineAboveTick != -1
                ? list.Take(lineAboveTick + 1).Concat(list.Skip(lineAboveTick + 9)) : list;
        }

        private static class RemoveAiThinkManager
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Call && i.operand is MethodBase { Name: "AddAnimal" }) is var addAnimalLine && addAnimalLine != -1
                ? list.Take(addAnimalLine - 5).Append(new(OpCodes.Ldarg_0)).Append(new(OpCodes.Call, AccessTools.Method(typeof(RemoveAiThinkManager), nameof(RemoveComponents)))).Concat(list.Skip(addAnimalLine + 1)) : list;

            internal static void RemoveComponents(BaseAnimalNPC npc)
            {
                npc.CancelInvoke(npc.TickAi);
                foreach (var type in new[] { typeof(AiManagedAgent), typeof(AnimalBrain), typeof(NPCNavigator), typeof(NavMeshAgent) })
                    if (npc.GetComponent(type) is UnityEngine.Component comp) UnityEngine.Object.Destroy(comp);
                AIThinkManager.RemoveAnimal(npc);
            }
        }

        private static class QuickBackpackDespawn
        {
            internal static bool Prefix(DroppedItemContainer __instance, ref float __result) =>
                (__result = __instance.inventory.itemList.All(item => config?.fastDespawnList?.Contains(item.info.shortname) ?? false) ? config?.quickDespawnTime ?? 30f : config?.normalDespawnTime ?? 1200f) > 0 && false;
        }

        private static class StopReset
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Call && i.operand is MethodBase { Name: "ResetRemovalTime" }) is var resetLine && resetLine != -1
                ? list.Take(resetLine).Concat(list.Skip(resetLine + 2)) : list;
        }

        private static class AlwaysInStock
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                int line = list.FindLastIndex(i => i.opcode == OpCodes.Ldstr && (string)i.operand == "Vending machine error, contact developers!");
                if (line == -1) return list;

                var methodInfo = typeof(AlwaysInStock).GetMethod(nameof(AlwaysInStock.HandleItem), BindingFlags.Static | BindingFlags.NonPublic);
                list.InsertRange(line + 8, new List<CodeInstruction>()
                {
                    new CodeInstruction(OpCodes.Ldarg_0),
                    new CodeInstruction(OpCodes.Ldloc_S, 13),
                    new CodeInstruction(OpCodes.Ldarg_1),
                    new CodeInstruction(OpCodes.Callvirt, methodInfo)
                });
                return list;
            }

            internal static void HandleItem(VendingMachine vm, Item soldItem, BasePlayer buyer)
            {
                try
                {
                    // Enhanced null safety for May 2025 API
                    if (vm == null || soldItem == null || !(vm is NPCVendingMachine) || vm.IsDestroyed) return;

                    // Enhanced item creation for May 2025 API
                    Item item = ItemManager.Create(soldItem.info, soldItem.amount, soldItem.skin);
                    if (item == null) return;

                    if (soldItem.blueprintTarget != 0) item.blueprintTarget = soldItem.blueprintTarget;

                    Instance?.NextTick(() =>
                    {
                        try
                        {
                            if (item == null || item.parent == null) return;
                            if (vm == null || vm.IsDestroyed)
                            {
                                item.Remove(0f);
                                return;
                            }

                            // Enhanced transaction handling for May 2025 API
                            vm.transactionActive = true;

                            // Use safer container operations for May 2025 API
                            if (vm.inventory != null && !item.MoveToContainer(vm.inventory, -1, true))
                                item.Remove(0f);

                            vm.transactionActive = false;

                            // Use safer network update for May 2025 API
                            Instance?.SendNetworkUpdateSafe(vm);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Modifications] HandleItem NextTick error: {ex.Message}");
                            // Cleanup on error
                            if (item != null && item.parent != null) item.Remove(0f);
                            if (vm != null && !vm.IsDestroyed) vm.transactionActive = false;
                        }
                    });
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] HandleItem error: {ex.Message}");
                }
            }
        }

        private static class NoTerrainViolation { internal static bool Prefix(AntiHackType type) => type != AntiHackType.InsideTerrain; }

        private static class SamSiteRange { internal static void Prefix(SamSite __instance) => (__instance.vehicleScanRadius, __instance.missileScanRadius) = (150f, 225f); }

        private static class CustomItems
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions) =>
                instructions.ToList() is var list && list.FindIndex(i => i.opcode == OpCodes.Call && i.operand is MethodBase { Name: "Strip" }) is var line && line != -1
                ? list.Take(line + 1).Append(new(OpCodes.Ldarg_0)).Append(new(OpCodes.Call, AccessTools.Method(typeof(CustomItems), nameof(GiveItems)))).Append(list.Last()) : list;

            internal static void GiveItems(PlayerInventory inv)
            {
                if (config?.customRespawnItems != null)
                {
                    foreach (var item in config.customRespawnItems)
                        inv.GiveItem(ItemManager.CreateByName(item.shortname, item.amount, item.skin), inv.containerBelt);
                }
            }
        }

        private static class FullStackRecyclePatch
        {
            internal static bool RecycleThinkPrefix(Recycler __instance)
            {
                try
                {
                    if (config?.useFullStackRecycle != true) return true; // Let original method run

                    // Only modify the recycling logic, let the original method handle UI and state
                    if (__instance.inventory?.itemList == null || __instance.inventory.itemList.Count == 0)
                        return true; // Let original method handle empty recycler

                    // Find the first recyclable item
                    var firstItem = __instance.inventory.itemList.FirstOrDefault(item =>
                        item?.info?.Blueprint?.ingredients != null && item.info.Blueprint.ingredients.Count > 0);

                    if (firstItem == null) return true; // Let original method handle no recyclable items

                    // Process the FULL STACK instead of just one item
                    int fullAmount = firstItem.amount; // Use the entire stack

                    // Process the full amount
                    foreach (var ingredient in firstItem.info.Blueprint.ingredients)
                    {
                        if (ingredient?.itemDef == null) continue;

                        // Calculate the full return amount (typically 50% of input)
                        int returnAmount = Mathf.FloorToInt(fullAmount * ingredient.amount * 0.5f);

                        if (returnAmount > 0)
                        {
                            var returnItem = ItemManager.Create(ingredient.itemDef, returnAmount);
                            if (returnItem != null)
                            {
                                if (!returnItem.MoveToContainer(__instance.inventory))
                                {
                                    // Drop items if recycler is full
                                    returnItem.Drop(__instance.transform.position + Vector3.up, Vector3.zero);
                                }
                            }
                        }
                    }

                    // Remove the processed item completely
                    firstItem.RemoveFromContainer();
                    firstItem.Remove();

                    Debug.Log($"[Awaken Modifications] Full stack recycling processed {fullAmount} items of {firstItem.info.shortname}");

                    return false; // Skip original method since we handled the recycling
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] FullStackRecyclePatch.RecycleThinkPrefix error: {ex.Message}");
                    return true; // Let original method run on error
                }
            }
        }

        private static class SaveAnnouncer
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                var methodInfo = typeof(SaveAnnouncer).GetMethod(nameof(SaveAnnouncer.SendMessage), BindingFlags.Static | BindingFlags.NonPublic);
                list.Insert(0, new CodeInstruction(OpCodes.Callvirt, methodInfo));
                return list;
            }

            internal static void SendMessage() => Instance?.server.Broadcast("<color=#E9EAE6>Oasis</color> <color=#606060>»</color> Server is saving, expect lag for the next 5 seconds.");
        }

        private static class MetabolismAdjustment
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();

                int foundLine = list.FindLastIndex(i => i.opcode == OpCodes.Ret);
                if (foundLine == -1) return list;

                var methodInfo = typeof(MetabolismAdjustment).GetMethod(nameof(MetabolismAdjustment.FillVitals), BindingFlags.Static | BindingFlags.NonPublic);
                list.InsertRange(foundLine, new List<CodeInstruction>()
                {
                   new CodeInstruction(OpCodes.Ldarg_0),
                   new CodeInstruction(OpCodes.Callvirt, methodInfo)
                });

                return list;
            }

            internal static void FillVitals(BasePlayer player)
            {
                try
                {
                    if (config?.useMetabolismAdjustment == true)
                    {
                        player.metabolism.hydration.value = config.startingThirst;
                        player.metabolism.calories.value = config.startingHunger;
                        player.health = config.startingHealth;
                    }

                    if (config?.useNoHeatCold == true)
                    {
                        player.metabolism.temperature.min = 20;
                        player.metabolism.temperature.max = 20;
                    }

                    // Use the new safe network update method
                    Instance?.SendNetworkUpdateSafe(player);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in FillVitals: {ex.Message}");
                    // Fallback to regular network update
                    try
                    {
                        player.SendNetworkUpdate();
                    }
                    catch (Exception fallbackEx)
                    {
                        Debug.LogError($"[Awaken Modifications] Fallback network update failed in FillVitals: {fallbackEx.Message}");
                    }
                }
            }
        }

        private static class FastBarrels
        {
            internal static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions, ILGenerator generator)
            {
                var list = instructions.ToList();
                if (list.Count == 0) return list;

                try
                {
                    // Updated for May 2025 Rust API - more robust transpiler approach
                    var newInstructions = new List<CodeInstruction>
                    {
                        new CodeInstruction(OpCodes.Ldarg_0),
                        new CodeInstruction(OpCodes.Ldarg_1),
                        new CodeInstruction(OpCodes.Call, AccessTools.Method(typeof(FastBarrels), nameof(KillBarrel))),
                        new CodeInstruction(OpCodes.Brfalse_S, GetLabel(list.FirstOrDefault() ?? new CodeInstruction(OpCodes.Nop), generator)),
                        new CodeInstruction(OpCodes.Ret)
                    };

                    return newInstructions.Concat(list);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] FastBarrels transpiler error: {ex.Message}");
                    return list; // Return original instructions if transpiler fails
                }
            }

            private static Label GetLabel(CodeInstruction inst, ILGenerator? generator)
            {
                try
                {
                    if (inst?.labels?.Count > 0) return inst.labels[0];
                    if (generator != null)
                    {
                        var lbl = generator.DefineLabel();
                        if (inst?.labels != null) inst.labels.Add(lbl);
                        return lbl;
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] GetLabel error: {ex.Message}");
                }
                return default;
            }

            internal static bool KillBarrel(LootContainer barrel, HitInfo info)
            {
                try
                {
                    // Enhanced null safety and validation for May 2025 API
                    if (barrel == null || info == null || barrel.IsDestroyed) return false;

                    var damageType = info.damageTypes.GetMajorityDamageType();
                    if (barrel.isLootable || barrel is HackableLockedCrate ||
                        (damageType != Rust.DamageType.Stab && damageType != Rust.DamageType.Blunt && damageType != Rust.DamageType.Slash) ||
                        !(info.InitiatorPlayer is BasePlayer attacker) || attacker == null) return false;

                    // Enhanced inventory handling for May 2025 API with pickup notifications
                    if (barrel.inventory?.itemList != null)
                    {
                        var itemsToMove = barrel.inventory.itemList.ToList();
                        foreach (var item in itemsToMove)
                        {
                            if (item == null) continue;

                            // Show item pickup notification as if it was farmed/picked up
                            ShowItemPickupNotification(attacker, item);

                            if (!item.MoveToContainer(attacker.inventory.containerMain) &&
                                !item.MoveToContainer(attacker.inventory.containerBelt))
                            {
                                // Use safer drop method for May 2025 API
                                var dropPosition = attacker.inventory.containerMain.dropPosition;
                                var dropVelocity = attacker.inventory.containerMain.dropVelocity;
                                item.Drop(dropPosition, dropVelocity);
                            }
                        }
                    }

                    // Use safer kill method for May 2025 API
                    barrel.Kill(BaseNetworkable.DestroyMode.Gib);
                    return true;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] KillBarrel error: {ex.Message}");
                    return false;
                }
            }

            // Helper method to show item pickup notification as if the item was farmed/picked up
            private static void ShowItemPickupNotification(BasePlayer player, Item item)
            {
                try
                {
                    if (player == null || item == null) return;

                    // Use the same notification system that shows when picking up items
                    // This creates the popup notification showing what items were obtained
                    player.Command("note.inv", item.info.itemid, item.amount);

                    // Sound effect removed to reduce noise from fast barrel breaking
                    // Effect.server.Run("assets/bundled/prefabs/fx/notice/item.select.fx.prefab", player, 0u, Vector3.zero, Vector3.forward);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] ShowItemPickupNotification error: {ex.Message}");
                }
            }
        }
        #endregion

        #region Functions
        private void SetPlayerNoWorkbench(BasePlayer player)
        {
            try
            {
                if (player.HasPlayerFlag(BasePlayer.PlayerFlags.ReceivingSnapshot))
                {
                    timer.In(3, () => SetPlayerNoWorkbench(player));
                    return;
                }

                // Use the correct RPC method from NoWorkbench.cs
                player.ClientRPCPlayer(null, player, "craftMode", 1);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in SetPlayerNoWorkbench: {ex.Message}");
            }
        }

        private IEnumerator RemoveCooldownTimer()
        {
            while (removeCooldowns.Count > 0)
            {
                int cooldownSeconds = (config?.removeCommandCooldownMinutes ?? 15) * 60;
                foreach (var (userId, time) in removeCooldowns.ToArray())
                {
                    if (Time.time - time > cooldownSeconds)
                    {
                        removeCooldowns.Remove(userId);
                        removeEntityCounts.Remove(userId); // Reset entity count when cooldown expires
                    }
                }
                yield return CoroutineEx.waitForSeconds(60f);
            }
        }

        private void UpdatePlayerTemperatures(float min, float max)
        {
            foreach (var player in BasePlayer.activePlayerList)
                UpdatePlayerTemperature(player, min, max);
        }
        private void UpdatePlayerTemperature(BasePlayer player, float min, float max)
        {
            try
            {
                // Only update if values actually changed to reduce network traffic
                if (Math.Abs(player.metabolism.temperature.min - min) > 0.01f ||
                    Math.Abs(player.metabolism.temperature.max - max) > 0.01f)
                {
                    player.metabolism.temperature.min = min;
                    player.metabolism.temperature.max = max;
                    player.metabolism.isDirty = true;
                    player.metabolism.SendChangesToClient();

                    // Use the new safe network update method
                    SendNetworkUpdateSafe(player);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error updating player temperature: {ex.Message}");
            }
        }
        #endregion

        #region Commands
        private void RemoveCMD(IPlayer iPlayer, string command, string[] args)
        {
            try
            {
                if (iPlayer.IsServer || config?.useRemoveCommand != true) return;
                var player = (BasePlayer)iPlayer.Object;

                int maxEntities = config?.removeCommandMaxEntities ?? 5;
                int cooldownMinutes = config?.removeCommandCooldownMinutes ?? 15;

                // Handle /remove without arguments - activate tool and deselect item
                if (args.Length == 0)
                {
                    // Check cooldown
                    if (removeCooldowns.ContainsKey(player.UserIDString))
                    {
                        player.ChatMessage($"You have already used your remove allowance within the past {cooldownMinutes} minutes, please wait.");
                        return;
                    }

                    // Check current entity count
                    int currentCount = removeEntityCounts.GetValueOrDefault(player.UserIDString, 0);
                    if (currentCount >= maxEntities)
                    {
                        player.ChatMessage($"You have reached the maximum of {maxEntities} entities removed. Please wait {cooldownMinutes} minutes before removing more.");
                        removeCooldowns[player.UserIDString] = Time.time;
                        return;
                    }

                    // Check if player already has the tool active
                    var existingTool = player.GetComponent<RemoveToolHandler>();
                    if (existingTool != null)
                    {
                        player.ChatMessage("You already have the removal tool active. Select any item to disable it.");
                        return;
                    }

                    // Deselect current item
                    if (player.GetActiveItem() != null)
                    {
                        player.UpdateActiveItem(new ItemId());
                    }

                    // Give the player the removal tool
                    var removeTool = player.gameObject.AddComponent<RemoveToolHandler>();
                    removeTool.Initialize(player, maxEntities, cooldownMinutes);
                    player.ChatMessage($"Removal tool activated! You can remove up to {maxEntities - currentCount} more entities. Left-click to remove objects you own. Select any item to disable the tool.");
                    return;
                }

                if (args[0].Equals("tool", StringComparison.OrdinalIgnoreCase))
                {
                    // Check cooldown
                    if (removeCooldowns.ContainsKey(player.UserIDString))
                    {
                        player.ChatMessage($"You have already used your remove allowance within the past {cooldownMinutes} minutes, please wait.");
                        return;
                    }

                    // Check current entity count
                    int currentCount = removeEntityCounts.GetValueOrDefault(player.UserIDString, 0);
                    if (currentCount >= maxEntities)
                    {
                        player.ChatMessage($"You have reached the maximum of {maxEntities} entities removed. Please wait {cooldownMinutes} minutes before removing more.");
                        removeCooldowns[player.UserIDString] = Time.time;
                        return;
                    }

                    // Check if player already has the tool active
                    var existingTool = player.GetComponent<RemoveToolHandler>();
                    if (existingTool != null)
                    {
                        player.ChatMessage("You already have the removal tool active. Use '/remove stop' to disable it.");
                        return;
                    }

                    // Give the player the removal tool
                    var removeTool = player.gameObject.AddComponent<RemoveToolHandler>();
                    removeTool.Initialize(player, maxEntities, cooldownMinutes);
                    player.ChatMessage($"Removal tool activated! You can remove up to {maxEntities - currentCount} more entities. Look at an object and left-click to remove it.");
                    return;
                }

                if (args[0].Equals("stop", StringComparison.OrdinalIgnoreCase))
                {
                    var existingTool = player.GetComponent<RemoveToolHandler>();
                    if (existingTool != null)
                    {
                        existingTool.DeactivateTool();
                        player.ChatMessage("Removal tool disabled.");
                    }
                    else
                    {
                        player.ChatMessage("You don't have the removal tool active.");
                    }
                    return;
                }

                player.ChatMessage("Usage: /remove (to activate and deselect item), /remove tool (to activate), or /remove stop (to disable)");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in RemoveCMD: {ex.Message}");
                if (iPlayer.Object is BasePlayer player)
                    player.ChatMessage("An error occurred while processing the remove command. Please contact an administrator.");
            }
        }

        private void BlockCMD(IPlayer iPlayer, string command, string[] args)
        {
            try
            {
                if (iPlayer.IsServer) return;
                var player = (BasePlayer)iPlayer.Object;

                // Check if AwakenActionBlocker plugin is loaded
                var actionBlocker = plugins.Find("awakenActionBlocker");
                if (actionBlocker == null)
                {
                    player.ChatMessage("<color=#ff6b6b>AwakenActionBlocker plugin not found. Cannot check block status.</color>");
                    return;
                }

                // Get raid block status
                var isRaidBlocked = actionBlocker.Call("IsRaidBlocked", player.UserIDString);
                var isCombatBlocked = actionBlocker.Call("IsCombatBlocked", player.UserIDString);

                if (isRaidBlocked == null && isCombatBlocked == null)
                {
                    player.ChatMessage("<color=#ff6b6b>Unable to retrieve block status from AwakenActionBlocker.</color>");
                    return;
                }

                bool raidBlocked = isRaidBlocked is bool rb && rb;
                bool combatBlocked = isCombatBlocked is bool cb && cb;

                if (!raidBlocked && !combatBlocked)
                {
                    player.ChatMessage("<color=#00ff00>✓ You are not blocked</color> - No raid or combat restrictions active.");
                    return;
                }

                // Get remaining time if blocked
                var raidTimeLeft = actionBlocker.Call("GetRaidBlockTimeLeft", player.UserIDString);
                var combatTimeLeft = actionBlocker.Call("GetCombatBlockTimeLeft", player.UserIDString);

                string message = "<color=#ff9900>⚠ You are currently blocked:</color>\n";

                if (raidBlocked)
                {
                    if (raidTimeLeft is float raidTime && raidTime == -1f)
                    {
                        message += "<color=#ff6b6b>• Raid Block:</color> Zone-based (leave the area to remove)\n";
                    }
                    else if (raidTimeLeft is float raidTimePositive && raidTimePositive > 0)
                    {
                        int minutes = Mathf.FloorToInt(raidTimePositive / 60f);
                        int seconds = Mathf.FloorToInt(raidTimePositive % 60f);
                        message += $"<color=#ff6b6b>• Raid Block:</color> {minutes}m {seconds}s remaining\n";
                    }
                    else
                    {
                        message += "<color=#ff6b6b>• Raid Block:</color> Active (unknown type)\n";
                    }
                }

                if (combatBlocked)
                {
                    if (combatTimeLeft is float combatTime && combatTime > 0)
                    {
                        int minutes = Mathf.FloorToInt(combatTime / 60f);
                        int seconds = Mathf.FloorToInt(combatTime % 60f);
                        message += $"<color=#ff6b6b>• Combat Block:</color> {minutes}m {seconds}s remaining\n";
                    }
                    else
                    {
                        message += "<color=#ff6b6b>• Combat Block:</color> Active (time unknown)\n";
                    }
                }

                message += "<color=#cccccc>Blocked actions: Teleporting, Banking, Trading, Removing items</color>";
                player.ChatMessage(message);

                // Debug information for admins
                if (player.IsAdmin)
                {
                    var debugInfo = actionBlocker.Call("GetDebugInfo", player.UserIDString);
                    if (debugInfo != null)
                    {
                        player.ChatMessage($"<color=#888888>[DEBUG] {debugInfo}</color>");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in BlockCMD: {ex.Message}");
                if (iPlayer.Object is BasePlayer player)
                    player.ChatMessage("An error occurred while checking block status. Please contact an administrator.");
            }
        }

        private void BlockDebugCMD(IPlayer iPlayer, string command, string[] args)
        {
            try
            {
                if (iPlayer.IsServer) return;
                var player = (BasePlayer)iPlayer.Object;

                if (!player.IsAdmin)
                {
                    player.ChatMessage("<color=#ff6b6b>This command is only available to administrators.</color>");
                    return;
                }

                // Check if AwakenActionBlocker plugin is loaded
                var actionBlocker = plugins.Find("awakenActionBlocker");
                if (actionBlocker == null)
                {
                    player.ChatMessage("<color=#ff6b6b>AwakenActionBlocker plugin not found.</color>");
                    return;
                }

                string targetPlayerName = args.Length > 0 ? args[0] : player.displayName;
                BasePlayer targetPlayer = player;

                // If a player name was provided, find that player
                if (args.Length > 0 && !args[0].Equals(player.displayName, StringComparison.OrdinalIgnoreCase))
                {
                    targetPlayer = BasePlayer.Find(targetPlayerName);
                    if (targetPlayer == null)
                    {
                        player.ChatMessage($"<color=#ff6b6b>Player '{targetPlayerName}' not found.</color>");
                        return;
                    }
                }

                player.ChatMessage($"<color=#7000fd>=== BLOCK DEBUG INFO FOR {targetPlayer.displayName} ===</color>");

                // Get basic block status
                var isRaidBlocked = actionBlocker.Call("IsRaidBlocked", targetPlayer.UserIDString);
                var isCombatBlocked = actionBlocker.Call("IsCombatBlocked", targetPlayer.UserIDString);
                var raidTimeLeft = actionBlocker.Call("GetRaidBlockTimeLeft", targetPlayer.UserIDString);
                var combatTimeLeft = actionBlocker.Call("GetCombatBlockTimeLeft", targetPlayer.UserIDString);
                var debugInfo = actionBlocker.Call("GetDebugInfo", targetPlayer.UserIDString);

                player.ChatMessage($"<color=#cccccc>UserID:</color> {targetPlayer.UserIDString}");
                player.ChatMessage($"<color=#cccccc>Position:</color> {targetPlayer.transform.position}");
                player.ChatMessage($"<color=#cccccc>Current Time:</color> {DateTime.Now:HH:mm:ss}");

                bool raidBlocked = isRaidBlocked is bool rb && rb;
                bool combatBlocked = isCombatBlocked is bool cb && cb;

                player.ChatMessage($"<color=#cccccc>Raid Blocked:</color> {(raidBlocked ? "<color=#ff6b6b>YES</color>" : "<color=#00ff00>NO</color>")}");
                player.ChatMessage($"<color=#cccccc>Combat Blocked:</color> {(combatBlocked ? "<color=#ff6b6b>YES</color>" : "<color=#00ff00>NO</color>")}");

                if (raidTimeLeft != null)
                {
                    float raidTime = (float)raidTimeLeft;
                    if (raidTime == -1f)
                        player.ChatMessage($"<color=#cccccc>Raid Block Type:</color> <color=#ff9900>Zone-based (no timer)</color>");
                    else if (raidTime > 0)
                        player.ChatMessage($"<color=#cccccc>Raid Time Left:</color> <color=#ff9900>{raidTime:F1} seconds</color>");
                    else
                        player.ChatMessage($"<color=#cccccc>Raid Time Left:</color> <color=#00ff00>0 seconds</color>");
                }

                if (combatTimeLeft != null)
                {
                    float combatTime = (float)combatTimeLeft;
                    if (combatTime > 0)
                        player.ChatMessage($"<color=#cccccc>Combat Time Left:</color> <color=#ff9900>{combatTime:F1} seconds</color>");
                    else
                        player.ChatMessage($"<color=#cccccc>Combat Time Left:</color> <color=#00ff00>0 seconds</color>");
                }

                if (debugInfo != null)
                {
                    player.ChatMessage($"<color=#cccccc>Debug Info:</color> {debugInfo}");
                }

                // Check for nearby raid zones or entities that might be causing blocks
                player.ChatMessage($"<color=#7000fd>=== NEARBY ENTITIES ANALYSIS ===</color>");

                var nearbyEntities = new List<BaseEntity>();
                Vis.Entities(targetPlayer.transform.position, 50f, nearbyEntities);

                int raidableEntities = 0;
                int damagedEntities = 0;

                foreach (var entity in nearbyEntities)
                {
                    if (entity == null) continue;

                    // Check for entities that might trigger raid blocks
                    if (entity.ShortPrefabName.Contains("rocket") ||
                        entity.ShortPrefabName.Contains("explosive") ||
                        entity.ShortPrefabName.Contains("c4") ||
                        entity.ShortPrefabName.Contains("satchel"))
                    {
                        raidableEntities++;
                    }

                    // Check for damaged building blocks
                    if (entity is BuildingBlock buildingBlock && buildingBlock.Health() < buildingBlock.MaxHealth())
                    {
                        damagedEntities++;
                    }
                }

                player.ChatMessage($"<color=#cccccc>Nearby Entities (50m):</color> {nearbyEntities.Count}");
                player.ChatMessage($"<color=#cccccc>Raid-related Entities:</color> {raidableEntities}");
                player.ChatMessage($"<color=#cccccc>Damaged Building Blocks:</color> {damagedEntities}");

                player.ChatMessage($"<color=#888888>Use '/blockdebug <playername>' to check other players</color>");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error in BlockDebugCMD: {ex.Message}");
                if (iPlayer.Object is BasePlayer player)
                    player.ChatMessage("An error occurred while getting debug information. Please contact an administrator.");
            }
        }

        #region Discord Logging
        private void SendDiscordMessage(string webhook, string embedName, int embedColor, Dictionary<string, string> values, string content = null)
        {
            try
            {
                if (string.IsNullOrEmpty(webhook))
                {
                    Debug.LogWarning("[Awaken Modifications] Discord webhook URL is empty.");
                    return;
                }

                var embed = new
                {
                    title = embedName,
                    color = embedColor,
                    fields = values.Select(kv => new { name = kv.Key, value = kv.Value, inline = false }).ToArray(),
                    footer = new
                    {
                        text = "Awaken Rust Servers",
                        icon_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213031984762961/oasisredglowing.png"
                    }
                };

                var payload = new
                {
                    content = content,
                    embeds = new[] { embed },
                    username = "Awaken Remove Tool",
                    avatar_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213031984762961/oasisredglowing.png"
                };

                string jsonPayload = JsonConvert.SerializeObject(payload);
                webrequest.EnqueuePost(webhook, jsonPayload, (code, response) =>
                {
                    if (code != 200 && code != 204)
                    {
                        Debug.LogError($"[Awaken Modifications] Failed to send Discord message. Status code: {code}");
                    }
                }, this, new Dictionary<string, string> { { "Content-Type", "application/json" } });
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error sending Discord message: {ex.Message}");
            }
        }

        private void LogRemoveAction(BasePlayer player, BaseEntity entity, int remaining)
        {
            try
            {
                if (!config.useDiscordLogging || string.IsNullOrEmpty(config.discordWebhook))
                    return;

                var values = new Dictionary<string, string>
                {
                    ["Player"] = $"{player.displayName} ({player.UserIDString})",
                    ["Entity"] = $"{entity.ShortPrefabName} (ID: {entity.net.ID})",
                    ["Position"] = $"{entity.transform.position}",
                    ["Grid"] = GetGrid(entity.transform.position),
                    ["Remaining Uses"] = $"{remaining}",
                    ["Time"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                SendDiscordMessage(config.discordWebhook, "Remove Tool Usage", 16711680, values);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error logging remove action: {ex.Message}");
            }
        }

        private static string GetGrid(Vector3 pos)
        {
            const float scale = 150f;
            float x = pos.x + World.Size / 2f;
            float z = pos.z + World.Size / 2f;
            int lat = (int)(x / scale);
            char latChar = (char)('A' + lat);
            int lon = (int)(World.Size / scale - z / scale);
            return latChar + lon.ToString();
        }

        private void LogToDiscord(string message)
        {
            try
            {
                if (!config.useDiscordLogging || string.IsNullOrEmpty(config.discordWebhook))
                    return;

                var payload = new
                {
                    content = message,
                    username = "Awaken Modifications",
                    avatar_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213031984762961/oasisredglowing.png"
                };

                string jsonPayload = JsonConvert.SerializeObject(payload);
                webrequest.EnqueuePost(config.discordWebhook, jsonPayload, (code, response) =>
                {
                    if (code != 200 && code != 204)
                    {
                        Debug.LogError($"[Awaken Modifications] Failed to send Discord message. Status code: {code}");
                    }
                }, this, new Dictionary<string, string> { { "Content-Type", "application/json" } });
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Modifications] Error sending Discord message: {ex.Message}");
            }
        }
        #endregion

        #region Helpers
        private static readonly List<string> armor = new List<string> { "hazmatsuit", "metal.facemask", "metal.plate.torso", "roadsign.jacket", "roadsign.kilt", "coffeecan.helmet" };
        #endregion
        #endregion

        #region Remove Tool Component
        public class RemoveToolHandler : MonoBehaviour
        {
            private BasePlayer player;
            private int maxEntities;
            private int cooldownMinutes;
            private float lastRemoveTime;
            private float lastActivityTime;
            private Timer inactivityTimer;
            private Timer itemCheckTimer;
            private ItemId lastActiveItemId;
            private const float REMOVE_COOLDOWN = 0.5f; // Prevent spam clicking
            private const float MAX_DISTANCE = 3f;
            private const float INACTIVITY_TIMEOUT = 30f; // 30 seconds of inactivity
            private const float ITEM_CHECK_INTERVAL = 0.1f; // Check for item selection every 100ms

            public void Initialize(BasePlayer basePlayer, int maxEntitiesAllowed, int cooldownMins)
            {
                player = basePlayer;
                maxEntities = maxEntitiesAllowed;
                cooldownMinutes = cooldownMins;
                lastRemoveTime = 0f;
                lastActivityTime = Time.time;
                lastActiveItemId = player.GetActiveItem()?.uid ?? new ItemId();

                // Start the inactivity timer
                StartInactivityTimer();

                // Start monitoring for item selection
                StartItemSelectionMonitoring();

                // Show UI or notification
                ShowRemoveToolUI();
            }

            public void HandlePlayerInput(InputState inputState)
            {
                try
                {
                    if (inputState.IsDown(BUTTON.FIRE_PRIMARY) && Time.time - lastRemoveTime > REMOVE_COOLDOWN)
                    {
                        TryRemoveEntity();
                        lastRemoveTime = Time.time;
                        ResetInactivityTimer(); // Reset timer on any removal attempt
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.HandlePlayerInput: {ex.Message}");
                }
            }

            private void TryRemoveEntity()
            {
                try
                {
                    if (player == null || !player.IsConnected)
                    {
                        DeactivateTool();
                        return;
                    }

                    // Check if player is still within limits
                    int currentCount = Instance.removeEntityCounts.GetValueOrDefault(player.UserIDString, 0);
                    if (currentCount >= maxEntities)
                    {
                        player.ChatMessage($"You have reached the maximum of {maxEntities} entities removed. Please wait {cooldownMinutes} minutes before removing more.");
                        Instance.removeCooldowns[player.UserIDString] = Time.time;
                        DeactivateTool();
                        return;
                    }

                    // Check cooldown
                    if (Instance.removeCooldowns.ContainsKey(player.UserIDString))
                    {
                        player.ChatMessage($"You have already used your remove allowance within the past {cooldownMinutes} minutes, please wait.");
                        DeactivateTool();
                        return;
                    }

                    // Raycast to find entity
                    if (Physics.Raycast(player.eyes.HeadRay(), out var hit, MAX_DISTANCE))
                    {
                        var entity = hit.GetEntity();
                        if (entity != null)
                        {
                            // Check building blocked
                            if (player.IsBuildingBlocked())
                            {
                                player.ChatMessage("You cannot remove objects when building blocked.");
                                return;
                            }

                            // Check ownership
                            if (entity.OwnerID != player.userID)
                            {
                                player.ChatMessage("This object is not owned by you.");
                                return;
                            }

                            // Check if it's a removable entity type
                            if (!IsRemovableEntity(entity))
                            {
                                player.ChatMessage("This type of entity cannot be removed with this tool.");
                                return;
                            }

                            // Log the removal action before removing the entity
                            int remaining = maxEntities - currentCount - 1;
                            Instance.LogRemoveAction(player, entity, remaining);

                            // Remove the entity
                            entity.Kill(BaseNetworkable.DestroyMode.Gib);
                            currentCount++;
                            Instance.removeEntityCounts[player.UserIDString] = currentCount;

                            // Reset the inactivity timer since something was successfully removed
                            ResetInactivityTimer();

                            if (remaining > 0)
                            {
                                player.ChatMessage($"Removed 1x {entity.ShortPrefabName}. {remaining} removals remaining.");
                                UpdateRemoveToolUI(remaining);
                            }
                            else
                            {
                                player.ChatMessage($"Removed 1x {entity.ShortPrefabName}. Maximum entities reached. Tool disabled.");
                                Instance.removeCooldowns[player.UserIDString] = Time.time;
                                DeactivateTool();
                            }

                            // Start cooldown timer if this is the first removal
                            if (Instance.removeCooldowns.Count == 1)
                                ServerMgr.Instance.StartCoroutine(Instance.RemoveCooldownTimer());
                        }
                        else
                        {
                            player.ChatMessage("No removable object found. Look directly at a building block or deployable you own.");
                        }
                    }
                    else
                    {
                        player.ChatMessage("No object found in range. Get closer to the object you want to remove.");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.TryRemoveEntity: {ex.Message}");
                }
            }

            private bool IsRemovableEntity(BaseEntity entity)
            {
                // Allow building blocks
                if (entity is BuildingBlock) return true;

                // Allow common deployables
                if (entity is Door || entity is Signage || entity is StorageContainer ||
                    entity is BaseOven || entity is ElectricBattery || entity is AutoTurret ||
                    entity is SamSite || entity is FlameTurret || entity is GunTrap ||
                    entity is Landmine || entity is BearTrap) return true;

                // Allow sleeping bags and beds
                if (entity.ShortPrefabName.Contains("sleepingbag") || entity.ShortPrefabName.Contains("bed")) return true;

                // Allow workbenches
                if (entity.ShortPrefabName.Contains("workbench")) return true;

                return false;
            }

            private void ShowRemoveToolUI()
            {
                try
                {
                    int currentCount = Instance.removeEntityCounts.GetValueOrDefault(player.UserIDString, 0);
                    int remaining = maxEntities - currentCount;
                    player.ChatMessage($"<color=#00ff00>REMOVAL TOOL ACTIVE</color> - {remaining}/{maxEntities} removals remaining. Left-click to remove objects you own. Select any item to disable the tool.");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.ShowRemoveToolUI: {ex.Message}");
                }
            }

            private void UpdateRemoveToolUI(int remaining)
            {
                try
                {
                    // Simple chat message update - could be enhanced with actual UI later
                    if (remaining > 0)
                    {
                        player.ChatMessage($"<color=#ffff00>{remaining}/{maxEntities} removals remaining</color>");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.UpdateRemoveToolUI: {ex.Message}");
                }
            }

            private void StartInactivityTimer()
            {
                try
                {
                    // Cancel any existing timer
                    inactivityTimer?.Destroy();

                    // Start a new 30-second timer
                    inactivityTimer = Instance.timer.Once(INACTIVITY_TIMEOUT, () =>
                    {
                        if (player != null && player.IsConnected)
                        {
                            player.ChatMessage("<color=#ff9900>Removal tool auto-disabled due to inactivity.</color>");
                        }
                        DeactivateTool();
                    });
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.StartInactivityTimer: {ex.Message}");
                }
            }

            private void ResetInactivityTimer()
            {
                try
                {
                    lastActivityTime = Time.time;

                    // Cancel the current timer and start a new one
                    inactivityTimer?.Destroy();

                    // Start a new 30-second timer
                    inactivityTimer = Instance.timer.Once(INACTIVITY_TIMEOUT, () =>
                    {
                        if (player != null && player.IsConnected)
                        {
                            player.ChatMessage("<color=#ff9900>Removal tool auto-disabled due to inactivity.</color>");
                        }
                        DeactivateTool();
                    });
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.ResetInactivityTimer: {ex.Message}");
                }
            }

            private void StartItemSelectionMonitoring()
            {
                try
                {
                    // Start a repeating timer to check for item selection
                    itemCheckTimer = Instance.timer.Repeat(ITEM_CHECK_INTERVAL, 0, () =>
                    {
                        CheckForItemSelection();
                    });
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.StartItemSelectionMonitoring: {ex.Message}");
                }
            }

            private void CheckForItemSelection()
            {
                try
                {
                    if (player == null || !player.IsConnected)
                    {
                        DeactivateTool();
                        return;
                    }

                    ItemId currentActiveItemId = player.GetActiveItem()?.uid ?? new ItemId();

                    // If player selected an item (went from no item to having an item)
                    if (lastActiveItemId == new ItemId() && currentActiveItemId != new ItemId())
                    {
                        player.ChatMessage("<color=#ff9900>Removal tool disabled - item selected.</color>");
                        DeactivateTool();
                        return;
                    }

                    lastActiveItemId = currentActiveItemId;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.CheckForItemSelection: {ex.Message}");
                }
            }

            public void DeactivateTool()
            {
                try
                {
                    // Clean up the timers
                    inactivityTimer?.Destroy();
                    inactivityTimer = null;
                    itemCheckTimer?.Destroy();
                    itemCheckTimer = null;

                    if (player != null && player.IsConnected)
                    {
                        player.ChatMessage("<color=#ff0000>Removal tool disabled.</color>");
                    }

                    Destroy(this);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolHandler.DeactivateTool: {ex.Message}");
                }
            }

            private void OnDestroy()
            {
                try
                {
                    // Clean up the timers
                    inactivityTimer?.Destroy();
                    inactivityTimer = null;
                    itemCheckTimer?.Destroy();
                    itemCheckTimer = null;
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Modifications] Error in RemoveToolComponent.OnDestroy: {ex.Message}");
                }
            }
        }
        #endregion
    }

    // Extension methods must be in a static class
    public static class HarmonyExtensions
    {
        public static void PatchAll(this Harmony harmony, Type patchType, Type[] targetTypes, string[] methodNames)
        {
            var patches = targetTypes.SelectMany(t => methodNames.Select(m => (t, m))).ToList();
            foreach (var p in patches)
                harmony.Patch(AccessTools.Method(p.t, p.m), prefix: new HarmonyMethod(patchType, "Prefix"));
        }

        public static IEnumerable<CodeInstruction> InsertRange(this IEnumerable<CodeInstruction> list, int index, IEnumerable<CodeInstruction> instructions) =>
            list.Take(index).Concat(instructions).Concat(list.Skip(index));

        public static IEnumerable<CodeInstruction> WithAt(this IEnumerable<CodeInstruction> list, int index, OpCode? opcode = null, object? operand = null) =>
            list.Take(index).Append(new CodeInstruction(list.ElementAt(index)) { opcode = opcode ?? list.ElementAt(index).opcode, operand = operand ?? list.ElementAt(index).operand }).Concat(list.Skip(index + 1));

        public static IEnumerable<CodeInstruction> WithRange(this IEnumerable<CodeInstruction> list, int index, int count, Func<CodeInstruction, CodeInstruction> transform) =>
            list.Take(index).Concat(list.Skip(index).Take(count).Select(transform)).Concat(list.Skip(index + count));
    }
}









