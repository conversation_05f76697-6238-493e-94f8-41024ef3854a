using System;
using UnityEngine;
using Oxide.Core;
using Oxide.Core.Plugins;

namespace Oxide.Plugins
{
    [Info("DoubleRocket", "Jason // Skelee", "1.2.0")]
    [Description("Makes all rocket launchers hold 2 rockets")]

    public class DoubleRocket : RustPlugin
    {
        // Array of valid launcher short names (normal and dragon launchers)
        private static readonly string[] ValidLaunchers = {
            "rocket.launcher",        // Normal rocket launcher
            "rocket.launcher.dragon"  // Dragon rocket launcher
        };

        private const int NewMaxAmmoAmount = 2;

        // Helper method to check if an item is a valid launcher
        private bool IsValidLauncher(string itemShortName)
        {
            return Array.Exists(ValidLaunchers, launcher => launcher == itemShortName);
        }

        #region Hooks

        // Triggered when the plugin loads/reloads - automatically update all existing launchers
        private void Loaded()
        {
            Puts("DoubleRocket: Plugin loaded, automatically updating all existing launchers...");

            // Multiple attempts to ensure all launchers are found and updated
            timer.Once(0.5f, () => UpdateAllLaunchers());
            timer.Once(2f, () => UpdateAllLaunchers());
            timer.Once(5f, () => UpdateAllLaunchers());
        }

        // Triggered when the server initializes - update all existing launchers
        private void OnServerInitialized()
        {
            Puts("DoubleRocket: Server initialized, updating all existing launchers...");
            UpdateAllLaunchers();
        }

        // Triggered when the held entity is spawned into the world (e.g., when equipped)
        private void OnEntitySpawned(BaseNetworkable entity)
        {
            var projectile = entity as BaseProjectile;
            if (projectile == null) return;

            var item = projectile.GetItem();
            if (item == null || !IsValidLauncher(item.info.shortname)) return;

            UpdateLauncherCapacity(projectile, item.info.shortname);
        }

        // Triggered when a player equips an item
        private void OnItemEquipped(Item item, BasePlayer player)
        {
            if (item?.GetHeldEntity() is BaseProjectile projectile && IsValidLauncher(item.info.shortname))
            {
                timer.Once(0.1f, () => UpdateLauncherCapacity(projectile, item.info.shortname));
            }
        }

        // Triggered when a player picks up an item
        private void OnItemPickup(Item item, BasePlayer player)
        {
            if (IsValidLauncher(item.info.shortname))
            {
                timer.Once(0.1f, () => {
                    if (item?.GetHeldEntity() is BaseProjectile projectile)
                    {
                        UpdateLauncherCapacity(projectile, item.info.shortname);
                    }
                });
            }
        }

        // Ensures reload respects the custom capacity
        private object OnReloadWeapon(BasePlayer player, BaseProjectile projectile)
        {
            if (projectile == null || !IsValidLauncher(projectile.GetItem()?.info.shortname))
                return null;

            var itemShortName = projectile.GetItem()?.info.shortname ?? "unknown";
            UpdateLauncherCapacity(projectile, itemShortName);

            return null;
        }

        // Triggered when a weapon is fired
        private void OnWeaponFired(BaseProjectile projectile, BasePlayer player, ItemModProjectile mod)
        {
            if (projectile == null || !IsValidLauncher(projectile.GetItem()?.info.shortname))
                return;

            var itemShortName = projectile.GetItem()?.info.shortname ?? "unknown";

            // Ensure capacity is still correct after firing
            timer.Once(0.1f, () => UpdateLauncherCapacity(projectile, itemShortName));
        }

        #endregion

        #region Helper Methods

        // Update a specific launcher's capacity
        private void UpdateLauncherCapacity(BaseProjectile projectile, string itemShortName)
        {
            if (projectile?.primaryMagazine == null)
            {
                Puts($"Warning: Launcher ({itemShortName}) has no primary magazine!");
                return;
            }

            int oldCapacity = projectile.primaryMagazine.capacity;

            if (oldCapacity != NewMaxAmmoAmount)
            {
                projectile.primaryMagazine.capacity = NewMaxAmmoAmount;

                // Force a network update to ensure clients see the change
                projectile.SendNetworkUpdate();

                Puts($"Launcher ({itemShortName}) capacity updated from {oldCapacity} to {NewMaxAmmoAmount}.");
            }
            else
            {
                Puts($"Launcher ({itemShortName}) already has correct capacity of {NewMaxAmmoAmount}.");
            }
        }

        // Update all existing launchers on the server
        private void UpdateAllLaunchers()
        {
            int updated = 0;

            // Update launchers in player inventories (active players)
            foreach (var player in BasePlayer.activePlayerList)
            {
                updated += UpdatePlayerLaunchers(player);
            }

            // Update launchers in sleeping player inventories
            foreach (var player in BasePlayer.sleepingPlayerList)
            {
                updated += UpdatePlayerLaunchers(player);
            }

            // Update launchers in all BaseProjectile entities on the server
            foreach (var entity in BaseNetworkable.serverEntities)
            {
                if (entity is BaseProjectile projectile)
                {
                    var item = projectile.GetItem();
                    if (item != null && IsValidLauncher(item.info.shortname))
                    {
                        UpdateLauncherCapacity(projectile, item.info.shortname);
                        updated++;
                    }
                }
            }

            // Update launchers in storage containers, dropped items, etc.
            foreach (var entity in BaseNetworkable.serverEntities)
            {
                if (entity is StorageContainer container && container.inventory != null)
                {
                    foreach (var item in container.inventory.itemList)
                    {
                        if (IsValidLauncher(item.info.shortname) && item.GetHeldEntity() is BaseProjectile projectile)
                        {
                            UpdateLauncherCapacity(projectile, item.info.shortname);
                            updated++;
                        }
                    }
                }
                else if (entity is DroppedItemContainer droppedContainer && droppedContainer.inventory != null)
                {
                    foreach (var item in droppedContainer.inventory.itemList)
                    {
                        if (IsValidLauncher(item.info.shortname) && item.GetHeldEntity() is BaseProjectile projectile)
                        {
                            UpdateLauncherCapacity(projectile, item.info.shortname);
                            updated++;
                        }
                    }
                }
            }

            Puts($"DoubleRocket: Updated {updated} existing launchers across all locations.");
        }

        // Helper method to update launchers for a specific player
        private int UpdatePlayerLaunchers(BasePlayer player)
        {
            int updated = 0;

            if (player?.inventory == null) return 0;

            // Check belt
            if (player.inventory.containerBelt != null)
            {
                foreach (var item in player.inventory.containerBelt.itemList)
                {
                    if (IsValidLauncher(item.info.shortname) && item.GetHeldEntity() is BaseProjectile projectile)
                    {
                        UpdateLauncherCapacity(projectile, item.info.shortname);
                        updated++;
                    }
                }
            }

            // Check main inventory
            if (player.inventory.containerMain != null)
            {
                foreach (var item in player.inventory.containerMain.itemList)
                {
                    if (IsValidLauncher(item.info.shortname) && item.GetHeldEntity() is BaseProjectile projectile)
                    {
                        UpdateLauncherCapacity(projectile, item.info.shortname);
                        updated++;
                    }
                }
            }

            // Check wear container (in case someone has a launcher equipped as clothing somehow)
            if (player.inventory.containerWear != null)
            {
                foreach (var item in player.inventory.containerWear.itemList)
                {
                    if (IsValidLauncher(item.info.shortname) && item.GetHeldEntity() is BaseProjectile projectile)
                    {
                        UpdateLauncherCapacity(projectile, item.info.shortname);
                        updated++;
                    }
                }
            }

            return updated;
        }

        #endregion

        #region Debug Commands (Temporary)

        [ConsoleCommand("doublerocket.update")]
        private void CmdUpdateLaunchers(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;

            Puts("Manual update triggered...");
            UpdateAllLaunchers();
        }

        [ConsoleCommand("doublerocket.debug")]
        private void CmdDebugLaunchers(ConsoleSystem.Arg arg)
        {
            if (arg.Player() != null && !arg.Player().IsAdmin) return;

            int totalLaunchers = 0;

            // Count all rocket launchers on the server
            foreach (var entity in BaseNetworkable.serverEntities)
            {
                if (entity is BaseProjectile projectile)
                {
                    var item = projectile.GetItem();
                    if (item != null && IsValidLauncher(item.info.shortname))
                    {
                        totalLaunchers++;
                        Puts($"Found launcher: {item.info.shortname}, Capacity: {projectile.primaryMagazine?.capacity ?? 0}");
                    }
                }
            }

            Puts($"Total rocket launchers found: {totalLaunchers}");
        }

        #endregion
    }
}
