using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Rust;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Anti Offline", "Skelee", "1.1.0")]
    [Description("Reduces explosive damage during specified offline hours to protect bases.")]

    public class AwakenAntiOffline : CovalencePlugin
    {
        #region Config
        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("System Time to Deactivate Raiding")] string RaidDeactivateTime = "12:00 PM",
            [property: JsonProperty("System Time to Reactivate Raiding")] string RaidActivateTime = "12:30 AM",
            [property: JsonProperty("Amount to scale explosive damage (0-1) (0 - No Damage, 0.25 - 25% Damage, 0.5 - Half Damage, 1 - Normal Damage)")] float ScaleDamage = 0.25f,
            [property: JsonProperty("Send message when raiding in deactivated hours?")] bool SendMessage = true,
            [property: JsonProperty("Send message when connecting in deactivated hours?")] bool SendMessageOnConnect = true,
            [property: JsonProperty("Reset raid protection to default on server wipe?")] bool ResetRaidOnWipe = true);

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Language
        protected override void LoadDefaultMessages() => lang.RegisterMessages(new Dictionary<string, string>
        {
            ["ScaledDown"] = "<color=#7000fd>[Awaken Anti Offline]</color> Explosive damage has been <color=green>reduced</color> by {0}%, until {1} {2}",
            ["ScaledUp"] = "<color=#7000fd>[Awaken Anti Offline]</color> Explosive damage has been <color=green>reinstated</color> back to full, until {0} {1}",
            ["PlayerMessage"] = "<color=#7000fd>[Awaken Anti Offline]</color> Damage is <color=red>scaled down</color> {0}% as you're raiding during offline hours!",
            ["PlayerMessageConnect"] = "<color=#7000fd>[Awaken Anti Offline]</color> Explosive damage is <color=red>scaled down</color> {0}% during offline hours! Damage restores at {1} {2}.",
            ["OfflineProtectionTip"] = "<color=red>⚠ OFFLINE PROTECTION ACTIVE ⚠</color>\n<color=orange>This base will only take</color> <color=yellow>{0}%</color> <color=orange>damage</color>"
        }, this);
        #endregion

        #region Data
        private class Save
        {
            public bool RaidingDeactivated { get; set; } = true;
        }

        private Save? _save;

        private void SaveData() => Interface.Oxide.DataFileSystem.WriteObject(Name, _save);
        #endregion

        #region Defines
        private Timer? antiRaidTimer;
        private string dateTime = "";
        #endregion

        #region Hooks
        private void Loaded()
        {
            _save = Interface.Oxide.DataFileSystem.ReadObject<Save>(Name) ?? new Save();
            if (config?.ResetRaidOnWipe == true) Unsubscribe(nameof(OnNewSave));
            permission.RegisterPermission("awakenanti offline.toggle", this);
        }

        private void OnServerInitialized(bool initial)
        {
            antiRaidTimer = timer.Repeat(60, 0, () => // Changed from 1 second to 60 seconds
            {
                dateTime = DateTime.Now.ToString("hh:mm tt");
                if (dateTime == config?.RaidDeactivateTime && !_save?.RaidingDeactivated == true)
                {
                    server.Broadcast(string.Format(lang.GetMessage("ScaledDown", this), (config.ScaleDamage * 100), config.RaidActivateTime, TimeZoneInfo.Local.StandardName));
                    _save = new Save { RaidingDeactivated = true };
                    SaveData();
                }
                else if (dateTime == config?.RaidActivateTime && _save?.RaidingDeactivated == true)
                {
                    server.Broadcast(string.Format(lang.GetMessage("ScaledUp", this), config.RaidDeactivateTime, TimeZoneInfo.Local.StandardName));
                    _save = new Save { RaidingDeactivated = false };
                    SaveData();
                }
            });
        }

        private void OnNewSave(string filename)
        {
            _save = new Save { RaidingDeactivated = false };
            SaveData();
        }

        private void Unload()
        {
            antiRaidTimer?.Destroy();
            config = null;
        }

        private object? OnEntityTakeDamage(BaseCombatEntity? entity, HitInfo? info)
        {
            if (info == null || info.damageTypes.GetMajorityDamageType() != DamageType.Explosion) return null;
            if (_save?.RaidingDeactivated != true) return null;

            info.damageTypes.ScaleAll(config?.ScaleDamage ?? 0f);
            if (config?.SendMessage == true && info.InitiatorPlayer != null && info.WeaponPrefab != null)
            {
                // Only show the red gametip, no chat message
                ShowOfflineProtectionTip(info.InitiatorPlayer);
            }
            return null;
        }

        private void OnPlayerConnected(BasePlayer? player)
        {
            if (config?.SendMessageOnConnect == true && _save?.RaidingDeactivated == true)
                player?.ChatMessage(string.Format(lang.GetMessage("PlayerMessageConnect", this), (config.ScaleDamage * 100), config.RaidActivateTime, TimeZoneInfo.Local.StandardName));
        }
        #endregion

        #region Commands
        [Command("toggleraidprotection")]
        private void ToggleRaidProtectionCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (!iPlayer.IsServer && !permission.UserHasPermission(iPlayer.Id, "awakenantioffline.toggle"))
            {
                iPlayer.Reply("You don't have permission to use this command.");
                return;
            }

            _save = new Save { RaidingDeactivated = !(_save?.RaidingDeactivated ?? false) };

            var (logMessage, broadcastMessage) = _save.RaidingDeactivated ?
                ("Turned on raid protection.", string.Format(lang.GetMessage("ScaledDown", this), (config?.ScaleDamage * 100), config?.RaidActivateTime, TimeZoneInfo.Local.StandardName)) :
                ("Turned off raid protection.", string.Format(lang.GetMessage("ScaledUp", this), config?.RaidDeactivateTime, TimeZoneInfo.Local.StandardName));

            Debug.Log(logMessage);
            server.Broadcast(broadcastMessage);
            SaveData();

            iPlayer.Reply($"Raid protection {(_save.RaidingDeactivated ? "enabled" : "disabled")}.");
        }
        #endregion

        #region Notification Functions
        private void ShowGameTip(BasePlayer player, string message)
        {
            // Show red toast notification instead of blue gametip
            player.SendConsoleCommand("gametip.showtoast", "1", message);
            timer.Once(5f, () => player.SendConsoleCommand("gametip.hidetoast"));
        }

        private void ShowOfflineProtectionTip(BasePlayer player)
        {
            var damagePercentage = (config?.ScaleDamage ?? 0f) * 100;
            ShowGameTip(player, string.Format(lang.GetMessage("OfflineProtectionTip", this), damagePercentage.ToString("F0")));
        }
        #endregion
    }
}










