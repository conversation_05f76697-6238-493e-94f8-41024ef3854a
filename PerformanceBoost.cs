using Oxide.Core;
using Oxide.Core.Plugins;
using UnityEngine;
using System.Linq;

namespace Oxide.Plugins
{
    [Info("PerformanceBoost", "Skelee", "1.0.0")]
    [Description("Optimizes Rust server performance by dynamically adjusting settings and cleaning up entities.")]
    public class PerformanceBoost : RustPlugin
    {
        private const float CheckInterval = 60f; // Check every minute
        private float lastCheckTime;
        private int targetFps = 60; // Default target FPS
        private bool isInitialized = false;

        #region Configuration
        private class ConfigData
        {
            public int TargetServerFps { get; set; } = 60;
            public float GarbageCollectInterval { get; set; } = 1800f; // 30 minutes default
            public int MaxEntitiesBeforeCleanup { get; set; } = 250000; // Cleanup threshold
            public int TargetTickRate { get; set; } = 30; // Server tickrate
            public bool EnableDiscordWebhook { get; set; } = false;
            public string DiscordWebhookUrl { get; set; } = "";
            public bool LogToConsole { get; set; } = true;
        }

        private ConfigData config;
        private float lastGcTime;

        protected override void LoadDefaultConfig()
        {
            config = new ConfigData();
            SaveConfig();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            config = Config.ReadObject<ConfigData>();
            if (config == null)
            {
                LoadDefaultConfig();
            }
        }

        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }
        #endregion

        #region Initialization
        void Init()
        {
            timer.Every(CheckInterval, CheckPerformance);
            lastCheckTime = Time.realtimeSinceStartup;
            lastGcTime = Time.realtimeSinceStartup;
            targetFps = config.TargetServerFps;
            ConVar.Server.tickrate = config.TargetTickRate;
            isInitialized = true;
            Puts("PerformanceBoost initialized. Monitoring server FPS and entities.");
        }

        void Unload()
        {
            isInitialized = false;
            Puts("PerformanceBoost unloaded.");
        }
        #endregion

        #region Performance Monitoring
        private void CheckPerformance()
        {
            if (!isInitialized) return;

            float currentTime = Time.realtimeSinceStartup;
            if (currentTime - lastCheckTime < CheckInterval) return;
            lastCheckTime = currentTime;

            float serverFps = Performance.current.frameRate;
            int entityCount = BaseNetworkable.serverEntities.Count;

            LogPerformance(serverFps, entityCount);

            // Trigger garbage collection based on interval and FPS
            ManageGarbageCollection(serverFps);

            // Cleanup entities if over threshold
            if (entityCount > config.MaxEntitiesBeforeCleanup)
            {
                CleanupEntities();
            }

            // Adjust tickrate dynamically based on FPS
            AdjustTickRate(serverFps);

            // Cap server FPS to avoid overworking CPU
            if (serverFps > targetFps + 10)
            {
                ConVar.FPS.limit = targetFps;
                LogMessage($"Server FPS capped at {targetFps} to reduce CPU load.");
            }
        }

        private void ManageGarbageCollection(float serverFps)
        {
            float currentTime = Time.realtimeSinceStartup;
            if (currentTime - lastGcTime >= config.GarbageCollectInterval || serverFps < targetFps * 0.8f)
            {
                System.GC.Collect();
                lastGcTime = currentTime;
                LogMessage($"Garbage collection triggered. FPS: {serverFps:F1}");
            }
        }

        private void AdjustTickRate(float serverFps)
        {
            int currentTickRate = ConVar.Server.tickrate;
            if (serverFps < targetFps * 0.8f && currentTickRate > 10)
            {
                ConVar.Server.tickrate = Mathf.Max(currentTickRate - 5, 10);
                LogMessage($"FPS low ({serverFps:F1}). Reduced tickrate to {ConVar.Server.tickrate}.");
            }
            else if (serverFps > targetFps * 1.2f && currentTickRate < config.TargetTickRate)
            {
                ConVar.Server.tickrate = Mathf.Min(currentTickRate + 5, config.TargetTickRate);
                LogMessage($"FPS high ({serverFps:F1}). Increased tickrate to {ConVar.Server.tickrate}.");
            }
        }

        private void CleanupEntities()
        {
            int removedCount = 0;
            foreach (var entity in BaseNetworkable.serverEntities.ToList())
            {
                if (entity == null || entity.IsDestroyed) continue;

                if (entity is DroppedItem item)
                {
                    item.Kill();
                    removedCount++;
                }
            }
            LogMessage($"Cleaned up {removedCount} dropped items to reduce entity count.");
        }
        #endregion

        #region Logging
        private void LogMessage(string message)
        {
            if (config.LogToConsole)
            {
                Puts(message);
            }

            if (config.EnableDiscordWebhook && !string.IsNullOrEmpty(config.DiscordWebhookUrl))
            {
                webrequest.Enqueue(config.DiscordWebhookUrl, "{\"content\": \"" + message + "\"}", (code, response) =>
                {
                    if (code != 204) Puts($"Discord webhook failed: {code} - {response}");
                }, this);
            }
        }

        private void LogPerformance(float fps, int entityCount)
        {
            string status = $"Server FPS: {fps:F1}, Entities: {entityCount}, Tickrate: {ConVar.Server.tickrate}";
            if (fps < targetFps * 0.8f)
            {
                status += " (WARNING: Low FPS)";
            }
            LogMessage(status);
        }
        #endregion

        #region Commands
        [ConsoleCommand("perfboost.status")]
        private void CmdPerfStatus(ConsoleSystem.Arg arg)
        {
            float fps = Performance.current.frameRate;
            int entityCount = BaseNetworkable.serverEntities.Count;
            string status = $"Current Server FPS: {fps:F1}\nEntity Count: {entityCount}\nTarget FPS: {targetFps}\nTickrate: {ConVar.Server.tickrate}";
            arg.ReplyWith(status);
        }

        [ChatCommand("perfboost")]
        private void CmdPerfBoost(BasePlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;
            CmdPerfStatus(null);
        }
        #endregion
    }
}