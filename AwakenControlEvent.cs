using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Control Event", "Skelee", "1.0.0")]
    [Description("Creates control point events where clans fight for control of designated areas with spawn persistence and comprehensive stat tracking.")]
    public class AwakenControlEvent : CovalencePlugin
    {
        [PluginReference] private Plugin ClanCores;
        [PluginReference] private Plugin AwakenStats;
        #region Config
        public class UI
        {
            [JsonProperty(PropertyName = "Use UI")] public bool useUI;
            [JsonProperty(PropertyName = "UI Text")] public string UIText;
            [JsonProperty(PropertyName = "UI Text Color")] public string UITextColor;
            [JsonProperty(PropertyName = "UI Text Font Size")] public int UITextFontSize;
            [JsonProperty(PropertyName = "UI Background Color")] public string UIBackgroundColor;
        }

        public class Messages
        {
            [JsonProperty(PropertyName = "Display Name for Chat Messages")] public string chatName;
            [JsonProperty(PropertyName = "Display Name Color for Chat Messages")] public string chatNameColor;
            [JsonProperty(PropertyName = "Steam ID Avatar for Chat Messages")] public ulong avatarSteamID;
            [JsonProperty(PropertyName = "Send Global Chat Message when event starts?")] public bool sendChatMessageOnStart;
            [JsonProperty(PropertyName = "Send End Result to Global Chat?")] public bool sendEndResultToChat;
            [JsonProperty(PropertyName = "Send End Result to Discord?")] public bool sendEndResultToDiscord;
        }

        public class Discord
        {
            [JsonProperty(PropertyName = "Use Discord")] public bool useDiscord;
            [JsonProperty(PropertyName = "Discord Webhook URL")] public string discordWebhookURL;
            [JsonProperty(PropertyName = "Embed Title Winners")] public string embedTitleWinners;
            [JsonProperty(PropertyName = "Embed Color Winners")] public int embedColorWinners;
            [JsonProperty(PropertyName = "Embed Title No Winners")] public string embedTitleNoWinners;
            [JsonProperty(PropertyName = "Embed Color No Winners")] public int embedColorNoWinners;
            [JsonProperty(PropertyName = "Server Details")] public string serverDetails;
        }

        public class ControlSettings
        {
            [JsonProperty(PropertyName = "Control Point Radius")] public float controlRadius;
            [JsonProperty(PropertyName = "Capture Time (seconds)")] public float captureTime;
            [JsonProperty(PropertyName = "Event Duration (seconds)")] public float eventDuration;
            [JsonProperty(PropertyName = "Minimum Players to Start")] public int minPlayersToStart;
            [JsonProperty(PropertyName = "Control Points")] public List<ControlPoint> controlPoints;
            [JsonProperty(PropertyName = "Spawn Points")] public List<SpawnPoint> spawnPoints;
        }

        public class ControlPoint
        {
            [JsonProperty(PropertyName = "Name")] public string name;
            [JsonProperty(PropertyName = "Position")] public Vector3 position;
            [JsonProperty(PropertyName = "Radius")] public float radius;
        }

        public class SpawnPoint
        {
            [JsonProperty(PropertyName = "Name")] public string name;
            [JsonProperty(PropertyName = "Position")] public Vector3 position;
            [JsonProperty(PropertyName = "For Control Point")] public string controlPointName;
        }

        public class WeaponRestrictions
        {
            [JsonProperty(PropertyName = "Allowed Weapons")] public List<string> allowedWeapons;
            [JsonProperty(PropertyName = "Allowed Attachments")] public List<string> allowedAttachments;
            [JsonProperty(PropertyName = "Block Other Weapons")] public bool blockOtherWeapons;
            [JsonProperty(PropertyName = "Block Other Attachments")] public bool blockOtherAttachments;
        }

        static Configuration config;
        public class Configuration
        {
            [JsonProperty(PropertyName = "Use Clans")] public bool useClans;
            [JsonProperty(PropertyName = "Message Settings")] public Messages messages;
            [JsonProperty(PropertyName = "UI Settings")] public UI ui;
            [JsonProperty(PropertyName = "Discord Settings")] public Discord discord;
            [JsonProperty(PropertyName = "Control Settings")] public ControlSettings controlSettings;
            [JsonProperty(PropertyName = "Weapon Restrictions")] public WeaponRestrictions weaponRestrictions;
            [JsonProperty(PropertyName = "API Webhook URL")] public string apiWebhookURL;
            [JsonProperty(PropertyName = "Webhook")] public string webhook;

            public static Configuration DefaultConfig()
            {
                return new Configuration
                {
                    useClans = true,
                    messages = new Messages
                    {
                        chatName = "Atlas Events",
                        chatNameColor = "#ff6b35",
                        avatarSteamID = 76561198194158447,
                        sendChatMessageOnStart = true,
                        sendEndResultToChat = true,
                        sendEndResultToDiscord = true
                    },
                    ui = new UI
                    {
                        useUI = true,
                        UIText = "CONTROL",
                        UITextColor = "1 0.42 0.21 1", // Orange color
                        UITextFontSize = 20,
                        UIBackgroundColor = "0 0 0 0.5"
                    },
                    discord = new Discord
                    {
                        useDiscord = true,
                        discordWebhookURL = "",
                        embedTitleWinners = "Atlas Control Event - Winners",
                        embedColorWinners = 16737843, // Orange color
                        embedTitleNoWinners = "Atlas Control Event - No Winners",
                        embedColorNoWinners = 15158332,
                        serverDetails = "Atlas - US 10x | No BPs | Kits | Shop"
                    },
                    controlSettings = new ControlSettings
                    {
                        controlRadius = 50f,
                        captureTime = 60f,
                        eventDuration = 1800f, // 30 minutes
                        minPlayersToStart = 4,
                        controlPoints = new List<ControlPoint>(),
                        spawnPoints = new List<SpawnPoint>()
                    },
                    weaponRestrictions = new WeaponRestrictions
                    {
                        allowedWeapons = new List<string> { "rifle.ak", "rifle.m2" },
                        allowedAttachments = new List<string> { "weapon.mod.silencer", "weapon.mod.holosight" },
                        blockOtherWeapons = true,
                        blockOtherAttachments = true
                    },
                    apiWebhookURL = "https://cdn.pulserustservers.com/api/webhook",
                    webhook = "REPLACE"
                };
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig()
        {
            config = Configuration.DefaultConfig();
            SaveConfig();
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Localization
        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["EventStartedChat"] = "<color={0}>{1}</color>: Control Event has started! Use /control to join the battle!",
                ["EventEndedChat"] = "<color={0}>{1}</color>: Control Event has ended!",
                ["AlreadyInEvent"] = "You are already in the control event!",
                ["NoActiveEvent"] = "There is no active control event.",
                ["EventFull"] = "The control event is currently full.",
                ["TeleportedToEvent"] = "You have been teleported to the control event!",
                ["EventStarted"] = "Control Event Started!",
                ["EventEnded"] = "Control Event Ended!",
                ["ClanControlling"] = "Clan {0} is now controlling {1}!",
                ["ClanWins"] = "Clan {0} wins the control event!",
                ["NoWinner"] = "No clan won the control event.",
                ["NotEnoughPlayers"] = "Not enough players to start the control event. Minimum: {0}",
                ["ControlPointSet"] = "Control point '{0}' set at your current position.",
                ["SpawnPointSet"] = "Spawn point '{0}' set for control point '{1}' at your current position.",
                ["InvalidControlPoint"] = "Invalid control point name. Available: {0}",
                ["ControlPointNotFound"] = "Control point '{0}' not found.",
                ["SpawnPointNotFound"] = "No spawn points found for control point '{0}'."
            }, this);
        }

        private void SendMessage(BasePlayer player, string messageKey, params object[] args)
        {
            if (player == null) return;
            string message = string.Format(lang.GetMessage(messageKey, this, player.UserIDString), args);
            player.ChatMessage($"<color={config.messages.chatNameColor}>{config.messages.chatName}</color>: {message}");
        }

        private void SendGlobalMessage(string messageKey, params object[] args)
        {
            string message = string.Format(lang.GetMessage(messageKey, this), args);
            foreach (var player in BasePlayer.activePlayerList)
            {
                player.ChatMessage(message);
            }
        }
        #endregion

        #region Defines
        public static AwakenControlEvent Instance;
        [PluginReference] private Plugin? AwakenClans, AwakenVotingSystem;
        private const string AdminPermission = "awakencontrol.admin";
        private const string UsePermission = "awakencontrol.use";
        private List<ControlEventComp> activeEvents = new List<ControlEventComp>();
        private Dictionary<string, ControlEventComp> playersInEvent = new Dictionary<string, ControlEventComp>();
        private Dictionary<string, string> clanSpawnAssignments = new Dictionary<string, string>(); // clan -> spawn point name
        #endregion

        #region Initialization
        private void Init()
        {
            Instance = this;
            permission.RegisterPermission(AdminPermission, this);
            permission.RegisterPermission(UsePermission, this);

            AddCovalenceCommand("control", nameof(ControlCommand));
            AddCovalenceCommand("startcontrol", nameof(StartControlCommand));
            AddCovalenceCommand("endcontrol", nameof(EndControlCommand));
            AddCovalenceCommand("setcontrolpoint", nameof(SetControlPointCommand));
            AddCovalenceCommand("setspawnpoint", nameof(SetSpawnPointCommand));
        }

        private void Unload()
        {
            foreach (var eventComp in activeEvents.ToList())
            {
                if (eventComp != null)
                    eventComp.EndEvent();
            }
            Instance = null;
        }
        #endregion

        #region Data Classes
        public class TeamStats
        {
            public int kills = 0;
            public int deaths = 0;
            public int akCount = 0;
            public int m2Count = 0;
            public float controlTime = 0f;
            public string clanTag = "";
            public List<string> members = new List<string>();
        }

        public class PlayerStats
        {
            public int kills = 0;
            public int deaths = 0;
            public int akCount = 0;
            public int m2Count = 0;
            public string playerName = "";
            public string clanTag = "";
        }

        public class WeaponCount
        {
            public int akCount = 0;
            public int m2Count = 0;
        }

        public class ControlPointStatus
        {
            public string name;
            public Vector3 position;
            public float radius;
            public string controllingClan = "";
            public float captureProgress = 0f;
            public bool isContested = false;
            public List<string> playersInZone = new List<string>();
        }
        #endregion

        #region Commands
        [Command("control")]
        private void ControlCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (playersInEvent.ContainsKey(player.UserIDString))
            {
                SendMessage(player, "AlreadyInEvent");
                return;
            }

            if (activeEvents.Count == 0)
            {
                SendMessage(player, "NoActiveEvent");
                return;
            }

            ControlEventComp targetEvent = activeEvents.FirstOrDefault();
            if (targetEvent == null) return;

            // Get player's clan
            string clanTag = GetPlayerClan(player);
            if (string.IsNullOrEmpty(clanTag))
            {
                SendMessage(player, "You must be in a clan to participate in control events.");
                return;
            }

            // Find appropriate spawn point
            Vector3 spawnPosition = GetClanSpawnPosition(clanTag, targetEvent);
            if (spawnPosition == Vector3.zero)
            {
                SendMessage(player, "No spawn points available for your clan.");
                return;
            }

            player.Teleport(spawnPosition);
            SendMessage(player, "TeleportedToEvent");
        }

        [Command("startcontrol")]
        private void StartControlCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, AdminPermission))
            {
                SendMessage(player, "You don't have permission to use this command.");
                return;
            }

            if (activeEvents.Count > 0)
            {
                SendMessage(player, "A control event is already active.");
                return;
            }

            if (config.controlSettings.controlPoints.Count == 0)
            {
                SendMessage(player, "No control points configured. Use /setcontrolpoint to add some.");
                return;
            }

            StartControlEvent();
            SendMessage(player, "Control event started!");
        }

        [Command("endcontrol")]
        private void EndControlCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, AdminPermission))
            {
                SendMessage(player, "You don't have permission to use this command.");
                return;
            }

            if (activeEvents.Count == 0)
            {
                SendMessage(player, "No active control event to end.");
                return;
            }

            foreach (var eventComp in activeEvents.ToList())
            {
                eventComp.EndEvent();
            }
            SendMessage(player, "Control event ended!");
        }

        [Command("setcontrolpoint")]
        private void SetControlPointCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, AdminPermission))
            {
                SendMessage(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length < 1)
            {
                SendMessage(player, "Usage: /setcontrolpoint <name>");
                return;
            }

            string pointName = args[0];
            Vector3 position = player.transform.position;

            // Remove existing control point with same name
            config.controlSettings.controlPoints.RemoveAll(cp => cp.name == pointName);

            // Add new control point
            config.controlSettings.controlPoints.Add(new ControlPoint
            {
                name = pointName,
                position = position,
                radius = config.controlSettings.controlRadius
            });

            SaveConfig();
            SendMessage(player, "ControlPointSet", pointName);
        }

        [Command("setspawnpoint")]
        private void SetSpawnPointCommand(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            BasePlayer player = iPlayer.Object as BasePlayer;

            if (!permission.UserHasPermission(player.UserIDString, AdminPermission))
            {
                SendMessage(player, "You don't have permission to use this command.");
                return;
            }

            if (args.Length < 2)
            {
                SendMessage(player, "Usage: /setspawnpoint <name> <controlpoint>");
                return;
            }

            string spawnName = args[0];
            string controlPointName = args[1];

            // Check if control point exists
            if (!config.controlSettings.controlPoints.Any(cp => cp.name == controlPointName))
            {
                var availablePoints = string.Join(", ", config.controlSettings.controlPoints.Select(cp => cp.name));
                SendMessage(player, "InvalidControlPoint", availablePoints);
                return;
            }

            Vector3 position = player.transform.position;

            // Remove existing spawn point with same name
            config.controlSettings.spawnPoints.RemoveAll(sp => sp.name == spawnName);

            // Add new spawn point
            config.controlSettings.spawnPoints.Add(new SpawnPoint
            {
                name = spawnName,
                position = position,
                controlPointName = controlPointName
            });

            SaveConfig();
            SendMessage(player, "SpawnPointSet", spawnName, controlPointName);
        }
        #endregion

        #region Helper Methods
        private string GetPlayerClan(BasePlayer player)
        {
            if (!config.useClans || AwakenClans == null) return player.UserIDString;

            // Use the GetClanTag API method directly with player ID
            var clanTag = AwakenClans.Call("GetClanTag", player.userID);
            return clanTag as string ?? player.UserIDString;
        }

        private Vector3 GetClanSpawnPosition(string clanTag, ControlEventComp eventComp)
        {
            // Check if clan already has an assigned spawn point
            if (clanSpawnAssignments.ContainsKey(clanTag))
            {
                string assignedSpawnName = clanSpawnAssignments[clanTag];
                var spawnPoint = config.controlSettings.spawnPoints.FirstOrDefault(sp => sp.name == assignedSpawnName);
                if (spawnPoint != null)
                {
                    return spawnPoint.position;
                }
            }

            // Assign a new spawn point to the clan
            var availableSpawns = config.controlSettings.spawnPoints
                .Where(sp => !clanSpawnAssignments.ContainsValue(sp.name))
                .ToList();

            if (availableSpawns.Count > 0)
            {
                var selectedSpawn = availableSpawns[UnityEngine.Random.Range(0, availableSpawns.Count)];
                clanSpawnAssignments[clanTag] = selectedSpawn.name;
                return selectedSpawn.position;
            }

            return Vector3.zero;
        }

        private void StartControlEvent()
        {
            var eventObject = new GameObject("ControlEvent");
            var eventComp = eventObject.AddComponent<ControlEventComp>();
            eventComp.StartEvent();

            if (config.messages.sendChatMessageOnStart)
            {
                SendGlobalMessage("EventStartedChat", config.messages.chatNameColor, config.messages.chatName);
            }
        }
        #endregion

        #region Event Component
        public class ControlEventComp : MonoBehaviour
        {
            public Dictionary<string, TeamStats> teamStats = new Dictionary<string, TeamStats>();
            public Dictionary<string, PlayerStats> playerStats = new Dictionary<string, PlayerStats>();
            public Dictionary<string, WeaponCount> weaponCounts = new Dictionary<string, WeaponCount>();
            public Dictionary<string, ControlPointStatus> controlPoints = new Dictionary<string, ControlPointStatus>();

            public float eventStartTime;
            public float eventEndTime;
            public bool eventActive = false;

            private List<SphereCollider> controlColliders = new List<SphereCollider>();

            public void StartEvent()
            {
                eventActive = true;
                eventStartTime = UnityEngine.Time.realtimeSinceStartup;
                eventEndTime = eventStartTime + config.controlSettings.eventDuration;

                Instance.activeEvents.Add(this);

                // Initialize control points
                InitializeControlPoints();

                // Start update loops
                InvokeRepeating(nameof(UpdateControlPoints), 1f, 1f);
                InvokeRepeating(nameof(UpdatePlayerUI), 1f, 1f);

                Invoke(nameof(EndEvent), config.controlSettings.eventDuration);
            }

            private void InitializeControlPoints()
            {
                foreach (var cp in config.controlSettings.controlPoints)
                {
                    var status = new ControlPointStatus
                    {
                        name = cp.name,
                        position = cp.position,
                        radius = cp.radius
                    };
                    controlPoints[cp.name] = status;

                    // Create collider for control point
                    var colliderObj = new GameObject($"ControlPoint_{cp.name}");
                    colliderObj.transform.position = cp.position;
                    colliderObj.transform.SetParent(transform);

                    var collider = colliderObj.AddComponent<SphereCollider>();
                    collider.radius = cp.radius;
                    collider.isTrigger = true;

                    var trigger = colliderObj.AddComponent<ControlPointTrigger>();
                    trigger.controlPointName = cp.name;
                    trigger.eventComp = this;

                    controlColliders.Add(collider);
                }
            }

            private void UpdateControlPoints()
            {
                if (!eventActive) return;

                foreach (var kvp in controlPoints)
                {
                    var cp = kvp.Value;
                    UpdateControlPointStatus(cp);
                }
            }

            private void UpdateControlPointStatus(ControlPointStatus cp)
            {
                // Count clans in the control point
                var clansInZone = new Dictionary<string, int>();

                foreach (var playerId in cp.playersInZone.ToList())
                {
                    var player = BasePlayer.FindByID(ulong.Parse(playerId));
                    if (player == null || !player.IsConnected)
                    {
                        cp.playersInZone.Remove(playerId);
                        continue;
                    }

                    string clanTag = Instance.GetPlayerClan(player);
                    if (!clansInZone.ContainsKey(clanTag))
                        clansInZone[clanTag] = 0;
                    clansInZone[clanTag]++;
                }

                // Determine control status
                if (clansInZone.Count == 0)
                {
                    // No one in zone
                    cp.isContested = false;
                    if (!string.IsNullOrEmpty(cp.controllingClan))
                    {
                        cp.captureProgress = Mathf.Max(0, cp.captureProgress - Time.deltaTime);
                        if (cp.captureProgress <= 0)
                        {
                            cp.controllingClan = "";
                        }
                    }
                }
                else if (clansInZone.Count == 1)
                {
                    // Single clan in zone
                    var dominantClan = clansInZone.First().Key;
                    cp.isContested = false;

                    if (cp.controllingClan == dominantClan)
                    {
                        // Already controlling, add control time
                        if (teamStats.ContainsKey(dominantClan))
                        {
                            teamStats[dominantClan].controlTime += Time.deltaTime;
                        }
                    }
                    else
                    {
                        // Capturing
                        cp.captureProgress += Time.deltaTime;
                        if (cp.captureProgress >= config.controlSettings.captureTime)
                        {
                            // Captured!
                            string previousClan = cp.controllingClan;
                            cp.controllingClan = dominantClan;
                            cp.captureProgress = config.controlSettings.captureTime;

                            Instance.SendGlobalMessage("ClanControlling", dominantClan, cp.name);
                        }
                    }
                }
                else
                {
                    // Multiple clans, contested
                    cp.isContested = true;
                    cp.captureProgress = Mathf.Max(0, cp.captureProgress - Time.deltaTime * 0.5f);
                }
            }

            private void UpdatePlayerUI()
            {
                if (!config.ui.useUI || !eventActive) return;

                foreach (var player in BasePlayer.activePlayerList)
                {
                    if (Instance.playersInEvent.ContainsKey(player.UserIDString))
                    {
                        Instance.CreateControlUI(player, this);
                    }
                }
            }

            public void OnPlayerEnterControlPoint(string controlPointName, BasePlayer player)
            {
                if (!controlPoints.ContainsKey(controlPointName)) return;

                var cp = controlPoints[controlPointName];
                if (!cp.playersInZone.Contains(player.UserIDString))
                {
                    cp.playersInZone.Add(player.UserIDString);
                }

                // Add player to event if not already
                if (!Instance.playersInEvent.ContainsKey(player.UserIDString))
                {
                    Instance.playersInEvent[player.UserIDString] = this;

                    // Initialize player stats
                    if (!playerStats.ContainsKey(player.UserIDString))
                    {
                        playerStats[player.UserIDString] = new PlayerStats
                        {
                            playerName = player.displayName,
                            clanTag = Instance.GetPlayerClan(player)
                        };
                    }

                    // Initialize weapon counts
                    if (!weaponCounts.ContainsKey(player.UserIDString))
                    {
                        weaponCounts[player.UserIDString] = new WeaponCount();
                        CountPlayerWeapons(player);
                    }
                }
            }

            public void OnPlayerExitControlPoint(string controlPointName, BasePlayer player)
            {
                if (!controlPoints.ContainsKey(controlPointName)) return;

                var cp = controlPoints[controlPointName];
                cp.playersInZone.Remove(player.UserIDString);
            }

            private void CountPlayerWeapons(BasePlayer player)
            {
                if (!weaponCounts.ContainsKey(player.UserIDString)) return;

                var weaponCount = weaponCounts[player.UserIDString];
                weaponCount.akCount = 0;
                weaponCount.m2Count = 0;

                // Count weapons in inventory
                var allItems = new List<Item>();
                allItems.AddRange(player.inventory.containerMain.itemList);
                allItems.AddRange(player.inventory.containerBelt.itemList);
                allItems.AddRange(player.inventory.containerWear.itemList);

                foreach (var item in allItems)
                {
                    if (item.info.shortname == "rifle.ak")
                        weaponCount.akCount++;
                    else if (item.info.shortname == "rifle.m2")
                        weaponCount.m2Count++;
                }
            }

            public void OnPlayerKill(BasePlayer killer, BasePlayer victim)
            {
                if (!eventActive) return;

                string killerClan = Instance.GetPlayerClan(killer);
                string victimClan = Instance.GetPlayerClan(victim);

                // Update player stats
                if (playerStats.ContainsKey(killer.UserIDString))
                {
                    playerStats[killer.UserIDString].kills++;
                }
                if (playerStats.ContainsKey(victim.UserIDString))
                {
                    playerStats[victim.UserIDString].deaths++;
                }

                // Update team stats
                if (!teamStats.ContainsKey(killerClan))
                {
                    teamStats[killerClan] = new TeamStats { clanTag = killerClan };
                }
                if (!teamStats.ContainsKey(victimClan))
                {
                    teamStats[victimClan] = new TeamStats { clanTag = victimClan };
                }

                teamStats[killerClan].kills++;
                teamStats[victimClan].deaths++;

                // Update weapon counts
                CountPlayerWeapons(killer);
                if (weaponCounts.ContainsKey(killer.UserIDString))
                {
                    teamStats[killerClan].akCount = teamStats[killerClan].members
                        .Where(m => weaponCounts.ContainsKey(m))
                        .Sum(m => weaponCounts[m].akCount);
                    teamStats[killerClan].m2Count = teamStats[killerClan].members
                        .Where(m => weaponCounts.ContainsKey(m))
                        .Sum(m => weaponCounts[m].m2Count);
                }
            }

            public void EndEvent()
            {
                if (!eventActive) return;
                eventActive = false;

                CancelInvoke();

                // Determine winner
                var winner = teamStats.OrderByDescending(t => t.Value.controlTime).FirstOrDefault();

                if (winner.Key != null && winner.Value.controlTime > 0)
                {
                    Instance.SendGlobalMessage("ClanWins", winner.Key);

                    // Award clan core points via API
                    Instance.AwardClanCorePoints(winner.Key, "control");

                    // Add control win to AwakenStats for the winning clan
                    Instance.AddControlWinToStats(winner.Key);
                }
                else
                {
                    Instance.SendGlobalMessage("NoWinner");
                }

                // Send results
                if (config.messages.sendEndResultToChat)
                    Instance.SendInGameControlResults(this);

                if (config.messages.sendEndResultToDiscord)
                    Instance.SendControlResults(this);

                // Cleanup
                foreach (var player in BasePlayer.activePlayerList)
                {
                    if (Instance.playersInEvent.ContainsKey(player.UserIDString))
                    {
                        Instance.playersInEvent.Remove(player.UserIDString);
                        Instance.DestroyControlUI(player);
                    }
                }

                Instance.activeEvents.Remove(this);
                Instance.clanSpawnAssignments.Clear();

                Destroy(gameObject);
            }
        }

        public class ControlPointTrigger : MonoBehaviour
        {
            public string controlPointName;
            public ControlEventComp eventComp;

            private void OnTriggerEnter(Collider other)
            {
                var player = other.GetComponentInParent<BasePlayer>();
                if (player != null && eventComp != null)
                {
                    eventComp.OnPlayerEnterControlPoint(controlPointName, player);
                }
            }

            private void OnTriggerExit(Collider other)
            {
                var player = other.GetComponentInParent<BasePlayer>();
                if (player != null && eventComp != null)
                {
                    eventComp.OnPlayerExitControlPoint(controlPointName, player);
                }
            }
        }
        #endregion

        #region Hooks
        private void OnPlayerDeath(BasePlayer player, HitInfo info)
        {
            if (info?.InitiatorPlayer is BasePlayer killer && killer != player)
            {
                foreach (var eventComp in activeEvents)
                {
                    if (eventComp.playerStats.ContainsKey(killer.UserIDString) ||
                        eventComp.playerStats.ContainsKey(player.UserIDString))
                    {
                        eventComp.OnPlayerKill(killer, player);
                        break;
                    }
                }
            }
        }

        private void OnPlayerDisconnected(BasePlayer player)
        {
            if (playersInEvent.ContainsKey(player.UserIDString))
            {
                var eventComp = playersInEvent[player.UserIDString];
                playersInEvent.Remove(player.UserIDString);

                // Remove from all control points
                foreach (var cp in eventComp.controlPoints.Values)
                {
                    cp.playersInZone.Remove(player.UserIDString);
                }

                DestroyControlUI(player);
            }
        }
        #endregion

        #region UI
        private void CreateControlUI(BasePlayer player, ControlEventComp eventComp)
        {
            if (!config.ui.useUI) return;

            var elements = new CuiElementContainer();

            // Main panel
            elements.Add(new CuiPanel
            {
                Image = { Color = config.ui.UIBackgroundColor },
                RectTransform = { AnchorMin = "0.02 0.85", AnchorMax = "0.25 0.98" }
            }, "Hud", "ControlEventUI");

            // Title
            elements.Add(new CuiLabel
            {
                Text = {
                    Text = config.ui.UIText,
                    FontSize = config.ui.UITextFontSize,
                    Align = TextAnchor.MiddleCenter,
                    Color = config.ui.UITextColor
                },
                RectTransform = { AnchorMin = "0 0.8", AnchorMax = "1 1" }
            }, "ControlEventUI");

            // Time remaining
            float timeRemaining = eventComp.eventEndTime - UnityEngine.Time.realtimeSinceStartup;
            int minutes = (int)(timeRemaining / 60);
            int seconds = (int)(timeRemaining % 60);

            elements.Add(new CuiLabel
            {
                Text = {
                    Text = $"Time: {minutes:00}:{seconds:00}",
                    FontSize = 12,
                    Align = TextAnchor.MiddleCenter,
                    Color = "1 1 1 1"
                },
                RectTransform = { AnchorMin = "0 0.6", AnchorMax = "1 0.8" }
            }, "ControlEventUI");

            // Control points status
            float yPos = 0.5f;
            foreach (var cp in eventComp.controlPoints.Values)
            {
                string status = cp.isContested ? "CONTESTED" :
                               string.IsNullOrEmpty(cp.controllingClan) ? "NEUTRAL" :
                               $"CONTROLLED BY {cp.controllingClan}";

                string color = cp.isContested ? "1 1 0 1" :
                              string.IsNullOrEmpty(cp.controllingClan) ? "0.7 0.7 0.7 1" :
                              "0 1 0 1";

                elements.Add(new CuiLabel
                {
                    Text = {
                        Text = $"{cp.name}: {status}",
                        FontSize = 10,
                        Align = TextAnchor.MiddleLeft,
                        Color = color
                    },
                    RectTransform = { AnchorMin = $"0.05 {yPos - 0.1f}", AnchorMax = $"0.95 {yPos}" }
                }, "ControlEventUI");

                yPos -= 0.12f;
            }

            CuiHelper.DestroyUi(player, "ControlEventUI");
            CuiHelper.AddUi(player, elements);
        }

        private void DestroyControlUI(BasePlayer player)
        {
            CuiHelper.DestroyUi(player, "ControlEventUI");
        }
        #endregion

        #region Discord & Results
        private void SendControlResults(ControlEventComp eventComp)
        {
            if (string.IsNullOrEmpty(config.apiWebhookURL)) return;

            var winner = eventComp.teamStats.OrderByDescending(t => t.Value.controlTime).FirstOrDefault();
            bool hasWinner = winner.Key != null && winner.Value.controlTime > 0;

            // Calculate event duration
            float eventDurationMinutes = (eventComp.eventEndTime - eventComp.eventStartTime) / 60f;
            int totalPlayers = eventComp.playerStats.Count;
            int totalTeams = eventComp.teamStats.Count;

            // Get player stats and group by clan (using maze plugin style exactly)
            var playerStats = new List<(ulong uid, int kills, int deaths, float kdr, string name, string clan)>();
            foreach (var kvp in eventComp.playerStats)
            {
                var player = kvp.Value;
                float kdr = player.deaths > 0 ? (float)player.kills / player.deaths : player.kills;
                playerStats.Add((kvp.Key, player.kills, player.deaths, kdr, player.displayName, player.clanTag));
            }

            // Group by clan and calculate clan stats
            var clanGroups = playerStats
                .Where(p => !string.IsNullOrEmpty(p.clan))
                .GroupBy(p => p.clan)
                .Select(g => new {
                    Clan = g.Key,
                    TotalKills = g.Sum(p => p.kills),
                    TotalDeaths = g.Sum(p => p.deaths),
                    Players = g.OrderByDescending(p => p.kills).ToList(),
                    ControlTime = eventComp.teamStats.ContainsKey(g.Key) ? eventComp.teamStats[g.Key].controlTime : 0f
                })
                .OrderByDescending(g => g.ControlTime)
                .ToList();

            var fieldsList = new List<object>();

            // Details section
            var totalKills = eventComp.teamStats.Sum(t => t.Value.kills);
            fieldsList.Add(new {
                name = "Details",
                value = $"Total Teams: ``{totalTeams}``\n Duration: ``{eventDurationMinutes:0} min``",
                inline = true
            });

            fieldsList.Add(new {
                name = "‎ ",
                value = $"Total Players: ``{totalPlayers}``\n Total Kills: ``{totalKills}``",
                inline = true
            });

            // Winner section
            string winningClan = hasWinner ? winner.Key : null;
            if (!string.IsNullOrEmpty(winningClan))
            {
                var winGroup = clanGroups.FirstOrDefault(g => g.Clan == winningClan);
                if (winGroup != null)
                {
                    int winnerControlMinutes = (int)(winGroup.ControlTime / 60);
                    int winnerControlSeconds = (int)(winGroup.ControlTime % 60);
                    var topPlayer = winGroup.Players.FirstOrDefault();
                    string topPlayerName = topPlayer.name ?? "Unknown";

                    fieldsList.Add(new {
                        name = "Top Controller",
                        value = $"**{topPlayerName}** – Controlled for {winnerControlMinutes}:{winnerControlSeconds:00} min",
                        inline = false
                    });
                }
            }

            // Winners section
            var winnersSb = new StringBuilder();
            foreach (var clan in clanGroups.Take(3))
            {
                int controlMinutes = (int)(clan.ControlTime / 60);
                int controlSeconds = (int)(clan.ControlTime % 60);
                winnersSb.AppendLine($"**[{clan.Clan}]** – **{controlMinutes}:{controlSeconds:00} min** – **{clan.TotalKills} kills** – **{clan.TotalDeaths} deaths**");
            }

            if (winnersSb.Length > 0)
            {
                fieldsList.Add(new {
                    name = "Winners",
                    value = winnersSb.ToString(),
                    inline = false
                });
            }

            // Other clans section (runners up)
            var runnerSb = new StringBuilder();
            foreach (var clan in clanGroups.Skip(3).Take(5))
            {
                int controlMinutes = (int)(clan.ControlTime / 60);
                int controlSeconds = (int)(clan.ControlTime % 60);
                runnerSb.AppendLine($"**[{clan.Clan}]** – **{controlMinutes}:{controlSeconds:00} min** – **{clan.TotalKills} kills** – **{clan.TotalDeaths} deaths**");
            }

            if (runnerSb.Length > 0)
            {
                fieldsList.Add(new {
                    name = "Other Clans",
                    value = runnerSb.ToString(),
                    inline = false
                });
            }

            // Create Discord embed (using maze plugin style exactly)
            var payload = new
            {
                content = "",
                embeds = new[]
                {
                    new
                    {
                        title = "Awaken Control",
                        color = 16737843,
                        fields = fieldsList.ToArray(),
                        thumbnail = new {
                            url = "https://cdn.awakenrust.com/oasis_control.png"
                        },
                        footer = new {
                            text = "Awaken Rust Servers",
                            icon_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213031984762961/oasisredglowing.png"
                        },
                        timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    }
                },
                username = "Awaken Events",
                avatar_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213031984762961/oasisredglowing.png",
                attachments = new object[0]
            };

            string json = JsonConvert.SerializeObject(payload, Formatting.None);
            webrequest.EnqueuePost(config.apiWebhookURL, json, (code, res) =>
            {
                if (code is not 200 and not 204)
                    PrintError($"Discord webhook error: {code} – {res}");
            }, this, RequestHeaders);
        }

        private static Dictionary<string, string> RequestHeaders => new Dictionary<string, string>
        {
            ["Content-Type"] = "application/json"
        };



        private void SendInGameControlResults(ControlEventComp eventComp)
        {
            var winner = eventComp.teamStats.OrderByDescending(t => t.Value.controlTime).FirstOrDefault();

            if (winner.Key != null && winner.Value.controlTime > 0)
            {
                SendGlobalMessage("EventEndedChat", config.messages.chatNameColor, config.messages.chatName);
                SendGlobalMessage("ClanWins", winner.Key);

                var topStats = eventComp.teamStats.OrderByDescending(t => t.Value.controlTime).Take(3);
                foreach (var team in topStats)
                {
                    string message = $"Clan {team.Key}: {team.Value.controlTime:F1}s control, {team.Value.kills} kills, {team.Value.deaths} deaths";
                    foreach (var player in BasePlayer.activePlayerList)
                    {
                        player.ChatMessage($"<color={config.messages.chatNameColor}>{config.messages.chatName}</color>: {message}");
                    }
                }
            }
            else
            {
                SendGlobalMessage("EventEndedChat", config.messages.chatNameColor, config.messages.chatName);
                SendGlobalMessage("NoWinner");
            }
        }
        #endregion

        #region API
        [HookMethod("StartEvent")]
        public void StartEvent(string eventName = "control")
        {
            if (activeEvents.Count > 0) return;
            StartControlEvent();
        }
        #endregion

        #region Clan Core Integration
        private void AwardClanCorePoints(string clanName, string eventType)
        {
            if (ClanCores == null || !ClanCores.IsLoaded)
            {
                PrintWarning("[Control Event] ClanCores plugin not found - cannot award points");
                return;
            }

            try
            {
                bool success = (bool)ClanCores.Call("API_AwardEventPoints", clanName, eventType);
                if (success)
                {
                    Puts($"[Control Event] Successfully awarded clan core points to '{clanName}' for {eventType} event win");
                }
                else
                {
                    PrintWarning($"[Control Event] Failed to award clan core points to '{clanName}'");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[Control Event] Error awarding clan core points: {ex.Message}");
            }
        }
        #endregion

        #region AwakenStats Integration
        private void AddControlWinToStats(string clanName)
        {
            if (AwakenStats == null || !AwakenStats.IsLoaded)
            {
                PrintWarning("[Control Event] AwakenStats plugin not found - cannot record event win");
                return;
            }

            try
            {
                // Add control win for the entire clan
                AwakenStats.Call("AddEventWinForClan", clanName, "control");
                Puts($"[Control Event] Successfully recorded control win for clan '{clanName}' in AwakenStats");
            }
            catch (Exception ex)
            {
                PrintError($"[Control Event] Error recording control win in AwakenStats: {ex.Message}");
            }
        }
        #endregion
    }
}










