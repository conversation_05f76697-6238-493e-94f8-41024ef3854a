using Newtonsoft.Json;
using Oxide.Core.Libraries.Covalence;
using System.Collections.Generic;
using System.Linq;
using Rust;

namespace Oxide.Plugins
{
    [Info("Awaken Blueprint Manager", "Skelee", "1.1.7")]
    [Description("Gives players blueprints defined in config.")]

    public class AwakenBlueprintManager : CovalencePlugin
    {
        #region Config
        private static ConfigData? _config;

        private record ConfigData(
            [property: JsonProperty("Unlock All Blueprints?")] bool UnlockAllBPs = false,
            [property: JsonProperty("Use Default BP List?")] bool UseDefaultBPList = true,
            [property: JsonProperty("Default Blueprints")] List<string> DefaultBPs = null!,
            [property: JsonProperty("Remove Fun BPs")] bool RemoveFunBPs = false,
            [property: JsonProperty("Tier 1 BPs")] bool Tier1Bps = true,
            [property: JsonProperty("Tier 2 BPs")] bool Tier2Bps = false,
            [property: JsonProperty("Tier 3 BPs")] bool Tier3Bps = false)
        {
            public ConfigData() : this(false, true, new List<string> { "rifle.ak", "furnace", "workbench1" }, false, true, false, false) { }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                _config = Config.ReadObject<ConfigData>();
                if (_config == null)
                {
                    PrintWarning("Config is null, using default.");
                    _config = LoadDefaultConfig();
                }
            }
            catch
            {
                PrintWarning("Error loading config, using default.");
                _config = LoadDefaultConfig();
            }
        }

        private ConfigData LoadDefaultConfig()
        {
            configCreated = false;
            var defaultConfig = new ConfigData();
            Config.WriteObject(defaultConfig, true);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(_config, true);

        private bool configCreated = true;
        #endregion

        #region Defines
        private readonly List<int> bpsCache = new();
        private int addedCount;
        #endregion

        #region Hooks
        private void OnServerInitialized()
        {
            if (_config == null)
            {
                PrintWarning("Config is null, cannot check blueprints.");
                return;
            }
            CheckBlueprints();
        }

        private void OnPlayerConnected(BasePlayer? player)
        {
            UnlockBlueprints(player);
        }
        #endregion

        #region Functions
        private void CheckBlueprints()
        {
            bpsCache.Clear();
            if (ItemManager.bpList == null)
            {
                PrintWarning("Blueprint list is null.");
                return;
            }
            if (_config!.UseDefaultBPList)
            {
                foreach (var shortname in _config.DefaultBPs)
                {
                    var itemDef = ItemManager.FindItemDefinition(shortname);
                    if (itemDef == null)
                    {
                        PrintWarning($"[DEBUG] Invalid shortname in DefaultBPs: {shortname}");
                    }
                }
            }

            foreach (var bp in ItemManager.bpList)
            {
                if (bp?.targetItem == null)
                {
                    continue;
                }

                bool addBlueprint = false;
                if (_config!.UnlockAllBPs)
                {
                    addBlueprint = true;
                }
                else
                {
                    bool isFun = bp.targetItem.category == ItemCategory.Fun;
                    bool inDefaultList = _config.UseDefaultBPList && _config.DefaultBPs.Contains(bp.targetItem.shortname);
                    bool matchesTier = !_config.UseDefaultBPList && (
                        (_config.Tier1Bps && bp.workbenchLevelRequired == 1) ||
                        (_config.Tier2Bps && bp.workbenchLevelRequired == 2) ||
                        (_config.Tier3Bps && bp.workbenchLevelRequired == 3));

                    if (_config.RemoveFunBPs && isFun)
                    {
                        PrintWarning($"[DEBUG] Excluding fun blueprint: {bp.targetItem.shortname}");
                        continue;
                    }

                    addBlueprint = inDefaultList || matchesTier;
                }

                if (addBlueprint)
                {
                    bpsCache.Add(bp.targetItem.itemid);
                }
            }
        }

        private void UnlockBlueprints(BasePlayer? player)
        {
            if (player?.blueprints is not { } blueprints)
            {
                PrintWarning($"Invalid player or blueprints for {player?.displayName ?? "unknown"}.");
                return;
            }


            addedCount = 0;
            foreach (var bp in bpsCache)
            {
                var itemDef = ItemManager.FindItemDefinition(bp);
                if (itemDef == null)
                {
                    continue;
                }

                if (blueprints.IsUnlocked(itemDef))
                {
                    continue;
                }

                blueprints.Unlock(itemDef);
                addedCount++;
            }

            if (addedCount == 0)
            {
                return;
            }

            player.SendNetworkUpdateImmediate();
            player.ClientRPC(null, "UnlockedBlueprint", 0);
        }
        #endregion
    }
}









