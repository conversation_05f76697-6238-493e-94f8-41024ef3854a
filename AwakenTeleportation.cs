using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEngine;
using Network;

namespace Oxide.Plugins
{
    [Info("Awaken Teleportation", "Skelee", "2.0.0")]
    [Description("Handles all teleportation requests on Awaken Servers. Updated for May 2025 Rust API.")]
    public class AwakenTeleportation : CovalencePlugin
    {
        #region Configuration
        private ConfigData config;

        private class ConfigData
        {
            public BlockSettings BlockSettings { get; set; } = new BlockSettings();
            public TimerSettings TimerSettings { get; set; } = new TimerSettings();
            public TPCooldownSettings TPCooldownSettings { get; set; } = new TPCooldownSettings();
            public HomeSettings HomeSettings { get; set; } = new HomeSettings();
            public HomePermissionSettings HomePermissionSettings { get; set; } = new HomePermissionSettings();
            public TPRSettings TPRSettings { get; set; } = new TPRSettings();
            public OutpostSettings OutpostSettings { get; set; } = new OutpostSettings();
            public ClanHomeSettings ClanHomeSettings { get; set; } = new ClanHomeSettings();
            public List<string> VIPPermissions { get; set; } = new List<string> { "awaken.vip" };
        }

        private class BlockSettings
        {
            public bool BlockOnBuildingBlocked { get; set; } = true;
            public bool BlockOnSwimming { get; set; } = true;
            public bool BlockOnBleeding { get; set; } = true;
            public bool BlockOnCold { get; set; } = true;
            public bool BlockOnHot { get; set; } = true;
            public bool BlockOnFalling { get; set; } = true;
        }

        private class TimerSettings
        {
            public int DefaultTimer { get; set; } = 20;
            public Dictionary<string, int> PermissionTimers { get; set; } = new Dictionary<string, int>
            {
                ["awaken.vip"] = 5,
                ["awaken.vip.plus"] = 3,
                ["awaken.vip.elite"] = 0
            };
        }

        private class TPCooldownSettings
        {
            public int DefaultCooldown { get; set; } = 300;
            public Dictionary<string, int> PermissionCooldowns { get; set; } = new Dictionary<string, int>
            {
                ["awaken.vip"] = 180,
                ["awaken.vip.plus"] = 120,
                ["awaken.vip.elite"] = 60
            };
        }

        private class HomeSettings
        {
            public bool UseHomes { get; set; } = true;
            public int HomeCooldown { get; set; } = 300;
        }

        private class HomePermissionSettings
        {
            public int DefaultMaxHomes { get; set; } = 1;
            public Dictionary<string, int> PermissionHomeLimits { get; set; } = new Dictionary<string, int>
            {
                ["awaken.vip"] = 3,
                ["awaken.vip.plus"] = 5,
                ["awaken.vip.elite"] = 10
            };
        }

        private class TPRSettings
        {
            public int TPRCooldown { get; set; } = 300;
            public float TPRTimeout { get; set; } = 30f;
        }

        private class OutpostSettings
        {
            public int OutpostCooldown { get; set; } = 300;
        }
        
        private class ClanHomeSettings
        {
            public bool UseClanHomes { get; set; } = true;
            public int ClanHomeCooldown { get; set; } = 300;
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<ConfigData>();
                if (config == null) throw new Exception("Could not read config file");
                SaveConfig();
            }
            catch
            {
                PrintError("Configuration file is corrupt! Recreating...");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = new ConfigData();

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Defines
        [PluginReference] private Plugin? AwakenClans, AwakenActionBlocker;
        private readonly List<Vector3> outpostSpawns = new();
        private readonly List<Vector3> customOutpostSpawns = new();
        private readonly List<Vector3> customDefaultSpawns = new();
        private readonly Dictionary<string, Timer> playerTimers = new();
        private readonly Dictionary<string, List<string>> playerTprRequests = new();
        private readonly Dictionary<string, List<string>> playerTps = new();
        private readonly Dictionary<string, float> playerTprCooldowns = new();
        private readonly Dictionary<string, float> playerHomeCooldowns = new();
        private readonly Dictionary<string, float> playerOutpostCooldowns = new();
        private readonly Dictionary<string, float> playerClanHomeCooldowns = new();
        private readonly Dictionary<string, float> playerGlobalTPCooldowns = new(); // Track global TP cooldowns
        private readonly Dictionary<BasePlayer, string> _players = new();
        private readonly Dictionary<string, BasePlayer> _ids = new();
        private PlayerInfo? pInfo;
        #endregion

        #region Hooks
        private void Loaded()
        {
            AddCovalenceCommand("atp", nameof(ATPCmd));
            AddCovalenceCommand("tpr", nameof(TPRCmd));
            AddCovalenceCommand("tpa", nameof(TPACmd));
            AddCovalenceCommand("tpc", nameof(TPCCmd));
            AddCovalenceCommand("outpost", nameof(OutpostCmd));

            // Admin outpost management commands
            AddCovalenceCommand("setoutpost", nameof(SetOutpostCmd));
            AddCovalenceCommand("listoutpost", nameof(ListOutpostCmd));
            AddCovalenceCommand("deloutpost", nameof(DelOutpostCmd));
            AddCovalenceCommand("resetoutpost", nameof(ResetOutpostCmd));

            // Admin default spawn management commands
            AddCovalenceCommand("setdefaultspawn", nameof(SetDefaultSpawnCmd));
            AddCovalenceCommand("listdefaultspawn", nameof(ListDefaultSpawnCmd));
            AddCovalenceCommand("deldefaultspawn", nameof(DelDefaultSpawnCmd));
            AddCovalenceCommand("resetdefaultspawn", nameof(ResetDefaultSpawnCmd));

            // Debug command for clan API testing
            AddCovalenceCommand("testclanapi", nameof(TestClanAPICmd));
            AddCovalenceCommand("refreshclandata", nameof(RefreshClanDataCmd));
            AddCovalenceCommand("fixteamui", nameof(FixTeamUICmd));
            AddCovalenceCommand("debugclandata", nameof(DebugClanDataCmd));

            if (config?.HomeSettings.UseHomes == true)
            {
                AddCovalenceCommand("home", nameof(HomeCMD));
                AddCovalenceCommand("sethome", nameof(SetHomeCMD));
                AddCovalenceCommand("delhome", nameof(DelHomeCMD));
            }

            if (config?.ClanHomeSettings.UseClanHomes == true)
            {
                AddCovalenceCommand("clanhome", nameof(ClanHomeCMD));
                AddCovalenceCommand("setclanhome", nameof(SetClanHomeCMD));
                AddCovalenceCommand("delclanhome", nameof(DelClanHomeCMD));
            }

            foreach (var perm in config?.VIPPermissions ?? new List<string>())
                permission.RegisterPermission(perm, this);

            // Register timer permissions
            if (config?.TimerSettings?.PermissionTimers != null)
            {
                foreach (var perm in config.TimerSettings.PermissionTimers.Keys)
                    permission.RegisterPermission(perm, this);
            }

            // Register home permission settings
            if (config?.HomePermissionSettings?.PermissionHomeLimits != null)
            {
                foreach (var perm in config.HomePermissionSettings.PermissionHomeLimits.Keys)
                    permission.RegisterPermission(perm, this);
            }
        }

        private void OnServerInitialized()
        {
            // Load default outpost spawns from monuments
            foreach (var monument in TerrainMeta.Path.Monuments)
                if (monument.name.Contains("compound", StringComparison.OrdinalIgnoreCase))
                    outpostSpawns.Add(monument.transform.position + new Vector3(0, 1, 0));

            // Load custom outpost spawns from data file
            LoadCustomOutpostSpawns();

            // Load custom default spawns from data file
            LoadCustomDefaultSpawns();

            foreach (var player in BasePlayer.activePlayerList)
                OnPlayerConnected(player);
        }

        private void OnPlayerConnected(BasePlayer? player)
        {
            if (player == null) return;
            var uid = UnityEngine.Random.Range(1000, 9999).ToString();
            while (_ids.ContainsKey(uid) || BasePlayer.activePlayerList.Any(p => p.displayName.Contains(uid)))
                uid = UnityEngine.Random.Range(1000, 9999).ToString();
            _ids[uid] = player;
            _players[player] = uid;
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            if (playerTimers.TryGetValue(player.UserIDString, out var timer) && timer != null)
            {
                timer.Destroy();
                playerTimers.Remove(player.UserIDString);
            }

            if (playerTprRequests.TryGetValue(player.UserIDString, out var requests))
            {
                foreach (var request in requests)
                    if (BasePlayer.Find(request) is { } requester)
                        requester.ChatMessage($"{player.displayName} has disconnected, teleport request cancelled.");
                playerTprRequests.Remove(player.UserIDString);
            }

            _ids.Remove(_players.GetValueOrDefault(player));
            _players.Remove(player);
        }

        private object? OnEntityTakeDamage(BasePlayer player, HitInfo info)
        {
            if (player == null || info?.InitiatorPlayer == null || !playerTps.TryGetValue(player.UserIDString, out var tps)) return null;
            if (tps.Contains(info.InitiatorPlayer.UserIDString)) return true;
            return null;
        }

        // Override default spawn locations if custom spawns are set
        private object? OnPlayerRespawn(BasePlayer player)
        {
            if (player == null || customDefaultSpawns.Count == 0) return null;

            try
            {
                // Get a random custom spawn point
                var spawnPosition = customDefaultSpawns[UnityEngine.Random.Range(0, customDefaultSpawns.Count)];

                // Return a custom spawn point to override default behavior
                return new BasePlayer.SpawnPoint
                {
                    pos = spawnPosition,
                    rot = Quaternion.identity
                };
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error in OnPlayerRespawn: {ex.Message}");
                return null; // Fall back to default spawning
            }
        }

        // New hook for the May 2025 Rust update
        private void OnServerSave()
        {
            try
            {
                // Clean up expired cooldowns to prevent memory leaks
                CleanupExpiredCooldowns();

                // Log cleanup for debugging
                Puts("[Awaken Teleportation] Cleaned up expired cooldowns during server save.");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error during OnServerSave: {ex.Message}");
            }
        }

        // New method to clean up expired cooldowns
        private void CleanupExpiredCooldowns()
        {
            try
            {
                int tprRemoved = CleanupCooldownDictionary(playerTprCooldowns, config?.TPRSettings.TPRCooldown ?? 300);
                int homeRemoved = CleanupCooldownDictionary(playerHomeCooldowns, config?.HomeSettings.HomeCooldown ?? 300);
                int outpostRemoved = CleanupCooldownDictionary(playerOutpostCooldowns, config?.OutpostSettings.OutpostCooldown ?? 300);
                int clanHomeRemoved = CleanupCooldownDictionary(playerClanHomeCooldowns, config?.ClanHomeSettings.ClanHomeCooldown ?? 300);

                int totalRemoved = tprRemoved + homeRemoved + outpostRemoved + clanHomeRemoved;
                if (totalRemoved > 0)
                {
                    Debug.Log($"[Awaken Teleportation] Cleaned up {totalRemoved} expired cooldowns " +
                              $"(TPR: {tprRemoved}, Home: {homeRemoved}, Outpost: {outpostRemoved}, ClanHome: {clanHomeRemoved})");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error cleaning up cooldowns: {ex.Message}");
            }
        }

        // Helper method to clean up a specific cooldown dictionary
        private int CleanupCooldownDictionary(Dictionary<string, float> cooldowns, int cooldownTime)
        {
            if (cooldowns == null || cooldowns.Count == 0) return 0;

            var keysToRemove = cooldowns
                .Where(kvp => Time.time - kvp.Value >= cooldownTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in keysToRemove)
                cooldowns.Remove(key);

            return keysToRemove.Count;
        }
        #endregion

        #region Functions
        private void Teleport(BasePlayer player, Vector3 position)
        {
            if (player == null || Vector3.Distance(position, Vector3.zero) < 5f) return;

            try
            {
                // Clear any active items and states
                player.UpdateActiveItem(new ItemId(0));
                player.EnsureDismounted();

                // Handle parent entities (vehicles, chairs, etc.)
                if (player.HasParent())
                {
                    player.SetParent(null, true, true);
                    // Wait a frame to ensure parent is fully cleared (2025 API improvement)
                    player.Invoke(() => ContinueTeleport(player, position), 0.1f);
                    return;
                }

                ContinueTeleport(player, position);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error during teleport preparation: {ex.Message}");
                player.ChatMessage("An error occurred while preparing for teleport. Please try again.");
            }
        }

        private void ContinueTeleport(BasePlayer player, Vector3 position)
        {
            try
            {
                if (!player.IsConnected) return;

                // End any active looting
                player.EndLooting();

                // Handle sleeping state
                if (!player.IsSleeping())
                {
                    player.SetPlayerFlag(BasePlayer.PlayerFlags.Sleeping, true);
                    player.sleepStartTime = Time.time;
                    BasePlayer.sleepingPlayerList.Add(player);

                    // Cancel periodic updates
                    player.CancelInvoke("InventoryUpdate");
                    player.CancelInvoke("TeamUpdate");

                    // New in 2025 API: Cancel additional updates
                    player.CancelInvoke("EnvironmentUpdate");
                    player.CancelInvoke("MetabolismUpdate");
                }

                // Remove from current triggers
                player.RemoveFromTriggers();

                // Perform the actual teleport
                player.Teleport(position);

                // Handle network visibility (updated for 2025 API)
                if (player.IsConnected)
                {
                    if (!Net.sv.visibility.IsInside(player.net.group, position))
                    {
                        // Set receiving snapshot flag
                        player.SetPlayerFlag(BasePlayer.PlayerFlags.ReceivingSnapshot, true);

                        // Start loading client-side
                        player.ClientRPCPlayer(null, player, "StartLoading");

                        // Send entity update with improved error handling
                        try
                        {
                            player.SendEntityUpdate();
                            player.UpdateNetworkGroup();
                            player.SendNetworkUpdateImmediate(false);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Teleportation] Network update error: {ex.Message}");
                            // Continue anyway - the player will still teleport
                        }
                    }

                    // New in 2025 API: Ensure player is properly grounded after teleport
                    player.Invoke(() => EnsureGrounded(player), 0.5f);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error during teleport execution: {ex.Message}");
                player.ChatMessage("An error occurred during teleport. Please contact an administrator.");
            }
            finally
            {
                player.ForceUpdateTriggers();
            }
        }

        // New method for 2025 API to ensure player is properly grounded
        private void EnsureGrounded(BasePlayer player)
        {
            if (player == null || !player.IsConnected) return;

            // Check if player is floating and fix if needed
            if (!player.IsOnGround() && !player.IsSwimming() && !player.IsFlying)
            {
                // Perform a ground check with the new API
                var groundPoint = player.transform.position;
                groundPoint.y = TerrainMeta.HeightMap.GetHeight(groundPoint);

                // Only adjust if significantly above ground
                if (player.transform.position.y > groundPoint.y + 1f)
                {
                    groundPoint.y += 0.2f; // Slight offset to prevent clipping
                    player.MovePosition(groundPoint);
                    player.SendNetworkUpdateImmediate();
                }
            }
        }

        private BasePlayer? FindPlayersSingle(string value, BasePlayer? player)
        {
            if (string.IsNullOrEmpty(value)) return null;

            if (_ids.TryGetValue(value, out var target) && target.IsValid()) return target;

            var foundPlayers = FindPlayers(value, true);
            return foundPlayers.Count switch
            {
                0 => HandlePlayerNotFound(player),
                > 1 => HandleMultiplePlayersFound(player, foundPlayers),
                _ => foundPlayers.First()
            };
        }

        private BasePlayer? HandlePlayerNotFound(BasePlayer? player)
        {
            player?.ChatMessage("Couldn't find a player with that name.");
            return null;
        }

        private BasePlayer? HandleMultiplePlayersFound(BasePlayer? player, List<BasePlayer> targets)
        {
            player?.ChatMessage($"Found multiple players: {GetMultiplePlayers(targets)}");
            return null;
        }

        private List<BasePlayer> FindPlayers(string arg, bool all = false) =>
            string.IsNullOrEmpty(arg) ? new List<BasePlayer>() :
            _ids.TryGetValue(arg, out var target) && target.IsValid() && (all || target.IsConnected) ? new List<BasePlayer> { target } :
            (all ? BasePlayer.allPlayerList : BasePlayer.activePlayerList)
                .Where(p => p != null && !string.IsNullOrEmpty(p.displayName) && (p.UserIDString == arg || p.displayName.Contains(arg, StringComparison.OrdinalIgnoreCase)))
                .ToList();

        private string GetMultiplePlayers(List<BasePlayer> players) => string.Join(", ", players.Select(p =>
        {
            if (!_players.ContainsKey(p)) OnPlayerConnected(p);
            return $"<color=#FFA500>{_players[p]}</color> - {p.displayName}";
        }));

        private bool IsInClan(BasePlayer player, BasePlayer target) =>
            AwakenClans?.Call<bool>("SameClan", player.userID, target.userID) ?? false;

        private bool IsBlocked(BasePlayer player)
        {
            // Check if player is mounted (on a vehicle, horse, etc.)
            if (player.isMounted)
            {
                player.ChatMessage("You cannot teleport while mounted. Dismount first.");
                return true;
            }

            // Check if player is hostile
            if (player.IsHostile())
            {
                player.ChatMessage("You cannot teleport while hostile.");
                return true;
            }

            // Check if player is in combat
            if (InCombat(player))
            {
                player.ChatMessage("You cannot teleport while in combat.");
                return true;
            }

            // Check if player is building blocked
            if (config?.BlockSettings?.BlockOnBuildingBlocked == true && player.IsBuildingBlocked())
            {
                player.ChatMessage("You cannot teleport while building blocked.");
                return true;
            }

            // Check if player is swimming
            if (config?.BlockSettings?.BlockOnSwimming == true && player.IsSwimming())
            {
                player.ChatMessage("You cannot teleport while swimming.");
                return true;
            }

            // Check if player is bleeding
            if (config?.BlockSettings?.BlockOnBleeding == true && player.metabolism.bleeding.value > 0)
            {
                player.ChatMessage("You cannot teleport while bleeding.");
                return true;
            }

            // Check if player is cold
            if (config?.BlockSettings?.BlockOnCold == true && player.metabolism.temperature.value <= 0)
            {
                player.ChatMessage("You cannot teleport while too cold.");
                return true;
            }

            // Check if player is hot
            if (config?.BlockSettings?.BlockOnHot == true && player.metabolism.temperature.value >= 40)
            {
                player.ChatMessage("You cannot teleport while too hot.");
                return true;
            }

            // Check if player is falling
            if (config?.BlockSettings?.BlockOnFalling == true && !player.IsOnGround() && !player.IsSwimming())
            {
                player.ChatMessage("You cannot teleport while falling.");
                return true;
            }

            return false;
        }

        private bool CanPlayerTeleport(BasePlayer player, string teleportType)
        {
            // First check if player is blocked by combat, hostility, etc.
            if (IsBlocked(player))
                return false;

            // Check global TP cooldown
            if (IsOnTPCooldown(player.UserIDString))
            {
                string timeLeft = GetCooldownTimeRemaining(player.UserIDString);
                player.ChatMessage($"You must wait {timeLeft} seconds before teleporting again.");
                return false;
            }

            // Additional teleport-specific cooldown checks can go here
            // (e.g., home cooldown, outpost cooldown, etc.)

            return true;
        }

        private void SetCooldown(BasePlayer player, string type)
        {
            try
            {
                // Get the appropriate cooldown dictionary
                var playerCooldowns = type switch
                {
                    "tpr" => playerTprCooldowns,
                    "home" => playerHomeCooldowns,
                    "outpost" => playerOutpostCooldowns,
                    "clanhome" => playerClanHomeCooldowns,
                    _ => null
                };

                if (playerCooldowns == null)
                {
                    Debug.LogWarning($"[Awaken Teleportation] Unknown cooldown type: {type}");
                    return;
                }

                // Set cooldown timestamp
                playerCooldowns[player.UserIDString] = Time.time;

                // Log cooldown for debugging purposes
                if (player.IsAdmin)
                {
                    int cooldownTime = type switch
                    {
                        "tpr" => config?.TPRSettings.TPRCooldown ?? 300,
                        "home" => config?.HomeSettings.HomeCooldown ?? 300,
                        "outpost" => config?.OutpostSettings.OutpostCooldown ?? 300,
                        "clanhome" => config?.ClanHomeSettings.ClanHomeCooldown ?? 300,
                        _ => 300
                    };

                    player.ChatMessage($"<color=#aaaaaa>Debug: {type} cooldown set for {cooldownTime} seconds.</color>");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error setting cooldown: {ex.Message}");
            }
        }

        private bool HasVIPPermission(BasePlayer player)
        {
            try
            {
                // Check if player is admin (admins always have VIP permissions)
                if (player.IsAdmin) return true;

                // Check if player has any VIP permission
                return config?.VIPPermissions.Any(perm => permission.UserHasPermission(player.UserIDString, perm)) ?? false;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error checking VIP permissions: {ex.Message}");
                return false; // Default to no VIP permissions if there's an error
            }
        }

        private int GetPlayerTimerDelay(BasePlayer player)
        {
            try
            {
                // Check permission timers in order of priority (lowest timer wins)
                if (config?.TimerSettings?.PermissionTimers != null)
                {
                    var applicableTimers = config.TimerSettings.PermissionTimers
                        .Where(kvp => permission.UserHasPermission(player.UserIDString, kvp.Key))
                        .Select(kvp => kvp.Value);

                    if (applicableTimers.Any())
                        return applicableTimers.Min();
                }

                // Return default timer if no permissions match
                return config?.TimerSettings?.DefaultTimer ?? 20;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error getting player timer delay: {ex.Message}");
                return 20; // Default to 20 seconds if there's an error
            }
        }

        private int GetPlayerMaxHomes(BasePlayer player)
        {
            try
            {
                // Check permission home limits in order of priority (highest limit wins)
                if (config?.HomePermissionSettings?.PermissionHomeLimits != null)
                {
                    var applicableLimits = config.HomePermissionSettings.PermissionHomeLimits
                        .Where(kvp => permission.UserHasPermission(player.UserIDString, kvp.Key))
                        .Select(kvp => kvp.Value);

                    if (applicableLimits.Any())
                        return applicableLimits.Max();
                }

                // Return default max homes if no permissions match
                return config?.HomePermissionSettings?.DefaultMaxHomes ?? 1;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error getting player max homes: {ex.Message}");
                return 1; // Default to 1 home if there's an error
            }
        }

        private void StartTeleportTimer(BasePlayer player, Vector3 position, string teleportType, Action onComplete = null)
        {
            try
            {
                int timerDelay = GetPlayerTimerDelay(player);

                // If timer is 0, teleport immediately
                if (timerDelay <= 0)
                {
                    Teleport(player, position);
                    onComplete?.Invoke();
                    return;
                }

                // Cancel any existing timer for this player
                CancelPlayerTimer(player);

                player.ChatMessage($"Teleporting in {timerDelay} seconds...");

                // Show gametip 5 seconds before teleport (if timer is longer than 5 seconds)
                if (timerDelay > 5)
                {
                    timer.Once(timerDelay - 5f, () =>
                    {
                        if (player != null && player.IsConnected)
                        {
                            player.Command("gametip.showgametip", "You will teleport in 5 seconds");

                            // Hide the gametip after 5 seconds
                            timer.Once(5f, () =>
                            {
                                if (player != null && player.IsConnected)
                                {
                                    player.Command("gametip.hidegametip");
                                }
                            });
                        }
                    });
                }

                // Create countdown timer
                playerTimers[player.UserIDString] = timer.Once(timerDelay, () =>
                {
                    try
                    {
                        // Check if player is still valid and connected
                        if (player == null || !player.IsConnected)
                            return;

                        // Perform the teleport
                        Teleport(player, position);
                        onComplete?.Invoke();
                    }
                    finally
                    {
                        // Clean up timer reference
                        playerTimers.Remove(player.UserIDString);
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error starting teleport timer: {ex.Message}");
                // Fallback to immediate teleport
                Teleport(player, position);
                onComplete?.Invoke();
            }
        }

        private void CancelPlayerTimer(BasePlayer player)
        {
            if (playerTimers.TryGetValue(player.UserIDString, out var existingTimer) && existingTimer != null)
            {
                existingTimer.Destroy();
                playerTimers.Remove(player.UserIDString);
            }
        }
        #endregion

        #region Cooldown Methods
        private int GetPlayerTPCooldown(string userId)
        {
            if (config?.TPCooldownSettings?.PermissionCooldowns == null)
                return config?.TPCooldownSettings?.DefaultCooldown ?? 20;

            var player = covalence.Players.FindPlayerById(userId);
            if (player == null)
                return config.TPCooldownSettings.DefaultCooldown;

            // Check for permissions from highest to lowest cooldown reduction
            foreach (var permCooldown in config.TPCooldownSettings.PermissionCooldowns.OrderBy(x => x.Value))
            {
                if (permission.UserHasPermission(userId, permCooldown.Key))
                    return permCooldown.Value;
            }

            return config.TPCooldownSettings.DefaultCooldown;
        }

        private bool IsOnTPCooldown(string userId)
        {
            if (!playerGlobalTPCooldowns.TryGetValue(userId, out float cooldownExpiry))
                return false;

            if (Time.realtimeSinceStartup >= cooldownExpiry)
            {
                playerGlobalTPCooldowns.Remove(userId);
                return false;
            }

            return true;
        }

        private void SetTPCooldown(string userId)
        {
            int cooldownTime = GetPlayerTPCooldown(userId);
            
            // If cooldown is 0, player has instant permission
            if (cooldownTime <= 0)
                return;
                
            playerGlobalTPCooldowns[userId] = Time.realtimeSinceStartup + cooldownTime;
        }

        private string GetCooldownTimeRemaining(string userId)
        {
            if (!playerGlobalTPCooldowns.TryGetValue(userId, out float cooldownExpiry))
                return "0";

            float timeLeft = cooldownExpiry - Time.realtimeSinceStartup;
            return timeLeft > 0 ? Math.Ceiling(timeLeft).ToString() : "0";
        }
        #endregion

        #region Commands
        private void ATPCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            BasePlayer? target = args.Length > 0 ? FindPlayersSingle(args[0], player) : null;
            if (target == null) return;

            // Admin teleport doesn't use cooldown
            Teleport(player, target.transform.position);
            player.ChatMessage($"You have teleported to {target.displayName}.");
        }

        private void TPRCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            if (IsBlocked(player)) return;

            if (args.Length == 0)
            {
                player.ChatMessage("Usage: /tpr <player>");
                return;
            }

            // Check global TP cooldown
            if (IsOnTPCooldown(player.UserIDString))
            {
                string timeLeft = GetCooldownTimeRemaining(player.UserIDString);
                player.ChatMessage($"You must wait {timeLeft} seconds before teleporting again.");
                return;
            }

            BasePlayer? target = FindPlayersSingle(args[0], player);
            if (target == null) return;
            if (target == player) { player.ChatMessage("You cannot teleport to yourself."); return; }

            if (!HasVIPPermission(player) && IsOnCooldown(player, "tpr", config?.TPRSettings.TPRCooldown ?? 300)) return;

            if (!playerTprRequests.TryGetValue(target.UserIDString, out var requests))
                playerTprRequests[target.UserIDString] = requests = new List<string>();

            if (requests.Contains(player.UserIDString))
            {
                player.ChatMessage($"You already have a pending teleport request to {target.displayName}.");
                return;
            }

            requests.Add(player.UserIDString);
            player.ChatMessage($"Teleport request sent to {target.displayName}.");
            target.ChatMessage($"{player.displayName} has requested to teleport to you. Type /tpa to accept or /tpc to cancel.");

            if (playerTimers.TryGetValue($"{player.UserIDString}_{target.UserIDString}", out var existingTimer) && existingTimer != null)
                existingTimer.Destroy();

            playerTimers[$"{player.UserIDString}_{target.UserIDString}"] = timer.Once(config?.TPRSettings.TPRTimeout ?? 30f, () =>
            {
                if (requests.Contains(player.UserIDString))
                {
                    requests.Remove(player.UserIDString);
                    player.ChatMessage($"Your teleport request to {target.displayName} has timed out.");
                    target.ChatMessage($"Teleport request from {player.displayName} has timed out.");
                }
            });
        }

        private void TPACmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            if (IsBlocked(player)) return;

            if (!playerTprRequests.TryGetValue(player.UserIDString, out var requests) || requests.Count == 0)
            {
                player.ChatMessage("You have no pending teleport requests.");
                return;
            }

            var requesterID = requests[0];
            var requester = BasePlayer.Find(requesterID);
            if (requester == null)
            {
                player.ChatMessage("The player who requested to teleport to you is no longer online.");
                requests.Remove(requesterID);
                return;
            }

            // Check global TP cooldown for the requester
            if (IsOnTPCooldown(requesterID))
            {
                string timeLeft = GetCooldownTimeRemaining(requesterID);
                player.ChatMessage($"{requester.displayName} is on teleport cooldown for {timeLeft} seconds.");
                requester.ChatMessage($"You must wait {timeLeft} seconds before teleporting.");
                return;
            }

            if (playerTimers.TryGetValue($"{requesterID}_{player.UserIDString}", out var existingTimer) && existingTimer != null)
                existingTimer.Destroy();

            requests.Remove(requesterID);

            // Send acceptance messages immediately
            requester.ChatMessage($"Your teleport request to {player.displayName} has been accepted.");
            player.ChatMessage($"You have accepted {requester.displayName}'s teleport request.");

            StartTeleportTimer(requester, player.transform.position, "tpr", () =>
            {
                // Set up protection and cooldown after teleport completes
                if (!playerTps.TryGetValue(requester.UserIDString, out var tps))
                    playerTps[requester.UserIDString] = tps = new List<string>();

                tps.Add(player.UserIDString);
                timer.Once(30f, () => tps.Remove(player.UserIDString));

                // Set global TP cooldown
                SetTPCooldown(requester.UserIDString);

                if (!HasVIPPermission(requester))
                    SetCooldown(requester, "tpr");
            });
        }

        private void TPCCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;

            if (!playerTprRequests.TryGetValue(player.UserIDString, out var requests) || requests.Count == 0)
            {
                player.ChatMessage("You have no pending teleport requests.");
                return;
            }

            var requesterID = requests[0];
            var requester = BasePlayer.Find(requesterID);

            // Send denial messages immediately
            if (requester != null)
                requester.ChatMessage($"Your teleport request to {player.displayName} has been denied.");
            player.ChatMessage("You have denied the teleport request.");

            if (playerTimers.TryGetValue($"{requesterID}_{player.UserIDString}", out var existingTimer) && existingTimer != null)
                existingTimer.Destroy();

            requests.Remove(requesterID);
        }

        private void OutpostCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            
            // Use CanPlayerTeleport to check all conditions including hostility
            if (!CanPlayerTeleport(player, "outpost")) return;

            // Use custom outpost spawns if available, otherwise use default ones
            var availableSpawns = customOutpostSpawns.Count > 0 ? customOutpostSpawns : outpostSpawns;

            if (availableSpawns.Count == 0)
            {
                player.ChatMessage("No outpost locations found on the map.");
                return;
            }

            if (!HasVIPPermission(player) && IsOnCooldown(player, "outpost", config?.OutpostSettings.OutpostCooldown ?? 300)) return;

            var outpostPosition = availableSpawns[UnityEngine.Random.Range(0, availableSpawns.Count)];
            StartTeleportTimer(player, outpostPosition, "outpost", () =>
            {
                player.ChatMessage("You have teleported to the Outpost.");
                
                // Set global TP cooldown
                SetTPCooldown(player.UserIDString);
                
                if (!HasVIPPermission(player))
                    SetCooldown(player, "outpost");
            });
        }

        private void HomeCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            if (IsBlocked(player)) return;
            if (config?.HomeSettings.UseHomes != true) return;

            // Check global TP cooldown
            if (IsOnTPCooldown(player.UserIDString))
            {
                string timeLeft = GetCooldownTimeRemaining(player.UserIDString);
                player.ChatMessage($"You must wait {timeLeft} seconds before teleporting again.");
                return;
            }

            if (!Interface.Oxide.DataFileSystem.ExistsDatafile($"AwakenTeleportation/homes/{player.UserIDString}"))
            {
                player.ChatMessage("You have no homes set. Use /sethome <name> to set a home.");
                return;
            }

            var data = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<string, Vector3>>($"AwakenTeleportation/homes/{player.UserIDString}");
            if (data.Count == 0)
            {
                player.ChatMessage("You have no homes set. Use /sethome <name> to set a home.");
                return;
            }

            if (args.Length == 0)
            {
                player.ChatMessage($"Your homes: {string.Join(", ", data.Keys)}");
                return;
            }

            var homeName = args[0].ToLower();
            if (!data.TryGetValue(homeName, out var homePosition))
            {
                player.ChatMessage($"Home '{homeName}' not found. Your homes: {string.Join(", ", data.Keys)}");
                return;
            }

            if (!HasVIPPermission(player) && IsOnCooldown(player, "home", config?.HomeSettings.HomeCooldown ?? 300)) return;

            StartTeleportTimer(player, homePosition, "home", () =>
            {
                player.ChatMessage($"You have teleported to your home '{homeName}'.");
                
                // Set global TP cooldown
                SetTPCooldown(player.UserIDString);
                
                if (!HasVIPPermission(player))
                    SetCooldown(player, "home");
            });
        }

        private void SetHomeCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            if (config?.HomeSettings.UseHomes != true) return;

            if (args.Length == 0)
            {
                player.ChatMessage("Usage: /sethome <name>");
                return;
            }

            var homeName = args[0].ToLower();
            var data = Interface.Oxide.DataFileSystem.ExistsDatafile($"AwakenTeleportation/homes/{player.UserIDString}") ?
                Interface.Oxide.DataFileSystem.ReadObject<Dictionary<string, Vector3>>($"AwakenTeleportation/homes/{player.UserIDString}") :
                new Dictionary<string, Vector3>();

            int maxHomes = GetPlayerMaxHomes(player);
            if (data.Count >= maxHomes && !data.ContainsKey(homeName))
            {
                player.ChatMessage($"You have reached the maximum number of homes ({maxHomes}). Delete a home with /delhome <name> first.");
                return;
            }

            data[homeName] = player.transform.position;
            Interface.Oxide.DataFileSystem.WriteObject($"AwakenTeleportation/homes/{player.UserIDString}", data);
            player.ChatMessage($"Home '{homeName}' set at your current position.");
        }

        private void DelHomeCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            if (config?.HomeSettings.UseHomes != true) return;

            if (args.Length == 0)
            {
                player.ChatMessage("Usage: /delhome <name>");
                return;
            }

            if (!Interface.Oxide.DataFileSystem.ExistsDatafile($"AwakenTeleportation/homes/{player.UserIDString}"))
            {
                player.ChatMessage("You have no homes set.");
                return;
            }

            var data = Interface.Oxide.DataFileSystem.ReadObject<Dictionary<string, Vector3>>($"AwakenTeleportation/homes/{player.UserIDString}");
            var homeName = args[0].ToLower();

            if (!data.ContainsKey(homeName))
            {
                player.ChatMessage($"Home '{homeName}' not found. Your homes: {string.Join(", ", data.Keys)}");
                return;
            }

            data.Remove(homeName);
            Interface.Oxide.DataFileSystem.WriteObject($"AwakenTeleportation/homes/{player.UserIDString}", data);
            player.ChatMessage($"Home '{homeName}' deleted.");
        }
        #endregion

        #region Outpost Management Commands
        private void SetOutpostCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            // Add current position as custom outpost spawn
            var position = player.transform.position;
            customOutpostSpawns.Add(position);
            SaveCustomOutpostSpawns();

            player.ChatMessage($"Custom outpost spawn point added at your current position: {position}");
            player.ChatMessage($"Total custom outpost spawns: {customOutpostSpawns.Count}");
        }

        private void ListOutpostCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            var sb = new StringBuilder();
            sb.AppendLine("=== Outpost Spawn Points ===");

            if (customOutpostSpawns.Count > 0)
            {
                sb.AppendLine($"Custom Spawns ({customOutpostSpawns.Count}):");
                for (int i = 0; i < customOutpostSpawns.Count; i++)
                {
                    var spawn = customOutpostSpawns[i];
                    sb.AppendLine($"  {i + 1}. {spawn} (Distance: {Vector3.Distance(player.transform.position, spawn):F1}m)");
                }
            }
            else
            {
                sb.AppendLine("No custom spawns set.");
            }

            if (outpostSpawns.Count > 0)
            {
                sb.AppendLine($"Default Spawns ({outpostSpawns.Count}):");
                for (int i = 0; i < outpostSpawns.Count; i++)
                {
                    var spawn = outpostSpawns[i];
                    sb.AppendLine($"  {i + 1}. {spawn} (Distance: {Vector3.Distance(player.transform.position, spawn):F1}m)");
                }
            }
            else
            {
                sb.AppendLine("No default spawns found.");
            }

            var availableSpawns = customOutpostSpawns.Count > 0 ? customOutpostSpawns : outpostSpawns;
            sb.AppendLine($"Currently using: {(customOutpostSpawns.Count > 0 ? "Custom" : "Default")} spawns ({availableSpawns.Count} total)");

            player.ChatMessage(sb.ToString());
        }

        private void DelOutpostCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            if (customOutpostSpawns.Count == 0)
            {
                player.ChatMessage("No custom outpost spawns to delete.");
                return;
            }

            if (args.Length == 0)
            {
                player.ChatMessage("Usage: /deloutpost <index> or /deloutpost all");
                player.ChatMessage("Use /listoutpost to see available spawn points with their indices.");
                return;
            }

            if (args[0].ToLower() == "all")
            {
                int count = customOutpostSpawns.Count;
                customOutpostSpawns.Clear();
                SaveCustomOutpostSpawns();
                player.ChatMessage($"Deleted all {count} custom outpost spawn points.");
                return;
            }

            if (!int.TryParse(args[0], out int index) || index < 1 || index > customOutpostSpawns.Count)
            {
                player.ChatMessage($"Invalid index. Please use a number between 1 and {customOutpostSpawns.Count}.");
                return;
            }

            var removedSpawn = customOutpostSpawns[index - 1];
            customOutpostSpawns.RemoveAt(index - 1);
            SaveCustomOutpostSpawns();

            player.ChatMessage($"Deleted custom outpost spawn point {index}: {removedSpawn}");
            player.ChatMessage($"Remaining custom outpost spawns: {customOutpostSpawns.Count}");
        }

        private void ResetOutpostCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            int customCount = customOutpostSpawns.Count;
            customOutpostSpawns.Clear();
            SaveCustomOutpostSpawns();

            player.ChatMessage($"Reset outpost spawns to default. Removed {customCount} custom spawn points.");
            player.ChatMessage($"Now using {outpostSpawns.Count} default outpost spawn points from monuments.");
        }

        private void LoadCustomOutpostSpawns()
        {
            try
            {
                if (Interface.Oxide.DataFileSystem.ExistsDatafile("AwakenTeleportation/custom_outpost_spawns"))
                {
                    var data = Interface.Oxide.DataFileSystem.ReadObject<List<Vector3>>("AwakenTeleportation/custom_outpost_spawns");
                    if (data != null && data.Count > 0)
                    {
                        customOutpostSpawns.Clear();
                        customOutpostSpawns.AddRange(data);
                        Puts($"[Awaken Teleportation] Loaded {customOutpostSpawns.Count} custom outpost spawn points.");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error loading custom outpost spawns: {ex.Message}");
            }
        }

        private void SaveCustomOutpostSpawns()
        {
            try
            {
                // Ensure directory exists
                string dirPath = Path.Combine(Interface.Oxide.DataDirectory, "AwakenTeleportation");
                if (!Directory.Exists(dirPath))
                    Directory.CreateDirectory(dirPath);

                Interface.Oxide.DataFileSystem.WriteObject("AwakenTeleportation/custom_outpost_spawns", customOutpostSpawns);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error saving custom outpost spawns: {ex.Message}");
            }
        }

        private void TestClanAPICmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            var sb = new StringBuilder();
            sb.AppendLine("=== Clan API Test Results ===");

            if (AwakenClans == null)
            {
                sb.AppendLine("❌ AwakenClans plugin reference is NULL");
                player.ChatMessage(sb.ToString());
                return;
            }

            sb.AppendLine("✅ AwakenClans plugin reference found");

            try
            {
                // Test GetClan API
                var clan = AwakenClans.Call<object>("GetClan", player.userID);
                sb.AppendLine($"GetClan result: {(clan != null ? "✅ ClanInfo object found" : "❌ NULL")}");

                // Test GetClanTag API
                var clanTag = AwakenClans.Call<string>("GetClanTag", player.userID);
                sb.AppendLine($"GetClanTag result: {(string.IsNullOrEmpty(clanTag) ? "❌ Empty/NULL" : $"✅ '{clanTag}'")}");

                // Test IsClanOwner API
                var isOwner = AwakenClans.Call<bool>("IsClanOwner", player.UserIDString);
                sb.AppendLine($"IsClanOwner result: {(isOwner ? "✅ True" : "❌ False")}");

                // Test GetClanMemebers API
                var members = AwakenClans.Call<Dictionary<string, string>>("GetClanMemebers", player.userID);
                sb.AppendLine($"GetClanMemebers result: {(members != null && members.Count > 0 ? $"✅ {members.Count} members" : "❌ Empty/NULL")}");

                if (clan != null)
                {
                    sb.AppendLine("--- Clan Object Details ---");
                    try
                    {
                        var nameField = clan.GetType().GetField("ClanName");
                        var tagField = clan.GetType().GetField("ClanTag");
                        var ownerField = clan.GetType().GetField("ClanOwner");

                        sb.AppendLine($"ClanName: {nameField?.GetValue(clan)?.ToString() ?? "NULL"}");
                        sb.AppendLine($"ClanTag: {tagField?.GetValue(clan)?.ToString() ?? "NULL"}");
                        sb.AppendLine($"ClanOwner: {ownerField?.GetValue(clan)?.ToString() ?? "NULL"}");
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"Error reading clan object: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                sb.AppendLine($"❌ API Test Error: {ex.Message}");
            }

            player.ChatMessage(sb.ToString());
        }

        private void RefreshClanDataCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            if (AwakenClans == null)
            {
                player.ChatMessage("❌ AwakenClans plugin not found");
                return;
            }

            try
            {
                // Try to force a reload of clan data by calling the plugin's reload method
                var result = AwakenClans.Call("Reload");
                player.ChatMessage($"✅ Attempted to reload AwakenClans plugin. Result: {result ?? "No return value"}");

                // Wait a moment then test again
                timer.Once(2f, () => {
                    player.ChatMessage("🔄 Testing clan API after reload...");
                    TestClanAPICmd(iPlayer, command, args);
                });
            }
            catch (Exception ex)
            {
                player.ChatMessage($"❌ Error reloading clan data: {ex.Message}");
                player.ChatMessage("💡 Try manually reloading the AwakenClans plugin with: o.reload AwakenClans");
            }
        }

        private void FixTeamUICmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            if (AwakenClans == null)
            {
                player.ChatMessage("❌ AwakenClans plugin not found");
                return;
            }

            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("=== Team UI Fix Attempt ===");

                // Get clan information
                var clan = AwakenClans.Call<object>("GetClan", player.userID);
                if (clan == null)
                {
                    sb.AppendLine("❌ You are not in a clan");
                    player.ChatMessage(sb.ToString());
                    return;
                }

                sb.AppendLine("✅ Found clan data");

                // Try to get the clan's team ID using reflection
                var teamIdField = clan.GetType().GetField("ClanTeamId");
                if (teamIdField != null)
                {
                    var teamId = (ulong)teamIdField.GetValue(clan);
                    sb.AppendLine($"📋 Clan Team ID: {teamId}");

                    // Check if team exists
                    var team = RelationshipManager.ServerInstance.FindTeam(teamId);
                    if (team != null)
                    {
                        sb.AppendLine($"✅ Team found with {team.members.Count} members");

                        // Check if player is in team
                        if (team.members.Contains(player.userID))
                        {
                            sb.AppendLine("✅ You are in the team");
                        }
                        else
                        {
                            sb.AppendLine("❌ You are NOT in the team - attempting to add...");
                            team.AddPlayer(player);
                            team.MarkDirty();
                            player.SendNetworkUpdate();
                            sb.AppendLine("✅ Added you to the team");
                        }

                        // Force refresh team UI
                        team.MarkDirty();
                        foreach (var memberId in team.members)
                        {
                            var member = BasePlayer.FindByID(memberId);
                            if (member != null && member.IsConnected)
                            {
                                member.SendNetworkUpdate();
                            }
                        }
                        sb.AppendLine("🔄 Refreshed team UI for all members");
                    }
                    else
                    {
                        sb.AppendLine("❌ Team not found in RelationshipManager");
                        sb.AppendLine("💡 This indicates a serious TeamUI integration issue");
                    }
                }
                else
                {
                    sb.AppendLine("❌ Could not access ClanTeamId field");
                }

                player.ChatMessage(sb.ToString());
            }
            catch (Exception ex)
            {
                player.ChatMessage($"❌ Error fixing team UI: {ex.Message}");
            }
        }

        private void DebugClanDataCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            if (AwakenClans == null)
            {
                player.ChatMessage("❌ AwakenClans plugin not found");
                return;
            }

            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("=== Clan Data Debug ===");
                sb.AppendLine($"Your Player ID: {player.userID}");
                sb.AppendLine($"Your Player ID String: {player.UserIDString}");

                // Try to get all clans using the API
                var allClans = AwakenClans.Call<List<object>>("GetAllClans");
                if (allClans != null && allClans.Count > 0)
                {
                    sb.AppendLine($"✅ Found {allClans.Count} clans in cache:");

                    foreach (var clanObj in allClans)
                    {
                        try
                        {
                            // Use reflection to access clan properties
                            var clanType = clanObj.GetType();
                            var clanName = clanType.GetProperty("ClanName")?.GetValue(clanObj)?.ToString() ?? "Unknown";
                            var clanTag = clanType.GetProperty("ClanTag")?.GetValue(clanObj)?.ToString() ?? "Unknown";
                            var clanOwner = clanType.GetProperty("ClanOwner")?.GetValue(clanObj)?.ToString() ?? "Unknown";
                            var clanMembers = clanType.GetProperty("ClanMembers")?.GetValue(clanObj) as Dictionary<string, string>;

                            sb.AppendLine($"  📋 Clan: {clanName} [{clanTag}]");
                            sb.AppendLine($"     Owner: {clanOwner}");

                            if (clanMembers != null)
                            {
                                sb.AppendLine($"     Members ({clanMembers.Count}):");
                                foreach (var member in clanMembers)
                                {
                                    bool isYou = member.Key == player.UserIDString || member.Key == player.userID.ToString();
                                    sb.AppendLine($"       {(isYou ? "👤 YOU: " : "     ")}{member.Key} = {member.Value}");
                                }
                            }
                            else
                            {
                                sb.AppendLine("     ❌ No members data");
                            }
                            sb.AppendLine();
                        }
                        catch (Exception ex)
                        {
                            sb.AppendLine($"     ❌ Error reading clan data: {ex.Message}");
                        }
                    }
                }
                else
                {
                    sb.AppendLine("❌ No clans found in cache or GetAllClans returned null");
                }

                // Check if there are clan data files
                var clanDataPath = Path.Combine(Interface.Oxide.DataDirectory, "Clans");
                if (Directory.Exists(clanDataPath))
                {
                    var clanFiles = Directory.GetFiles(clanDataPath, "*.json");
                    sb.AppendLine($"📁 Found {clanFiles.Length} clan data files:");
                    foreach (var file in clanFiles)
                    {
                        var fileName = Path.GetFileNameWithoutExtension(file);
                        sb.AppendLine($"   - {fileName}.json");
                    }
                }
                else
                {
                    sb.AppendLine("❌ Clans data directory doesn't exist");
                }

                player.ChatMessage(sb.ToString());
            }
            catch (Exception ex)
            {
                player.ChatMessage($"❌ Error debugging clan data: {ex.Message}");
            }
        }
        #endregion

        #region Default Spawn Management Commands
        private void SetDefaultSpawnCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            // Add current position as custom default spawn
            var position = player.transform.position;
            customDefaultSpawns.Add(position);
            SaveCustomDefaultSpawns();

            player.ChatMessage($"Custom default spawn point added at your current position: {position}");
            player.ChatMessage($"Total custom default spawns: {customDefaultSpawns.Count}");
            player.ChatMessage("Players will now spawn at custom locations instead of beaches.");
        }

        private void ListDefaultSpawnCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            var sb = new StringBuilder();
            sb.AppendLine("=== Default Spawn Points ===");

            if (customDefaultSpawns.Count > 0)
            {
                sb.AppendLine($"Custom Default Spawns ({customDefaultSpawns.Count}):");
                for (int i = 0; i < customDefaultSpawns.Count; i++)
                {
                    var spawn = customDefaultSpawns[i];
                    sb.AppendLine($"  {i + 1}. {spawn} (Distance: {Vector3.Distance(player.transform.position, spawn):F1}m)");
                }
                sb.AppendLine("Players are currently spawning at these custom locations.");
            }
            else
            {
                sb.AppendLine("No custom default spawns set.");
                sb.AppendLine("Players are currently spawning at default beach locations.");
            }

            player.ChatMessage(sb.ToString());
        }

        private void DelDefaultSpawnCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            if (customDefaultSpawns.Count == 0)
            {
                player.ChatMessage("No custom default spawns to delete.");
                return;
            }

            if (args.Length == 0)
            {
                player.ChatMessage("Usage: /deldefaultspawn <index> or /deldefaultspawn all");
                player.ChatMessage("Use /listdefaultspawn to see available spawn points with their indices.");
                return;
            }

            if (args[0].ToLower() == "all")
            {
                int count = customDefaultSpawns.Count;
                customDefaultSpawns.Clear();
                SaveCustomDefaultSpawns();
                player.ChatMessage($"Deleted all {count} custom default spawn points.");
                player.ChatMessage("Players will now spawn at default beach locations.");
                return;
            }

            if (!int.TryParse(args[0], out int index) || index < 1 || index > customDefaultSpawns.Count)
            {
                player.ChatMessage($"Invalid index. Please use a number between 1 and {customDefaultSpawns.Count}.");
                return;
            }

            var removedSpawn = customDefaultSpawns[index - 1];
            customDefaultSpawns.RemoveAt(index - 1);
            SaveCustomDefaultSpawns();

            player.ChatMessage($"Deleted custom default spawn point {index}: {removedSpawn}");
            player.ChatMessage($"Remaining custom default spawns: {customDefaultSpawns.Count}");

            if (customDefaultSpawns.Count == 0)
            {
                player.ChatMessage("No custom spawns remaining. Players will now spawn at default beach locations.");
            }
        }

        private void ResetDefaultSpawnCmd(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player || !player.IsAdmin) return;

            int customCount = customDefaultSpawns.Count;
            customDefaultSpawns.Clear();
            SaveCustomDefaultSpawns();

            player.ChatMessage($"Reset default spawns to vanilla behavior. Removed {customCount} custom spawn points.");
            player.ChatMessage("Players will now spawn at default beach locations.");
        }

        private void LoadCustomDefaultSpawns()
        {
            try
            {
                if (Interface.Oxide.DataFileSystem.ExistsDatafile("AwakenTeleportation/custom_default_spawns"))
                {
                    var data = Interface.Oxide.DataFileSystem.ReadObject<List<Vector3>>("AwakenTeleportation/custom_default_spawns");
                    if (data != null && data.Count > 0)
                    {
                        customDefaultSpawns.Clear();
                        customDefaultSpawns.AddRange(data);
                        Puts($"[Awaken Teleportation] Loaded {customDefaultSpawns.Count} custom default spawn points.");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error loading custom default spawns: {ex.Message}");
            }
        }

        private void SaveCustomDefaultSpawns()
        {
            try
            {
                // Ensure directory exists
                string dirPath = Path.Combine(Interface.Oxide.DataDirectory, "AwakenTeleportation");
                if (!Directory.Exists(dirPath))
                    Directory.CreateDirectory(dirPath);

                Interface.Oxide.DataFileSystem.WriteObject("AwakenTeleportation/custom_default_spawns", customDefaultSpawns);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error saving custom default spawns: {ex.Message}");
            }
        }
        #endregion

        #region Clan Home Commands
        private void ClanHomeCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            if (IsBlocked(player)) return;
            if (config?.ClanHomeSettings.UseClanHomes != true) return;

            // Check global TP cooldown
            if (IsOnTPCooldown(player.UserIDString))
            {
                string timeLeft = GetCooldownTimeRemaining(player.UserIDString);
                player.ChatMessage($"You must wait {timeLeft} seconds before teleporting again.");
                return;
            }

            try
            {
                // Get clan information using direct file reading
                var clanData = GetPlayerClanDirect(player);

                if (clanData == null)
                {
                    player.ChatMessage("You are not in a clan. Join or create a clan first.");
                    return;
                }

                // Get clan name
                string clanName = clanData.ClanName;
                if (string.IsNullOrEmpty(clanName))
                {
                    player.ChatMessage("Error retrieving clan information. Please try again later.");
                    return;
                }

                // Check if clan home exists
                string dataPath = $"AwakenTeleportation/clanhomes/{clanName}";
                if (!Interface.Oxide.DataFileSystem.ExistsDatafile(dataPath))
                {
                    player.ChatMessage("Your clan has no home set. The clan leader can set one with /setclanhome.");
                    return;
                }

                // Read home position with error handling
                Vector3 homePosition;
                try
                {
                    homePosition = Interface.Oxide.DataFileSystem.ReadObject<Vector3>(dataPath);
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Teleportation] Error reading clan home data: {ex.Message}");
                    player.ChatMessage("Error reading clan home data. Please contact an administrator.");
                    return;
                }

                if (homePosition == Vector3.zero)
                {
                    player.ChatMessage("Your clan has no home set. The clan leader can set one with /setclanhome.");
                    return;
                }

                // Check cooldown
                if (!HasVIPPermission(player) && IsOnCooldown(player, "clanhome", config?.ClanHomeSettings.ClanHomeCooldown ?? 300))
                    return;

                // Start teleport timer
                StartTeleportTimer(player, homePosition, "clanhome", () =>
                {
                    player.ChatMessage("You have teleported to your clan home.");
                    
                    // Set global TP cooldown
                    SetTPCooldown(player.UserIDString);
                    
                    if (!HasVIPPermission(player))
                        SetCooldown(player, "clanhome");
                });
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error in ClanHomeCMD: {ex.Message}");
                player.ChatMessage("An error occurred while processing your request. Please try again later.");
            }
        }

        private void SetClanHomeCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;

            if (config?.ClanHomeSettings.UseClanHomes != true) return;

            try
            {
                // Get clan information using direct file reading
                var clanData = GetPlayerClanDirect(player);

                if (clanData == null)
                {
                    player.ChatMessage("You are not in a clan. Join or create a clan first.");
                    return;
                }

                // Check if player is clan owner
                bool isOwner = clanData.ClanOwner == player.UserIDString;

                if (!isOwner)
                {
                    player.ChatMessage("Only the clan leader can set the clan home.");
                    return;
                }

                // Get clan name
                string clanName = clanData.ClanName;

                if (string.IsNullOrEmpty(clanName))
                {
                    player.ChatMessage("Error retrieving clan information. Please try again later.");
                    return;
                }

                // Save clan home position
                string dataPath = $"AwakenTeleportation/clanhomes/{clanName}";

                try
                {
                    // Ensure directory exists
                    string dirPath = Path.Combine(Interface.Oxide.DataDirectory, "AwakenTeleportation/clanhomes");
                    if (!Directory.Exists(dirPath))
                    {
                        Directory.CreateDirectory(dirPath);
                    }

                    var position = player.transform.position;
                    Interface.Oxide.DataFileSystem.WriteObject(dataPath, position);
                    player.ChatMessage("Clan home set at your current position.");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Teleportation] Error saving clan home data: {ex.Message}");
                    player.ChatMessage("Error saving clan home data. Please try again later.");
                    return;
                }

                // Notify all online clan members
                NotifyClanMembersDirect(player, clanData, "Clan leader has set a new clan home. Use /clanhome to teleport there.");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error in SetClanHomeCMD: {ex.Message}");
                player.ChatMessage($"❌ An error occurred: {ex.Message}");
            }
        }

        private void DelClanHomeCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || iPlayer.Object is not BasePlayer player) return;
            if (config?.ClanHomeSettings.UseClanHomes != true) return;

            try
            {
                // Get clan information using direct file reading
                var clanData = GetPlayerClanDirect(player);
                if (clanData == null)
                {
                    player.ChatMessage("You are not in a clan. Join or create a clan first.");
                    return;
                }

                // Check if player is clan owner
                bool isOwner = clanData.ClanOwner == player.UserIDString;
                if (!isOwner)
                {
                    player.ChatMessage("Only the clan leader can delete the clan home.");
                    return;
                }

                // Get clan name
                string clanName = clanData.ClanName;
                if (string.IsNullOrEmpty(clanName))
                {
                    player.ChatMessage("Error retrieving clan information. Please try again later.");
                    return;
                }

                // Check if clan home exists
                string dataPath = $"AwakenTeleportation/clanhomes/{clanName}";
                if (!Interface.Oxide.DataFileSystem.ExistsDatafile(dataPath))
                {
                    player.ChatMessage("Your clan has no home set.");
                    return;
                }

                // Delete clan home
                try
                {
                    Interface.Oxide.DataFileSystem.WriteObject(dataPath, Vector3.zero);
                    player.ChatMessage("Clan home deleted.");
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[Awaken Teleportation] Error deleting clan home data: {ex.Message}");
                    player.ChatMessage("Error deleting clan home data. Please try again later.");
                    return;
                }

                // Notify all online clan members
                NotifyClanMembersDirect(player, clanData, "Clan leader has removed the clan home.");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error in DelClanHomeCMD: {ex.Message}");
                player.ChatMessage("An error occurred while processing your request. Please try again later.");
            }
        }



        // Helper method to notify clan members using direct clan data
        private void NotifyClanMembersDirect(BasePlayer sender, ClanData clanData, string message)
        {
            try
            {
                if (clanData?.ClanMemebers == null || clanData.ClanMemebers.Count == 0) return;

                // Notify all online clan members except the sender
                foreach (var memberId in clanData.ClanMemebers.Keys)
                {
                    if (memberId == sender.UserIDString) continue;

                    // Find player and send message
                    if (BasePlayer.Find(memberId) is { } member && member.IsConnected)
                        member.ChatMessage(message);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error notifying clan members: {ex.Message}");
            }
        }

        // Direct clan data reading - bypasses API completely
        private ClanData GetPlayerClanDirect(BasePlayer player)
        {
            try
            {
                var clansDir = Path.Combine(Interface.Oxide.DataDirectory, "Clans");
                if (!Directory.Exists(clansDir))
                {
                    return null;
                }

                var clanFiles = Directory.GetFiles(clansDir, "*.json");
                var playerIdStr = player.userID.ToString();

                foreach (var file in clanFiles)
                {
                    try
                    {
                        var clanName = Path.GetFileNameWithoutExtension(file);

                        // Read the clan data directly from file
                        var clanData = Interface.Oxide.DataFileSystem.ReadObject<ClanData>($"Clans/{clanName}");
                        if (clanData == null)
                        {
                            continue;
                        }

                        // Check if player is in this clan
                        if (clanData.ClanMemebers.ContainsKey(playerIdStr))
                        {
                            return clanData;
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"[Awaken Teleportation] Error reading clan file {file}: {ex.Message}");
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Teleportation] Error in GetPlayerClanDirect: {ex.Message}");
                return null;
            }
        }


        // Clan data structure matching AwakenClans
        public class ClanData
        {
            public ulong ClanTeamId { get; set; } = 0;
            public string ClanName { get; set; } = "";
            public string ClanTag { get; set; } = "";
            public string ClanOwner { get; set; } = "";
            public int MaxClanMembers { get; set; } = 16;
            public bool ClanFF { get; set; } = false;
            public List<string> ClanOfficers { get; set; } = new List<string>();
            public Dictionary<string, string> ClanMemebers { get; set; } = new Dictionary<string, string>(); // Note: keeping the typo to match AwakenClans
            public List<string> ClanInvites { get; set; } = new List<string>();
            public List<string> ClanAllys { get; set; } = new List<string>();
            public List<string> ClanAllyInvites { get; set; } = new List<string>();
            public Dictionary<string, bool> ClanAllyFF { get; set; } = new Dictionary<string, bool>();
            public List<string> ClanMergeAllys { get; set; } = new List<string>();
            public List<string> ClanMergeAllyInvites { get; set; } = new List<string>();
        }
        #endregion

        #region Data
        private class PlayerInfo
        {
            public Dictionary<string, Vector3> Homes = new Dictionary<string, Vector3>();
        }
        #endregion
    }
}





























