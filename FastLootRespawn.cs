using Oxide.Core.Plugins;
using Oxide.Core;
using System.Collections.Generic;
using UnityEngine;
using System.Linq;
using System.Net;
using System.Text;
using Random = UnityEngine.Random;

namespace Oxide.Plugins
{
    [Info("FastLootRespawn", "Mohamed Amine <PERSON>", "1.2.4")]
    public class FastLootRespawn : RustPlugin
    {
        private static readonly List<string> AllowedCrates = new List<string>
        {
            "crate_elite",
            "crate_normal"
        };

        private void OnLootEntityEnd(BasePlayer player, BaseEntity entity)
        {
            if (entity is StorageContainer container && IsAllowedCrate(container))
            {
                bool directRemoval = bool.Parse(Config["Direct container removal without dropping loot"].ToString());
                bool enableEntKill = bool.Parse(Config["Enable When Player Dont Finish Looting Containers Will Drop Loot On Ground"]?.ToString() ?? "true");

                if (enableEntKill)
                {
                    if (directRemoval)
                    {
                        if (container != null && !container.IsDestroyed)
                        {
                            container.Invoke(() =>
                            {
                                if (container != null && !container.IsDestroyed)
                                    container.Kill();
                            }, 0.1f);
                        }
                    }
                    else
                    {
                        var itemList = container.inventory.itemList.ToList();
                        foreach (var item in itemList)
                        {
                            item.RemoveFromContainer();
                            
                            item.Drop(container.transform.position + Vector3.up, Vector3.zero);
                        }
                    }
                }
            }
        }


        private Dictionary<Vector3, Timer> scheduledRespawns = new Dictionary<Vector3, Timer>();
        private List<MonumentInfo> monuments = new List<MonumentInfo>();
        private Dictionary<string, (float min, float max)> monumentRespawnTimes = new Dictionary<string, (float min, float max)>();


        private void OnServerInitialized()
        {
            
            monuments = TerrainMeta.Path.Monuments
                .Select(m => m.GetComponent<MonumentInfo>())
                .ToList();

            
            if (IsServerWiped())
            {
                Config.Clear();
                LoadDefaultConfig();
            }
            else
            {
                LoadConfigFile();
                RemoveNonExistingMonumentsFromConfig();
            }

            StartCrateMonitor();
            CheckForCrateStacking();
        }

        
        protected override void LoadDefaultConfig()
        {
            Config.Clear();
            Config["Direct container removal without dropping loot"] = false;
            Config["Enable When Player Dont Finish Looting Containers Will Drop Loot On Ground"] = true;

            foreach (var monument in monuments)
            {
                Config[$"{monument.displayPhrase.english.ToLower()}"] = "1800-3600"; 
            }

            SaveConfig();
        }

        private void LoadConfigFile()
        {
            if (Config["Direct container removal without dropping loot"] == null)
            {
                Config["Direct container removal without dropping loot"] = false;
                SaveConfig();
            }

            if (Config["Enable When Player Dont Finish Looting Containers Will Drop Loot On Ground"] == null)
            {
                Config["Enable When Player Dont Finish Looting Containers Will Drop Loot On Ground"] = true;
                SaveConfig();
            }

            
            foreach (var monument in monuments)
            {
                string monumentKey = $"{monument.displayPhrase.english.ToLower()}";
                if (Config[monumentKey] != null)
                {
                    string configValue = Config.Get<string>(monumentKey);
                    if (TryParseRespawnTimeRange(configValue, out var range))
                    {
                        monumentRespawnTimes[monumentKey] = range;
                    }
                    else
                    {
                        monumentRespawnTimes[monumentKey] = (1800f, 3600f);
                        Config[monumentKey] = "1800-3600";
                        SaveConfig();
                    }
                }
                else
                {
                    monumentRespawnTimes[monumentKey] = (1800f, 3600f);
                    Config[monumentKey] = "1800-3600";
                    SaveConfig();
                }
            }
        }

        private bool TryParseRespawnTimeRange(string input, out (float min, float max) range)
        {
            range = (1800f, 3600f);
            if (string.IsNullOrEmpty(input))
                return false;

            string[] parts = input.Split('-');
            if (parts.Length == 2 &&
                float.TryParse(parts[0], out float min) &&
                float.TryParse(parts[1], out float max))
            {
                range = (min, max);
                return true;
            }
            return false;
        }

        private void RemoveNonExistingMonumentsFromConfig()
        {
            var existingMonuments = monuments
                .Select(m => m.displayPhrase.english.ToLower())
                .ToHashSet();

            var keysToRemove = new List<string>();

            
            foreach (var key in Config.ToDictionary(k => k.Key, v => v.Value).Keys)
            {
                if (!existingMonuments.Contains(key)
                    && key != "Direct container removal without dropping loot"
                    && key != "Enable When Player Dont Finish Looting Containers Will Drop Loot On Ground")
                {
                    keysToRemove.Add(key);
                }
            }

            foreach (var key in keysToRemove)
            {
                Config.Remove(key);
            }

            SaveConfig();
        }

        
        private void OnEntityKill(BaseNetworkable entity)
        {
            if (entity is StorageContainer crate && IsAllowedCrate(crate))
            {
                Vector3 position = crate.transform.position;
                if (IsInsideMonument(position, out string monumentName)
                    || monumentName.ToLower().Contains("tunnel")
                    || position.y < 0)
                {
                    
                    ScheduleCrateRespawn(crate, position, monumentName);
                }
            }
        }

        private bool IsAllowedCrate(BaseEntity crate)
        {
            return AllowedCrates.Any(allowed => crate.ShortPrefabName == allowed)
                   && crate.ShortPrefabName != "crate_normal_2";
        }

        private void ScheduleCrateRespawn(StorageContainer crate, Vector3 position, string monumentName)
        {
            if (scheduledRespawns.Remove(position, out Timer existingTimer))
            {
                existingTimer.Destroy();
            }

            (float min, float max) respawnTimeRange =
                monumentRespawnTimes.ContainsKey(monumentName.ToLower())
                ? monumentRespawnTimes[monumentName.ToLower()]
                : (1800f, 3600f);

            float respawnTime = Random.Range(respawnTimeRange.min, respawnTimeRange.max);

            Timer newTimer = timer.Once(respawnTime, () => RespawnCrate(crate.PrefabName, position, monumentName));
            scheduledRespawns[position] = newTimer;
        }

        
        private bool IsNearCargoShip(Vector3 position, float radius = 150f)
        {
            
            var cargoShips = BaseNetworkable.serverEntities
                .OfType<BaseEntity>()
                .Where(ent => !ent.IsDestroyed && ent.ShortPrefabName.Contains("cargoship"))
                .ToList();

            
            foreach (var ship in cargoShips)
            {
                float dist = Vector3.Distance(position, ship.transform.position);
                if (dist <= radius)
                    return true;
            }
            return false;
        }

        
        private bool IsFloatingPosition(Vector3 position, float maxCheckDistance = 3f)
        {
            
            int layerMask = LayerMask.GetMask("Terrain", "World", "Water", "Construction");

           
            Vector3 startPos = position + Vector3.up * 0.1f;

            
            if (Physics.Raycast(startPos, Vector3.down, out RaycastHit hit, maxCheckDistance, layerMask))
            {
                
                return false;
            }

            
            return true;
        }

        
        private void RespawnCrate(string prefabName, Vector3 position, string monumentName)
        {
            
            if (IsCrateAlreadyPresent(position))
                return;

            
            if (!AllowedCrates.Any(allowed => prefabName.Contains(allowed)) || prefabName == "crate_normal_2")
                return;

            
            if (IsNearCargoShip(position, 150f))
            {
                
                return;
            }

            
            if (IsFloatingPosition(position, 3f))
            {
                
                return;
            }

            
            var newCrate = GameManager.server.CreateEntity(prefabName, position, Quaternion.identity) as StorageContainer;
            if (newCrate == null)
                return;

            newCrate.Spawn();
        }

        private bool IsCrateAlreadyPresent(Vector3 position)
        {
            Collider[] colliders = Physics.OverlapSphere(position, 1f);
            return colliders.Any(collider => collider.GetComponentInParent<StorageContainer>() != null);
        }

        private bool IsInsideMonument(Vector3 position, out string monumentName)
        {
            foreach (var monument in monuments)
            {
                
                Vector3 closestPoint = monument.transform.position;
                float distanceSqr = (position - closestPoint).sqrMagnitude;
                if (distanceSqr <= monument.Bounds.extents.sqrMagnitude)
                {
                    monumentName = monument.displayPhrase.english;
                    return true;
                }
            }
            monumentName = string.Empty;
            return false;
        }

        private bool IsServerWiped()
        {
            string savePath = $"{ConVar.Server.rootFolder}/{ConVar.Server.identity}/{ConVar.Server.identity}.sav";
            if (System.IO.File.Exists(savePath))
            {
                var saveCreatedTime = System.IO.File.GetCreationTime(savePath);
                return (System.DateTime.Now - saveCreatedTime).TotalMinutes < 5;
            }
            return false;
        }

        
        private Timer crateCheckTimer;

        private void StartCrateMonitor()
        {
            crateCheckTimer = timer.Every(20f, CheckForCrateStacking);
        }

        private void CheckForCrateStacking()
        {
            var allCrates = BaseNetworkable.serverEntities
                .OfType<StorageContainer>()
                .Where(crate => IsAllowedCrate(crate))
                .ToArray();

            foreach (var crate in allCrates)
            {
                if (crate != null && !crate.IsDestroyed)
                {
                    var nearbyCrates = Physics.OverlapSphere(crate.transform.position, 1f)
                        .Where(collider => collider.GetComponentInParent<StorageContainer>() != null
                                           && !collider.GetComponentInParent<StorageContainer>().IsDestroyed)
                        .Select(collider => collider.GetComponentInParent<StorageContainer>())
                        .Distinct()
                        .ToList();

                    if (nearbyCrates.Count > 1)
                    {
                        var sortedCrates = nearbyCrates
                            .OrderBy(c => Vector3.Distance(crate.transform.position, c.transform.position))
                            .ToList();

                        
                        var cratesToRemove = sortedCrates.Skip(1).Where(c => IsAllowedCrate(c)).ToList();
                        foreach (var removeCrate in cratesToRemove)
                        {
                            if (removeCrate != null && !removeCrate.IsDestroyed)
                            {
                                removeCrate.Kill();
                            }
                        }
                    }
                }
            }
        }
    }
}
 