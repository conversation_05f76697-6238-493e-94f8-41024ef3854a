using Facepunch;
using Network;
using Newtonsoft.Json;
using Oxide.Core.Libraries.Covalence;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Environment", "Skelee", "1.1.3")]
    [Description("Handles time and weather in Awaken Servers.")]

    public class AwakenEnviroment : CovalencePlugin
    {
        #region Config
        private static Configuration? config;

        private record Configuration(
            [property: JsonProperty("Use Day/Night Cycle (If false, uses freeze time)")] bool UseDayNightCycle = true,
            [property: JsonProperty("Day Cycle (Mins)")] int DayCycle = 50,
            [property: JsonProperty("Night Cycle (Mins)")] int NightCycle = 10,
            [property: JsonProperty("Time To Freeze At")] int FreezeTime = 12,
            [property: JsonProperty("Remove Weather")] bool RemoveWeather = true,
            [property: JsonProperty("Use No Sun Glare")] bool UseNoSunGlare = true,
            [property: JsonProperty("Use Bright Nights")] bool UseBrightNights = true)
        {
            public Configuration() : this(true, 50, 10, 12, true, true, true) { }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null)
                {
                    config = LoadDefaultConfig();
                }
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                config = LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Defines
        private TOD_Time? TimeInstance;
        private EnvSync? envSync;
        private readonly long fullMoon = new DateTime(2024, 1, 25).ToBinary();
        private bool isDay;
        private Timer? nightTimer;
        private Timer? sunglareTimer;
        #endregion

        #region Hooks
        private void OnServerInitialized()
        {
            if (TOD_Sky.Instance == null) { Puts("Could not find sky instance, disabling Oasis Environment."); return; }
            if ((TimeInstance = TOD_Sky.Instance.Components.Time) == null) { Puts("Could not find time instance, disabling Oasis Environment."); return; }
            if ((envSync = BaseNetworkable.serverEntities.OfType<EnvSync>().FirstOrDefault()) == null) { Puts("Could not find env sync instance, disabling Oasis Environment."); return; }
            if (config == null) { Puts("Config is null, disabling Oasis Environment."); return; }

            if (config.UseDayNightCycle != true)
            {
                TimeInstance.ProgressTime = false;
                ConVar.Env.time = config.FreezeTime;
            }
            else
            {
                TimeInstance.ProgressTime = true;
                TimeInstance.UseTimeCurve = false;
                TimeInstance.OnSunrise += OnSunrise;
                TimeInstance.OnSunset += OnSunset;
                TimeInstance.OnHour += OnHour;

                if (TOD_Sky.Instance.Cycle.Hour > TOD_Sky.Instance.SunriseTime && TOD_Sky.Instance.Cycle.Hour < TOD_Sky.Instance.SunsetTime)
                    OnSunrise();
                else
                    OnSunset();
            }

            if (config.RemoveWeather == true) RemoveWeather();
            if (config.UseNoSunGlare == true)
            {
                RemoveSunGlare();
                // Start a timer to periodically reapply sunglare settings (reduced frequency)
                sunglareTimer = timer.Every(300f, () => { // Changed from 30s to 5 minutes
                    if (config?.UseNoSunGlare == true && isDay)
                    {
                        RemoveSunGlare();
                        // Only send weather updates if settings actually changed
                        ServerMgr.SendReplicatedVars("weather.");
                    }
                });
            }
            ServerMgr.SendReplicatedVars("weather.");
        }

        private void Unload()
        {
            if (TimeInstance != null)
            {
                TimeInstance.OnSunrise -= OnSunrise;
                TimeInstance.OnSunset -= OnSunset;
                TimeInstance.OnHour -= OnHour;
            }
            sunglareTimer?.Destroy();
            nightTimer?.Destroy();
            ServerMgr.SendReplicatedVars("weather.");
        }
        #endregion

        #region Commands
        [Command("sunglare")]
        private void SunglareCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            if (config?.UseNoSunGlare == true)
            {
                RemoveSunGlare();
                ServerMgr.SendReplicatedVars("weather.");
                player.Reply("Sunglare settings applied and sent to clients.");
            }
            else
            {
                player.Reply("Sunglare removal is disabled in config.");
            }
        }
        #endregion

        #region Functions
        private void OnHour()
        {
            bool isCurrentlyDay = TOD_Sky.Instance.Cycle.Hour > TOD_Sky.Instance.SunriseTime && TOD_Sky.Instance.Cycle.Hour < TOD_Sky.Instance.SunsetTime;
            if (isCurrentlyDay && !isDay)
            {
                OnSunrise();
            }
            else if (!isCurrentlyDay && isDay)
            {
                OnSunset();
            }
        }

        private void OnSunrise()
        {
            if (TimeInstance == null || config == null) return;
            TimeInstance.DayLengthInMinutes = config.DayCycle * (24.0f / (TOD_Sky.Instance.SunsetTime - TOD_Sky.Instance.SunriseTime));
            isDay = true;
            Puts("Sunrise");
            if (config.UseNoSunGlare == true)
            {
                RemoveSunGlare();
                ServerMgr.SendReplicatedVars("weather.");
            }
        }

        private void OnSunset()
        {
            if (TimeInstance == null || config == null) return;
            TimeInstance.DayLengthInMinutes = config.NightCycle * (24.0f / (24.0f - (TOD_Sky.Instance.SunsetTime - TOD_Sky.Instance.SunriseTime)));
            isDay = false;
            Puts("Sunset");
            if (config.UseBrightNights == true) ClearNight();
        }

        void RemoveWeather()
        {
            ConVar.Weather.dust_chance = 0f;
            ConVar.Weather.clear_chance = 0f;
            ConVar.Weather.fog_chance = 0f;
            ConVar.Weather.rain_chance = 0f;
            ConVar.Weather.storm_chance = 0f;
            ConVar.Weather.overcast_chance = 0f;
            ConVar.Weather.fog = 0f;
            ConVar.Weather.rain = 0f;
            ConVar.Weather.thunder = 0f;
            ConVar.Weather.wind = 0f;
            ConVar.Weather.rainbow = 0f;
        }

        void ClearNight()
        {
            TOD_Sky.Instance.Night.AmbientMultiplier = 10f;
            TOD_Sky.Instance.Night.ShadowStrength = 0.5f;
        }

        void RemoveSunGlare()
        {
            // Atmosphere settings to reduce sunglare
            ConVar.Weather.atmosphere_mie = 0f;
            ConVar.Weather.atmosphere_contrast = 1.0f;
            ConVar.Weather.atmosphere_brightness = 0.5f;
            ConVar.Weather.atmosphere_directionality = 0f;
            ConVar.Weather.atmosphere_rayleigh = 0.8f;

            // Cloud settings to help block sun
            ConVar.Weather.cloud_brightness = 1.2f;
            ConVar.Weather.cloud_coverage = 0.3f;
            ConVar.Weather.cloud_opacity = 0.8f;
            ConVar.Weather.cloud_coloring = 0.2f;
            ConVar.Weather.cloud_saturation = 0.8f;
            ConVar.Weather.cloud_scattering = 0.1f;
            ConVar.Weather.cloud_sharpness = 0.2f;
            ConVar.Weather.cloud_size = 1.0f;

            // Additional atmosphere settings for better sunglare reduction
            ConVar.Weather.atmosphere_brightness = 0.4f;
            ConVar.Weather.atmosphere_contrast = 0.8f;
        }
        #endregion
    }
}









