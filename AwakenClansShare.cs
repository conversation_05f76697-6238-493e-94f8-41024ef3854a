/*
 * AwakenClansShare - Updated for Merge Ally System
 *
 * IMPORTANT SECURITY BEHAVIOR:
 * - Clan Members: Full access to turrets, cupboards, and locked entities
 * - Regular Allies: Full access to entities (for second team UI functionality)
 * - Merge Allies: NO ACCESS to any entities - they can raid together but cannot access bases
 *
 * This maintains the security boundary where merge allies are for raiding cooperation only,
 * while regular allies get full access for team UI and clan members get full access.
 */

using Oxide.Core.Plugins;
using Oxide.Game.Rust;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Clans Share", "Skelee", "2.1.0")]
    [Description("Sharing between clan members for Awaken Servers - Updated for May 2025 Rust API with Merge Ally Support")]
    public class AwakenClansShare : CovalencePlugin
    {
        #region Defines
        [PluginReference] private Plugin? AwakenClans;

        private enum UpdateType { AutoTurret, BuildingPriv, CodeLock, KeyLock }

        private record EntityCache(
            HashSet<AutoTurret>? AutoTurrets = null,
            HashSet<BuildingPrivlidge>? BuildingPrivlidges = null,
            HashSet<CodeLock>? CodeLocks = null,
            HashSet<KeyLock>? KeyLocks = null)
        {
            public EntityCache() : this(new HashSet<AutoTurret>(), new HashSet<BuildingPrivlidge>(), new HashSet<CodeLock>(), new HashSet<KeyLock>()) { }
        }

        private readonly Dictionary<ulong, EntityCache> playerEntities = new Dictionary<ulong, EntityCache>();
        #endregion

        #region Hooks
        private void Init() => Unsubscribe(nameof(OnEntitySpawned));

        private void OnServerInitialized()
        {
            try
            {
                // Subscribe to entity spawned events
                Subscribe(nameof(OnEntitySpawned));

                // Process existing entities in batches to avoid performance issues
                // This is a new approach for the 2025 Rust API to handle larger entity counts
                ProcessExistingEntitiesInBatches();
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error during initialization: {ex.Message}");
            }
        }

        // New method to process entities in batches for better performance
        private void ProcessExistingEntitiesInBatches()
        {
            try
            {
                // Get all entities
                var allEntities = BaseNetworkable.serverEntities.ToList();
                int totalCount = allEntities.Count;
                int processedCount = 0;
                int batchSize = 1000; // Process 1000 entities at a time

                // Process in batches
                for (int i = 0; i < totalCount; i += batchSize)
                {
                    int currentBatchSize = Math.Min(batchSize, totalCount - i);
                    var batch = allEntities.GetRange(i, currentBatchSize);

                    foreach (var entity in batch)
                    {
                        if (entity?.IsDestroyed == false)
                        {
                            // Try to cast to relevant entity types
                            var turret = entity as AutoTurret;
                            var priv = entity as BuildingPrivlidge;
                            var codeLock = entity as CodeLock;
                            var keyLock = entity as KeyLock;

                            // Add entity if it's a supported type
                            if (turret != null)
                                AddEntity(turret);
                            else if (priv != null)
                                AddEntity(priv);
                            else if (codeLock != null)
                                AddEntity(codeLock);
                            else if (keyLock != null)
                                AddEntity(keyLock);
                        }
                    }

                    processedCount += currentBatchSize;
                    if (processedCount % 5000 == 0)
                        Puts($"[Awaken Clans Share] Processed {processedCount}/{totalCount} entities");
                }

                Puts($"[Awaken Clans Share] Completed processing {processedCount} entities");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error processing entities in batches: {ex.Message}");
            }
        }

        private void OnEntitySpawned(AutoTurret turret) => AddEntity(turret);
        private void OnEntitySpawned(BuildingPrivlidge priv) => AddEntity(priv);
        private void OnEntitySpawned(CodeLock codeLock) => AddEntity(codeLock);
        private void OnEntitySpawned(KeyLock keyLock) => AddEntity(keyLock);

        private void OnEntityKill(AutoTurret? autoTurret) => RemoveEntity(autoTurret);
        private void OnEntityKill(BuildingPrivlidge? buildingPrivlidge) => RemoveEntity(buildingPrivlidge);
        private void OnEntityKill(CodeLock? codeLock) => RemoveEntity(codeLock);
        private void OnEntityKill(KeyLock? keyLock) => RemoveEntity(keyLock);

        private object? CanUseLockedEntity(BasePlayer? player, BaseLock? baseLock)
        {
            try
            {
                // Basic validation
                if (player == null || baseLock == null || !baseLock.IsLocked())
                    return null;

                var ownerId = GetOwnerId(baseLock);

                // If no valid owner or player is the owner, allow access
                if (!ownerId.IsSteamId() || ownerId == player.userID)
                    return null;

                // Check if they're in the same clan
                if (AwakenClans?.Call<bool>("SameClan", ownerId, player.userID) == true)
                    return true; // Allow access for clan members

                // Check if they're regular allies (should have access for second team UI)
                if (AwakenClans?.Call<bool>("AreRegularAllies", ownerId, player.userID) == true)
                    return true; // Allow access for regular allies

                // IMPORTANT: Merge allies should NOT have access to locked entities
                // This is the key difference - merge allies can raid together but can't access bases

                return null; // Deny access for merge allies and non-allies
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error in CanUseLockedEntity: {ex.Message}");
                return null;
            }
        }

        private ulong GetOwnerId(BaseLock baseLock) => baseLock.OwnerID.IsSteamId() ? baseLock.OwnerID :
            baseLock.GetParentEntity() is BaseEntity parent && parent.OwnerID.IsSteamId() ? parent.OwnerID : 0;
        #endregion

        #region Functions
        private void AddEntity(BaseNetworkable? entity)
        {
            try
            {
                // Validate entity and cast to BaseEntity to access OwnerID
                if (entity == null || entity.IsDestroyed || entity is not BaseEntity baseEntity || !baseEntity.OwnerID.IsSteamId()) return;

                // Get or create cache for this owner
                var cache = playerEntities.GetValueOrDefault(baseEntity.OwnerID);
                if (cache == null)
                    cache = playerEntities[baseEntity.OwnerID] = new EntityCache();

                // Process based on entity type
                switch (entity)
                {
                    case AutoTurret turret:
                        // Check if turret is already in cache to avoid duplicates
                        if (cache.AutoTurrets != null && !cache.AutoTurrets.Contains(turret))
                        {
                            cache.AutoTurrets.Add(turret);
                            // Update clan authorization
                            UpdateClanAuth(UpdateType.AutoTurret, turret);
                        }
                        break;

                    case BuildingPrivlidge priv:
                        // Check if privilege is already in cache to avoid duplicates
                        if (cache.BuildingPrivlidges != null && !cache.BuildingPrivlidges.Contains(priv))
                        {
                            cache.BuildingPrivlidges.Add(priv);
                            // Update clan authorization
                            UpdateClanAuth(UpdateType.BuildingPriv, priv);
                        }
                        break;

                    case CodeLock codeLock:
                        // Check if codelock is already in cache to avoid duplicates
                        if (cache.CodeLocks != null && !cache.CodeLocks.Contains(codeLock))
                        {
                            cache.CodeLocks.Add(codeLock);
                            // Update clan authorization
                            UpdateClanAuth(UpdateType.CodeLock, codeLock);
                        }
                        break;

                    case KeyLock keyLock:
                        // Check if keylock is already in cache to avoid duplicates
                        if (cache.KeyLocks != null && !cache.KeyLocks.Contains(keyLock))
                        {
                            cache.KeyLocks.Add(keyLock);
                            // Update clan authorization
                            UpdateClanAuth(UpdateType.KeyLock, keyLock);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error adding entity: {ex.Message}");
            }
        }

        private void RemoveEntity(BaseNetworkable? entity)
        {
            try
            {
                // Validate entity and check if it's in our cache
                if (entity == null || entity is not BaseEntity baseEntity || !baseEntity.OwnerID.IsSteamId() ||
                    !playerEntities.TryGetValue(baseEntity.OwnerID, out var cache))
                    return;

                // Remove based on entity type
                switch (entity)
                {
                    case AutoTurret turret:
                        cache.AutoTurrets?.Remove(turret);
                        break;

                    case BuildingPrivlidge priv:
                        cache.BuildingPrivlidges?.Remove(priv);
                        break;

                    case CodeLock codeLock:
                        cache.CodeLocks?.Remove(codeLock);
                        break;

                    case KeyLock keyLock:
                        cache.KeyLocks?.Remove(keyLock);
                        break;
                }

                // Clean up empty caches to prevent memory leaks
                if ((cache.AutoTurrets?.Count ?? 0) == 0 &&
                    (cache.BuildingPrivlidges?.Count ?? 0) == 0 &&
                    (cache.CodeLocks?.Count ?? 0) == 0 &&
                    (cache.KeyLocks?.Count ?? 0) == 0)
                    playerEntities.Remove(baseEntity.OwnerID);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error removing entity: {ex.Message}");
            }
        }

        private void UpdateClanAuth(UpdateType type, BaseNetworkable entity)
        {
            try
            {
                // Cast to BaseEntity to access OwnerID
                if (entity is not BaseEntity baseEntity) return;

                // Get clan members AND regular allies (but not merge allies) using the new API
                var authorizedPlayers = AwakenClans?.Call<Dictionary<string, string>>("GetAlliedClanMembers", baseEntity.OwnerID);

                // Only proceed if there are authorized players
                if (authorizedPlayers != null && authorizedPlayers.Count > 0)
                {
                    // IMPORTANT: We authorize clan members AND regular allies for turrets and cupboards
                    // Merge allies do NOT get access to these entities
                    // This maintains the security boundary that merge allies are for raiding only

                    // Send update to the entity with clan members and regular allies
                    SendClanUpdate(type, authorizedPlayers, entity);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error updating clan auth: {ex.Message}");
            }
        }

        private void SendClanUpdate(UpdateType type, Dictionary<string, string> clanMembers, BaseNetworkable entity)
        {
            try
            {
                // Validate entity
                if (entity?.IsDestroyed != false) return;

                switch (type)
                {
                    case UpdateType.AutoTurret when entity is AutoTurret turret:
                        try
                        {
                            // Remember online state
                            var isOnline = turret.IsOnline();

                            // Take turret offline while updating
                            if (isOnline) turret.SetIsOnline(false);

                            // Clear and update authorized players
                            turret.authorizedPlayers.Clear();
                            UpdateAuthorizedPlayers(turret.authorizedPlayers, clanMembers);

                            // Restore online state
                            if (isOnline) turret.SetIsOnline(true);

                            // Send network update with the new 2025 API method if available
                            try
                            {
                                // Try to use the new thread-safe method first
                                SendNetworkUpdateSafe(turret);
                            }
                            catch (Exception)
                            {
                                // Fall back to the old method
                                turret.SendNetworkUpdate();
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Clans Share] Error updating auto turret: {ex.Message}");
                        }
                        break;

                    case UpdateType.BuildingPriv when entity is BuildingPrivlidge priv:
                        try
                        {
                            // Clear and update authorized players
                            priv.authorizedPlayers.Clear();
                            UpdateAuthorizedPlayers(priv.authorizedPlayers, clanMembers);

                            // Send network update with the new 2025 API method if available
                            try
                            {
                                // Try to use the new thread-safe method first
                                SendNetworkUpdateSafe(priv);
                            }
                            catch (Exception)
                            {
                                // Fall back to the old method
                                priv.SendNetworkUpdate();
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Clans Share] Error updating building privilege: {ex.Message}");
                        }
                        break;

                    case UpdateType.CodeLock when entity is CodeLock codeLock:
                        try
                        {
                            // Clear and update authorized players for codelock
                            codeLock.whitelistPlayers.Clear();
                            UpdateCodeLockPlayers(codeLock.whitelistPlayers, clanMembers);

                            // Send network update
                            try
                            {
                                SendNetworkUpdateSafe(codeLock);
                            }
                            catch (Exception)
                            {
                                codeLock.SendNetworkUpdate();
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Clans Share] Error updating code lock: {ex.Message}");
                        }
                        break;

                    case UpdateType.KeyLock when entity is KeyLock keyLock:
                        try
                        {
                            // Clear and update authorized players for keylock
                            keyLock.keyCode = 0; // Reset key code

                            // For key locks, we'll set the first clan member as the key holder
                            // and add others to the whitelist if the keylock supports it
                            if (clanMembers.Count > 0)
                            {
                                var firstMember = clanMembers.First();
                                if (ulong.TryParse(firstMember.Key, out var firstPlayerId))
                                {
                                    keyLock.keyCode = (int)firstPlayerId; // Use player ID as key code
                                }
                            }

                            // Send network update
                            try
                            {
                                SendNetworkUpdateSafe(keyLock);
                            }
                            catch (Exception)
                            {
                                keyLock.SendNetworkUpdate();
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"[Awaken Clans Share] Error updating key lock: {ex.Message}");
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error in SendClanUpdate: {ex.Message}");
            }
        }

        // Helper method to use the new thread-safe network update method introduced in 2025
        private void SendNetworkUpdateSafe(BaseNetworkable entity)
        {
            // This method uses reflection to call the new method if it exists
            // This approach allows the plugin to work with both old and new Rust versions
            var method = entity.GetType().GetMethod("SendNetworkUpdateSafe");
            if (method != null)
                method.Invoke(entity, null);
            else
                entity.SendNetworkUpdate();
        }

        private void UpdateAuthorizedPlayers(HashSet<PlayerNameID> authorizedPlayers, Dictionary<string, string> clanMembers)
        {
            try
            {
                // Add each clan member to the authorized players list
                foreach (var (id, name) in clanMembers)
                {
                    if (ulong.TryParse(id, out var playerId))
                    {
                        // Create a new PlayerNameID with the player's ID and name
                        var playerNameId = new PlayerNameID { userid = playerId, username = name };

                        // Add to the authorized players list
                        authorizedPlayers.Add(playerNameId);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error updating authorized players: {ex.Message}");
            }
        }

        private void UpdateCodeLockPlayers(List<ulong> whitelistPlayers, Dictionary<string, string> clanMembers)
        {
            try
            {
                // Add each clan member to the codelock whitelist
                foreach (var (id, name) in clanMembers)
                {
                    if (ulong.TryParse(id, out var playerId))
                    {
                        // Add player ID to the whitelist
                        whitelistPlayers.Add(playerId);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error updating codelock players: {ex.Message}");
            }
        }

        // New hook for the May 2025 Rust API to handle entity ownership changes
        private void OnEntityOwnershipChanged(BaseNetworkable entity, ulong oldOwner, ulong newOwner)
        {
            try
            {
                // Remove from old owner's cache
                if (oldOwner.IsSteamId())
                {
                    RemoveEntity(entity);
                }

                // Add to new owner's cache if it's a relevant entity
                if (newOwner.IsSteamId())
                {
                    if (entity is AutoTurret turret)
                        AddEntity(turret);
                    else if (entity is BuildingPrivlidge priv)
                        AddEntity(priv);
                    else if (entity is CodeLock codeLock)
                        AddEntity(codeLock);
                    else if (entity is KeyLock keyLock)
                        AddEntity(keyLock);
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error handling ownership change: {ex.Message}");
            }
        }

        // Hook for handling clan membership changes
        private void OnClanMembershipChanged(ulong playerId)
        {
            try
            {
                // Update all entities owned by this player when their clan membership changes
                UpdatePlayerEntities(playerId);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error handling clan membership change: {ex.Message}");
            }
        }

        // Hook for handling when a player joins a clan
        private void OnPlayerJoinedClan(ulong playerId, string clanName)
        {
            try
            {
                // Update entities when player joins a clan
                UpdatePlayerEntities(playerId);

                // Also update all clan members' entities to include the new member
                UpdateClanEntities(clanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error handling player joined clan: {ex.Message}");
            }
        }

        // Hook for handling when a player leaves a clan
        private void OnPlayerLeftClan(ulong playerId, string clanName)
        {
            try
            {
                // Update entities when player leaves a clan
                UpdatePlayerEntities(playerId);

                // Also update all remaining clan members' entities to remove the departed member
                UpdateClanEntities(clanName);
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error handling player left clan: {ex.Message}");
            }
        }

        // Helper method to update all entities owned by a specific player
        private void UpdatePlayerEntities(ulong playerId)
        {
            try
            {
                if (playerEntities.TryGetValue(playerId, out var cache))
                {
                    // Update auto turrets
                    if (cache.AutoTurrets != null)
                    {
                        foreach (var turret in cache.AutoTurrets.ToList())
                        {
                            if (turret != null && !turret.IsDestroyed)
                                UpdateClanAuth(UpdateType.AutoTurret, turret);
                        }
                    }

                    // Update building privileges
                    if (cache.BuildingPrivlidges != null)
                    {
                        foreach (var priv in cache.BuildingPrivlidges.ToList())
                        {
                            if (priv != null && !priv.IsDestroyed)
                                UpdateClanAuth(UpdateType.BuildingPriv, priv);
                        }
                    }

                    // Update code locks
                    if (cache.CodeLocks != null)
                    {
                        foreach (var codeLock in cache.CodeLocks.ToList())
                        {
                            if (codeLock != null && !codeLock.IsDestroyed)
                                UpdateClanAuth(UpdateType.CodeLock, codeLock);
                        }
                    }

                    // Update key locks
                    if (cache.KeyLocks != null)
                    {
                        foreach (var keyLock in cache.KeyLocks.ToList())
                        {
                            if (keyLock != null && !keyLock.IsDestroyed)
                                UpdateClanAuth(UpdateType.KeyLock, keyLock);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error updating player entities: {ex.Message}");
            }
        }

        // Helper method to update entities for all members of a clan
        private void UpdateClanEntities(string clanName)
        {
            try
            {
                // Get all clan members
                var clanMembers = AwakenClans?.Call<Dictionary<string, string>>("GetClanMemebers", clanName);
                if (clanMembers != null)
                {
                    foreach (var memberId in clanMembers.Keys)
                    {
                        if (ulong.TryParse(memberId, out var playerId))
                        {
                            UpdatePlayerEntities(playerId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[Awaken Clans Share] Error updating clan entities: {ex.Message}");
            }
        }
        #endregion
    }
}
