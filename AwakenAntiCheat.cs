using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Core.Plugins;
using UnityEngine;
using System.Collections;
using Oxide.Core.Libraries;
using System.Net.WebSockets;
using System.Threading;
using System.Threading.Tasks;

namespace Oxide.Plugins
{


    [Info("Awaken AntiCheat", "Skelee", "1.0.0")]
    [Description("Advanced anti-cheat system with enhanced detection algorithms")]
    public class AwakenAntiCheat : RustPlugin
    {
        #region Configuration

        private Configuration config;

        public class Configuration
        {
            [JsonProperty("API Settings")]
            public ApiSettings Api = new ApiSettings();

            [JsonProperty("Detection Settings")]
            public DetectionSettings Detection = new DetectionSettings();

            [JsonProperty("Action Settings")]
            public ActionSettings Actions = new ActionSettings();

            [JsonProperty("Performance Settings")]
            public PerformanceSettings Performance = new PerformanceSettings();

            public class ApiSettings
            {
                [JsonProperty("API Endpoint")]
                public string ApiEndpoint = "https://anti.awakenrust.com/api/v1";

                [JsonProperty("WebSocket Endpoint")]
                public string WebSocketEndpoint = "wss://anti.awakenrust.com/ws";

                [JsonProperty("API Key")]
                public string ApiKey = "demo-api-key-12345";

                [JsonProperty("Server ID")]
                public string ServerId = "demo-server-1";

                [JsonProperty("Enable API Reporting")]
                public bool EnableApiReporting = true;

                [JsonProperty("Enable WebSocket")]
                public bool EnableWebSocket = true;

                [JsonProperty("Report Timeout (seconds)")]
                public int ReportTimeout = 10;

                [JsonProperty("WebSocket Reconnect Interval (seconds)")]
                public int WebSocketReconnectInterval = 30;

                [JsonProperty("Max Queue Size")]
                public int MaxQueueSize = 1000;
            }

            public class DetectionSettings
            {
                [JsonProperty("Enable Flyhack Detection")]
                public bool EnableFlyhack = true;

                [JsonProperty("Enable Jumpshot Detection")]
                public bool EnableJumpshot = true;

                [JsonProperty("Enable Aimbot Detection")]
                public bool EnableAimbot = true;

                [JsonProperty("Enable ESP Detection")]
                public bool EnableESP = true;

                [JsonProperty("Enable Speed Hack Detection")]
                public bool EnableSpeedhack = true;

                [JsonProperty("Enable No-Recoil Detection")]
                public bool EnableNoRecoil = true;

                [JsonProperty("Enable Rapid Fire Detection")]
                public bool EnableRapidFire = true;

                [JsonProperty("Enable Teleport Detection")]
                public bool EnableTeleport = true;

                [JsonProperty("Enable Noclip Detection")]
                public bool EnableNoclip = true;

                [JsonProperty("Noclip Detection Radius")]
                public float NoclipRadius = 0.3f;

                [JsonProperty("Noclip Violation Threshold")]
                public int NoclipThreshold = 3;

                [JsonProperty("Flyhack Sensitivity")]
                public float FlyhackSensitivity = 1.0f;

                [JsonProperty("Jumpshot Sensitivity")]
                public float JumpshotSensitivity = 1.0f;

                [JsonProperty("Aimbot Sensitivity")]
                public float AimbotSensitivity = 1.0f;

                [JsonProperty("Speed Hack Threshold")]
                public float SpeedhackThreshold = 15.0f;

                [JsonProperty("Teleport Distance Threshold")]
                public float TeleportThreshold = 50.0f;
            }

            public class ActionSettings
            {
                [JsonProperty("Enable Auto Kick")]
                public bool EnableAutoKick = false;

                [JsonProperty("Enable Auto Ban")]
                public bool EnableAutoBan = false;

                [JsonProperty("Kick Threshold")]
                public int KickThreshold = 5;

                [JsonProperty("Ban Threshold")]
                public int BanThreshold = 10;

                [JsonProperty("Whitelist Bypass")]
                public bool WhitelistBypass = true;

                [JsonProperty("Admin Notifications")]
                public bool AdminNotifications = true;
            }

            public class PerformanceSettings
            {
                [JsonProperty("Check Interval (seconds)")]
                public float CheckInterval = 0.5f;

                [JsonProperty("Max Checks Per Frame")]
                public int MaxChecksPerFrame = 10;

                [JsonProperty("Enable Debug Logging")]
                public bool EnableDebugLogging = false;
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) throw new Exception();
            }
            catch
            {
                PrintWarning("Creating new configuration file");
                config = new Configuration();
            }
            SaveConfig();
        }

        protected override void SaveConfig() => Config.WriteObject(config);

        #endregion

        #region Data Classes

        private class PlayerData
        {
            public string SteamId { get; set; }
            public string Username { get; set; }
            public Vector3 LastPosition { get; set; }
            public DateTime LastPositionTime { get; set; }
            public List<Vector3> MovementHistory { get; set; } = new List<Vector3>();
            public List<float> AimHistory { get; set; } = new List<float>();
            public int ViolationCount { get; set; }
            public DateTime LastViolation { get; set; }
            public bool IsWhitelisted { get; set; }
            public Dictionary<string, int> ViolationTypes { get; set; } = new Dictionary<string, int>();
            public int NoclipViolations { get; set; } = 0;
        }

        private class ViolationReport
        {
            public string steamId { get; set; }
            public string serverId { get; set; }
            public string violationType { get; set; }
            public string severity { get; set; }
            public int confidence { get; set; }
            public string description { get; set; }
            public Evidence evidence { get; set; }
        }

        private class Evidence
        {
            public Position position { get; set; }
            public Velocity velocity { get; set; }
            public Rotation rotation { get; set; }
            public Position targetPosition { get; set; }
            public float? distance { get; set; }
            public float? accuracy { get; set; }
            public float? reactionTime { get; set; }
            public Dictionary<string, object> metadata { get; set; }
        }

        private class Position
        {
            public float x { get; set; }
            public float y { get; set; }
            public float z { get; set; }
        }

        private class Velocity
        {
            public float x { get; set; }
            public float y { get; set; }
            public float z { get; set; }
        }

        private class Rotation
        {
            public float x { get; set; }
            public float y { get; set; }
        }

        #endregion

        #region WebSocket Classes

        private class WebSocketMessage
        {
            public string type { get; set; }
            public object data { get; set; }
            public string timestamp { get; set; } = DateTime.UtcNow.ToString("O");
            public string serverId { get; set; }
        }

        private class WebSocketManager
        {
            private ClientWebSocket webSocket;
            private CancellationTokenSource cancellationTokenSource;
            private readonly string endpoint;
            private readonly string apiKey;
            private readonly string serverId;
            private readonly Queue<string> messageQueue = new Queue<string>();
            private readonly object queueLock = new object();
            private bool isConnected = false;
            private Timer reconnectTimer;
            private readonly int maxQueueSize;

            public bool IsConnected => isConnected && webSocket?.State == WebSocketState.Open;
            public event Action<string> OnMessageReceived;
            public event Action OnConnected;
            public event Action OnDisconnected;

            public WebSocketManager(string endpoint, string apiKey, string serverId, int maxQueueSize = 1000)
            {
                this.endpoint = endpoint;
                this.apiKey = apiKey;
                this.serverId = serverId;
                this.maxQueueSize = maxQueueSize;
            }

            public async Task ConnectAsync()
            {
                try
                {
                    if (webSocket?.State == WebSocketState.Open) return;

                    cancellationTokenSource?.Cancel();
                    cancellationTokenSource = new CancellationTokenSource();

                    webSocket = new ClientWebSocket();
                    webSocket.Options.SetRequestHeader("Authorization", $"Bearer {apiKey}");
                    webSocket.Options.SetRequestHeader("X-Server-ID", serverId);

                    await webSocket.ConnectAsync(new Uri(endpoint), cancellationTokenSource.Token);
                    isConnected = true;
                    OnConnected?.Invoke();

                    // Start listening for messages
                    _ = Task.Run(ListenForMessages);

                    // Process queued messages
                    await ProcessQueuedMessages();
                }
                catch (Exception ex)
                {
                    Interface.Oxide.LogError($"[Awaken AntiCheat] WebSocket connection failed: {ex.Message}");
                    isConnected = false;
                }
            }

            public async Task DisconnectAsync()
            {
                try
                {
                    isConnected = false;
                    cancellationTokenSource?.Cancel();
                    reconnectTimer?.Destroy();

                    if (webSocket?.State == WebSocketState.Open)
                    {
                        await webSocket.CloseAsync(WebSocketCloseStatus.NormalClosure, "Plugin unloading", CancellationToken.None);
                    }
                }
                catch (Exception ex)
                {
                    Interface.Oxide.LogError($"[Awaken AntiCheat] WebSocket disconnect error: {ex.Message}");
                }
                finally
                {
                    webSocket?.Dispose();
                    cancellationTokenSource?.Dispose();
                }
            }

            public async Task SendMessageAsync(object message)
            {
                var json = JsonConvert.SerializeObject(message);

                if (IsConnected)
                {
                    try
                    {
                        var buffer = Encoding.UTF8.GetBytes(json);
                        await webSocket.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Text, true, cancellationTokenSource.Token);
                    }
                    catch (Exception ex)
                    {
                        Interface.Oxide.LogError($"[Awaken AntiCheat] WebSocket send error: {ex.Message}");
                        QueueMessage(json);
                        await TryReconnect();
                    }
                }
                else
                {
                    QueueMessage(json);
                }
            }

            private void QueueMessage(string message)
            {
                lock (queueLock)
                {
                    if (messageQueue.Count >= maxQueueSize)
                    {
                        messageQueue.Dequeue(); // Remove oldest message
                    }
                    messageQueue.Enqueue(message);
                }
            }

            private async Task ProcessQueuedMessages()
            {
                lock (queueLock)
                {
                    while (messageQueue.Count > 0 && IsConnected)
                    {
                        var message = messageQueue.Dequeue();
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                var buffer = Encoding.UTF8.GetBytes(message);
                                await webSocket.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Text, true, cancellationTokenSource.Token);
                            }
                            catch (Exception ex)
                            {
                                Interface.Oxide.LogError($"[Awaken AntiCheat] Error sending queued message: {ex.Message}");
                            }
                        });
                    }
                }
            }

            private async Task ListenForMessages()
            {
                var buffer = new byte[4096];

                try
                {
                    while (IsConnected && !cancellationTokenSource.Token.IsCancellationRequested)
                    {
                        var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(buffer), cancellationTokenSource.Token);

                        if (result.MessageType == WebSocketMessageType.Text)
                        {
                            var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                            OnMessageReceived?.Invoke(message);
                        }
                        else if (result.MessageType == WebSocketMessageType.Close)
                        {
                            break;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Interface.Oxide.LogError($"[Awaken AntiCheat] WebSocket listen error: {ex.Message}");
                }
                finally
                {
                    isConnected = false;
                    OnDisconnected?.Invoke();
                    await TryReconnect();
                }
            }

            private async Task TryReconnect()
            {
                if (cancellationTokenSource.Token.IsCancellationRequested) return;

                await Task.Delay(5000); // Wait 5 seconds before reconnecting
                await ConnectAsync();
            }
        }

        #endregion

        #region Fields

        private Dictionary<ulong, PlayerData> playerData = new Dictionary<ulong, PlayerData>();
        private Timer checkTimer;
        private Queue<BasePlayer> playersToCheck = new Queue<BasePlayer>();
        private Timer heartbeatTimer;
        private WebSocketManager webSocketManager;
        private Queue<ViolationReport> violationQueue = new Queue<ViolationReport>();
        private Timer violationProcessTimer;

        #endregion

        #region Oxide Hooks

        void Init()
        {
            permission.RegisterPermission("awakenanticheat.admin", this);
            permission.RegisterPermission("awakenanticheat.bypass", this);

            PrintWarning($"<color=#7000fd>Awaken AntiCheat</color> v{Version} loaded successfully!");
            PrintWarning($"API Endpoint: {config.Api.ApiEndpoint}");
            PrintWarning($"WebSocket Endpoint: {config.Api.WebSocketEndpoint}");
            PrintWarning($"Server ID: {config.Api.ServerId}");

            // Initialize WebSocket manager
            if (config.Api.EnableWebSocket)
            {
                InitializeWebSocket();
            }
        }

        void OnServerInitialized()
        {
            StartChecking();

            // Start heartbeat timer for server status
            if (config.Api.EnableApiReporting)
            {
                heartbeatTimer = timer.Every(30f, SendHeartbeat);
            }

            // Start violation processing timer
            violationProcessTimer = timer.Every(1f, ProcessViolationQueue);

            // Connect WebSocket
            if (config.Api.EnableWebSocket && webSocketManager != null)
            {
                _ = Task.Run(async () => await webSocketManager.ConnectAsync());
            }

            // Register existing players
            foreach (var player in BasePlayer.activePlayerList)
            {
                OnPlayerConnected(player);
            }

            PrintWarning($"<color=#7000fd>Awaken AntiCheat</color> initialized with {BasePlayer.activePlayerList.Count} players");
        }

        void Unload()
        {
            checkTimer?.Destroy();
            heartbeatTimer?.Destroy();
            violationProcessTimer?.Destroy();

            // Disconnect WebSocket
            if (webSocketManager != null)
            {
                _ = Task.Run(async () => await webSocketManager.DisconnectAsync());
            }

            PrintWarning("<color=#7000fd>Awaken AntiCheat</color> unloaded");
        }

        void OnPlayerConnected(BasePlayer player)
        {
            if (player == null || !player.userID.IsSteamId()) return;

            var data = new PlayerData
            {
                SteamId = player.UserIDString,
                Username = player.displayName,
                LastPosition = player.transform.position,
                LastPositionTime = DateTime.UtcNow,
                IsWhitelisted = permission.UserHasPermission(player.UserIDString, "awakenanticheat.bypass")
            };

            playerData[player.userID] = data;

            if (config.Performance.EnableDebugLogging)
                PrintWarning($"Player {player.displayName} ({player.UserIDString}) connected");
        }

        void OnPlayerDisconnected(BasePlayer player)
        {
            if (player == null) return;
            playerData.Remove(player.userID);
        }

        #endregion

        #region WebSocket Methods

        private void InitializeWebSocket()
        {
            try
            {
                webSocketManager = new WebSocketManager(
                    config.Api.WebSocketEndpoint,
                    config.Api.ApiKey,
                    config.Api.ServerId,
                    config.Api.MaxQueueSize
                );

                // Subscribe to WebSocket events
                webSocketManager.OnConnected += OnWebSocketConnected;
                webSocketManager.OnDisconnected += OnWebSocketDisconnected;
                webSocketManager.OnMessageReceived += OnWebSocketMessageReceived;

                PrintWarning("<color=#7000fd>Awaken AntiCheat</color> WebSocket manager initialized");
            }
            catch (Exception ex)
            {
                PrintError($"Failed to initialize WebSocket manager: {ex.Message}");
            }
        }

        private void OnWebSocketConnected()
        {
            PrintWarning("<color=#7000fd>Awaken AntiCheat</color> <color=#00ff00>WebSocket connected</color>");

            // Send server info on connection
            _ = Task.Run(async () =>
            {
                var serverInfo = new WebSocketMessage
                {
                    type = "server_info",
                    serverId = config.Api.ServerId,
                    data = new
                    {
                        serverName = ConVar.Server.hostname,
                        maxPlayers = ConVar.Server.maxplayers,
                        currentPlayers = BasePlayer.activePlayerList.Count,
                        map = ConVar.Server.level,
                        seed = ConVar.Server.seed,
                        worldSize = ConVar.Server.worldsize,
                        timestamp = DateTime.UtcNow.ToString("O")
                    }
                };

                await webSocketManager.SendMessageAsync(serverInfo);
            });
        }

        private void OnWebSocketDisconnected()
        {
            PrintWarning("<color=#7000fd>Awaken AntiCheat</color> <color=#ff6b6b>WebSocket disconnected</color>");
        }

        private void OnWebSocketMessageReceived(string message)
        {
            try
            {
                var data = JsonConvert.DeserializeObject<WebSocketMessage>(message);
                HandleWebSocketMessage(data);
            }
            catch (Exception ex)
            {
                PrintError($"Error processing WebSocket message: {ex.Message}");
            }
        }

        private void HandleWebSocketMessage(WebSocketMessage message)
        {
            switch (message.type)
            {
                case "ping":
                    _ = Task.Run(async () =>
                    {
                        var pong = new WebSocketMessage
                        {
                            type = "pong",
                            serverId = config.Api.ServerId,
                            data = new { timestamp = DateTime.UtcNow.ToString("O") }
                        };
                        await webSocketManager.SendMessageAsync(pong);
                    });
                    break;

                case "config_update":
                    HandleConfigUpdate(message.data);
                    break;

                case "player_action":
                    HandlePlayerAction(message.data);
                    break;

                default:
                    if (config.Performance.EnableDebugLogging)
                        PrintWarning($"Unknown WebSocket message type: {message.type}");
                    break;
            }
        }

        private void HandleConfigUpdate(object data)
        {
            try
            {
                // Handle real-time config updates from the dashboard
                var configData = JsonConvert.DeserializeObject<Dictionary<string, object>>(data.ToString());

                if (configData.ContainsKey("detection_settings"))
                {
                    // Update detection settings without restart
                    PrintWarning("<color=#7000fd>Awaken AntiCheat</color> Received config update from dashboard");
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error handling config update: {ex.Message}");
            }
        }

        private void HandlePlayerAction(object data)
        {
            try
            {
                var actionData = JsonConvert.DeserializeObject<Dictionary<string, object>>(data.ToString());

                if (actionData.ContainsKey("steamId") && actionData.ContainsKey("action"))
                {
                    var steamId = actionData["steamId"].ToString();
                    var action = actionData["action"].ToString();
                    var player = BasePlayer.Find(steamId);

                    if (player != null)
                    {
                        switch (action)
                        {
                            case "kick":
                                player.Kick("Kicked by AntiCheat system");
                                break;
                            case "ban":
                                player.IPlayer.Ban("Banned by AntiCheat system");
                                break;
                            case "teleport_spawn":
                                player.Teleport(ServerMgr.FindSpawnPoint().pos);
                                break;
                        }

                        PrintWarning($"<color=#7000fd>Awaken AntiCheat</color> Executed action '{action}' on player {player.displayName}");
                    }
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error handling player action: {ex.Message}");
            }
        }

        private void ProcessViolationQueue()
        {
            if (violationQueue.Count == 0 || !webSocketManager?.IsConnected == true) return;

            var batchSize = Math.Min(10, violationQueue.Count); // Process up to 10 violations per second
            var violations = new List<ViolationReport>();

            for (int i = 0; i < batchSize; i++)
            {
                if (violationQueue.Count > 0)
                {
                    violations.Add(violationQueue.Dequeue());
                }
            }

            if (violations.Count > 0)
            {
                _ = Task.Run(async () =>
                {
                    var batchMessage = new WebSocketMessage
                    {
                        type = "violation_batch",
                        serverId = config.Api.ServerId,
                        data = violations
                    };

                    await webSocketManager.SendMessageAsync(batchMessage);
                });
            }
        }

        #endregion

        #region Detection Methods

        private void StartChecking()
        {
            checkTimer = timer.Every(config.Performance.CheckInterval, () =>
            {
                if (BasePlayer.activePlayerList.Count == 0) return;

                // Add players to check queue
                foreach (var player in BasePlayer.activePlayerList)
                {
                    if (player != null && player.IsConnected)
                        playersToCheck.Enqueue(player);
                }

                // Process limited number of players per frame
                int checksThisFrame = 0;
                while (playersToCheck.Count > 0 && checksThisFrame < config.Performance.MaxChecksPerFrame)
                {
                    var player = playersToCheck.Dequeue();
                    if (player != null && player.IsConnected)
                    {
                        CheckPlayer(player);
                        checksThisFrame++;
                    }
                }
            });
        }

        private void CheckPlayer(BasePlayer player)
        {
            if (!playerData.ContainsKey(player.userID)) return;

            var data = playerData[player.userID];
            if (data.IsWhitelisted && config.Actions.WhitelistBypass) return;

            // Update player data
            var currentPos = player.transform.position;
            var currentTime = DateTime.UtcNow;
            var deltaTime = (float)(currentTime - data.LastPositionTime).TotalSeconds;

            if (deltaTime > 0.1f) // Only check if enough time has passed
            {
                // Movement-based checks
                if (config.Detection.EnableFlyhack)
                    CheckFlyhack(player, data, currentPos, deltaTime);

                if (config.Detection.EnableSpeedhack)
                    CheckSpeedhack(player, data, currentPos, deltaTime);

                if (config.Detection.EnableTeleport)
                    CheckTeleport(player, data, currentPos, deltaTime);

                if (config.Detection.EnableNoclip)
                    CheckNoclip(player, data, currentPos);

                // Update movement history
                data.MovementHistory.Add(currentPos);
                if (data.MovementHistory.Count > 50)
                    data.MovementHistory.RemoveAt(0);

                data.LastPosition = currentPos;
                data.LastPositionTime = currentTime;
            }

            // Combat-based checks
            if (config.Detection.EnableJumpshot)
                CheckJumpshot(player, data);

            if (config.Detection.EnableAimbot)
                CheckAimbot(player, data);

            if (config.Detection.EnableESP)
                CheckESP(player, data);
        }

        private void CheckFlyhack(BasePlayer player, PlayerData data, Vector3 currentPos, float deltaTime)
        {
            if (player.IsFlying || player.IsAdmin) return;

            var distance = Vector3.Distance(data.LastPosition, currentPos);
            var speed = distance / deltaTime;
            var verticalSpeed = Mathf.Abs(currentPos.y - data.LastPosition.y) / deltaTime;

            // Check for impossible vertical movement
            if (verticalSpeed > 20f * config.Detection.FlyhackSensitivity && !player.IsOnGround())
            {
                var confidence = Mathf.Clamp((verticalSpeed / 20f) * 100f, 50f, 100f);
                ReportViolation(player, "FLYHACK", "HIGH", (int)confidence,
                    $"Impossible vertical movement: {verticalSpeed:F2} m/s",
                    currentPos, player.GetParentVelocity());
            }

            // Check for sustained flight
            if (data.MovementHistory.Count >= 10)
            {
                var avgHeight = data.MovementHistory.Skip(data.MovementHistory.Count - 10).Average(p => p.y);
                if (avgHeight > currentPos.y - 2f && verticalSpeed < 1f && !player.IsOnGround())
                {
                    ReportViolation(player, "FLYHACK", "MEDIUM", 75,
                        "Sustained flight detected", currentPos, player.GetParentVelocity());
                }
            }
        }

        private void CheckSpeedhack(BasePlayer player, PlayerData data, Vector3 currentPos, float deltaTime)
        {
            var distance = Vector3.Distance(data.LastPosition, currentPos);
            var speed = distance / deltaTime;

            // Ignore if player is in vehicle or flying
            if (player.GetMountedVehicle() != null || player.IsFlying) return;

            var maxSpeed = config.Detection.SpeedhackThreshold;
            if (player.IsRunning()) maxSpeed *= 1.5f;
            if (player.IsSwimming()) maxSpeed *= 0.7f;

            if (speed > maxSpeed)
            {
                var confidence = Mathf.Clamp((speed / maxSpeed) * 100f, 60f, 100f);
                var severity = speed > maxSpeed * 2f ? "CRITICAL" : "HIGH";

                ReportViolation(player, "SPEEDHACK", severity, (int)confidence,
                    $"Excessive movement speed: {speed:F2} m/s (max: {maxSpeed:F2})",
                    currentPos, player.GetParentVelocity());
            }
        }

        private void CheckTeleport(BasePlayer player, PlayerData data, Vector3 currentPos, float deltaTime)
        {
            var distance = Vector3.Distance(data.LastPosition, currentPos);

            // Ignore if player is in vehicle or just spawned
            if (player.GetMountedVehicle() != null || deltaTime > 5f) return;

            if (distance > config.Detection.TeleportThreshold)
            {
                var confidence = Mathf.Clamp((distance / config.Detection.TeleportThreshold) * 100f, 70f, 100f);

                ReportViolation(player, "TELEPORT", "CRITICAL", (int)confidence,
                    $"Teleportation detected: {distance:F2}m in {deltaTime:F2}s",
                    currentPos, Vector3.zero, data.LastPosition);
            }
        }

        private void CheckNoclip(BasePlayer player, PlayerData data, Vector3 currentPos)
        {
            // Skip if player is admin, flying, sleeping, or dead
            if (player.IsAdmin || player.IsFlying || player.IsSleeping() || player.IsDead())
                return;

            // Skip if player is in a vehicle or on a lift
            if (player.GetMountedVehicle() != null || player.GetParentEntity() != null)
                return;

            // Skip if player is swimming
            if (player.IsSwimming())
                return;

            // Use configurable radius and check player's center position
            var playerCenter = currentPos + Vector3.up * 0.9f; // Player center height
            var hits = Physics.OverlapSphere(playerCenter, config.Detection.NoclipRadius, LayerMask.GetMask("Terrain", "Construction", "World"));

            if (hits.Length > 0)
            {
                // Additional validation - check if player is actually stuck inside
                bool isInsideSolid = false;

                foreach (var hit in hits)
                {
                    // Skip if it's a deployable that players can walk through
                    if (hit.GetComponent<Deployable>() != null)
                        continue;

                    // Skip if it's a door or window
                    if (hit.GetComponent<Door>() != null)
                        continue;

                    // Check if the collider completely contains the player
                    var bounds = hit.bounds;
                    if (bounds.Contains(playerCenter))
                    {
                        isInsideSolid = true;
                        break;
                    }
                }

                // Only report if player is actually inside a solid object
                if (isInsideSolid)
                {
                    // Increment violation counter to prevent false positives
                    data.NoclipViolations++;

                    // Only report after multiple detections
                    if (data.NoclipViolations >= config.Detection.NoclipThreshold)
                    {
                        ReportViolation(player, "NOCLIP", "HIGH", 90,
                            $"Player detected inside solid objects (violations: {data.NoclipViolations})",
                            currentPos, player.GetParentVelocity());

                        data.NoclipViolations = 0; // Reset counter after reporting
                    }
                }
            }
            else
            {
                // Reset violation counter if player is not inside anything
                data.NoclipViolations = 0;
            }
        }

        private void CheckJumpshot(BasePlayer player, PlayerData data)
        {
            if (!player.IsOnGround() && player.GetHeldEntity() is BaseProjectile weapon)
            {
                // Check if player is actively firing while jumping
                if (player.serverInput.IsDown(BUTTON.FIRE_PRIMARY) && weapon.primaryMagazine.contents > 0)
                {
                    ReportViolation(player, "JUMPSHOT", "MEDIUM", 70,
                        "Shot fired while airborne", player.transform.position, player.GetParentVelocity());
                }
            }
        }

        private void CheckAimbot(BasePlayer player, PlayerData data)
        {
            // This is a simplified aimbot detection
            var heldEntity = player.GetHeldEntity();
            if (heldEntity is BaseProjectile weapon)
            {
                // Track rapid aim changes
                var currentAim = player.eyes.rotation.eulerAngles.y;
                data.AimHistory.Add(currentAim);

                if (data.AimHistory.Count > 10)
                {
                    data.AimHistory.RemoveAt(0);

                    // Check for unnatural aim patterns
                    var aimVariance = CalculateVariance(data.AimHistory);
                    if (aimVariance < 0.1f && data.AimHistory.Count == 10)
                    {
                        ReportViolation(player, "AIMBOT", "MEDIUM", 65,
                            "Suspicious aim consistency detected",
                            player.transform.position, player.GetParentVelocity());
                    }
                }
            }
        }

        private void CheckESP(BasePlayer player, PlayerData data)
        {
            // Simplified ESP detection - check for impossible target acquisition
            var nearbyPlayers = BasePlayer.activePlayerList
                .Where(p => p != player && Vector3.Distance(p.transform.position, player.transform.position) < 100f)
                .ToList();

            foreach (var target in nearbyPlayers)
            {
                // Check if player can see target using raycast
                if (!CanPlayerSeeTarget(player, target) && IsPlayerLookingAt(player, target))
                {
                    ReportViolation(player, "ESP", "MEDIUM", 60,
                        "Tracking player through walls",
                        player.transform.position, player.GetParentVelocity(), target.transform.position);
                }
            }
        }

        #endregion

        #region Utility Methods

        private float CalculateVariance(List<float> values)
        {
            if (values.Count < 2) return 0f;

            var mean = values.Average();
            var variance = values.Sum(x => Mathf.Pow(x - mean, 2)) / values.Count;
            return variance;
        }

        private bool IsPlayerLookingAt(BasePlayer player, BasePlayer target)
        {
            var direction = (target.transform.position - player.eyes.position).normalized;
            var dot = Vector3.Dot(player.eyes.BodyForward(), direction);
            return dot > 0.8f; // Player is looking roughly in target's direction
        }

        private bool CanPlayerSeeTarget(BasePlayer player, BasePlayer target)
        {
            var startPos = player.eyes.position;
            var endPos = target.eyes.position;
            var direction = (endPos - startPos).normalized;
            var distance = Vector3.Distance(startPos, endPos);

            // Perform raycast to check for obstacles
            RaycastHit hit;
            if (Physics.Raycast(startPos, direction, out hit, distance, LayerMask.GetMask("Terrain", "Construction", "Deployed")))
            {
                // If we hit something before reaching the target, player can't see them
                return hit.distance >= distance - 0.1f;
            }

            return true; // No obstacles found
        }

        private void ReportViolation(BasePlayer player, string violationType, string severity, int confidence,
            string description, Vector3 position, Vector3 velocity, Vector3? targetPosition = null)
        {
            if (!playerData.ContainsKey(player.userID)) return;

            var data = playerData[player.userID];
            data.ViolationCount++;
            data.LastViolation = DateTime.UtcNow;

            if (!data.ViolationTypes.ContainsKey(violationType))
                data.ViolationTypes[violationType] = 0;
            data.ViolationTypes[violationType]++;

            // Create violation report
            var report = new ViolationReport
            {
                steamId = player.UserIDString,
                serverId = config.Api.ServerId,
                violationType = violationType,
                severity = severity,
                confidence = confidence,
                description = description,
                evidence = new Evidence
                {
                    position = new Position { x = position.x, y = position.y, z = position.z },
                    velocity = new Velocity { x = velocity.x, y = velocity.y, z = velocity.z },
                    rotation = new Rotation { x = player.eyes.rotation.eulerAngles.x, y = player.eyes.rotation.eulerAngles.y },
                    targetPosition = targetPosition.HasValue ?
                        new Position { x = targetPosition.Value.x, y = targetPosition.Value.y, z = targetPosition.Value.z } : null,
                    metadata = new Dictionary<string, object>
                    {
                        ["playerName"] = player.displayName,
                        ["timestamp"] = DateTime.UtcNow.ToString("O"),
                        ["isOnGround"] = player.IsOnGround(),
                        ["isFlying"] = player.IsFlying,
                        ["health"] = player.health
                    }
                }
            };

            // Queue for WebSocket (real-time)
            if (config.Api.EnableWebSocket && webSocketManager?.IsConnected == true)
            {
                violationQueue.Enqueue(report);
            }

            // Send to API (backup/storage)
            if (config.Api.EnableApiReporting)
            {
                SendViolationToAPI(report);
            }

            // Real-time WebSocket notification for immediate violations
            if (config.Api.EnableWebSocket && (severity == "CRITICAL" || severity == "HIGH"))
            {
                _ = Task.Run(async () =>
                {
                    var immediateAlert = new WebSocketMessage
                    {
                        type = "immediate_violation",
                        serverId = config.Api.ServerId,
                        data = new
                        {
                            violation = report,
                            playerInfo = new
                            {
                                steamId = player.UserIDString,
                                name = player.displayName,
                                currentPosition = new { x = position.x, y = position.y, z = position.z },
                                isOnline = player.IsConnected,
                                ping = 0 // Ping not available in current API
                            }
                        }
                    };

                    await webSocketManager?.SendMessageAsync(immediateAlert);
                });
            }

            // Admin notifications
            if (config.Actions.AdminNotifications)
            {
                NotifyAdmins(player, violationType, severity, description);
            }

            // Auto actions
            HandleAutoActions(player, data);

            // Log violation with color coding
            var colorCode = severity switch
            {
                "CRITICAL" => "#ff0000",
                "HIGH" => "#ff6b6b",
                "MEDIUM" => "#ffa500",
                "LOW" => "#ffff00",
                _ => "#ffffff"
            };

            PrintWarning($"<color={colorCode}>[{severity} VIOLATION]</color> {player.displayName} ({player.UserIDString}): {violationType} - {description}");
        }

        private void SendViolationToAPI(ViolationReport report)
        {
            var json = JsonConvert.SerializeObject(report);
            var headers = new Dictionary<string, string>
            {
                ["Content-Type"] = "application/json",
                ["X-API-Key"] = config.Api.ApiKey
            };

            webrequest.Enqueue($"{config.Api.ApiEndpoint}/violations/report", json, (code, response) =>
            {
                if (code != 200)
                {
                    PrintError($"Failed to send violation report: {code} - {response}");
                }
                else if (config.Performance.EnableDebugLogging)
                {
                    PrintWarning("Violation report sent successfully");
                }
            }, this, RequestMethod.POST, headers, config.Api.ReportTimeout * 1000f);
        }

        private void NotifyAdmins(BasePlayer player, string violationType, string severity, string description)
        {
            var message = $"[AntiCheat] {player.displayName}: {violationType} ({severity}) - {description}";

            foreach (var admin in BasePlayer.activePlayerList.Where(p => p.IsAdmin))
            {
                admin.ChatMessage(message);
            }
        }

        private void HandleAutoActions(BasePlayer player, PlayerData data)
        {
            if (config.Actions.EnableAutoKick && data.ViolationCount >= config.Actions.KickThreshold)
            {
                player.Kick($"Kicked by AntiCheat: {data.ViolationCount} violations detected");
                PrintWarning($"Auto-kicked {player.displayName} for {data.ViolationCount} violations");
            }
            else if (config.Actions.EnableAutoBan && data.ViolationCount >= config.Actions.BanThreshold)
            {
                // Use server console command to ban player
                Server.Command($"banid {player.UserIDString} \"{player.displayName}\" \"Banned by AntiCheat: {data.ViolationCount} violations detected\"");
                player.Kick($"Banned by AntiCheat: {data.ViolationCount} violations detected");
                PrintWarning($"Auto-banned {player.displayName} for {data.ViolationCount} violations");
            }
        }

        #endregion

        #region Commands

        [ChatCommand("ac")]
        private void AntiCheatCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, "awakenanticheat.admin"))
            {
                player.ChatMessage("You don't have permission to use this command.");
                return;
            }

            if (args.Length == 0)
            {
                player.ChatMessage("AntiCheat Commands:");
                player.ChatMessage("/ac stats - View detection statistics");
                player.ChatMessage("/ac player <name> - View player violations");
                player.ChatMessage("/ac noclip <radius/threshold> <value> - Adjust noclip settings");
                player.ChatMessage("/ac reload - Reload configuration");
                return;
            }

            switch (args[0].ToLower())
            {
                case "stats":
                    ShowStats(player);
                    break;
                case "player":
                    if (args.Length > 1)
                        ShowPlayerStats(player, args[1]);
                    else
                        player.ChatMessage("Usage: /ac player <name>");
                    break;
                case "noclip":
                    HandleNoclipCommand(player, args);
                    break;
                case "reload":
                    LoadConfig();
                    player.ChatMessage("Configuration reloaded.");
                    break;
                default:
                    player.ChatMessage("Unknown command. Use /ac for help.");
                    break;
            }
        }

        private void HandleNoclipCommand(BasePlayer player, string[] args)
        {
            if (args.Length < 3)
            {
                player.ChatMessage("Noclip Settings:");
                player.ChatMessage($"Detection Enabled: {config.Detection.EnableNoclip}");
                player.ChatMessage($"Detection Radius: {config.Detection.NoclipRadius}");
                player.ChatMessage($"Violation Threshold: {config.Detection.NoclipThreshold}");
                player.ChatMessage("Usage: /ac noclip <radius/threshold> <value>");
                return;
            }

            var setting = args[1].ToLower();
            if (!float.TryParse(args[2], out float value))
            {
                player.ChatMessage("Invalid value. Please enter a number.");
                return;
            }

            switch (setting)
            {
                case "radius":
                    if (value < 0.1f || value > 1.0f)
                    {
                        player.ChatMessage("Radius must be between 0.1 and 1.0");
                        return;
                    }
                    config.Detection.NoclipRadius = value;
                    player.ChatMessage($"Noclip detection radius set to {value}");
                    break;

                case "threshold":
                    if (value < 1 || value > 10)
                    {
                        player.ChatMessage("Threshold must be between 1 and 10");
                        return;
                    }
                    config.Detection.NoclipThreshold = (int)value;
                    player.ChatMessage($"Noclip violation threshold set to {(int)value}");
                    break;

                default:
                    player.ChatMessage("Unknown setting. Use 'radius' or 'threshold'");
                    return;
            }

            SaveConfig();
            player.ChatMessage("Configuration saved.");
        }

        private void ShowStats(BasePlayer admin)
        {
            var totalPlayers = playerData.Count;
            var totalViolations = playerData.Values.Sum(p => p.ViolationCount);
            var recentViolations = playerData.Values.Count(p =>
                (DateTime.UtcNow - p.LastViolation).TotalMinutes < 60);

            admin.ChatMessage($"=== AntiCheat Statistics ===");
            admin.ChatMessage($"Players Monitored: {totalPlayers}");
            admin.ChatMessage($"Total Violations: {totalViolations}");
            admin.ChatMessage($"Recent Violations (1h): {recentViolations}");
        }

        private void ShowPlayerStats(BasePlayer admin, string playerName)
        {
            var targetPlayer = BasePlayer.activePlayerList.FirstOrDefault(p =>
                p.displayName.ToLower().Contains(playerName.ToLower()));

            if (targetPlayer == null)
            {
                admin.ChatMessage("Player not found.");
                return;
            }

            if (!playerData.ContainsKey(targetPlayer.userID))
            {
                admin.ChatMessage("No data available for this player.");
                return;
            }

            var data = playerData[targetPlayer.userID];
            admin.ChatMessage($"=== {targetPlayer.displayName} Stats ===");
            admin.ChatMessage($"Total Violations: {data.ViolationCount}");
            admin.ChatMessage($"Whitelisted: {data.IsWhitelisted}");

            if (data.ViolationTypes.Any())
            {
                admin.ChatMessage("Violation Types:");
                foreach (var kvp in data.ViolationTypes)
                {
                    admin.ChatMessage($"  {kvp.Key}: {kvp.Value}");
                }
            }
        }

        #endregion

        #region Server Communication

        private void SendHeartbeat()
        {
            var heartbeatData = new
            {
                serverId = config.Api.ServerId,
                serverName = ConVar.Server.hostname,
                players = BasePlayer.activePlayerList.Count,
                maxPlayers = ConVar.Server.maxplayers,
                violations = playerData.Values.Sum(p => p.ViolationCount),
                recentViolations = playerData.Values.Count(p => (DateTime.UtcNow - p.LastViolation).TotalMinutes < 60),
                uptime = UnityEngine.Time.realtimeSinceStartup,
                timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                performance = new
                {
                    fps = Performance.current.frameRate,
                    memoryUsage = GC.GetTotalMemory(false),
                    playersBeingChecked = playersToCheck.Count,
                    queuedViolations = violationQueue.Count
                }
            };

            // Send via WebSocket (real-time)
            if (config.Api.EnableWebSocket && webSocketManager?.IsConnected == true)
            {
                _ = Task.Run(async () =>
                {
                    var wsMessage = new WebSocketMessage
                    {
                        type = "heartbeat",
                        serverId = config.Api.ServerId,
                        data = heartbeatData
                    };

                    await webSocketManager.SendMessageAsync(wsMessage);
                });
            }

            // Send via HTTP API (backup)
            if (config.Api.EnableApiReporting)
            {
                var json = JsonConvert.SerializeObject(heartbeatData);
                var headers = new Dictionary<string, string>
                {
                    ["Content-Type"] = "application/json",
                    ["X-API-Key"] = config.Api.ApiKey
                };

                webrequest.Enqueue($"{config.Api.ApiEndpoint}/servers/heartbeat", json, (code, response) =>
                {
                    if (code != 200 && config.Performance.EnableDebugLogging)
                    {
                        PrintError($"Failed to send heartbeat: {code} - {response}");
                    }
                }, this, RequestMethod.POST, headers, config.Api.ReportTimeout * 1000f);
            }
        }

        #endregion
    }
}
