using Facepunch;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Bag Manager", "Skelee", "1.1.0")]
    [Description("Prevents players from placing sleeping bags too close to each other.")]

    public class AwakenBagManager : CovalencePlugin
    {
        #region Defines
        private readonly Dictionary<string, List<Vector3>> playersBagData = new Dictionary<string, List<Vector3>>();
        #endregion

        #region Hooks
        private void Loaded() => RefreshBagData();

        private object? CanBuild(Planner? planner, Construction prefab, Construction.Target target)
        {
            if (planner?.GetModDeployable()?.entityPrefab.Get()?.GetComponent<SleepingBag>() != null &&
                planner.GetOwnerPlayer() is { } player &&
                playersBagData.TryGetValue(player.UserIDString, out var bags) &&
                bags.Any(bag => player.Distance(bag) <= 75f))
            {
                player.ChatMessage($"<color=#C63749>WARNING</color> <color=#5B5E5D>»</color> You cannot place a bag here because you have another bag within <color=#ABAFAE>75</color>m.");
                return true;
            }
            return null;
        }

        private void OnEntityBuilt(Planner? plan, GameObject? go)
        {
            if (go?.GetComponent<SleepingBag>() is { } bag && plan?.GetOwnerPlayer() is { } player)
            {
                if (!playersBagData.TryGetValue(player.UserIDString, out var list))
                {
                    list = new List<Vector3>();
                    playersBagData[player.UserIDString] = list;
                }
                list.Add(bag.transform.position);
            }
        }

        private void OnEntityKill(SleepingBag? bag) => RemoveBagPosition(bag?.OwnerID.ToString(), bag?.transform.position);

        private object? CanPickupEntity(BasePlayer? player, SleepingBag? bag)
        {
            if (bag != null && player != null && bag.OwnerID == player.userID)
            {
                RemoveBagPosition(bag.OwnerID.ToString(), bag.transform.position);
                return null;
            }
            return null;
        }

        private object? CanAssignBed(BasePlayer? player, SleepingBag? bag, ulong targetPlayerId)
        {
            if (bag != null)
            {
                RemoveBagPosition(bag.OwnerID.ToString(), bag.transform.position);
                return null;
            }
            return null;
        }
        #endregion

        #region Functions
        private void RefreshBagData()
        {
            playersBagData.Clear();
            foreach (var bag in BaseNetworkable.serverEntities.OfType<SleepingBag>())
            {
                string ownerId = bag.OwnerID.ToString();
                if (!playersBagData.TryGetValue(ownerId, out var list))
                {
                    list = new List<Vector3>();
                    playersBagData[ownerId] = list;
                }
                list.Add(bag.transform.position);
            }
        }

        private void RemoveBagPosition(string? ownerId, Vector3? position)
        {
            if (ownerId != null && position.HasValue && playersBagData.TryGetValue(ownerId, out var list))
                list.Remove(position.Value);
        }
        #endregion
    }
}










