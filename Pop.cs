using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using Oxide.Core;
using Oxide.Core.Libraries;
using Oxide.Core.Plugins;
using Oxide.Core.Libraries.Covalence;
using Rust;

//  ____  _       _ _       
// / ___|(_) __ _(_) | ___  
// \___ \| |/ _` | | |/ _ \ 
//  ___) | | (_| | | | (_) |
// |____/|_|\__, |_|_|\___/ 
//          |___/             
// ☽✧✵✧✵✧✵✧✵✧✵✧✵✧☾
// ✦    RUST PLUGINS   ✦
// ✦     Sigilo.dev    ✦
// ☽✧✵✧✵✧✵✧✵✧✵✧✵✧☾

namespace Oxide.Plugins
{
    [Info("Pop", "Sigilo", "1.8.0")]
    [Description("Displays the number of connected, connecting, queued players and sleepers.")]

    public class Pop : CovalencePlugin
    {
        private string command;
        private string broadcastCommand;
        private string onlineColor;
        private string joiningColor;
        private string queuedColor;
        private string sleepersColor;
        private int broadcastDelay;
        private DateTime lastBroadcast;
        private bool enablePersonalCommand;
        private bool enableGlobalCommand;
        private ulong customIconSteamId;
        private bool showOnline;
        private bool showJoining;
        private bool showQueued;
        private bool showSleepers;

        private void Init()
        {
            permission.RegisterPermission("pop.use", this);
            LoadConfig();
            
            if (enablePersonalCommand && !string.IsNullOrEmpty(command))
            {
                AddCovalenceCommand(command, "PopCommand");
            }
            if (enableGlobalCommand && !string.IsNullOrEmpty(broadcastCommand))
            {
                AddCovalenceCommand(broadcastCommand, "BroadcastPopCommand");
            }
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                command = Config.Get<string>("Command") ?? "pop";
                broadcastCommand = Config.Get<string>("BroadcastCommand") ?? "!pop";
                onlineColor = Config.Get<string>("OnlineColor") ?? "#ff686b";
                joiningColor = Config.Get<string>("JoiningColor") ?? "#ff686b";
                queuedColor = Config.Get<string>("QueuedColor") ?? "#ff686b";
                sleepersColor = Config.Get<string>("SleepersColor") ?? "#ff686b";
                
                broadcastDelay = Config["BroadcastDelay"] != null ? 
                    Convert.ToInt32(Config["BroadcastDelay"]) : 10;
                
                enablePersonalCommand = Config["EnablePersonalCommand"] != null ? 
                    Convert.ToBoolean(Config["EnablePersonalCommand"]) : true;
                
                enableGlobalCommand = Config["EnableGlobalCommand"] != null ? 
                    Convert.ToBoolean(Config["EnableGlobalCommand"]) : true;
                
                customIconSteamId = Config["CustomIconSteamId"] != null ? 
                    Convert.ToUInt64(Config["CustomIconSteamId"]) : 0UL;
                
                showOnline = true;
                showJoining = true;
                showQueued = true;
                showSleepers = true;
                
                object showOnlineObj = GetConfigValue("ShowOnline");
                if (showOnlineObj != null && bool.TryParse(showOnlineObj.ToString(), out bool showOnlineValue))
                    showOnline = showOnlineValue;
                
                object showJoiningObj = GetConfigValue("ShowJoining");
                if (showJoiningObj != null && bool.TryParse(showJoiningObj.ToString(), out bool showJoiningValue))
                    showJoining = showJoiningValue;
                
                object showQueuedObj = GetConfigValue("ShowQueued");
                if (showQueuedObj != null && bool.TryParse(showQueuedObj.ToString(), out bool showQueuedValue))
                    showQueued = showQueuedValue;
                
                object showSleepersObj = GetConfigValue("ShowSleepers");
                if (showSleepersObj != null && bool.TryParse(showSleepersObj.ToString(), out bool showSleepersValue))
                    showSleepers = showSleepersValue;
                
                SaveNewConfigOptions();
            }
            catch (Exception ex)
            {
                PrintError($"Error loading config: {ex.Message}");
                LoadDefaultConfig();
            }
        }
        
        private object GetConfigValue(string key)
        {
            try
            {
                if (Config[key] != null)
                    return Config[key];
            }
            catch (Exception ex)
            {
                PrintError($"Error getting config value '{key}': {ex.Message}");
            }
            return null;
        }
        
        private void SaveNewConfigOptions()
        {
            try
            {
                bool changed = false;
                
                if (GetConfigValue("SleepersColor") == null)
                {
                    Config["SleepersColor"] = "#ff686b";
                    changed = true;
                }
                
                if (GetConfigValue("ShowOnline") == null)
                {
                    Config["ShowOnline"] = true;
                    changed = true;
                }
                
                if (GetConfigValue("ShowJoining") == null)
                {
                    Config["ShowJoining"] = true;
                    changed = true;
                }
                
                if (GetConfigValue("ShowQueued") == null)
                {
                    Config["ShowQueued"] = true;
                    changed = true;
                }
                
                if (GetConfigValue("ShowSleepers") == null)
                {
                    Config["ShowSleepers"] = true;
                    changed = true;
                }
                
                if (GetConfigValue("CustomIconSteamId") == null)
                {
                    Config["CustomIconSteamId"] = 0UL;
                    changed = true;
                }
                
                if (changed)
                {
                    PrintWarning("Configuration file updated with new options.");
                    SaveConfig();
                }
            }
            catch (Exception ex)
            {
                PrintError($"Error saving new config options: {ex.Message}");
            }
        }

        protected override void LoadDefaultConfig()
        {
            PrintWarning("Creating new configuration file");
            Config.Clear();
            Config["Command"] = "pop";
            Config["BroadcastCommand"] = "!pop";
            Config["OnlineColor"] = "#ff686b";
            Config["JoiningColor"] = "#ff686b";
            Config["QueuedColor"] = "#ff686b";
            Config["SleepersColor"] = "#ff686b";
            Config["BroadcastDelay"] = 10;
            Config["EnablePersonalCommand"] = true;
            Config["EnableGlobalCommand"] = true;
            Config["CustomIconSteamId"] = 0UL;
            Config["ShowOnline"] = true;
            Config["ShowJoining"] = true;
            Config["ShowQueued"] = true;
            Config["ShowSleepers"] = true;
            SaveConfig();
        }

        private int GetQueuedPlayersCount()
        {
            return ServerMgr.Instance.connectionQueue.Queued;
        }

        private int GetSleepersCount()
        {
            return BasePlayer.sleepingPlayerList.Count;
        }

        private string FormatStatusMessage(int connectedPlayers, int joiningPlayers, int playersInQueue, int sleepersCount)
        {
            List<string> parts = new List<string>();
            
            if (showOnline)
                parts.Add($"Online: {string.Format("<color={0}>{1}</color>", onlineColor, connectedPlayers)}");
            
            if (showJoining)
                parts.Add($"Joining: {string.Format("<color={0}>{1}</color>", joiningColor, joiningPlayers)}");
            
            if (showQueued)
                parts.Add($"Queued: {string.Format("<color={0}>{1}</color>", queuedColor, playersInQueue)}");
            
            if (showSleepers)
                parts.Add($"Sleepers: {string.Format("<color={0}>{1}</color>", sleepersColor, sleepersCount)}");
            
            if (parts.Count == 0)
                parts.Add($"Online: {string.Format("<color={0}>{1}</color>", onlineColor, connectedPlayers)}");
            
            string message = string.Join(" | ", parts);
            
            return message;
        }

        private void PopCommand(IPlayer player, string cmd, string[] args)
        {
            if (!permission.UserHasPermission(player.Id, "pop.use"))
            {
                player.Reply(lang.GetMessage("NoPermission", this, player.Id));
                return;
            }

            int connectedPlayers = BasePlayer.activePlayerList.Count;
            int joiningPlayers = ServerMgr.Instance.connectionQueue.Joining;
            int playersInQueue = GetQueuedPlayersCount();
            int sleepersCount = GetSleepersCount();

            string message = FormatStatusMessage(connectedPlayers, joiningPlayers, playersInQueue, sleepersCount);
            
            if (customIconSteamId != 0)
            {
                BasePlayer basePlayer = player.Object as BasePlayer;
                if (basePlayer != null)
                {
                    basePlayer.SendConsoleCommand("chat.add", 0, customIconSteamId, message);
                    return;
                }
            }
            
            player.Reply(message);
        }

        private void BroadcastPopCommand(IPlayer player, string cmd, string[] args)
        {
            if (!permission.UserHasPermission(player.Id, "pop.use"))
            {
                player.Reply(lang.GetMessage("NoPermission", this, player.Id));
                return;
            }

            if (DateTime.Now < lastBroadcast.AddMinutes(broadcastDelay))
            {
                string tooSoonMessage = string.Format(lang.GetMessage("TooSoon", this, player.Id), broadcastDelay);
                player.Reply(tooSoonMessage);
                return;
            }

            lastBroadcast = DateTime.Now;

            int connectedPlayers = BasePlayer.activePlayerList.Count;
            int joiningPlayers = ServerMgr.Instance.connectionQueue.Joining;
            int playersInQueue = GetQueuedPlayersCount();
            int sleepersCount = GetSleepersCount();

            string message = FormatStatusMessage(connectedPlayers, joiningPlayers, playersInQueue, sleepersCount);
            
            if (customIconSteamId != 0)
            {
                foreach (BasePlayer basePlayer in BasePlayer.activePlayerList)
                {
                    basePlayer.SendConsoleCommand("chat.add", 0, customIconSteamId, message);
                }
                return;
            }
            
            server.Broadcast(message);
        }

        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                {"NoPermission", "You don't have permission to use this command."},
                {"TooSoon", "You must wait {0} minutes between broadcasts (try /pop)."},
                {"PopMessage", "Players Online: {0}/{1} | Joining: {2} | Queued: {3} | Sleepers: {4}"}
            }, this);
        }

        void OnUserChat(IPlayer player, string message)
        {
            if (enableGlobalCommand && !string.IsNullOrEmpty(broadcastCommand) && message == broadcastCommand)
            {
                BroadcastPopCommand(player, message, null);
                return;
            }
        }
    }
} 