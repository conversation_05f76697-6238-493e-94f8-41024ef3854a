using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Collections;
using Newtonsoft.Json;
using UnityEngine;
using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using Rust;
using Rust.Modular;


namespace Oxide.Plugins
{
    [Info("Awaken Maze", "Skelee", "1.9.6")]
    public class AwakenMaze : RustPlugin
    {
        [PluginReference]
        Plugin? AwakenClans, ImageLibrary, AwakenVotingSystem, ClanCores, AwakenStats;

        private static AwakenMaze _instance;
        private bool _isSpawningEntities = false;
        private DateTime _lastSpawnStartTime = DateTime.MinValue;
        private readonly object _spawnLock = new object();

        // Performance optimization fields
        private readonly HashSet<ulong> _processedPlayers = new HashSet<ulong>();

        #region Image handling
        void Loaded() {
            if (ImageLibrary == null)
            {
                Puts("Image Library is missing");
                return;
            }
            ImageLibrary?.Call("AddImage", "https://img.drxp.xyz/uploads/w9DuQlaEWB.png", "Image_7425");
        }
        #endregion

        #region Configuration
        private ConfigData config;

        public class UISettings
        {
            [JsonProperty("Use UI")]
            public bool UseUI { get; set; } = true;
            [JsonProperty("UI Text")]
            public string UIText { get; set; } = "MAZE";
            [JsonProperty("UI Text Color")]
            public string UITextColor { get; set; } = "1 0.5 0 1"; // Orange color
            [JsonProperty("UI Text Font Size")]
            public int UITextFontSize { get; set; } = 20;
            [JsonProperty("UI Background Color")]
            public string UIBackgroundColor { get; set; } = "0 0 0 0.5"; // Semi-transparent black
            [JsonProperty("Logo Image URL")]
            public string LogoImageURL { get; set; } = "https://cdn.awakenrust.com/oasis_maze.png";
        }

        private class LootCollectionSettings
        {
            [JsonProperty("Collect All Loot (not just M249s)")]
            public bool CollectAllLoot { get; set; } = true;

            [JsonProperty("Items to Place in Center Coffins (high-value items)")]
            public List<string> CenterCoffinItems { get; set; } = new List<string>
            {
                "lmg.m249",
                "rifle.ak",
                "rifle.lr300",
                "smg.mp5",
                "pistol.python",
                "explosive.timed",
                "ammo.rocket.basic",
                "sulfur",
                "metal.refined",
                "cloth",
                "wood",
                "stones",
                "metal.fragments",
                // HQM Kit items
                "metal.facemask",
                "metal.plate.torso",
                "roadsign.kilt",
                "roadsign.jacket",
                "pants",
                "hoodie",
                "syringe.medical",
                "largemedkit"
            };

            [JsonProperty("Items to Exclude from Collection (don't collect these)")]
            public List<string> ExcludedItems { get; set; } = new List<string>
            {
                "torch",
                "rock",
                "bandage",
                "apple",
                "mushroom",
                "corn",
                "pumpkin"
            };

            [JsonProperty("Max Items Per Coffin")]
            public int MaxItemsPerCoffin { get; set; } = 30;

            [JsonProperty("Center Coffin Spacing")]
            public float CenterCoffinSpacing { get; set; } = 1.5f;

            [JsonProperty("Door Coffin Spacing")]
            public float DoorCoffinSpacing { get; set; } = 2.0f;

            [JsonProperty("Door Coffin Distance from Center")]
            public float DoorCoffinDistance { get; set; } = 15.0f;
        }

        public class WallDecaySettings
        {
            [JsonProperty("Enable Wall Decay (instead of instant removal)")]
            public bool EnableWallDecay { get; set; } = true;
            [JsonProperty("Wall Decay Duration (seconds to fully decay)")]
            public float WallDecayDuration { get; set; } = 10.0f;
            [JsonProperty("Wall Decay Tick Interval (seconds between decay ticks)")]
            public float WallDecayTickInterval { get; set; } = 1.0f;
            [JsonProperty("Wall Selection Method (last, second, third, fourth, etc.)")]
            public string WallSelectionMethod { get; set; } = "last";
            [JsonProperty("Ice Wall Decay Multiplier (faster decay for ice walls)")]
            public float IceWallDecayMultiplier { get; set; } = 2.0f;
        }

        private class ConfigData
        {
            [JsonProperty("Config Version")]
            public string ConfigVersion { get; set; } = "1.4.0";

            [JsonProperty("Maze Center Prefab Path (Do not change this..)")]
            public string MazeCenterPrefabPath { get; set; } = "assets/bundled/prefabs/radtown/oil_barrel.prefab";
            [JsonProperty("Record Radius (Size of the Zone for saving/loading maze entities - should match your maze size)")]
            public float RecordRadius { get; set; } = 100f;
            [JsonProperty("Teleport Timer (Time it takes to teleport the player)")]
            public float TeleportTimer { get; set; } = 5.0f;
            [JsonProperty("Event Notification Time (seconds before the event starts)")]
            public float EventNotificationTime { get; set; } = 5.0f;
            [JsonProperty("Event Timer (How long the event runs for)")]
            public float EventTimer { get; set; } = 200.0f;
            [JsonProperty("Entry Dome Prefab (For zone entry/exit effect)")]
            public string EntryDomePrefab { get; set; } = "assets/bundled/prefabs/modding/events/twitch/br_sphere_purple.prefab";
            [JsonProperty("Entry Dome Radius (Visual dome size and event area - should be larger than Record Radius)")]
            public float EntryDomeRadius { get; set; } = 120.0f;
            [JsonProperty("Kill Dome Prefab (Removes entities outside zone)")]
            public string KillDomePrefab { get; set; } = "assets/bundled/prefabs/modding/events/twitch/br_sphere_purple.prefab";
            [JsonProperty("Kill Dome Radius (Starting radius for wall decay)")]
            public float KillDomeRadius { get; set; } = 80.0f;
            [JsonProperty("Kill Dome Shrink Amount (How much the dome shrinks each interval)")]
            public float KillDomeShrinkAmount { get; set; } = 10.0f;
            [JsonProperty("Kill Dome Shrinks Every (seconds between shrinks)")]
            public float KillDomeShrinkEvery { get; set; } = 30.0f;
            [JsonProperty("Wall Decay Settings")]
            public WallDecaySettings WallDecay { get; set; } = new WallDecaySettings();
            [JsonProperty("Door Move Up Distance")]
            public float DoorMoveUpDistance { get; set; } = 10.0f;
            [JsonProperty("Door Open Animation Duration (seconds)")]
            public float DoorOpenAnimDuration { get; set; } = 3.0f;
            [JsonProperty("Door Open Hold Duration (seconds)")]
            public float DoorOpenHoldDuration { get; set; } = 25.0f;
            [JsonProperty("Door Close Animation Duration (seconds)")]
            public float DoorCloseAnimDuration { get; set; } = 3.0f;
            [JsonProperty("Items to Remove on Teleport / Craft")]
            public List<string> ItemsToRemove { get; set; } = new List<string>();

            [JsonProperty("Loot Collection Settings")]
            public LootCollectionSettings LootCollection { get; set; } = new LootCollectionSettings();

            [JsonProperty("Discord Webhook URL")]
            public string DiscordWebhookURL { get; set; } = "";
            [JsonProperty("Server Details (shown in Discord embed)")]
            public string ServerDetails { get; set; } = "Awaken - US 10x | No BPs | Kits | Shop";
            [JsonProperty("UI Settings")]
            public UISettings UI { get; set; } = new UISettings();
        }

        protected override void LoadDefaultConfig()
        {
            config = new ConfigData();
            SaveConfig();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<ConfigData>();
                if (config == null)
                {
                    LoadDefaultConfig();
                }
                else
                {
                    // Check if config needs updating
                    CheckConfigVersion();
                }
            }
            catch (Exception ex)
            {
                PrintError("Error loading config: " + ex.Message);
                LoadDefaultConfig();
            }
        }

        private void CheckConfigVersion()
        {
            const string currentVersion = "1.4.0";

            if (string.IsNullOrEmpty(config.ConfigVersion) || config.ConfigVersion != currentVersion)
            {
                Puts($"Config version mismatch. Current: {config.ConfigVersion ?? "Unknown"}, Expected: {currentVersion}");
                Puts("Updating config with new settings...");

                // Update version
                config.ConfigVersion = currentVersion;

                // Add any new config options here for future updates
                // Example: if (config.NewSetting == null) config.NewSetting = defaultValue;

                SaveConfig();
                Puts("Config updated successfully!");
            }
        }
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Language and Localization
        protected override void LoadDefaultMessages()
        {
            lang.RegisterMessages(new Dictionary<string, string>
            {
                ["KillDomeRadiusUpdated"] = "Kill dome radius is now {0:F1}.",
                ["DoorsOpening"] = "Doors are opening...",
                ["DoorsClosed"] = "Doors have closed.",
                ["MazeEventStartNotification"] = "Maze event will start in {0} seconds. Doors have been spawned as notification.",
                ["MazeEventStarted"] = "Maze event started!",
                ["ZoneCleanup"] = "Zone left: All event entities removed except for the doors.",
                ["MazeStartedGameTip"] = "/MAZE TO JOIN.",
                ["CommandBlocked"] = "Commands are disabled while you are inside the maze.",
                ["TPRCancelled"] = "Teleport request cancelled: Target is currently in a maze event.",
                ["TeleportCancelled"] = "Teleport cancelled.",
                ["TeleportCancelledByCommand"] = "Teleport cancelled by /tpc command."
            }, this);
        }
        #endregion

        #region Data Persistence
        private readonly string dataFolder = Path.Combine(Interface.Oxide.DataDirectory, "awakenMaze");
        private readonly string mazeDataFile = "awakenMazeArenas.json";
        private string mazeDataFilePath;
        private Dictionary<string, MazeArena> savedMazes = new Dictionary<string, MazeArena>(StringComparer.OrdinalIgnoreCase);
        private void SetupDataPaths()
        {
            if (!Directory.Exists(dataFolder))
                Directory.CreateDirectory(dataFolder);
            mazeDataFilePath = Path.Combine(dataFolder, mazeDataFile);
            if (!File.Exists(mazeDataFilePath))
                File.WriteAllText(mazeDataFilePath, "{}");
        }
        private void SaveMazeData() => File.WriteAllText(mazeDataFilePath, JsonConvert.SerializeObject(savedMazes, Formatting.Indented));
        private void LoadMazeData()
        {
            if (File.Exists(mazeDataFilePath))
            {
                try
                {
                    savedMazes = JsonConvert.DeserializeObject<Dictionary<string, MazeArena>>(File.ReadAllText(mazeDataFilePath))
                        ?? new Dictionary<string, MazeArena>(StringComparer.OrdinalIgnoreCase);
                }
                catch (Exception ex)
                {
                    PrintError("Could not load maze data: " + ex.Message);
                    savedMazes = new Dictionary<string, MazeArena>(StringComparer.OrdinalIgnoreCase);
                }
            }
        }
        #endregion

        #region Helper Classes
        public class SerializableVector3
        {
            public float x { get; set; }
            public float y { get; set; }
            public float z { get; set; }
            public SerializableVector3() { }
            public SerializableVector3(Vector3 vector)
            {
                x = vector.x;
                y = vector.y;
                z = vector.z;
            }
            public Vector3 ToVector3() => new Vector3(x, y, z);
        }
        #endregion

        #region Maze Data Management
        private class MazeObject
        {
            public string Prefab { get; set; }
            public SerializableVector3 LocalPosition { get; set; }
            public SerializableVector3 LocalRotation { get; set; }
            public SerializableVector3 LocalEulerAngles { get; set; }
        }
        private class DoorSpawn
        {
            public string Prefab { get; set; }
            public SerializableVector3 LocalPosition { get; set; }
            public SerializableVector3 LocalRotation { get; set; }
        }
        private class MazeArena
        {
            public string Name { get; set; }
            public ulong OwnerID { get; set; }
            public SerializableVector3 MazeCenter { get; set; }
            public List<MazeObject> MazeObjects { get; set; } = new List<MazeObject>();
            public List<SerializableVector3> SpawnPoints { get; set; } = new List<SerializableVector3>();
            public List<DoorSpawn> DoorSpawns { get; set; } = new List<DoorSpawn>();
            public bool Ended { get; set; } = false;
            [JsonIgnore]
            public List<BaseEntity> SpawnedEntities { get; set; } = new List<BaseEntity>();
        }
        private MazeArena activeMazeSession = null;
        #endregion

        #region Event Ending Flag
        private bool eventEnding = false;
        private HashSet<ItemId> trackedM249ItemUids = new HashSet<ItemId>();
        #endregion

        #region Player Tracking
        private HashSet<ulong> playersInMaze = new HashSet<ulong>();
        private Timer eventStatusUITimer = null;
        private float doorsCloseTimeAbs = 0f;
        private float mazeEndTimeAbs = 0f;
        private bool doorsAreClosed = false;
        #endregion

        #region Teleport Cancellation
        private class TeleportInfo
        {
            public Timer teleportTimer;
            public Vector3 startPos;
            public Vector3 targetPos;
        }
        private Dictionary<ulong, TeleportInfo> pendingTeleports = new Dictionary<ulong, TeleportInfo>();
        #endregion

        #region Door Handling
        private List<BaseEntity> spawnedDoorEntities = new List<BaseEntity>();
        private void SpawnDoorSpawns(MazeArena maze)
        {
            try
            {
                if (maze?.DoorSpawns == null)
                {
                    Puts("[Maze Debug] Maze or DoorSpawns is null in SpawnDoorSpawns");
                    return;
                }

                Vector3 center = maze.MazeCenter.ToVector3();
                int doorSpawnedCount = 0;
                int doorFailedCount = 0;
                foreach (var door in new List<DoorSpawn>(maze.DoorSpawns))
                {
                    try
                    {
                        Vector3 spawnPos = center + door.LocalPosition.ToVector3();
                        Quaternion spawnRot = Quaternion.Euler(door.LocalRotation.ToVector3());
                        BaseEntity doorEntity = GameManager.server.CreateEntity(door.Prefab, spawnPos, spawnRot);
                        if (doorEntity != null)
                        {
                            doorEntity.Spawn();
                            spawnedDoorEntities.Add(doorEntity);
                            doorSpawnedCount++;
                        }
                        else
                        {
                            doorFailedCount++;
                            Puts($"[Maze Debug] Failed to create door entity: {door.Prefab} at position {spawnPos}");
                        }
                    }
                    catch (Exception ex)
                    {
                        doorFailedCount++;
                        Puts($"[Maze Debug] Exception spawning door: {ex.Message}");
                    }
                }

                if (doorFailedCount > 0)
                {
                    Puts($"[Maze Debug] Door spawning summary: {doorSpawnedCount} successful, {doorFailedCount} failed out of {maze.DoorSpawns.Count} total doors");
                }
            }
            catch (Exception ex)
            {
                Puts($"[Maze Debug] Exception in SpawnDoorSpawns: {ex.Message}");
            }
        }
        private void AnimateDoorsOpening()
        {
            int steps = 20;
            float stepTime = config.DoorOpenAnimDuration / steps;
            float increment = config.DoorMoveUpDistance / steps;
            timer.Repeat(stepTime, steps, () =>
            {
                foreach (var doorEntity in spawnedDoorEntities)
                {
                    if (doorEntity != null && !doorEntity.IsDestroyed)
                    {
                        doorEntity.transform.position += Vector3.up * increment;
                        doorEntity.SendNetworkUpdate();
                    }
                }
            });
        }
        private void AnimateDoorsClosing()
        {
            int steps = 60;
            float stepTime = config.DoorCloseAnimDuration / steps;
            float decrement = config.DoorMoveUpDistance / steps;

            var initialPositions = spawnedDoorEntities
                .Where(door => door != null && !door.IsDestroyed)
                .Select(door => door.transform.position)
                .ToList();

            int currentStep = 0;
            timer.Repeat(stepTime, steps, () =>
            {
                if (currentStep >= steps)
                    return;

                for (int i = 0; i < spawnedDoorEntities.Count; i++)
                {
                    var door = spawnedDoorEntities[i];
                    if (door == null || door.IsDestroyed) continue;

                    float t = (float)(currentStep + 1) / steps;
                    float ease = t * t * (3f - 2f * t);

                    Vector3 basePos = initialPositions[i];
                    door.transform.position = basePos - Vector3.up * (config.DoorMoveUpDistance * ease);
                    door.SendNetworkUpdate();
                }

                currentStep++;
            });
        }

        #endregion

        #region Helper Methods
        private HashSet<ulong> mazeWinners = new HashSet<ulong>();
        private bool mazeHasEnded = false;

        /// <summary>
        /// Gets the clan tag for a player using the AwakenClans API
        /// </summary>
        /// <param name="player">The player to get clan tag for</param>
        /// <returns>Clan tag string, empty if not in clan or error</returns>
        private string GetPlayerClan(BasePlayer player)
        {
            if (player == null || AwakenClans == null)
            {
                return "";
            }

            try
            {
                // Primary method: Use GetClan API to get the full clan object
                var clanObj = AwakenClans.Call("GetClan", player.userID);
                if (clanObj != null)
                {
                    // Try to get ClanTag property from the clan object
                    var clanType = clanObj.GetType();
                    var tagProperty = clanType.GetProperty("ClanTag");
                    if (tagProperty != null)
                    {
                        var tag = tagProperty.GetValue(clanObj)?.ToString();
                        if (!string.IsNullOrEmpty(tag))
                            return tag;
                    }
                }

                // Fallback 1: Try GetClanTag API
                var clanTag = AwakenClans.Call("GetClanTag", player.userID);
                if (clanTag != null && !string.IsNullOrEmpty(clanTag.ToString()))
                {
                    return clanTag.ToString();
                }

                // Fallback 2: Try GetClanOf API (Clans.cs compatible)
                var clanOf = AwakenClans.Call("GetClanOf", player.userID);
                if (clanOf != null && !string.IsNullOrEmpty(clanOf.ToString()))
                {
                    return clanOf.ToString();
                }

                return "";
            }
            catch (Exception ex)
            {
                PrintError($"[Maze] Error getting clan for player {player.displayName}: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Checks if a player is in any clan using the AwakenClans API
        /// </summary>
        /// <param name="player">The player to check</param>
        /// <returns>True if player is in a clan, false otherwise</returns>
        private bool IsPlayerInClan(BasePlayer player)
        {
            if (player == null || AwakenClans == null)
                return false;

            try
            {
                // Primary method: Check if GetClan returns a non-null object
                var clanObj = AwakenClans.Call("GetClan", player.userID);
                if (clanObj != null)
                    return true;

                // Fallback: Try IsInClan API
                var result = AwakenClans.Call("IsInClan", player.userID);
                return result != null && (bool)result;
            }
            catch (Exception ex)
            {
                PrintError($"[Maze] Error checking if player {player.displayName} is in clan: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Checks if two players are in the same clan using the AwakenClans API
        /// </summary>
        /// <param name="player1">First player</param>
        /// <param name="player2">Second player</param>
        /// <returns>True if both players are in the same clan, false otherwise</returns>
        private bool ArePlayersInSameClan(BasePlayer player1, BasePlayer player2)
        {
            if (player1 == null || player2 == null || AwakenClans == null)
                return false;

            try
            {
                var result = AwakenClans.Call("SameClan", player1.userID, player2.userID);
                return result != null && (bool)result;
            }
            catch (Exception ex)
            {
                PrintError($"[Maze] Error checking if players are in same clan: {ex.Message}");
                return false;
            }
        }
        #endregion

        #region Dome Effects
        private List<BaseEntity> entryDomes = new List<BaseEntity>();
        private List<BaseEntity> killDomes = new List<BaseEntity>();
        private Timer killDomeShrinkTimer = null;
        private float currentKillDomeRadius = 0f;
        private Vector3 eventCenter;
        private List<BaseEntity> testSpawnedEntities = new List<BaseEntity>();

        #region Wall Decay System
        private class DecayingWall
        {
            public BaseEntity Entity { get; set; }
            public float MaxHealth { get; set; }
            public float CurrentHealth { get; set; }
            public Timer DecayTimer { get; set; }
            public float DecayStartTime { get; set; }
        }

        private Dictionary<NetworkableId, DecayingWall> decayingWalls = new Dictionary<NetworkableId, DecayingWall>();
        #endregion
        private void CreateEntryDome(Vector3 pos, float radius)
        {
            BaseEntity dome = GameManager.server.CreateEntity(config.EntryDomePrefab, pos, Quaternion.identity, true);
            if (dome != null)
            {
                var sphere = dome.GetComponent<SphereEntity>();
                if (sphere != null)
                {
                    sphere.currentRadius = radius * 2;
                    sphere.lerpSpeed = 0f;
                    sphere.lerpRadius = sphere.currentRadius;
                }
                dome.Spawn();
                foreach (Collider col in dome.GetComponentsInChildren<Collider>())
                    UnityEngine.Object.Destroy(col);
                dome.gameObject.layer = (int)Rust.Layer.Reserved1;
                entryDomes.Add(dome);
            }
        }
        private void CreateKillDome(Vector3 pos, float radius)
        {
            BaseEntity dome = GameManager.server.CreateEntity(config.KillDomePrefab, pos, Quaternion.identity, true);
            if (dome != null)
            {
                var sphere = dome.GetComponent<SphereEntity>();
                if (sphere != null)
                {
                    sphere.currentRadius = radius * 2;
                    sphere.lerpSpeed = 0f;
                    sphere.lerpRadius = sphere.currentRadius;
                }
                dome.Spawn();

                // Remove all colliders to prevent interaction
                foreach (Collider col in dome.GetComponentsInChildren<Collider>())
                    UnityEngine.Object.Destroy(col);

                // Make the dome invisible by disabling all renderers
                foreach (Renderer renderer in dome.GetComponentsInChildren<Renderer>())
                {
                    if (renderer != null)
                        renderer.enabled = false;
                }

                // Set to invisible layer and disable networking for visual components
                dome.gameObject.layer = (int)Rust.Layer.Reserved1;
                dome.SetFlag(BaseEntity.Flags.Disabled, true);

                killDomes.Add(dome);
            }
        }

        private void DestroyEntryDomes()
        {
            foreach (var dome in entryDomes)
            {
                if (dome != null && !dome.IsDestroyed)
                    dome.Kill();
            }
            entryDomes.Clear();
        }

        private void DestroyKillDomes()
        {
            foreach (var dome in killDomes)
            {
                if (dome != null && !dome.IsDestroyed)
                    dome.Kill();
            }
            killDomes.Clear();
        }

        private void CleanupEventEntities(Vector3 center, float radius)
        {
            if (activeMazeSession?.SpawnedEntities == null) return;

            // Get walls that need to be removed based on selection method
            var wallsToRemove = GetWallsToRemove(center, radius);

            if (config.WallDecay.EnableWallDecay && wallsToRemove.Count > 0)
            {
                // Start decay process for selected walls
                StartWallDecay(wallsToRemove);
            }
            else
            {
                // Fallback to instant removal if decay is disabled
                InstantRemoveWalls(center, radius);
            }
        }

        private List<BaseEntity> GetWallsToRemove(Vector3 center, float radius)
        {
            var wallsToRemove = new List<BaseEntity>();

            if (activeMazeSession?.SpawnedEntities == null) return wallsToRemove;

            // Get all walls outside the radius
            var wallsOutsideRadius = new List<BaseEntity>();

            for (int i = activeMazeSession.SpawnedEntities.Count - 1; i >= 0; i--)
            {
                BaseEntity ent = activeMazeSession.SpawnedEntities[i];
                if (ent == null || ent.IsDestroyed)
                {
                    activeMazeSession.SpawnedEntities.RemoveAt(i);
                    continue;
                }

                // Don't remove the maze center marker
                if (ent.PrefabName.Equals(config.MazeCenterPrefabPath, StringComparison.OrdinalIgnoreCase))
                    continue;

                float dist = Vector3.Distance(ent.transform.position, center);
                if (dist > radius)
                {
                    // Check if it's a wall/building entity (including ice walls)
                    if (IsWallEntity(ent))
                    {
                        // Debug log for ice walls specifically
                        if (ent.PrefabName.ToLower().Contains("ice"))
                        {
                            Puts($"[Maze Debug] ICE WALL detected outside radius: {ent.PrefabName} at distance {dist:F1}m");
                        }
                        wallsOutsideRadius.Add(ent);
                    }
                    else
                    {
                        // Non-wall entities get removed instantly
                        ent.Kill();
                        activeMazeSession.SpawnedEntities.RemoveAt(i);
                    }
                }
            }

            // Select walls based on configuration method
            wallsToRemove = SelectWallsByMethod(wallsOutsideRadius);

            return wallsToRemove;
        }

        private List<BaseEntity> SelectWallsByMethod(List<BaseEntity> availableWalls)
        {
            var selectedWalls = new List<BaseEntity>();

            if (availableWalls.Count == 0) return selectedWalls;

            string method = config.WallDecay.WallSelectionMethod.ToLower();

            switch (method)
            {
                case "last":
                    if (availableWalls.Count > 0)
                        selectedWalls.Add(availableWalls[availableWalls.Count - 1]);
                    break;
                case "second":
                    if (availableWalls.Count > 1)
                        selectedWalls.Add(availableWalls[availableWalls.Count - 2]);
                    break;
                case "third":
                    if (availableWalls.Count > 2)
                        selectedWalls.Add(availableWalls[availableWalls.Count - 3]);
                    break;
                case "fourth":
                    if (availableWalls.Count > 3)
                        selectedWalls.Add(availableWalls[availableWalls.Count - 4]);
                    break;
                case "all":
                    selectedWalls.AddRange(availableWalls);
                    break;
                default:
                    // Default to last
                    if (availableWalls.Count > 0)
                        selectedWalls.Add(availableWalls[availableWalls.Count - 1]);
                    break;
            }

            return selectedWalls;
        }

        private bool IsWallEntity(BaseEntity entity)
        {
            if (entity == null || string.IsNullOrEmpty(entity.PrefabName)) return false;

            string prefabName = entity.PrefabName.ToLower();

            // Standard building components
            if (prefabName.Contains("wall") || prefabName.Contains("foundation") ||
                prefabName.Contains("floor") || prefabName.Contains("door") ||
                prefabName.Contains("roof") || prefabName.Contains("stairs") ||
                prefabName.Contains("ramp") || prefabName.Contains("pillar"))
            {
                return true;
            }

            // Ice walls (high and short)
            if (prefabName.Contains("icewall") || prefabName.Contains("ice_wall") ||
                prefabName.Contains("wall.ice") || prefabName.Contains("wall_ice"))
            {
                return true;
            }

            // Other common maze building entities
            if (prefabName.Contains("barricade") || prefabName.Contains("fence") ||
                prefabName.Contains("gate") || prefabName.Contains("barrier"))
            {
                return true;
            }

            return false;
        }

        private void StartWallDecay(List<BaseEntity> wallsToDecay)
        {
            Puts($"[Maze Debug] Starting decay for {wallsToDecay.Count} walls");

            foreach (var wall in wallsToDecay)
            {
                if (wall == null || wall.IsDestroyed) continue;

                // Skip if already decaying
                if (decayingWalls.ContainsKey(wall.net.ID)) continue;

                var combatEntity = wall as BaseCombatEntity;
                if (combatEntity == null) continue;

                // Debug log for ice walls specifically
                if (wall.PrefabName.ToLower().Contains("ice"))
                {
                    Puts($"[Maze Debug] Starting decay for ICE WALL: {wall.PrefabName} with {combatEntity.Health()}/{combatEntity.MaxHealth()} health");
                }
                else
                {
                    Puts($"[Maze Debug] Starting decay for wall: {wall.PrefabName} with {combatEntity.Health()}/{combatEntity.MaxHealth()} health");
                }

                // Create decay info
                var decayInfo = new DecayingWall
                {
                    Entity = wall,
                    MaxHealth = combatEntity.MaxHealth(),
                    CurrentHealth = combatEntity.Health(),
                    DecayStartTime = Time.time
                };

                // Calculate damage per tick (with ice wall multiplier)
                float decayDuration = config.WallDecay.WallDecayDuration;
                bool isIceWall = wall.PrefabName.ToLower().Contains("ice");

                if (isIceWall)
                {
                    decayDuration /= config.WallDecay.IceWallDecayMultiplier;
                    Puts($"[Maze Debug] Ice wall decay duration reduced to {decayDuration:F1} seconds (multiplier: {config.WallDecay.IceWallDecayMultiplier}x)");
                }

                float totalTicks = decayDuration / config.WallDecay.WallDecayTickInterval;
                float damagePerTick = decayInfo.MaxHealth / totalTicks;

                // Start decay timer
                decayInfo.DecayTimer = timer.Repeat(config.WallDecay.WallDecayTickInterval, (int)totalTicks + 1, () =>
                {
                    if (wall == null || wall.IsDestroyed || !decayingWalls.ContainsKey(wall.net.ID))
                    {
                        CleanupDecayingWall(wall.net.ID);
                        return;
                    }

                    var decay = decayingWalls[wall.net.ID];
                    decay.CurrentHealth -= damagePerTick;

                    // Apply damage to the wall
                    var hitInfo = new HitInfo();
                    hitInfo.damageTypes.Set(Rust.DamageType.Decay, damagePerTick);
                    hitInfo.DoHitEffects = false;
                    hitInfo.HitMaterial = 0;

                    combatEntity.OnAttacked(hitInfo);

                    // Check if wall should be destroyed
                    if (decay.CurrentHealth <= 0 || combatEntity.Health() <= 0)
                    {
                        // Remove from maze entities list
                        if (activeMazeSession?.SpawnedEntities != null)
                        {
                            activeMazeSession.SpawnedEntities.Remove(wall);
                        }

                        CleanupDecayingWall(wall.net.ID);

                        // Show decay message to players
                        ShowWallDecayMessage();
                    }
                });

                decayingWalls[wall.net.ID] = decayInfo;
            }
        }

        private void InstantRemoveWalls(Vector3 center, float radius)
        {
            // Fallback method for instant removal (original behavior)
            for (int i = activeMazeSession.SpawnedEntities.Count - 1; i >= 0; i--)
            {
                BaseEntity ent = activeMazeSession.SpawnedEntities[i];
                if (ent == null || ent.IsDestroyed)
                {
                    activeMazeSession.SpawnedEntities.RemoveAt(i);
                    continue;
                }

                // Don't remove the maze center marker
                if (ent.PrefabName.Equals(config.MazeCenterPrefabPath, StringComparison.OrdinalIgnoreCase))
                    continue;

                float dist = Vector3.Distance(ent.transform.position, center);
                if (dist > radius)
                {
                    ent.Kill();
                    activeMazeSession.SpawnedEntities.RemoveAt(i);

                    // Show game tip for wall removal
                    if (IsWallEntity(ent))
                    {
                        ShowWallDecayMessage();
                    }
                }
            }
        }

        private void ShowWallDecayMessage()
        {
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (playersInMaze.Contains(player.userID))
                {
                    player.SendConsoleCommand("gametip.showgametip", "Maze walls are decaying...");
                    // Hide the game tip after 3 seconds
                    timer.Once(3f, () =>
                    {
                        if (player != null && player.IsConnected)
                        {
                            player.SendConsoleCommand("gametip.hidegametip");
                        }
                    });
                }
            }
        }

        private void CleanupDecayingWall(NetworkableId entityId)
        {
            if (decayingWalls.ContainsKey(entityId))
            {
                var decay = decayingWalls[entityId];
                decay.DecayTimer?.Destroy();
                decayingWalls.Remove(entityId);
            }
        }

        private void CleanupAllDecayingWalls()
        {
            foreach (var decay in decayingWalls.Values)
            {
                decay.DecayTimer?.Destroy();
            }
            decayingWalls.Clear();
        }

        private void StartKillDomeShrinkTimer()
        {
            if (killDomeShrinkTimer != null)
            {
                killDomeShrinkTimer.Destroy();
                killDomeShrinkTimer = null;
            }

            currentKillDomeRadius = config.KillDomeRadius;
            Puts($"[Maze Debug] Starting kill dome shrink timer with radius {currentKillDomeRadius}");

            killDomeShrinkTimer = timer.Repeat(config.KillDomeShrinkEvery, 0, () =>
            {
                if (activeMazeSession == null || eventEnding)
                {
                    killDomeShrinkTimer?.Destroy();
                    killDomeShrinkTimer = null;
                    return;
                }

                currentKillDomeRadius -= config.KillDomeShrinkAmount;
                Puts($"[Maze Debug] Kill dome shrinking to radius {currentKillDomeRadius}");

                if (currentKillDomeRadius <= 10f)
                {
                    // Final cleanup - remove all remaining entities
                    foreach (var player in BasePlayer.activePlayerList)
                    {
                        if (playersInMaze.Contains(player.userID))
                        {
                            player.SendConsoleCommand("gametip.showgametip", "Final maze collapse!");
                            // Hide the game tip after 3 seconds
                            timer.Once(3f, () =>
                            {
                                if (player != null && player.IsConnected)
                                {
                                    player.SendConsoleCommand("gametip.hidegametip");
                                }
                            });
                        }
                    }

                    if (activeMazeSession?.SpawnedEntities != null)
                    {
                        foreach (var ent in new List<BaseEntity>(activeMazeSession.SpawnedEntities))
                        {
                            if (ent != null && !ent.IsDestroyed &&
                                !ent.PrefabName.Equals(config.MazeCenterPrefabPath, StringComparison.OrdinalIgnoreCase))
                            {
                                ent.Kill();
                            }
                        }
                        activeMazeSession.SpawnedEntities.Clear();
                    }

                    DestroyKillDomes();
                    killDomeShrinkTimer?.Destroy();
                    killDomeShrinkTimer = null;
                }
                else
                {
                    // Update kill dome size (invisible)
                    foreach (var dome in killDomes)
                    {
                        if (dome != null && !dome.IsDestroyed)
                        {
                            var sphere = dome.GetComponent<SphereEntity>();
                            if (sphere != null)
                            {
                                sphere.currentRadius = currentKillDomeRadius * 2;
                                sphere.lerpRadius = sphere.currentRadius;

                                // Ensure dome remains invisible after updates
                                foreach (Renderer renderer in dome.GetComponentsInChildren<Renderer>())
                                {
                                    if (renderer != null)
                                        renderer.enabled = false;
                                }

                                dome.SendNetworkUpdate();
                            }
                        }
                    }

                    // Remove entities outside the new radius
                    CleanupEventEntities(eventCenter, currentKillDomeRadius);

                    // Show game tip for layer removal
                    foreach (var player in BasePlayer.activePlayerList)
                    {
                        if (playersInMaze.Contains(player.userID))
                        {
                            player.SendConsoleCommand("gametip.showgametip", $"Maze layer removed! Radius: {currentKillDomeRadius:F0}m");
                            // Hide the game tip after 3 seconds
                            timer.Once(3f, () =>
                            {
                                if (player != null && player.IsConnected)
                                {
                                    player.SendConsoleCommand("gametip.hidegametip");
                                }
                            });
                        }
                    }
                }
            });
        }
        #endregion

        #region Clan Check
        private Timer clanCheckTimer = null;
        private string lastWinningClan = null;

        private void StartClanCheckTimer()
        {
            if (clanCheckTimer != null)
                clanCheckTimer.Destroy();

            // Wait 60 seconds after doors close before starting clan checks
            // This gives players time to spread out and engage
            timer.Once(30f, () =>
            {
                if (activeMazeSession == null || eventEnding)
                    return;

                Puts("[Maze Debug] Starting clan check timer (60 seconds after doors closed)");

                clanCheckTimer = timer.Every(10f, () =>
                {
                    if (activeMazeSession == null || eventEnding)
                    {
                        clanCheckTimer?.Destroy();
                        clanCheckTimer = null;
                        return;
                    }

                    var clanStatus = GetClanStatusInMaze();
                    Puts($"[Maze Debug] Clan check: {clanStatus.AliveClanCount} alive clans, {clanStatus.TotalPlayersInMaze} total players in maze");

                    // Only end if there are 0 alive clans OR exactly 1 alive clan
                    if (clanStatus.AliveClanCount == 0)
                    {
                        Puts("[Maze Debug] No alive clans detected in maze - ending event");
                        PrintToChat("Maze event over - no clans remaining.");
                        ForceEndEvent();
                        clanCheckTimer?.Destroy();
                        clanCheckTimer = null;
                    }
                    else if (clanStatus.AliveClanCount == 1)
                    {
                        string winnerClan = clanStatus.AliveClans.FirstOrDefault();
                        if (!string.IsNullOrEmpty(winnerClan))
                        {
                            if (lastWinningClan == null)
                            {
                                // First time detecting 1 clan - start countdown and wall decay
                                lastWinningClan = winnerClan;
                                Puts($"[Maze Debug] Single clan '{winnerClan}' detected - starting wall decay and 30 second countdown");
                                PrintToChat($"<color=#DBE2E9>Only clan</color> <color=#9CFF1E>[{winnerClan}]</color> <color=#DBE2E9>remains! Maze walls will start decaying...</color>");

                                // Start the kill dome and wall decay
                                CreateKillDome(eventCenter, config.KillDomeRadius);
                                StartKillDomeShrinkTimer();

                                // End after 30 seconds if still only 1 clan
                                timer.Once(30f, () =>
                                {
                                    if (activeMazeSession != null && !eventEnding)
                                    {
                                        var finalStatus = GetClanStatusInMaze();
                                        if (finalStatus.AliveClanCount <= 1)
                                        {
                                            Puts($"[Maze Debug] Ending maze - winner clan: {lastWinningClan}");
                                            PrintToChat($"<color=#DBE2E9>Clan</color> <color=#9CFF1E>[{lastWinningClan}]</color> <color=#DBE2E9>wins the maze event!</color>");

                                            // Track winners for loot access
                                            TrackMazeWinners(lastWinningClan);

                                            // Award clan core points via API
                                            AwardClanCorePoints(lastWinningClan, "maze");

                                            // Add maze win to AwakenStats for the winning clan
                                            AddMazeWinToStats(lastWinningClan);

                                            ForceEndEvent();
                                            clanCheckTimer?.Destroy();
                                            clanCheckTimer = null;
                                        }
                                        else
                                        {
                                            // Reset if more clans joined
                                            lastWinningClan = null;
                                            Puts("[Maze Debug] More clans detected - resetting countdown");
                                            PrintToChat("More clans have entered the maze! The battle continues...");
                                        }
                                    }
                                });
                            }
                        }
                    }
                    else
                    {
                        // Multiple clans - reset any countdown
                        if (lastWinningClan != null)
                        {
                            lastWinningClan = null;
                            Puts("[Maze Debug] Multiple clans detected - resetting any countdown");
                        }
                    }
                });
            });
        }
        private class ClanStatus
        {
            public HashSet<string> AliveClans { get; set; } = new HashSet<string>();
            public int AliveClanCount => AliveClans.Count;
            public int TotalPlayersInMaze { get; set; } = 0;
            public Dictionary<string, int> ClanPlayerCounts { get; set; } = new Dictionary<string, int>();
        }

        /// <summary>
        /// Gets the current clan status in the maze using the latest AwakenClans API
        /// </summary>
        /// <returns>ClanStatus object with alive clans and player counts</returns>
        private ClanStatus GetClanStatusInMaze()
        {
            var status = new ClanStatus();

            if (AwakenClans == null)
            {
                return status;
            }

            try
            {
                // Check which clans have alive players in the maze
                foreach (ulong userID in playersInMaze.ToList()) // ToList to avoid modification during iteration
                {
                    BasePlayer player = BasePlayer.FindByID(userID);
                    if (player != null && player.IsConnected && activeMazeSession != null &&
                        Vector3.Distance(player.transform.position, activeMazeSession.MazeCenter.ToVector3()) <= config.RecordRadius)
                    {
                        status.TotalPlayersInMaze++;

                        // Get clan tag using our improved method
                        string clanTag = GetPlayerClan(player);

                        if (!string.IsNullOrEmpty(clanTag))
                        {
                            status.AliveClans.Add(clanTag);

                            if (!status.ClanPlayerCounts.ContainsKey(clanTag))
                                status.ClanPlayerCounts[clanTag] = 0;
                            status.ClanPlayerCounts[clanTag]++;
                        }
                    }
                    else if (player == null || !player.IsConnected)
                    {
                        // Remove disconnected players from maze tracking
                        playersInMaze.Remove(userID);
                    }
                }

                return status;
            }
            catch (Exception ex)
            {
                PrintError($"[Maze] Error in GetClanStatusInMaze: {ex.Message}");
                return status;
            }
        }

        private int GetUniqueClanCountInMaze()
        {
            // Legacy method for UI compatibility
            return GetClanStatusInMaze().AliveClanCount;
        }
        #endregion

        #region Plugin Integrations
        private void AwardClanCorePoints(string clanName, string eventType)
        {
            if (ClanCores == null || !ClanCores.IsLoaded)
            {
                PrintWarning("[Maze] ClanCores plugin not found - cannot award points");
                return;
            }

            try
            {
                bool success = (bool)ClanCores.Call("API_AwardEventPoints", clanName, eventType);
                if (success)
                {
                    Puts($"[Maze] Successfully awarded clan core points to '{clanName}' for {eventType} event win");
                }
                else
                {
                    PrintWarning($"[Maze] Failed to award clan core points to '{clanName}'");
                }
            }
            catch (Exception ex)
            {
                PrintError($"[Maze] Error awarding clan core points: {ex.Message}");
            }
        }

        private void AddMazeWinToStats(string clanName)
        {
            if (AwakenStats == null || !AwakenStats.IsLoaded)
            {
                PrintWarning("[Maze] AwakenStats plugin not found - cannot record event win");
                return;
            }

            try
            {
                // Add maze win for the entire clan
                AwakenStats.Call("AddEventWinForClan", clanName, "maze");
                Puts($"[Maze] Successfully recorded maze win for clan '{clanName}' in AwakenStats");
            }
            catch (Exception ex)
            {
                PrintError($"[Maze] Error recording maze win in AwakenStats: {ex.Message}");
            }
        }
        #endregion

        #region Event Statistics Tracking & Discord Embed Support
        private HashSet<ulong> eventPlayersParticipated = new HashSet<ulong>();
        private HashSet<string> eventClans = new HashSet<string>();
        private Dictionary<ulong, int> eventKills = new Dictionary<ulong, int>();
        private Dictionary<ulong, int> eventDeaths = new Dictionary<ulong, int>();
        private Dictionary<ulong, string> eventPlayerNames = new Dictionary<ulong, string>();
        private int eventM2sWon = 0;
        // Track damage and kills during maze events
        object OnEntityTakeDamage(BaseCombatEntity entity, HitInfo info)
        {
            BasePlayer victim = entity as BasePlayer;
            BasePlayer attacker = info?.InitiatorPlayer;

            if (victim == null || attacker == null) return null;

            // Only process damage during active maze events
            if (activeMazeSession == null || eventEnding) return null;

            // Check if both players are in maze area
            bool victimInMaze = IsPlayerInMazeArea(victim);
            bool attackerInMaze = IsPlayerInMazeArea(attacker);

            // Debug logging to see what's happening
            Puts($"[Maze Debug] Damage attempt: {attacker.displayName} (Maze:{attackerInMaze}) -> {victim.displayName} (Maze:{victimInMaze})");

            // Only allow damage if both players are inside the maze
            if (attackerInMaze && victimInMaze)
            {
                Puts($"[Maze Debug] ALLOWED: Both players inside maze - PvP enabled");
                return null; // Allow damage
            }

            // Block damage if players are in different areas (one inside, one outside)
            if (attackerInMaze != victimInMaze)
            {
                if (!attackerInMaze && victimInMaze)
                {
                    ShowGameTip(attacker, "You cannot damage players in the maze from outside!");
                    Puts($"[Maze Debug] BLOCKED: Attacker outside trying to damage inside");
                }
                else
                {
                    ShowGameTip(attacker, "You cannot damage players outside the maze from inside!");
                    Puts($"[Maze Debug] BLOCKED: Attacker inside trying to damage outside");
                }

                info.damageTypes.Clear();
                info.DoHitEffects = false;
                info.HitMaterial = 0;
                return true; // Block damage
            }

            // If both players are outside maze, allow normal PvP
            Puts($"[Maze Debug] ALLOWED: Both players outside maze - normal PvP rules apply");
            return null; // Allow damage
        }

        // Prevent building inside maze, allow sleeping bags outside only
        object CanBuild(Planner planner, Construction prefab, Construction.Target target)
        {
            if (activeMazeSession == null) return null;

            BasePlayer player = planner.GetOwnerPlayer();
            if (player == null) return null;

            Vector3 buildPos = target.position;
            bool isInMazeArea = IsPositionInMazeArea(buildPos);

            // Check if it's a sleeping bag or bed
            bool isSleepingItem = prefab.fullName.Contains("sleepingbag") || prefab.fullName.Contains("bed");

            if (isInMazeArea)
            {
                ShowGameTip(player, "You cannot build inside the maze area!");
                return false; // Block all building inside maze
            }

            if (!isInMazeArea && !isSleepingItem)
            {
                // Allow all building outside maze area except during active events
                if (!eventEnding && doorsAreClosed)
                {
                    ShowGameTip(player, "Limited building during maze event - sleeping bags only!");
                    return false;
                }
            }

            return null; // Allow building
        }

        // Prevent looting winner boxes by non-winners
        object CanLootEntity(BasePlayer player, StorageContainer container)
        {
            if (container == null || player == null) return null;

            // Check if this is a winner loot coffin (spawned after maze ends)
            if (mazeHasEnded && container.PrefabName.Contains("coffinstorage"))
            {
                // Check if container is near the last maze center
                if (activeMazeSession != null)
                {
                    float distance = Vector3.Distance(container.transform.position, activeMazeSession.MazeCenter.ToVector3());
                    if (distance <= config.EntryDomeRadius)
                    {
                        // This is likely a winner loot coffin
                        if (!mazeWinners.Contains(player.userID))
                        {
                            ShowGameTip(player, "Only maze winners can loot these coffins!");
                            return false;
                        }
                    }
                }
            }

            return null; // Allow looting
        }

        private bool IsPlayerInMazeArea(BasePlayer player)
        {
            if (player == null || activeMazeSession == null) return false;

            Vector3 playerPos = player.transform.position;
            Vector3 mazeCenter = activeMazeSession.MazeCenter.ToVector3();
            float distance = Vector3.Distance(playerPos, mazeCenter);
            bool inMaze = distance <= config.RecordRadius;

            // Debug logging for area detection
            Puts($"[Maze Debug] Player {player.displayName} position check: Distance={distance:F1}m, Radius={config.RecordRadius}m, InMaze={inMaze}");

            return inMaze;
        }

        private bool IsPositionInMazeArea(Vector3 position)
        {
            if (activeMazeSession == null) return false;
            return Vector3.Distance(position, activeMazeSession.MazeCenter.ToVector3()) <= config.RecordRadius;
        }



        /// <summary>
        /// Tracks maze winners from the winning clan using the latest AwakenClans API
        /// </summary>
        /// <param name="winningClanTag">The clan tag of the winning clan</param>
        private void TrackMazeWinners(string winningClanTag)
        {
            mazeWinners.Clear(); // Clear previous winners

            if (string.IsNullOrEmpty(winningClanTag))
            {
                return;
            }

            try
            {
                // Add all players from the winning clan to winners list
                foreach (ulong userID in playersInMaze.ToList()) // ToList to avoid modification during iteration
                {
                    BasePlayer player = BasePlayer.FindByID(userID);
                    if (player != null && player.IsConnected)
                    {
                        string playerClan = GetPlayerClan(player);
                        if (!string.IsNullOrEmpty(playerClan) && playerClan.Equals(winningClanTag, StringComparison.OrdinalIgnoreCase))
                        {
                            mazeWinners.Add(userID);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                PrintError($"[Maze] Error tracking maze winners: {ex.Message}");
            }
        }

        private Vector3 GetRandomSpawnPoint()
        {
            if (activeMazeSession == null || activeMazeSession.SpawnPoints == null || activeMazeSession.SpawnPoints.Count == 0)
            {
                Puts("[Maze Debug] No spawn points available for player movement");
                return Vector3.zero;
            }

            Vector3 mazeCenter = activeMazeSession.MazeCenter.ToVector3();
            int randomIndex = UnityEngine.Random.Range(0, activeMazeSession.SpawnPoints.Count);
            Vector3 spawnPoint = mazeCenter + activeMazeSession.SpawnPoints[randomIndex].ToVector3();

            return spawnPoint;
        }

        private void MovePlayersAlreadyInMaze()
        {
            if (activeMazeSession == null || activeMazeSession.SpawnPoints.Count == 0)
            {
                Puts("[Maze Debug] Cannot move players - no active session or spawn points");
                return;
            }

            Vector3 mazeCenter = activeMazeSession.MazeCenter.ToVector3();
            int movedPlayers = 0;

            foreach (var player in BasePlayer.activePlayerList.ToList())
            {
                if (player == null || !player.IsConnected) continue;

                // Check if player is already in maze area
                float distance = Vector3.Distance(player.transform.position, mazeCenter);
                if (distance <= config.RecordRadius)
                {
                    // Player is in maze area, move them to a spawn point
                    Vector3 spawnPoint = GetRandomSpawnPoint();
                    if (spawnPoint != Vector3.zero)
                    {
                        player.Teleport(spawnPoint);
                        ShowGameTip(player, "You were moved to a safe spawn point for the maze event!");
                        movedPlayers++;
                        Puts($"[Maze Debug] Moved player {player.displayName} from maze area to spawn point");
                    }
                }
            }

            if (movedPlayers > 0)
            {
                Puts($"[Maze Debug] Moved {movedPlayers} players who were already in maze area to spawn points");
            }
        }

        void OnPlayerDeath(BasePlayer player, HitInfo info)
        {
            if (player == null) return;

            ulong playerId = player.userID;
            if (playersInMaze.Contains(playerId))
            {
                playersInMaze.Remove(playerId);
                _processedPlayers.Remove(playerId); // Clean up processed players cache
                PrintToChat(player, "You have left the maze after dying.");

                // Remove UI when player leaves maze
                if (config.UI.UseUI)
                {
                    CuiHelper.DestroyUi(player, "awakenMazeUI");
                }
            }
            if (pendingTeleports.ContainsKey(playerId))
            {
                pendingTeleports[playerId].teleportTimer.Destroy();
                pendingTeleports.Remove(playerId);
            }
            if (!eventDeaths.ContainsKey(player.userID))
                eventDeaths[player.userID] = 0;
            eventDeaths[player.userID]++;
            if (info != null && info.Initiator is BasePlayer killer && killer != player)
            {
                if (!eventKills.ContainsKey(killer.userID))
                    eventKills[killer.userID] = 0;
                eventKills[killer.userID]++;
                if (!eventPlayerNames.ContainsKey(killer.userID))
                    eventPlayerNames[killer.userID] = killer.displayName;
            }
        }
        private void TeleportPlayer(BasePlayer player, Vector3 destination)
        {
            if (player == null || destination == Vector3.zero)
                return;
            InitiateSleep(player);
            player.UpdateActiveItem(default(ItemId));
            player.EnsureDismounted();
            if (player.HasParent())
                player.SetParent(null, true, true);
            if (player.IsConnected)
            {
                player.SetPlayerFlag(BasePlayer.PlayerFlags.ReceivingSnapshot, true);
                player.ClientRPCPlayer(null, player, "StartLoading", true);
            }
            Vector3 prevPos = player.transform.position;
            player.Teleport(destination);
            if (player.IsConnected)
            {
                if (!player._limitedNetworking)
                {
                    player.UpdateNetworkGroup();
                    player.SendNetworkUpdateImmediate(false);
                }
                player.ClearEntityQueue(null);
                player.SendFullSnapshot();
            }
            if (!player._limitedNetworking)
                player.ForceUpdateTriggers();
            var rb = player.GetComponent<Rigidbody>();
            if (rb != null)
                rb.velocity = Vector3.zero;
            Interface.CallHook("OnPlayerTeleported", player, prevPos, player.transform.position);
            RemoveBlockedItems(player);
            playersInMaze.Add(player.userID);
            if (!eventPlayersParticipated.Contains(player.userID))
                eventPlayersParticipated.Add(player.userID);
            if (!eventPlayerNames.ContainsKey(player.userID))
                eventPlayerNames[player.userID] = player.displayName;
            string clanTag = GetPlayerClan(player);
            if (!string.IsNullOrEmpty(clanTag))
                eventClans.Add(clanTag);

            // Show UI when player enters maze
            if (config.UI.UseUI)
            {
                CreateMazeUI(player);
            }
        }
        private void SendDiscordEmbed()
        {
            if (string.IsNullOrEmpty(config.DiscordWebhookURL))
            {
                PrintError("Discord webhook URL is not configured.");
                return;
            }

            var playerStats = new List<(ulong uid, int kills, int deaths, float kdr, string name, string clan)>();
            foreach (var kvp in eventKills)
            {
                ulong uid    = kvp.Key;
                int   kills  = kvp.Value;
                int   deaths = eventDeaths.GetValueOrDefault(uid, 0);
                float kdr    = deaths > 0 ? (float)kills / deaths : kills;
                string name  = eventPlayerNames.GetValueOrDefault(uid, "Unknown");

                // Use the bulletproof clan API
                BasePlayer player = BasePlayer.FindByID(uid);
                string clan = player != null ? GetPlayerClan(player) : "NoClan";
                if (string.IsNullOrEmpty(clan)) clan = "NoClan";

                playerStats.Add((uid, kills, deaths, kdr, name, clan));
            }

            var clanGroups = playerStats
                .GroupBy(p => p.clan)
                .Select(g => new {
                    Clan       = g.Key,
                    TotalKills = g.Sum(p => p.kills),
                    TotalDeaths= g.Sum(p => p.deaths),
                    TotalDamage = 0, // Maze doesn't track damage yet
                    TotalHeadshots = 0, // Maze doesn't track headshots yet
                    Players    = g.OrderByDescending(p => p.kills).ToList()
                })
                .OrderByDescending(g => g.TotalKills)
                .ToList();

            string winningClan = lastWinningClan ?? clanGroups.FirstOrDefault()?.Clan ?? "No Team";
            var winGroup = clanGroups.FirstOrDefault(g => g.Clan == winningClan);

            // Get the maze name for the title
            string mazeName = activeMazeSession?.Name ?? "Unknown";

            // Calculate total stats
            int totalKills = eventKills.Values.Sum();
            int totalPlayers = eventPlayersParticipated.Count;
            int totalTeams = clanGroups.Count;

            // Calculate duration (assuming event ran for full time if not ended early)
            TimeSpan duration = TimeSpan.FromSeconds(config.EventTimer);

            // Get top fragger
            var topFragger = playerStats.OrderByDescending(p => p.kills).ThenByDescending(p => p.kdr).FirstOrDefault();
            string topFraggerText = topFragger.uid != 0
                ? $"🏆 [{topFragger.name}](http://steamcommunity.com/profiles/{topFragger.uid}) - ``{topFragger.kills} kills``"
                : "No data.";

            // Build fields array dynamically (matching roam bubble style exactly)
            var fieldsList = new List<object>();

            // Add statistics fields similar to roam bubble plugin
            fieldsList.Add(new { name = "Details:", value = $"Total Kills: ``{totalKills:N0}``\nDuration: ``{duration.Minutes:00}:{duration.Seconds:00} min``", inline = true });
            fieldsList.Add(new { name = "‎", value = $"Total Players: ``{totalPlayers}``\nTotal Teams: ``{totalTeams}``", inline = true });
            fieldsList.Add(new { name = "Loot Check:", value = $"AKs: ``1``\nM2s: ``{eventM2sWon}``", inline = true });
            fieldsList.Add(new { name = "‎", value = $"{topFraggerText}", inline = false });
            fieldsList.Add(new { name = "Winners:", value = $"**[{winningClan}]**", inline = true });
            fieldsList.Add(new { name = "Kills:", value = $"``{winGroup?.TotalKills ?? 0}``", inline = true });
            fieldsList.Add(new { name = "Damage / Headshots:", value = $"``{winGroup?.TotalDamage ?? 0}`` / ``{winGroup?.TotalHeadshots ?? 0}``", inline = true });

            // Add Members field with properly formatted member list
            if (winGroup != null && winGroup.Players.Any())
            {
                var membersList = winGroup.Players
                    .Select(p => $"- [{p.name}](http://steamcommunity.com/profiles/{p.uid}) – ``{p.kills} Kills`` / ``{p.deaths} Deaths``");
                string membersText = string.Join("\n", membersList);
                fieldsList.Add(new { name = "Members:", value = membersText, inline = false });
            }

            // Create Discord embed (using roam bubble style exactly)
            var payload = new
            {
                content = "",
                embeds = new[]
                {
                    new
                    {
                        title = $"Awaken Maze - {mazeName}",
                        description = config.ServerDetails,
                        color = 1474,
                        fields = fieldsList.ToArray(),
                        thumbnail = new {
                            url = "https://cdn.awakenrust.com/oasis_maze.png" // Maze-specific image
                        },
                        footer = new {
                            text = "Awaken Rust Servers",
                            icon_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213148812906526/blueoasisglowing.png"
                        },
                        timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
                    }
                },
                username = "Awaken Events",
                avatar_url = "https://cdn.discordapp.com/attachments/1239328289357824042/1378213148812906526/blueoasisglowing.png",
                attachments = new object[0]
            };

            string json = JsonConvert.SerializeObject(payload, Formatting.None);
            webrequest.EnqueuePost(config.DiscordWebhookURL, json, (code, res) =>
            {
                if (code is not 200 and not 204)
                    PrintError($"Discord webhook error: {code} – {res}");
            }, this, RequestHeaders);
        }
        private static Dictionary<string, string> RequestHeaders => new Dictionary<string, string>
        {
            ["Content-Type"] = "application/json"
        };
        #endregion

        #region UI System
        private string HexToCuiColor(string hex, int alpha = 100)
        {
            if (hex.StartsWith("#"))
                hex = hex.Substring(1);

            if (hex.Length != 6)
                return "1 1 1 1"; // Default to white if invalid

            try
            {
                int r = Convert.ToInt32(hex.Substring(0, 2), 16);
                int g = Convert.ToInt32(hex.Substring(2, 2), 16);
                int b = Convert.ToInt32(hex.Substring(4, 2), 16);
                float a = alpha / 100f;

                return $"{r / 255f} {g / 255f} {b / 255f} {a}";
            }
            catch
            {
                return "1 1 1 1"; // Default to white if conversion fails
            }
        }

        private void CreateLogo(CuiElementContainer container, string parentName)
        {
            if (string.IsNullOrEmpty(config?.UI?.LogoImageURL))
            {
                return;
            }

            container.Add(new CuiElement
            {
                Name = "maze_logo_image",
                Parent = parentName,
                Components =
                {
                    new CuiRawImageComponent { Url = config.UI.LogoImageURL },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.3",
                        AnchorMax = "0.8 1.0",
                        OffsetMin = "0 0",
                        OffsetMax = "0 0"
                    }
                }
            });
        }

        private void CreateMazeUI(BasePlayer player, float time = -1)
        {
            if (player == null || !player.IsConnected || !config.UI.UseUI)
            {
                return;
            }

            // Show UI to all players during active maze events, not just those in maze
            if (activeMazeSession == null || eventEnding)
            {
                return;
            }

            // Destroy existing UI to prevent caching issues
            CuiHelper.DestroyUi(player, "awakenMazeUI");

            var container = new CuiElementContainer();

            // Main UI container
            container.Add(new CuiElement
            {
                Name = "awakenMazeUI",
                Parent = "Hud",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1"
                    }
                }
            });

            // Frame 2 - Main container (positioned to the left of health bar, bottom-right)
            container.Add(new CuiElement
            {
                Name = "Frame 2",
                Parent = "awakenMazeUI",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "1 0",
                        AnchorMax = "1 0",
                        OffsetMin = "-350 20",
                        OffsetMax = "-230 140"
                    }
                }
            });

            // Frame 3 - Stats container (positioned within Frame 2)
            container.Add(new CuiElement
            {
                Name = "Frame 3",
                Parent = "Frame 2",
                Components = {
                    new CuiImageComponent { Color = HexToCuiColor("#000000", 0) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0 0",
                        AnchorMax = "1 1",
                        OffsetMin = "0 0",
                        OffsetMax = "0 0"
                    }
                }
            });

            // MAZE Logo Image - Direct URL method
            CreateLogo(container, "Frame 3");

            // Get player stats for display
            int playerKills = GetPlayerKills(player);
            int playerDeaths = GetPlayerDeaths(player);
            int totalTeams = GetUniqueClanCountInMaze();

            // Format time remaining
            string timeText = time >= 0 ? FormatTime(time) : "00:00";

            // KILLS text with dynamic data
            container.Add(new CuiElement
            {
                Name = "KILLS {k}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"KILLS {playerKills}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.3",
                        AnchorMax = "0.5 0.4",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // DEATHS text with dynamic data
            container.Add(new CuiElement
            {
                Name = "DEATHS {d}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"DEATHS {playerDeaths}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.2",
                        AnchorMax = "0.5 0.3",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // TEAMS text with dynamic data
            container.Add(new CuiElement
            {
                Name = "TEAMS {k}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = $"TEAMS {totalTeams}", Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.1",
                        AnchorMax = "0.5 0.2",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            // Time text with dynamic data
            container.Add(new CuiElement
            {
                Name = "{time}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = timeText, Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.0",
                        AnchorMax = "0.5 0.1",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            try
            {
                CuiHelper.AddUi(player, container);
            }
            catch (Exception ex)
            {
                // Handle or log the exception if needed
            }
        }

        private string FormatTime(float timeInSeconds)
        {
            TimeSpan timeSpan = TimeSpan.FromSeconds(timeInSeconds);
            return $"{timeSpan.Minutes.ToString("D2")}:{timeSpan.Seconds.ToString("D2")}";
        }

        private void UpdateTimerUI(BasePlayer player, float time)
        {
            if (player == null || !player.IsConnected)
            {
                return;
            }

            // Update the time text in the existing UI
            string timeText = FormatTime(time);
            CuiHelper.DestroyUi(player, "{time}");

            var container = new CuiElementContainer();

            container.Add(new CuiElement
            {
                Name = "{time}",
                Parent = "Frame 3",
                Components = {
                    new CuiTextComponent { Text = timeText, Font = "robotocondensed-regular.ttf", FontSize = 10, Align = TextAnchor.UpperLeft, Color = HexToCuiColor("#FFFFFF", 100) },
                    new CuiRectTransformComponent
                    {
                        AnchorMin = "0.0 0.0",
                        AnchorMax = "0.5 0.1",
                        OffsetMin = "15 0",
                        OffsetMax = "5 0"
                    }
                }
            });

            try
            {
                CuiHelper.AddUi(player, container);
            }
            catch (Exception ex)
            {
                // Handle or log the exception if needed
            }
        }

        private int GetPlayerKills(BasePlayer player)
        {
            if (player == null) return 0;
            return eventKills.GetValueOrDefault(player.userID, 0);
        }

        private int GetPlayerDeaths(BasePlayer player)
        {
            if (player == null) return 0;
            return eventDeaths.GetValueOrDefault(player.userID, 0);
        }
        #endregion




        #region Game Tip Functions
        private void ShowGameTip(BasePlayer player, string message)
        {
            if (player != null && player.IsConnected)
            {
                player.SendConsoleCommand("gametip.showgametip", message);
                // Hide the game tip after 3 seconds
                timer.Once(3f, () =>
                {
                    if (player != null && player.IsConnected)
                    {
                        player.SendConsoleCommand("gametip.hidegametip");
                    }
                });
            }
        }

        private void ShowGameTipToAll(string message)
        {
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player != null && player.IsConnected)
                {
                    player.SendConsoleCommand("gametip.showgametip", message);
                    // Hide the game tip after 3 seconds
                    timer.Once(3f, () =>
                    {
                        if (player != null && player.IsConnected)
                        {
                            player.SendConsoleCommand("gametip.hidegametip");
                        }
                    });
                }
            }
        }

        private void ClearAllGameTips()
        {
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player != null && player.IsConnected)
                {
                    player.SendConsoleCommand("gametip.hidegametip");
                }
            }
        }

        private void ShowGameTipToMazePlayers(string message)
        {
            foreach (ulong uid in playersInMaze)
            {
                var player = BasePlayer.FindByID(uid);
                if (player != null && player.IsConnected)
                {
                    player.SendConsoleCommand("gametip.showgametip", message);
                    // Hide the game tip after 3 seconds
                    timer.Once(3f, () =>
                    {
                        if (player != null && player.IsConnected)
                        {
                            player.SendConsoleCommand("gametip.hidegametip");
                        }
                    });
                }
            }
        }

        private void ShowMazeNotification(BasePlayer player, string headerText, string phaseMessage, string timeText)
        {
            // Combine all the information into a single game tip message
            string message = $"{headerText}: {phaseMessage} {timeText}";
            if (player != null && player.IsConnected)
            {
                player.SendConsoleCommand("gametip.showgametip", message);
                // Hide the game tip after 5 seconds (longer for notifications with timing info)
                timer.Once(5f, () =>
                {
                    if (player != null && player.IsConnected)
                    {
                        player.SendConsoleCommand("gametip.hidegametip");
                    }
                });
            }
        }



        private void CheckWarningThresholds(float timeRemaining, string phaseName, out string header, out string message, out string timeStr)
        {
            header = null;
            message = null;
            timeStr = null;
            int secondsRemaining = Mathf.CeilToInt(timeRemaining);

            bool showWarning = false;
            int displayTime = 0;

            if (secondsRemaining <= 10 && secondsRemaining > 0)
            {
                showWarning = true;
                displayTime = secondsRemaining;
            }
            else if (secondsRemaining == 30)
            {
                showWarning = true;
                displayTime = 30;
            }
            else if (secondsRemaining == 60)
            {
                showWarning = true;
                displayTime = 60;
            }
            else if (secondsRemaining == 180)
            {
                showWarning = true;
                displayTime = 180;
            }
            else if (secondsRemaining == 300)
            {
                showWarning = true;
                displayTime = 300;
            }

            if (showWarning && displayTime > 0)
            {
                header = $"{phaseName.ToUpper()} SOON";
                message = $"{phaseName} in...";
                timeStr = displayTime > 60 ? FormatTime(displayTime) : displayTime.ToString();
            }
        }

        private void UpdateEventStatusUI()
        {
            if (activeMazeSession == null || eventEnding)
            {
                eventStatusUITimer?.Destroy();
                eventStatusUITimer = null;
                // No need to destroy UI elements since we're using game tips now
                playersInMaze.Clear();
                return;
            }

            float now = Time.realtimeSinceStartup;
            string header = null;
            string message = null;
            string timeStr = null;

            if (!doorsAreClosed)
            {
                float timeToDoorClose = doorsCloseTimeAbs - now;
                if (timeToDoorClose > 0)
                {
                    CheckWarningThresholds(timeToDoorClose, "Doors closing", out header, out message, out timeStr);
                }
            }
            else
            {
                float timeToMazeEnd = mazeEndTimeAbs - now;
                if (timeToMazeEnd > 0)
                {
                    CheckWarningThresholds(timeToMazeEnd, "Maze ending", out header, out message, out timeStr);
                }
                else
                {
                    eventStatusUITimer?.Destroy();
                    eventStatusUITimer = null;
                    return;
                }
            }

            // Show notifications to players in maze
            foreach (ulong uid in playersInMaze)
            {
                var ply = BasePlayer.FindByID(uid);
                if (ply != null && ply.IsConnected)
                {
                    if (header != null)
                    {
                        ShowMazeNotification(ply, header, message, timeStr);
                    }
                }
            }

            // Show UI to ALL players during active events
            if (config.UI.UseUI && doorsAreClosed)
            {
                float timeToMazeEnd = mazeEndTimeAbs - now;
                if (timeToMazeEnd > 0)
                {
                    foreach (var player in BasePlayer.activePlayerList)
                    {
                        if (player != null && player.IsConnected)
                        {
                            // Update UI every 30 seconds or if player not processed yet
                            bool shouldUpdate = !_processedPlayers.Contains(player.userID) || (Time.realtimeSinceStartup % 30f < 1f);
                            if (shouldUpdate)
                            {
                                CreateMazeUI(player, timeToMazeEnd);
                                _processedPlayers.Add(player.userID); // Mark as processed to reduce UI updates
                            }
                        }
                    }
                }
            }
        }
        #endregion

        #region Hooks
        private void Init()
        {
            _instance = this;

            // AGGRESSIVE reset of spawning state on plugin load/reload
            lock (_spawnLock)
            {
                _isSpawningEntities = false;
                _lastSpawnStartTime = DateTime.MinValue;
                Puts("[Maze Debug] FORCE RESET spawning flag and timestamp during Init()");
            }
            _processedPlayers.Clear();

            // Additional safety reset after a short delay
            timer.Once(0.5f, () =>
            {
                lock (_spawnLock)
                {
                    _isSpawningEntities = false;
                    _lastSpawnStartTime = DateTime.MinValue;
                    Puts("[Maze Debug] Secondary safety reset of spawning flag and timestamp");
                }
            });

            SetupDataPaths();
            LoadMazeData();

            Puts("[Maze Debug] Plugin initialized - spawning state aggressively reset");
        }

        private void OnServerInitialized()
        {
            // Stop any ongoing entity spawning on server restart/plugin reload
            StopEntitySpawning();

            // TRIPLE SAFETY: Force reset spawning flag again
            lock (_spawnLock)
            {
                _isSpawningEntities = false;
                _lastSpawnStartTime = DateTime.MinValue;
                Puts("[Maze Debug] OnServerInitialized - TRIPLE SAFETY reset of spawning flag and timestamp");
            }

            // Clean up any existing coffin storage entities from previous maze events
            CleanupExistingCoffins();

            if (AwakenVotingSystem == null)
            {
                Puts("AwakenVotingSystem plugin not found; maze events will not be votable.");
            }
            else
            {
                Puts("AwakenVotingSystem plugin found - maze events are available for voting.");
                // Note: AwakenVotingSystem handles maze events through its configuration
                // No need to register individual arenas as it uses a predefined list
            }
        }

        private void CleanupExistingCoffins()
        {
            int removedCount = 0;
            var coffinEntities = BaseNetworkable.serverEntities.OfType<StorageContainer>()
                .Where(entity => entity != null && !entity.IsDestroyed &&
                       entity.PrefabName.Contains("coffinstorage"))
                .ToList();

            foreach (var coffin in coffinEntities)
            {
                if (coffin != null && !coffin.IsDestroyed)
                {
                    coffin.Kill();
                    removedCount++;
                }
            }

            if (removedCount > 0)
            {
                Puts($"[Maze Cleanup] Removed {removedCount} existing coffin storage entities on server start");
            }
        }

        private void OnServerSave() => SaveMazeData();
        private void Unload()
        {
            StopEntitySpawning();
            CleanupPlugin();
            SaveMazeData();
            _instance = null;
        }



        private void StopEntitySpawning()
        {
            lock (_spawnLock)
            {
                bool wasSpawning = _isSpawningEntities;
                _isSpawningEntities = false; // ALWAYS reset, regardless of previous state
                _lastSpawnStartTime = DateTime.MinValue; // Reset timestamp too

                if (wasSpawning)
                {
                    Puts("[Maze Debug] Entity spawning stopped due to plugin reload/unload");
                }
                else
                {
                    Puts("[Maze Debug] Entity spawning flag and timestamp reset (was already false)");
                }
            }

            // Clear processed players cache
            _processedPlayers.Clear();

            // Additional safety: Reset again after a tiny delay
            timer.Once(0.1f, () =>
            {
                lock (_spawnLock)
                {
                    _isSpawningEntities = false;
                    _lastSpawnStartTime = DateTime.MinValue;
                    Puts("[Maze Debug] StopEntitySpawning - secondary safety reset with timestamp");
                }
            });

            // Log the stop for debugging
            Puts("[Maze Debug] Entity spawning operations halted with aggressive reset");
        }

        private void ForceStartMaze(BasePlayer player, string mazeName)
        {
            // Force reset the spawning state first
            lock (_spawnLock)
            {
                if (_isSpawningEntities)
                {
                    _isSpawningEntities = false;
                    SendReply(player, "🔧 Force reset spawning flag before starting maze");
                    Puts($"[Maze Debug] Force reset spawning flag by {player.displayName} before starting '{mazeName}'");
                }
            }

            // Clear processed players cache
            _processedPlayers.Clear();

            // Check if maze exists
            if (!savedMazes.ContainsKey(mazeName))
            {
                SendReply(player, $"❌ Maze '{mazeName}' not found.");
                return;
            }

            SendReply(player, $"🚀 Force starting maze '{mazeName}' - bypassing all checks");
            Puts($"[Maze Debug] Force starting maze '{mazeName}' by {player.displayName}");

            // Call the test maze method which bypasses event checks AND spawning flag
            RunMaze(player, mazeName, true, true, true, true);
        }

        private void ResetSpawningState(BasePlayer player)
        {
            SendReply(player, "🔄 RESETTING SPAWNING STATE");
            Puts($"[Maze Debug] Spawning state reset initiated by {player.displayName}");

            try
            {
                // Reset spawning flag
                lock (_spawnLock)
                {
                    _isSpawningEntities = false;
                    Puts("[Maze Debug] Spawning flag reset to false");
                }

                // Clear all caches
                _processedPlayers.Clear();
                Puts("[Maze Debug] Cleared processed players cache");

                SendReply(player, "✅ Spawning state has been reset");
                SendReply(player, "🎯 You can now start mazes normally");

            }
            catch (Exception ex)
            {
                Puts($"[Maze Debug] Reset error: {ex.Message}");
                SendReply(player, $"❌ Reset encountered error: {ex.Message}");
            }
        }

        private void CancelCurrentOperations(BasePlayer player)
        {
            try
            {
                int cancelledOperations = 0;

                // AGGRESSIVE: Force reset entity spawning flag multiple times
                lock (_spawnLock)
                {
                    // Force reset the flag regardless of current state
                    bool wasSpawning = _isSpawningEntities;
                    _isSpawningEntities = false;
                    cancelledOperations++;

                    if (wasSpawning)
                    {
                        Puts("[Maze Debug] Entity spawning was active - FORCE CANCELLED by admin command");
                    }
                    else
                    {
                        Puts("[Maze Debug] Entity spawning was not active - FORCE RESET flag anyway");
                    }
                }

                // Double-check and force reset again after a tiny delay
                timer.Once(0.1f, () =>
                {
                    lock (_spawnLock)
                    {
                        _isSpawningEntities = false;
                        Puts("[Maze Debug] Double-check force reset spawning flag");
                    }
                });

                // Cancel all pending teleports
                if (pendingTeleports.Count > 0)
                {
                    foreach (var kvp in pendingTeleports.ToList())
                    {
                        try
                        {
                            kvp.Value.teleportTimer?.Destroy();
                            cancelledOperations++;
                        }
                        catch (Exception ex)
                        {
                            Puts($"[Maze Debug] Error cancelling teleport timer: {ex.Message}");
                        }
                    }
                    pendingTeleports.Clear();
                    Puts($"[Maze Debug] Cancelled {pendingTeleports.Count} pending teleports");
                }

                // Stop timers safely
                try
                {
                    if (eventStatusUITimer != null)
                    {
                        eventStatusUITimer.Destroy();
                        eventStatusUITimer = null;
                        cancelledOperations++;
                        Puts("[Maze Debug] Event status UI timer cancelled");
                    }
                }
                catch (Exception ex)
                {
                    Puts($"[Maze Debug] Error cancelling event status timer: {ex.Message}");
                }

                try
                {
                    if (clanCheckTimer != null)
                    {
                        clanCheckTimer.Destroy();
                        clanCheckTimer = null;
                        cancelledOperations++;
                        Puts("[Maze Debug] Clan check timer cancelled");
                    }
                }
                catch (Exception ex)
                {
                    Puts($"[Maze Debug] Error cancelling clan check timer: {ex.Message}");
                }

                try
                {
                    if (killDomeShrinkTimer != null)
                    {
                        killDomeShrinkTimer.Destroy();
                        killDomeShrinkTimer = null;
                        cancelledOperations++;
                        Puts("[Maze Debug] Kill dome shrink timer cancelled");
                    }
                }
                catch (Exception ex)
                {
                    Puts($"[Maze Debug] Error cancelling kill dome shrink timer: {ex.Message}");
                }

                // Clear caches
                _processedPlayers.Clear();

                // Force end any active event safely
                if (activeMazeSession != null && !eventEnding)
                {
                    try
                    {
                        eventEnding = true;
                        activeMazeSession = null;
                        cancelledOperations++;
                        Puts("[Maze Debug] Active maze session cancelled");
                    }
                    catch (Exception ex)
                    {
                        Puts($"[Maze Debug] Error cancelling maze session: {ex.Message}");
                    }
                }

                SendReply(player, $"🛑 Cancelled {cancelledOperations} operations");
                SendReply(player, "✅ All maze operations have been safely cancelled");
                Puts($"[Maze Debug] All operations cancelled by {player.displayName} - {cancelledOperations} operations stopped");
            }
            catch (Exception ex)
            {
                SendReply(player, "❌ Error during cancellation - check console");
                Puts($"[Maze Debug] Critical error in CancelCurrentOperations: {ex.Message}");
                Puts($"[Maze Debug] Stack trace: {ex.StackTrace}");
            }
        }

        private void CleanupPlugin()
        {
            foreach (var kvp in pendingTeleports)
            {
                kvp.Value.teleportTimer?.Destroy();
            }
            pendingTeleports.Clear();
            clanCheckTimer?.Destroy();
            clanCheckTimer = null;
            killDomeShrinkTimer?.Destroy();
            killDomeShrinkTimer = null;
            eventStatusUITimer?.Destroy();
            eventStatusUITimer = null;
            foreach (BasePlayer player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "mazeCountdown");
                CuiHelper.DestroyUi(player, "mazeFinal");
                // Remove maze UI on plugin unload
                CuiHelper.DestroyUi(player, "awakenMazeUI");
            }
            if (activeMazeSession != null)
            {
                foreach (BaseEntity ent in activeMazeSession.SpawnedEntities.ToArray())
                {
                    if (ent != null && !ent.IsDestroyed)
                        ent.Kill();
                }
                activeMazeSession.SpawnedEntities.Clear();
            }
            foreach (BaseEntity ent in testSpawnedEntities.ToArray())
            {
                if (ent != null && !ent.IsDestroyed)
                    ent.Kill();
            }
            testSpawnedEntities.Clear();

            spawnedDoorEntities.Clear();
            foreach (BaseEntity ent in entryDomes.ToArray())
            {
                if (ent != null && !ent.IsDestroyed)
                    ent.Kill();
            }
            entryDomes.Clear();
            foreach (BaseEntity ent in killDomes.ToArray())
            {
                if (ent != null && !ent.IsDestroyed)
                    ent.Kill();
            }
            killDomes.Clear();
            playersInMaze.Clear();
            activeMazeSession = null;
            isEditing = false;
            SaveMazeData();
            playersInMaze.Clear();
            eventStatusUITimer?.Destroy();
            eventStatusUITimer = null;

            if (AwakenVotingSystem == null)
            {
                Puts("AwakenVotingSystem plugin not found - maze completion not reported.");
            }
            else
            {
                Puts("Notifying AwakenVotingSystem of maze completion");
                // Note: AwakenVotingSystem handles event completion automatically
            }
            trackedM249ItemUids.Clear();
        }

        private void OnEntitySpawned(BaseEntity entity)
        {
            if (activeMazeSession == null || entity == null || !isEditing)
                return;
            if (string.IsNullOrEmpty(entity.PrefabName))
                return;
            if (entity.PrefabName.ToLower().Contains("oil_barrel"))
                return;
            Vector3 center = activeMazeSession.MazeCenter.ToVector3();
            Vector3 offset = entity.transform.position - center;
            if (offset.magnitude > config.RecordRadius)
                return;
            MazeObject mObj = new MazeObject
            {
                Prefab = entity.PrefabName,
                LocalPosition = new SerializableVector3(offset),
                LocalRotation = new SerializableVector3(entity.transform.rotation.eulerAngles),
                LocalEulerAngles = new SerializableVector3(entity.transform.eulerAngles)
            };
            activeMazeSession.MazeObjects.Add(mObj);
            activeMazeSession.SpawnedEntities.Add(entity);
        }
        private void OnEntityKill(BaseEntity entity)
        {
            if (activeMazeSession == null || entity == null)
                return;
            if (!isEditing)
            {
                activeMazeSession.SpawnedEntities.Remove(entity);
                return;
            }
            if (string.IsNullOrEmpty(entity.PrefabName))
                return;
            if (entity.PrefabName.ToLower().Contains("oil_barrel"))
                return;
            Vector3 center = activeMazeSession.MazeCenter.ToVector3();
            Vector3 offset = entity.transform.position - center;
            if (offset.magnitude > config.RecordRadius)
                return;
            MazeObject target = null;
            foreach (var mObj in activeMazeSession.MazeObjects)
            {
                if (mObj.Prefab.Equals(entity.PrefabName, StringComparison.OrdinalIgnoreCase) &&
                    Vector3.Distance(mObj.LocalPosition.ToVector3(), offset) < 0.1f)
                {
                    target = mObj;
                    break;
                }
            }
            if (target != null)
                activeMazeSession.MazeObjects.Remove(target);
            activeMazeSession.SpawnedEntities.Remove(entity);
        }
        private void OnItemCraftFinished(ItemCraftTask task, Item item)
        {
            BasePlayer player = item.parent?.playerOwner;
            if (player == null)
                return;
            if (activeMazeSession == null || !playersInMaze.Contains(player.userID))
                return;
            if (config.ItemsToRemove == null || config.ItemsToRemove.Count == 0)
                return;
            if (config.ItemsToRemove.Any(x => string.Equals(x, item.info.shortname, StringComparison.OrdinalIgnoreCase)))
            {
                item.Remove();
            }
        }
        #endregion

        #region Command Blocking Hook
        object OnPlayerCommand(BasePlayer player, string command, string[] args)
        {
            if (activeMazeSession != null)
            {
                var center = activeMazeSession.MazeCenter.ToVector3();
                var radius = config.EntryDomeRadius;

                if (Vector3.Distance(player.transform.position, center) <= radius)
                {
                    SendReply(player, lang.GetMessage("CommandBlocked", this));
                    return false;
                }
            }

            return null;
        }

        #endregion

        #region TPR Hook
        object OnTeleportRequested(BasePlayer target, BasePlayer player)
        {
            if (playersInMaze.Contains(target.userID))
            {
                SendReply(player, lang.GetMessage("TPRCancelled", this));
                return true;
            }
            return null;
        }
        #endregion

        #region Commands
        [ChatCommand("maze")]
        private void MazeCommand(BasePlayer player, string command, string[] args)
        {
            if (args.Length < 1)
            {
                if (activeMazeSession != null && !eventEnding)
                {
                    if (pendingTeleports.ContainsKey(player.userID))
                    {
                        SendReply(player, "Teleport already in progress.");
                        return;
                    }
                    
                    Vector3 mazeCenter = activeMazeSession.MazeCenter.ToVector3();
                    Vector3 targetPos = mazeCenter;
                    
                    if (activeMazeSession.SpawnPoints != null && activeMazeSession.SpawnPoints.Count > 0)
                    {
                        int index = UnityEngine.Random.Range(0, activeMazeSession.SpawnPoints.Count);
                        targetPos = mazeCenter + activeMazeSession.SpawnPoints[index].ToVector3();
                    }
                    
                    SendReply(player, $"Teleporting in {config.TeleportTimer} seconds. You can move freely; use /tpc to cancel teleport.");
                    Puts($"[Maze Debug] Player {player.displayName} requested teleport to active maze '{activeMazeSession.Name}'");
                    
                    TeleportInfo info = new TeleportInfo
                    {
                        startPos = player.transform.position,
                        targetPos = targetPos,
                        teleportTimer = timer.Once(config.TeleportTimer, () =>
                        {
                            if (pendingTeleports.ContainsKey(player.userID))
                            {
                                // Make sure player is still valid and maze is still active
                                if (player != null && player.IsConnected && activeMazeSession != null && !eventEnding)
                                {
                                    TeleportPlayer(player, targetPos);
                                    SendReply(player, "Teleported to a maze spawn point of the active session.");
                                    
                                    // Add player to maze participants list
                                    if (!playersInMaze.Contains(player.userID))
                                    {
                                        playersInMaze.Add(player.userID);
                                        Puts($"[Maze Debug] Added player {player.displayName} to maze participants");
                                    }
                                }
                                pendingTeleports.Remove(player.userID);
                            }
                        })
                    };
                    
                    pendingTeleports[player.userID] = info;
                }
                else
                {
                    SendReply(player, "No active maze session.");
                }
                return;
            }
            string sub = args[0].ToLower();
            switch (sub)
            {
                case "removedoors":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze removedoors <name>");
                        return;
                    }
                    RemoveMazeDoors(player, args[1]);
                    break;
                case "removebuild":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    RemoveBuildObject(player);
                    break;
                case "new":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    CreateNewMaze(player);
                    break;
                case "save":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze save <name>");
                        return;
                    }
                    SaveCurrentMaze(player, args[1]);
                    break;
                case "updatecentre":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze updatecentre <name>");
                        return;
                    }
                    UpdateMazeCentre(player, args[1]);
                    break;
                case "adddoor":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze adddoor <name>");
                        return;
                    }
                    AddDoorSpawn(player, args[1]);
                    break;
                case "test":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze test <name>");
                        return;
                    }
                    RunMaze(player, args[1], false, true);
                    break;
                case "fs":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze fs <name>");
                        return;
                    }
                    ForceStartEvent(player, args[1]);
                    break;
                case "start":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze start <name>");
                        return;
                    }
                    StartMazeEvent(player, args[1]);
                    break;
                case "end":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    ForceEndEvent(player);
                    break;
                case "setspawn":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze setspawn <mazename>");
                        return;
                    }
                    SetSpawnPoint(player, args[1]);
                    break;
                case "remove":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze remove <mazename>");
                        return;
                    }
                    RemoveMaze(player, args[1]);
                    break;
                case "setup":
                    ShowSetup(player);
                    break;
                case "maze_debuglayers":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    MazeDebugLayersCommand(player, command, args);
                    break;
                case "radius":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    ShowRadiusInfo(player, args);
                    break;
                case "scan":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    ScanExistingEntitiesCommand(player);
                    break;
                case "info":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze info <mazename>");
                        return;
                    }
                    ShowMazeInfo(player, args[1]);
                    break;
                case "list":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    ListSavedMazes(player);
                    break;
                case "checkimage":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    CheckImageCommand(player);
                    break;
                case "resetspawn":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    ResetSpawningState(player);
                    break;
                case "force":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze force <mazename> - Force start maze even if spawning flag is stuck");
                        return;
                    }
                    ForceStartMaze(player, args[1]);
                    break;
                case "bypass":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    if (args.Length < 2)
                    {
                        SendReply(player, "Usage: /maze bypass <mazename> - Completely bypass spawning flag checks");
                        return;
                    }
                    SendReply(player, $"🚀 BYPASSING ALL CHECKS - Force spawning maze '{args[1]}'");
                    Puts($"[Maze Debug] Bypass command used by {player.displayName} for maze '{args[1]}'");
                    RunMaze(player, args[1], true, true, true, true);
                    break;
                case "reset":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    ResetSpawningState(player);
                    break;
                case "clearspawn":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    lock (_spawnLock)
                    {
                        bool wasSpawning = _isSpawningEntities;
                        _isSpawningEntities = false;
                        _lastSpawnStartTime = DateTime.MinValue;
                        SendReply(player, $"🔧 Manually cleared spawning state (was: {wasSpawning})");
                        Puts($"[Maze Debug] Manual spawn state clear by {player.displayName} - was spawning: {wasSpawning}");
                    }
                    break;
                case "status":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    lock (_spawnLock)
                    {
                        var timeSinceSpawn = DateTime.Now - _lastSpawnStartTime;
                        SendReply(player, $"📊 Spawning Status:");
                        SendReply(player, $"   • Is Spawning: {_isSpawningEntities}");
                        SendReply(player, $"   • Last Start: {(_lastSpawnStartTime == DateTime.MinValue ? "Never" : _lastSpawnStartTime.ToString("HH:mm:ss"))}");
                        SendReply(player, $"   • Time Since: {(_lastSpawnStartTime == DateTime.MinValue ? "N/A" : $"{timeSinceSpawn.TotalSeconds:F1}s")}");
                        SendReply(player, $"   • Instance: {(_instance != null ? "Valid" : "NULL")}");
                    }
                    break;
                case "testclan":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }

                    string playerClan = GetPlayerClan(player);
                    bool isInClan = IsPlayerInClan(player);

                    SendReply(player, $"🔍 Clan Test Results:");
                    SendReply(player, $"   • Your Clan Tag: '{playerClan}'");
                    SendReply(player, $"   • Is In Clan: {isInClan}");
                    SendReply(player, $"   • AwakenClans Plugin: {(AwakenClans != null ? "Found" : "NOT FOUND")}");

                    if (activeMazeSession != null)
                    {
                        var clanStatus = GetClanStatusInMaze();
                        SendReply(player, $"   • Unique Clans in Maze: {clanStatus.AliveClanCount}");
                        SendReply(player, $"   • Players in Maze: {clanStatus.TotalPlayersInMaze}");
                        SendReply(player, $"   • Alive Clans: [{string.Join(", ", clanStatus.AliveClans)}]");
                    }

                    // Test all possible AwakenClans API methods
                    if (AwakenClans != null)
                    {
                        try
                        {
                            SendReply(player, $"🔧 API Method Tests:");

                            // Test GetClan (primary method)
                            var clanObj = AwakenClans.Call("GetClan", player.userID);
                            SendReply(player, $"   • GetClan: {(clanObj != null ? "Object found" : "NULL")}");

                            if (clanObj != null)
                            {
                                var clanType = clanObj.GetType();
                                SendReply(player, $"   • Clan Object Type: {clanType.Name}");

                                // List all properties
                                var properties = clanType.GetProperties();
                                SendReply(player, $"   • Available Properties: {string.Join(", ", properties.Select(p => p.Name))}");

                                // Try to get specific properties
                                var clanTagProp = clanType.GetProperty("ClanTag");
                                if (clanTagProp != null)
                                {
                                    var tagValue = clanTagProp.GetValue(clanObj);
                                    SendReply(player, $"   • ClanTag Property: '{tagValue}'");
                                }

                                var clanNameProp = clanType.GetProperty("ClanName");
                                if (clanNameProp != null)
                                {
                                    var nameValue = clanNameProp.GetValue(clanObj);
                                    SendReply(player, $"   • ClanName Property: '{nameValue}'");
                                }
                            }

                            // Test direct API methods
                            var clanTag = AwakenClans.Call("GetClanTag", player.userID);
                            SendReply(player, $"   • GetClanTag: '{clanTag}'");

                            var clanName = AwakenClans.Call("GetClanName", player.userID);
                            SendReply(player, $"   • GetClanName: '{clanName}'");

                            var isInClanAPI = AwakenClans.Call("IsInClan", player.userID);
                            SendReply(player, $"   • IsInClan API: {isInClanAPI}");

                            var clanMembers = AwakenClans.Call("GetClanMemebers", player.userID);
                            SendReply(player, $"   • GetClanMemebers: {(clanMembers != null ? "Data found" : "NULL")}");

                            // Test alternative method names
                            var clanMembersAlt = AwakenClans.Call("GetClanMembers", player.userID);
                            SendReply(player, $"   • GetClanMembers (alt): {(clanMembersAlt != null ? "Data found" : "NULL")}");
                        }
                        catch (Exception ex)
                        {
                            SendReply(player, $"   • API Test Error: {ex.Message}");
                        }
                    }
                    break;
                case "cancel":
                    if (!player.IsAdmin)
                    {
                        SendReply(player, "Not authorized.");
                        return;
                    }
                    CancelCurrentOperations(player);
                    break;
                default:
                    SendReply(player, "Unknown subcommand. Use /maze setup to see available commands.");
                    break;
            }
        }

        private void CheckImageCommand(BasePlayer player)
        {
            SendReply(player, $"Config logo URL: {config?.UI?.LogoImageURL ?? "null"}");

            if (!string.IsNullOrEmpty(config?.UI?.LogoImageURL))
            {
                SendReply(player, "✅ Logo URL is configured and will be loaded directly");
                SendReply(player, "No ImageLibrary needed - using direct URL method");
            }
            else
            {
                SendReply(player, "❌ No logo URL configured in config");
            }
        }

        [ChatCommand("tpc")]
        private void TeleportCancelCommand(BasePlayer player, string command, string[] args)
        {
            if (pendingTeleports.ContainsKey(player.userID))
            {
                pendingTeleports[player.userID].teleportTimer.Destroy();
                pendingTeleports.Remove(player.userID);
                SendReply(player, lang.GetMessage("TeleportCancelledByCommand", this));
            }
            else
            {
                SendReply(player, "No pending teleport to cancel.");
            }
        }
        #endregion

        #region Maze Commands
        [ChatCommand("maze_debuglayers")]
        private void MazeDebugLayersCommand(BasePlayer player, string command, string[] args)
        {
            if (!player.IsAdmin)
            {
                SendReply(player, "Not authorized.");
                return;
            }

            float radius = 10f;
            if (args.Length > 0 && float.TryParse(args[0], out float customRadius))
            {
                radius = customRadius;
            }

            List<BaseEntity> foundEntities = new List<BaseEntity>();
            foreach (var serverEntity in BaseNetworkable.serverEntities)
            {
                if (serverEntity is BaseEntity baseEntity)
                {
                    if (baseEntity != null && baseEntity.transform != null && baseEntity.gameObject != null)
                    {
                        if (Vector3.Distance(player.transform.position, baseEntity.transform.position) <= radius)
                        {
                            foundEntities.Add(baseEntity);
                        }
                    }
                }
            }

            if (foundEntities.Count == 0)
            {
                SendReply(player, $"No entities found within {radius}m (iterated all server entities).");
                return;
            }

            Puts($"--- Layer Debug (Iterated Server Entities) --- Radius: {radius}m around {player.displayName} ---");
            foreach (var ent in foundEntities)
            {
                if (ent == null || ent.gameObject == null) continue;
                Puts($"Prefab: {ent.PrefabName}, Layer Name: {LayerMask.LayerToName(ent.gameObject.layer)}, Layer Int: {ent.gameObject.layer}, Distance: {Vector3.Distance(player.transform.position, ent.transform.position):F2}m");
            }
            Puts("--- End Layer Debug ---");
            SendReply(player, $"Logged layer info for {foundEntities.Count} entities to server console. Check for dropped M249s and PlayerCorpse.");
        }

        private void RemoveMazeDoors(BasePlayer player, string name)
        {
            MazeArena maze = null;
            if (activeMazeSession != null && activeMazeSession.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
                maze = activeMazeSession;
            else if (savedMazes.ContainsKey(name))
                maze = savedMazes[name];
            else
            {
                SendReply(player, $"Maze '{name}' not found.");
                return;
            }
            maze.DoorSpawns.Clear();
            SaveMazeData();
            SendReply(player, $"All door spawns have been removed for maze '{name}'.");
        }
        private void RemoveBuildObject(BasePlayer player)
        {
            if (activeMazeSession == null || !isEditing)
            {
                SendReply(player, "You must be in build mode to remove build objects. Use /maze new first.");
                return;
            }
            Vector3 mazeCenter = activeMazeSession.MazeCenter.ToVector3();
            Vector3 playerLocal = player.transform.position - mazeCenter;
            float closestDist = float.MaxValue;
            int bestIndex = -1;
            for (int i = 0; i < activeMazeSession.MazeObjects.Count; i++)
            {
                MazeObject obj = activeMazeSession.MazeObjects[i];
                float dist = Vector3.Distance(obj.LocalPosition.ToVector3(), playerLocal);
                if (dist < closestDist)
                {
                    closestDist = dist;
                    bestIndex = i;
                }
            }
            if (bestIndex == -1 || closestDist > 2.0f)
            {
                SendReply(player, "No build object found near your position.");
                return;
            }
            MazeObject removedObj = activeMazeSession.MazeObjects[bestIndex];
            activeMazeSession.MazeObjects.RemoveAt(bestIndex);
            Vector3 targetWorldPos = mazeCenter + removedObj.LocalPosition.ToVector3();
            BaseEntity targetEntity = null;
            foreach (var ent in activeMazeSession.SpawnedEntities)
            {
                if (ent != null && !ent.IsDestroyed &&
                    ent.PrefabName.Equals(removedObj.Prefab, StringComparison.OrdinalIgnoreCase) &&
                    Vector3.Distance(ent.transform.position, targetWorldPos) < 0.5f)
                {
                    targetEntity = ent;
                    break;
                }
            }
            if (targetEntity != null)
            {
                targetEntity.Kill();
                activeMazeSession.SpawnedEntities.Remove(targetEntity);
            }
            SaveMazeData();
            SendReply(player, "Build object removed from the maze.");
        }
        private void CreateNewMaze(BasePlayer player)
        {
            if (activeMazeSession != null)
            {
                SendReply(player, "A maze session is already active. End or save it before starting a new one.");
                return;
            }
            activeMazeSession = new MazeArena
            {
                OwnerID = player.userID,
                MazeCenter = new SerializableVector3(player.transform.position)
            };
            isEditing = true;

            // Scan for existing entities within the radius and add them to the maze
            int existingEntities = ScanExistingEntities(player.transform.position);

            string prefabPath = config.MazeCenterPrefabPath;
            BaseEntity barrel = GameManager.server.CreateEntity(prefabPath, player.transform.position, Quaternion.identity);
            if (barrel != null)
            {
                barrel.Spawn();
                activeMazeSession.SpawnedEntities.Add(barrel);
                player.SendConsoleCommand("ddraw.sphere",
                    100f,
                    "1 0 0 1",
                    player.transform.position,
                    config.RecordRadius,
                    100f
                );
                SendReply(player, $"Maze session started. Found {existingEntities} existing entities within {config.RecordRadius}m radius. An oil barrel spawned at center and a debug sphere drawn for 100f seconds.");
            }
            else
            {
                SendReply(player, "Failed to spawn oil barrel.");
            }
        }

        private int ScanExistingEntities(Vector3 center)
        {
            if (activeMazeSession == null) return 0;

            int entitiesFound = 0;
            foreach (var serverEntity in BaseNetworkable.serverEntities)
            {
                if (serverEntity is BaseEntity entity && entity != null && !entity.IsDestroyed)
                {
                    // Skip certain entity types that shouldn't be saved
                    if (ShouldSkipEntity(entity)) continue;

                    Vector3 offset = entity.transform.position - center;
                    if (offset.magnitude <= config.RecordRadius)
                    {
                        MazeObject mObj = new MazeObject
                        {
                            Prefab = entity.PrefabName,
                            LocalPosition = new SerializableVector3(offset),
                            LocalRotation = new SerializableVector3(entity.transform.rotation.eulerAngles),
                            LocalEulerAngles = new SerializableVector3(entity.transform.eulerAngles)
                        };
                        activeMazeSession.MazeObjects.Add(mObj);
                        activeMazeSession.SpawnedEntities.Add(entity);
                        entitiesFound++;
                    }
                }
            }

            Puts($"Scanned and found {entitiesFound} existing entities within {config.RecordRadius}m radius for maze session.");
            return entitiesFound;
        }

        private bool ShouldSkipEntity(BaseEntity entity)
        {
            if (string.IsNullOrEmpty(entity.PrefabName)) return true;

            // Skip players
            if (entity is BasePlayer) return true;

            // Skip certain entity types that shouldn't be part of mazes
            string prefabLower = entity.PrefabName.ToLower();

            // Skip oil barrels (used as center markers)
            if (prefabLower.Contains("oil_barrel")) return true;

            // Skip corpses and bags
            if (prefabLower.Contains("corpse") || prefabLower.Contains("item_drop")) return true;

            // Skip animals and NPCs
            if (prefabLower.Contains("bear") || prefabLower.Contains("wolf") || prefabLower.Contains("boar") ||
                prefabLower.Contains("stag") || prefabLower.Contains("chicken") || prefabLower.Contains("horse")) return true;

            // Skip vehicles (unless you want them in mazes)
            if (prefabLower.Contains("minicopter") || prefabLower.Contains("scrapheli") ||
                prefabLower.Contains("boat") || prefabLower.Contains("car")) return true;

            // Skip resource nodes
            if (prefabLower.Contains("ore_") || prefabLower.Contains("stone-ore") ||
                prefabLower.Contains("metal-ore") || prefabLower.Contains("sulfur-ore")) return true;

            // Skip trees and other natural resources
            if (prefabLower.Contains("tree") || prefabLower.Contains("bush") || prefabLower.Contains("hemp")) return true;

            // Skip dropped items
            if (prefabLower.Contains("item_drop") || prefabLower.Contains("droppeditem")) return true;

            return false;
        }

        private void SaveCurrentMaze(BasePlayer player, string name)
        {
            if (activeMazeSession == null)
            {
                SendReply(player, "No active maze session to save.");
                return;
            }
            activeMazeSession.Name = name;
            MazeArena mazeToSave = new MazeArena
            {
                Name = activeMazeSession.Name,
                OwnerID = activeMazeSession.OwnerID,
                MazeCenter = activeMazeSession.MazeCenter,
                MazeObjects = new List<MazeObject>(activeMazeSession.MazeObjects),
                SpawnPoints = new List<SerializableVector3>(activeMazeSession.SpawnPoints),
                DoorSpawns = new List<DoorSpawn>(activeMazeSession.DoorSpawns)
            };
            savedMazes[name] = mazeToSave;
            foreach (var ent in new List<BaseEntity>(activeMazeSession.SpawnedEntities))
            {
                if (ent != null && !ent.IsDestroyed)
                    ent.Kill();
            }
            SendReply(player, $"Maze '{name}' saved with {mazeToSave.MazeObjects.Count} objects.");
            activeMazeSession = null;
            isEditing = false;
            SaveMazeData();
            playersInMaze.Clear();
            eventStatusUITimer?.Destroy();
            eventStatusUITimer = null;

            if (AwakenVotingSystem == null)
            {
                Puts("AwakenVotingSystem plugin not found - maze arena not registered for voting.");
            }
            else
            {
                Puts($"AwakenVotingSystem plugin found - maze '{name}' is available for voting.");
                // Note: AwakenVotingSystem handles maze events through its configuration
                // No need to register individual arenas as it uses a predefined list
            }
        }

        private void UpdateMazeCentre(BasePlayer player, string name)
        {
            if (!savedMazes.ContainsKey(name))
            {
                SendReply(player, $"Maze '{name}' not found.");
                return;
            }
            MazeArena maze = savedMazes[name];
            maze.MazeCenter = new SerializableVector3(player.transform.position);
            SaveMazeData();
            SendReply(player, $"Maze '{name}' center updated to your current position.");
        }
        private void AddDoorSpawn(BasePlayer player, string name)
        {
            MazeArena maze = null;
            if (activeMazeSession != null && activeMazeSession.Name.Equals(name, StringComparison.OrdinalIgnoreCase))
                maze = activeMazeSession;
            else if (savedMazes.ContainsKey(name))
                maze = savedMazes[name];
            else
            {
                SendReply(player, $"Maze '{name}' not found.");
                return;
            }
            Vector3 mazeCenter = maze.MazeCenter.ToVector3();
            Vector3 localPos = player.transform.position - mazeCenter;
            float threshold = 1.0f;
            foreach (var existingDoor in maze.DoorSpawns)
            {
                if (Vector3.Distance(existingDoor.LocalPosition.ToVector3(), localPos) < threshold)
                {
                    SendReply(player, "A door spawn is already near this location.");
                    return;
                }
            }
            Quaternion doorRotation = Quaternion.LookRotation((player.transform.position - mazeCenter).normalized);
            DoorSpawn door = new DoorSpawn
            {
                Prefab = "assets/content/structures/interactive_garage_door/sliding_blast_door.prefab",
                LocalPosition = new SerializableVector3(localPos),
                LocalRotation = new SerializableVector3(doorRotation.eulerAngles)
            };
            maze.DoorSpawns.Add(door);
            SaveMazeData();
            SendReply(player, $"Door spawn added for maze '{name}'.");
        }

        private void RunMaze(BasePlayer player, string name, bool spawnZones, bool spawnDoors, bool isTestMode = true, bool bypassFlag = false)
        {
            if (!bypassFlag)
            {
                lock (_spawnLock)
                {
                    if (_isSpawningEntities)
                    {
                        if (player != null) SendReply(player, "Maze is already spawning, please wait...");
                        return;
                    }
                    _isSpawningEntities = true;
                }
            }

            try
            {
                if (!savedMazes.ContainsKey(name))
                {
                    if (player != null) SendReply(player, $"Maze '{name}' not found.");
                    Puts($"[Maze Debug] Maze '{name}' not found in saved mazes.");
                    if (!bypassFlag) _isSpawningEntities = false;
                    return;
                }

                MazeArena maze = isTestMode ? savedMazes[name] : activeMazeSession;
                if (maze == null)
                {
                    if (player != null) SendReply(player, $"Maze data is null for '{name}'.");
                    Puts($"[Maze Debug] Maze data is null for '{name}'.");
                    if (!bypassFlag) _isSpawningEntities = false;
                    return;
                }

                // Start optimized entity spawning
                SpawnMazeEntitiesOptimized(player, name, maze, spawnZones, spawnDoors, isTestMode, bypassFlag);
            }
            catch (Exception ex)
            {
                Puts($"[Maze Debug] Exception in RunMaze setup: {ex.Message}");
                if (player != null) SendReply(player, "Error starting maze - check console for details.");
                if (!bypassFlag) _isSpawningEntities = false;
            }
        }

        private void SpawnMazeEntitiesOptimized(BasePlayer player, string name, MazeArena maze, bool spawnZones, bool spawnDoors, bool isTestMode, bool bypassFlag = false)
        {
            lock (_spawnLock)
            {
                if (!bypassFlag && _isSpawningEntities)
                {
                    // Check if spawning has been stuck for more than 60 seconds
                    var timeSinceLastSpawn = DateTime.Now - _lastSpawnStartTime;
                    if (timeSinceLastSpawn.TotalSeconds > 60)
                    {
                        Puts($"[Maze Debug] Spawning has been stuck for {timeSinceLastSpawn.TotalSeconds:F1} seconds - AUTO CLEARING");
                        _isSpawningEntities = false;
                        _lastSpawnStartTime = DateTime.MinValue;
                    }
                    else
                    {
                        if (player != null)
                        {
                            SendReply(player, $"Entity spawning in progress for {timeSinceLastSpawn.TotalSeconds:F0}s. Please wait.");
                            SendReply(player, "Use '/maze cancel' to force stop all operations if needed.");
                            SendReply(player, "Or use '/maze force <mazename>' to bypass this check.");
                        }
                        Puts($"[Maze Debug] Spawn request for '{name}' blocked - spawning in progress for {timeSinceLastSpawn.TotalSeconds:F1}s");
                        return;
                    }
                }

                // Check if plugin is being unloaded/reloaded
                if (_instance == null)
                {
                    if (player != null) SendReply(player, "Cannot spawn entities - plugin is being reloaded.");
                    Puts("[Maze Debug] Spawn request rejected - plugin instance is null");
                    return;
                }

                if (bypassFlag)
                {
                    Puts($"[Maze Debug] BYPASSING spawning flag check for '{name}' - force spawn initiated");
                    if (player != null) SendReply(player, "🚀 Bypassing spawning flag - force spawning entities");
                }

                if (!_isSpawningEntities)
                {
                    _isSpawningEntities = true;
                    _lastSpawnStartTime = DateTime.Now;
                    Puts($"[Maze Debug] Starting entity spawning for '{name}' - flag set to true at {_lastSpawnStartTime:HH:mm:ss}");
                }
            }

            // Add a safety timer to reset the flag if something goes wrong
            Timer safetyTimer = timer.Once(30f, () =>
            {
                if (_isSpawningEntities)
                {
                    _isSpawningEntities = false;
                    Puts("[Maze Debug] Safety timer reset spawning flag after 30 seconds");
                    if (player != null) SendReply(player, "⚠️ Entity spawning timed out and was reset");
                }
            });

            Vector3 center = maze.MazeCenter.ToVector3();

            // Spawn center entity first
            BaseEntity centerEntity = GameManager.server.CreateEntity(config.MazeCenterPrefabPath, center, Quaternion.identity);
            if (centerEntity != null)
            {
                centerEntity.Spawn();
                if (isTestMode)
                {
                    testSpawnedEntities.Add(centerEntity);
                }
                else if (activeMazeSession != null && activeMazeSession.SpawnedEntities != null)
                {
                    activeMazeSession.SpawnedEntities.Add(centerEntity);
                }
            }

            // Use timer-based batched spawning for better performance
            var mazeObjects = new List<MazeObject>(maze.MazeObjects);
            int totalObjects = mazeObjects.Count;
            int spawnedCount = 0;
            int failedCount = 0;
            int currentIndex = 0;
            int batchSize = 15; // Increased batch size for better performance

            Timer spawnTimer = null;
            spawnTimer = timer.Every(0.1f, () =>
            {
                int batchEnd = 0;
                try
                {
                    // Check if plugin is being unloaded or spawning should stop (unless bypassing)
                    if (_instance == null || (!bypassFlag && !_isSpawningEntities))
                    {
                        spawnTimer?.Destroy();
                        safetyTimer?.Destroy();
                        if (!bypassFlag) _isSpawningEntities = false;
                        Puts("[Maze Debug] Entity spawning interrupted - plugin unloading or spawn stopped");
                        return;
                    }

                    batchEnd = Math.Min(currentIndex + batchSize, totalObjects);

                    for (int i = currentIndex; i < batchEnd; i++)
                    {
                        try
                        {
                            var obj = mazeObjects[i];

                            // Validate object data
                            if (obj == null || string.IsNullOrEmpty(obj.Prefab))
                            {
                                failedCount++;
                                Puts($"[Maze Debug] Invalid maze object at index {i}");
                                continue;
                            }

                            Vector3 spawnPos = center + new Vector3(obj.LocalPosition.x, obj.LocalPosition.y, obj.LocalPosition.z);
                            Quaternion spawnRot = Quaternion.Euler(obj.LocalEulerAngles.x, obj.LocalEulerAngles.y, obj.LocalEulerAngles.z);

                            // Validate spawn position
                            if (!IsValidPosition(spawnPos))
                            {
                                failedCount++;
                                Puts($"[Maze Debug] Invalid spawn position for {obj.Prefab}: {spawnPos}");
                                continue;
                            }

                            BaseEntity ent = GameManager.server.CreateEntity(obj.Prefab, spawnPos, spawnRot);
                            if (ent != null)
                            {
                                ent.Spawn();
                                if (isTestMode)
                                {
                                    testSpawnedEntities.Add(ent);
                                }
                                else if (activeMazeSession != null && activeMazeSession.SpawnedEntities != null)
                                {
                                    activeMazeSession.SpawnedEntities.Add(ent);
                                }
                                spawnedCount++;
                            }
                            else
                            {
                                failedCount++;
                                Puts($"[Maze Debug] Failed to create entity: {obj.Prefab} at position {spawnPos}");
                            }
                        }
                        catch (Exception ex)
                        {
                            failedCount++;
                            Puts($"[Maze Debug] Exception spawning entity at index {i}: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    spawnTimer?.Destroy();
                    safetyTimer?.Destroy();
                    if (!bypassFlag) _isSpawningEntities = false;
                    Puts($"[Maze Debug] Critical error in spawn timer: {ex.Message}");
                    Puts($"[Maze Debug] Stack trace: {ex.StackTrace}");
                    if (player != null) SendReply(player, "❌ Entity spawning failed - check console for details");
                    return;
                }

                currentIndex = batchEnd;

                // Check if we're done spawning
                if (currentIndex >= totalObjects)
                {
                    spawnTimer?.Destroy();

                    if (failedCount > 0)
                    {
                        Puts($"[Maze Debug] Spawning summary for '{name}': {spawnedCount} successful, {failedCount} failed out of {totalObjects} total objects");
                    }

                    // Handle doors and zones after spawning is complete
                    timer.Once(0.2f, () =>
                    {
                        try
                        {
                            // Check if plugin is still loaded
                            if (_instance == null)
                            {
                                _isSpawningEntities = false;
                                safetyTimer?.Destroy();
                                Puts("[Maze Debug] Spawn completion callback skipped - plugin unloaded");
                                return;
                            }

                            if (spawnDoors)
                            {
                                SpawnDoorSpawns(maze);
                            }

                            if (spawnZones)
                            {
                                CreateEntryDome(center, config.EntryDomeRadius);
                                eventCenter = center;
                            }

                            string msg = spawnZones ? $"Maze '{name}' tested with zones spawned." : $"Maze '{name}' tested.";
                            if (player != null) SendReply(player, msg);

                            // Reset the spawning flag and destroy safety timer (only if not bypassing)
                            if (!bypassFlag)
                            {
                                _isSpawningEntities = false;
                                Puts($"[Maze Debug] Entity spawning completed successfully for '{name}' - flag reset");
                            }
                            else
                            {
                                Puts($"[Maze Debug] Entity spawning completed successfully for '{name}' - bypass mode, flag not touched");
                            }
                            safetyTimer?.Destroy();
                        }
                        catch (Exception ex)
                        {
                            if (!bypassFlag) _isSpawningEntities = false;
                            safetyTimer?.Destroy();
                            Puts($"[Maze Debug] Error in spawn completion callback: {ex.Message}");
                            if (player != null) SendReply(player, "❌ Error completing maze spawn - check console");
                        }
                    });
                }
            });
        }

        private bool IsValidPosition(Vector3 position)
        {
            try
            {
                // Check for NaN or infinite values
                if (float.IsNaN(position.x) || float.IsNaN(position.y) || float.IsNaN(position.z) ||
                    float.IsInfinity(position.x) || float.IsInfinity(position.y) || float.IsInfinity(position.z))
                {
                    return false;
                }

                // Check for reasonable world bounds (Rust map is typically -4000 to +4000)
                if (Math.Abs(position.x) > 5000 || Math.Abs(position.z) > 5000)
                {
                    return false;
                }

                // Check for reasonable height bounds
                if (position.y < -1000 || position.y > 2000)
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Puts($"[Maze Debug] Error validating position {position}: {ex.Message}");
                return false;
            }
        }

        private void ClearSpawnedDoors()
        {
            string doorPrefab = "assets/content/structures/interactive_garage_door/sliding_blast_door.prefab";

            foreach (var netObj in BaseNetworkable.serverEntities)
            {
                if (netObj is BaseEntity ent &&
                    !ent.IsDestroyed &&
                    string.Equals(ent.PrefabName, doorPrefab, StringComparison.OrdinalIgnoreCase))
                {
                    ent.Kill();
                }
            }
            spawnedDoorEntities.Clear();
        }


        private void StartMazeEvent(BasePlayer player, string name)
        {
             ClearSpawnedDoors();
             trackedM249ItemUids.Clear();

            if (activeMazeSession != null)
            {
                SendReply(player, "Maze event is already active.");
                return;
            }
            if (!savedMazes.ContainsKey(name))
            {
                SendReply(player, $"Maze '{name}' not found.");
                return;
            }

            // Create a copy of the saved maze for the active session to avoid modifying saved data
            var savedMaze = savedMazes[name];
            activeMazeSession = new MazeArena
            {
                Name = savedMaze.Name,
                OwnerID = savedMaze.OwnerID,
                MazeCenter = savedMaze.MazeCenter,
                MazeObjects = new List<MazeObject>(savedMaze.MazeObjects),
                SpawnPoints = new List<SerializableVector3>(savedMaze.SpawnPoints),
                DoorSpawns = new List<DoorSpawn>(savedMaze.DoorSpawns),
                Ended = false,
                SpawnedEntities = new List<BaseEntity>()
            };
            eventEnding = false;

            // Clear previous maze state when new maze starts
            mazeHasEnded = false;
            mazeWinners.Clear();

            // Remove doors from previous maze
            if (spawnedDoorEntities != null && spawnedDoorEntities.Count > 0)
            {
                Puts("[Maze Debug] Removing doors from previous maze");
                PrintToChat("🚪 <color=#ff6b6b>Previous maze doors removed - new maze starting!</color>");
                foreach (BaseEntity doorEnt in spawnedDoorEntities.ToList())
                {
                    if (doorEnt != null && !doorEnt.IsDestroyed)
                        doorEnt.Kill();
                }
                spawnedDoorEntities.Clear();
            }

            // Initialize event systems immediately when event is called
            Vector3 center = activeMazeSession.MazeCenter.ToVector3();
            eventCenter = center;

            // Move any players already in maze area to teleport spots
            MovePlayersAlreadyInMaze();

            // Create entry dome immediately for event bubble
            CreateEntryDome(center, config.EntryDomeRadius);

            // Initialize stats tracking
            eventPlayersParticipated.Clear();
            eventClans.Clear();
            eventKills.Clear();
            eventDeaths.Clear();
            eventPlayerNames.Clear();
            eventM2sWon = 0;

            // Clear players in maze tracking
            playersInMaze.Clear();

            // Start UI timer immediately so players can see event status
            float now = Time.realtimeSinceStartup;
            doorsCloseTimeAbs = now + config.EventNotificationTime + config.DoorOpenAnimDuration + config.DoorOpenHoldDuration + 0.1f;
            mazeEndTimeAbs = now + config.EventTimer;
            doorsAreClosed = false;

            eventStatusUITimer?.Destroy();
            eventStatusUITimer = timer.Every(1f, UpdateEventStatusUI);

            // Clear processed players cache every 30 seconds to allow UI updates
            timer.Every(30f, () => _processedPlayers.Clear());

            SpawnDoorSpawns(activeMazeSession);
            PrintToChat(string.Format(lang.GetMessage("MazeEventStartNotification", this), config.EventNotificationTime));

            timer.Once(config.EventNotificationTime, () =>
            {
                try
                {
                    // Check if activeMazeSession is still valid
                    if (activeMazeSession == null)
                    {
                        Puts("[Maze Debug] activeMazeSession is null in main timer callback - event may have been cancelled");
                        return;
                    }

                    void NotifyMazePlayers(string header, string message, string timerText)
                    {
                        foreach (ulong uid in playersInMaze)
                        {
                            var ply = BasePlayer.FindByID(uid);
                            if (ply != null && ply.IsConnected)
                                ShowMazeNotification(ply, header, message, timerText);
                        }
                    }

                    // SPAWN ENTITIES FIRST - before doors open
                    Puts($"[Maze Debug] Starting maze entities spawn for '{name}' - activeMazeSession is valid");
                    RunMaze(player, name, false, false, false);

                    // Wait for entities to spawn, then open doors
                    timer.Once(2f, () =>
                    {
                        if (activeMazeSession == null)
                        {
                            Puts("[Maze Debug] activeMazeSession is null after entity spawn delay");
                            return;
                        }

                        Puts("[Maze Debug] Maze preparation complete - entities spawned successfully");
                        PrintToChat(lang.GetMessage("MazeEventStarted", this));
                        PrintToChat(lang.GetMessage("DoorsOpening", this));
                        AnimateDoorsOpening();

                        // UI timer already started at event beginning, no need to restart

                        ShowGameTipToAll("MAZE HAS STARTED: TO TELEPORT, USE /maze");

                        timer.Once(config.DoorOpenAnimDuration + config.DoorOpenHoldDuration + 0.1f, () =>
                        {
                            AnimateDoorsClosing();
                            PrintToChat(lang.GetMessage("DoorsClosed", this));
                            doorsAreClosed = true;

                            ShowGameTipToAll("MAZE NOTICE: DOORS CLOSED.. Good luck!");

                            timer.Once(5f, () =>
                            {
                                if (activeMazeSession != null)
                                {
                                    // Entry dome already created at event start, just start clan checking
                                    StartClanCheckTimer();
                                }
                                else
                                {
                                    Puts("[Maze Debug] activeMazeSession is null in 5s timer callback");
                                }
                            });
                        });
                    }); // Close the 2f timer block
                }
                catch (Exception ex)
                {
                    Puts($"[Maze Debug] Exception in maze event timer callback: {ex.Message}");
                    PrintToChat("Error starting maze event - check console for details.");
                }
            });

            timer.Once(config.EventTimer, () =>
            {
                if (activeMazeSession != null && !eventEnding)
                {
                    Puts($"[Maze Debug] Event timer expired, ending maze event for '{name}'");
                    ForceEndEvent(player);
                }
                else
                {
                    Puts($"[Maze Debug] Event timer expired but activeMazeSession is null or event already ending");
                }
            });
        }

        #region Loot Collection Methods

        private int SpawnCoffinGroup(List<Item> items, Vector3 basePosition, float spacing, string groupName)
        {
            if (items == null || items.Count == 0)
            {
                Puts($"[Maze Loot] No items to place in {groupName} coffins");
                return 0;
            }

            List<StorageContainer> spawnedCoffins = new List<StorageContainer>();
            StorageContainer currentCoffin = null;
            int itemsPlaced = 0;
            int currentCoffinItemCount = 0;

            for (int i = 0; i < items.Count; i++)
            {
                Item item = items[i];
                bool placedItem = false;

                // Try to place in current coffin if it has space
                if (currentCoffin != null && !currentCoffin.IsDestroyed &&
                    currentCoffin.inventory != null &&
                    currentCoffinItemCount < config.LootCollection.MaxItemsPerCoffin &&
                    currentCoffin.inventory.CanAccept(item))
                {
                    if (item.MoveToContainer(currentCoffin.inventory))
                    {
                        placedItem = true;
                        currentCoffinItemCount++;
                    }
                }

                // Spawn new coffin if needed
                if (!placedItem)
                {
                    Vector3 coffinPosition = CalculateCoffinPosition(basePosition, spawnedCoffins.Count, spacing);
                    BaseEntity coffinEntity = GameManager.server.CreateEntity("assets/prefabs/misc/halloween/coffin/coffinstorage.prefab", coffinPosition, Quaternion.identity);

                    if (coffinEntity != null)
                    {
                        coffinEntity.Spawn();
                        currentCoffin = coffinEntity.GetComponent<StorageContainer>();

                        if (currentCoffin != null && currentCoffin.inventory != null)
                        {
                            spawnedCoffins.Add(currentCoffin);
                            currentCoffinItemCount = 0;

                            if (item.MoveToContainer(currentCoffin.inventory))
                            {
                                placedItem = true;
                                currentCoffinItemCount++;
                            }
                        }
                        else
                        {
                            if (!coffinEntity.IsDestroyed) coffinEntity.Kill();
                            currentCoffin = null;
                        }
                    }
                }

                if (placedItem)
                {
                    itemsPlaced++;
                }
                else
                {
                    Puts($"[Maze Loot] Failed to place item: {item.info.shortname}");
                    item.Remove(); // Clean up item that couldn't be placed
                }
            }

            Puts($"[Maze Loot] {groupName} coffins: Spawned {spawnedCoffins.Count} coffins with {itemsPlaced} items");
            return itemsPlaced;
        }

        private Vector3 CalculateCoffinPosition(Vector3 basePosition, int coffinIndex, float spacing)
        {
            // Arrange coffins in a spiral pattern for better organization
            if (coffinIndex == 0)
            {
                return basePosition;
            }

            // Create a spiral pattern
            float angle = coffinIndex * 60f * Mathf.Deg2Rad; // 60 degrees between each coffin
            float radius = Mathf.Ceil(coffinIndex / 6f) * spacing; // Increase radius every 6 coffins

            float x = basePosition.x + radius * Mathf.Cos(angle);
            float z = basePosition.z + radius * Mathf.Sin(angle);

            return new Vector3(x, basePosition.y, z);
        }



        #endregion

        #region API Methods for Voting System

        [HookMethod("GetAllMazes")]
        public List<string> GetAllMazes()
        {
            var mazeNames = new List<string>();

            if (savedMazes != null)
            {
                foreach (var kvp in savedMazes)
                {
                    if (!string.IsNullOrEmpty(kvp.Key))
                    {
                        mazeNames.Add(kvp.Key);
                    }
                }
            }

            Puts($"[Maze API] GetAllMazes called - returning {mazeNames.Count} mazes: {string.Join(", ", mazeNames)}");
            return mazeNames;
        }

        [HookMethod("GetMazeInfo")]
        public Dictionary<string, object> GetMazeInfo(string mazeName)
        {
            var info = new Dictionary<string, object>();

            if (savedMazes != null && savedMazes.ContainsKey(mazeName))
            {
                var maze = savedMazes[mazeName];
                if (maze != null)
                {
                    info["Name"] = maze.Name;
                    info["EntityCount"] = maze.MazeObjects?.Count ?? 0;
                    info["SpawnPointCount"] = maze.SpawnPoints?.Count ?? 0;
                    info["DoorSpawnCount"] = maze.DoorSpawns?.Count ?? 0;
                    info["HasCenter"] = maze.MazeCenter != null;

                    Puts($"[Maze API] GetMazeInfo called for '{mazeName}' - maze found");
                    return info;
                }
            }

            Puts($"[Maze API] GetMazeInfo called for '{mazeName}' - maze not found");
            return null;
        }

        [HookMethod("StartMazeEvent")]
        public bool StartMazeEvent(string mazeName)
        {
            try
            {
                Puts($"[Maze API] StartMazeEvent called for '{mazeName}'");

                if (string.IsNullOrEmpty(mazeName))
                {
                    PrintError("[Maze API] StartMazeEvent called with null/empty maze name");
                    return false;
                }

                if (activeMazeSession != null)
                {
                    PrintError($"[Maze API] Cannot start maze '{mazeName}' - another maze session is already active");
                    return false;
                }

                // Check if maze exists
                if (savedMazes == null)
                {
                    PrintError("[Maze API] No maze data available");
                    return false;
                }

                if (!savedMazes.ContainsKey(mazeName))
                {
                    PrintError($"[Maze API] Maze '{mazeName}' not found in saved mazes");
                    return false;
                }

                // Start the maze event
                StartMazeEvent(null, mazeName);
                Puts($"[Maze API] Successfully started maze event '{mazeName}'");
                return true;
            }
            catch (Exception ex)
            {
                PrintError($"[Maze API] Error starting maze event '{mazeName}': {ex.Message}");
                return false;
            }
        }

        [HookMethod("IsMazeActive")]
        public bool IsMazeActive()
        {
            bool isActive = activeMazeSession != null && !eventEnding;
            Puts($"[Maze API] IsMazeActive called - returning {isActive}");
            return isActive;
        }

        [HookMethod("GetActiveMazeName")]
        public string GetActiveMazeName()
        {
            string mazeName = activeMazeSession?.Name ?? "";
            Puts($"[Maze API] GetActiveMazeName called - returning '{mazeName}'");
            return mazeName;
        }

        [HookMethod("EndMazeEvent")]
        public bool EndMazeEvent()
        {
            try
            {
                if (activeMazeSession == null)
                {
                    Puts("[Maze API] EndMazeEvent called but no active maze session");
                    return false;
                }

                string mazeName = activeMazeSession.Name;
                ForceEndEvent(null);
                Puts($"[Maze API] Successfully ended maze event '{mazeName}'");
                return true;
            }
            catch (Exception ex)
            {
                PrintError($"[Maze API] Error ending maze event: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Legacy API Hooks (for backward compatibility)

        [HookMethod("StartEvent")]
        public void StartEvent(string mazeName)
        {
            Puts($"[Maze API] Legacy StartEvent hook called for '{mazeName}'");
            StartMazeEvent(mazeName);
        }

        [HookMethod("StartMazeEventHook")]
        public void StartMazeEventHook(BasePlayer player, string name)
        {
            Puts($"[Maze API] Legacy StartMazeEventHook called for '{name}'");
            StartMazeEvent(player, name);
        }

        #endregion

        private void ForceStartEvent(BasePlayer player, string name)
        {
            StartMazeEvent(player, name);
        }

        private void ForceEndEvent()
        {
            ForceEndEvent(null);
        }

        private void ForceEndEvent(BasePlayer player)
        {
            if (eventEnding || activeMazeSession == null)
                return;
            eventEnding = true;

            eventStatusUITimer?.Destroy(); eventStatusUITimer = null;
            clanCheckTimer?.Destroy(); clanCheckTimer = null;
            killDomeShrinkTimer?.Destroy(); killDomeShrinkTimer = null;

            // Cleanup wall decay system
            CleanupAllDecayingWalls();

            foreach (var kvp in pendingTeleports.ToList())
            {
                kvp.Value.teleportTimer?.Destroy();
                pendingTeleports.Remove(kvp.Key);
            }

            Vector3 mazeCenter = activeMazeSession.MazeCenter.ToVector3();
            float collectionRadius = config.EntryDomeRadius;
            List<BaseEntity> entitiesToKill = new List<BaseEntity>();

            // Enhanced loot collection - collect ALL items, not just M249s
            List<Item> centerCoffinItems = new List<Item>(); // High-value items for center
            List<Item> doorCoffinItems = new List<Item>();   // Regular items for door area
            int totalM249AmountToSpawn = 0;
            List<ItemId> foundAndProcessedTrackedUids = new List<ItemId>();

            // Collect all dropped items in the maze area
            foreach (DroppedItem droppedItemEntity in BaseNetworkable.serverEntities.OfType<DroppedItem>())
            {
                if (droppedItemEntity?.item != null)
                {
                    if (Vector3.Distance(droppedItemEntity.transform.position, mazeCenter) <= collectionRadius)
                    {
                        string itemShortname = droppedItemEntity.item.info.shortname;

                        // Skip excluded items
                        if (config.LootCollection.ExcludedItems.Contains(itemShortname))
                        {
                            if (!entitiesToKill.Contains(droppedItemEntity)) entitiesToKill.Add(droppedItemEntity);
                            continue;
                        }

                        // Handle M249 tracking (legacy support)
                        if (itemShortname == "lmg.m249")
                        {
                            if (trackedM249ItemUids.Contains(droppedItemEntity.item.uid))
                            {
                                totalM249AmountToSpawn += droppedItemEntity.item.amount;
                                foundAndProcessedTrackedUids.Add(droppedItemEntity.item.uid);
                            }
                        }

                        // Collect all items if enabled
                        if (config.LootCollection.CollectAllLoot)
                        {
                            Item itemCopy = ItemManager.CreateByName(itemShortname, droppedItemEntity.item.amount);
                            if (itemCopy != null)
                            {
                                // Copy item condition and other properties
                                if (droppedItemEntity.item.hasCondition)
                                {
                                    itemCopy.condition = droppedItemEntity.item.condition;
                                    itemCopy.maxCondition = droppedItemEntity.item.maxCondition;
                                }

                                // Determine which coffin group this item belongs to
                                if (config.LootCollection.CenterCoffinItems.Contains(itemShortname))
                                {
                                    centerCoffinItems.Add(itemCopy);
                                }
                                else
                                {
                                    doorCoffinItems.Add(itemCopy);
                                }
                            }
                        }

                        if (!entitiesToKill.Contains(droppedItemEntity)) entitiesToKill.Add(droppedItemEntity);
                    }
                }
            }
            foreach (ItemId uid in foundAndProcessedTrackedUids)
            {
                trackedM249ItemUids.Remove(uid);
            }
            if (trackedM249ItemUids.Count > 0)
            {

            }
            trackedM249ItemUids.Clear();
            List<BaseEntity> containerEntitiesInZone = new List<BaseEntity>();
            int layerMask = LayerMask.GetMask("Physics Debris", "Ragdoll");
            Vis.Entities(mazeCenter, collectionRadius, containerEntitiesInZone, layerMask, QueryTriggerInteraction.Ignore);

            foreach (BaseEntity ent in containerEntitiesInZone)
            {
                if (ent == null || ent.IsDestroyed || entitiesToKill.Contains(ent)) continue;

                if (ent is PlayerCorpse corpse)
                {
                    if (corpse.containers != null)
                    {
                        foreach (ItemContainer containerInCorpse in corpse.containers)
                        {
                            if (containerInCorpse?.itemList == null) continue;
                            foreach (Item itemInCorpse in new List<Item>(containerInCorpse.itemList))
                            {
                                string itemShortname = itemInCorpse.info.shortname;

                                // Skip excluded items
                                if (config.LootCollection.ExcludedItems.Contains(itemShortname))
                                {
                                    continue;
                                }

                                // Handle M249 tracking (legacy support)
                                if (itemShortname == "lmg.m249")
                                {
                                    totalM249AmountToSpawn += itemInCorpse.amount;
                                }

                                // Collect all items if enabled
                                if (config.LootCollection.CollectAllLoot)
                                {
                                    Item itemCopy = ItemManager.CreateByName(itemShortname, itemInCorpse.amount);
                                    if (itemCopy != null)
                                    {
                                        // Copy item condition and other properties
                                        if (itemInCorpse.hasCondition)
                                        {
                                            itemCopy.condition = itemInCorpse.condition;
                                            itemCopy.maxCondition = itemInCorpse.maxCondition;
                                        }

                                        // Determine which coffin group this item belongs to
                                        if (config.LootCollection.CenterCoffinItems.Contains(itemShortname))
                                        {
                                            centerCoffinItems.Add(itemCopy);
                                        }
                                        else
                                        {
                                            doorCoffinItems.Add(itemCopy);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!entitiesToKill.Contains(corpse)) entitiesToKill.Add(corpse);
                }
                else if (ent.GetComponent<ItemContainer>() != null)
                {
                    var itemContainerComponent = ent.GetComponent<ItemContainer>();
                    if (itemContainerComponent?.itemList != null)
                    {
                        foreach (Item itemInContainer in new List<Item>(itemContainerComponent.itemList))
                        {
                            string itemShortname = itemInContainer.info.shortname;

                            // Skip excluded items
                            if (config.LootCollection.ExcludedItems.Contains(itemShortname))
                            {
                                continue;
                            }

                            // Handle M249 tracking (legacy support)
                            if (itemShortname == "lmg.m249")
                            {
                                totalM249AmountToSpawn += itemInContainer.amount;
                            }

                            // Collect all items if enabled
                            if (config.LootCollection.CollectAllLoot)
                            {
                                Item itemCopy = ItemManager.CreateByName(itemShortname, itemInContainer.amount);
                                if (itemCopy != null)
                                {
                                    // Copy item condition and other properties
                                    if (itemInContainer.hasCondition)
                                    {
                                        itemCopy.condition = itemInContainer.condition;
                                        itemCopy.maxCondition = itemInContainer.maxCondition;
                                    }

                                    // Determine which coffin group this item belongs to
                                    if (config.LootCollection.CenterCoffinItems.Contains(itemShortname))
                                    {
                                        centerCoffinItems.Add(itemCopy);
                                    }
                                    else
                                    {
                                        doorCoffinItems.Add(itemCopy);
                                    }
                                }
                            }
                        }
                    }
                    if (!entitiesToKill.Contains(ent)) entitiesToKill.Add(ent);
                }
                else
                {
                     if (!entitiesToKill.Contains(ent)) entitiesToKill.Add(ent);
                }
            }
            // Create M249s for legacy tracking
            List<Item> newM249sToPlaceInCoffins = new List<Item>();
            if (totalM249AmountToSpawn > 0)
            {
                for (int i = 0; i < totalM249AmountToSpawn; i++)
                {
                    Item newM2 = ItemManager.CreateByName("lmg.m249", 1);
                    if (newM2 != null)
                    {
                        newM249sToPlaceInCoffins.Add(newM2);
                        centerCoffinItems.Add(newM2); // Add to center coffins
                    }
                    else
                    {
                        Puts("CRITICAL: Failed to spawn a new M249 via ItemManager!");
                    }
                }
            }
            eventM2sWon = newM249sToPlaceInCoffins.Count;

            int killedCount = 0;
            foreach (BaseEntity entityToKill in entitiesToKill.Distinct())
            {
                if (entityToKill != null && !entityToKill.IsDestroyed)
                {
                    entityToKill.Kill();
                    killedCount++;
                }
            }

            // Spawn center coffins for high-value items
            int centerItemsPlaced = SpawnCoffinGroup(centerCoffinItems, mazeCenter, config.LootCollection.CenterCoffinSpacing, "Center");

            // Spawn door coffins for regular items (positioned away from center)
            Vector3 doorCoffinPosition = mazeCenter + new Vector3(config.LootCollection.DoorCoffinDistance, 0, 0);
            int doorItemsPlaced = SpawnCoffinGroup(doorCoffinItems, doorCoffinPosition, config.LootCollection.DoorCoffinSpacing, "Door");

            // Update M249 count for legacy tracking
            eventM2sWon = newM249sToPlaceInCoffins.Count;

            Puts($"[Maze Loot] Placed {centerItemsPlaced} center items and {doorItemsPlaced} door items in coffins");

            if (activeMazeSession != null) activeMazeSession.Ended = true;
            SaveMazeData();

            // Set maze as ended for loot protection
            mazeHasEnded = true;

            DestroyEntryDomes();
            DestroyKillDomes();

            if (activeMazeSession?.SpawnedEntities != null)
            {
                foreach (var ent in activeMazeSession.SpawnedEntities.ToList()) if (ent != null && !ent.IsDestroyed) ent.Kill();
                activeMazeSession.SpawnedEntities.Clear();
            }
            if (testSpawnedEntities != null)
            {
                foreach (var ent in testSpawnedEntities.ToList()) if (ent != null && !ent.IsDestroyed) ent.Kill();
                testSpawnedEntities.Clear();
            }

            // DO NOT remove doors - keep them until next maze is called
            Puts("[Maze Debug] Keeping doors for winner loot access until next maze");
            PrintToChat("🚪 <color=#ffa500>Maze doors will remain open for winners to collect loot!</color>");

            // Clear all game tips and UI
            ClearAllGameTips();

            foreach (BasePlayer p in BasePlayer.activePlayerList.ToList())
            {
                if (p == null || !p.IsConnected) continue;
                CuiHelper.DestroyUi(p, "mazeCountdown");
                CuiHelper.DestroyUi(p, "mazeFinal");
                // Remove maze UI when event ends
                CuiHelper.DestroyUi(p, "awakenMazeUI");
            }

            PrintToChat(lang.GetMessage("ZoneCleanup", this));
            SendDiscordEmbed();

            playersInMaze.Clear();
            activeMazeSession = null;
            isEditing = false;
            eventEnding = false;

            if (AwakenVotingSystem != null)
            {
                Puts("Notifying AwakenVotingSystem of Maze completion");
                // Note: AwakenVotingSystem handles event completion automatically
            }
            Puts("Maze event fully ended and cleaned up.");
        }

        private void SetSpawnPoint(BasePlayer player, string name)
        {
            if (!savedMazes.ContainsKey(name))
            {
                SendReply(player, $"Maze '{name}' not found.");
                return;
            }
            MazeArena maze = savedMazes[name];
            Vector3 relativeSpawnPoint = player.transform.position - maze.MazeCenter.ToVector3();
            maze.SpawnPoints.Add(new SerializableVector3(relativeSpawnPoint));
            SaveMazeData();
            SendReply(player, $"Spawn point added for maze '{name}' (relative to its center).");
        }

        private void RemoveMaze(BasePlayer player, string name)
        {
            if (!savedMazes.ContainsKey(name))
            {
                SendReply(player, $"Maze '{name}' not found.");
                return;
            }
            savedMazes.Remove(name);
            SaveMazeData();
            SendReply(player, $"Maze '{name}' has been removed.");
        }

        private void ShowRadiusInfo(BasePlayer player, string[] args)
        {
            if (args.Length > 1 && float.TryParse(args[1], out float newRadius))
            {
                if (newRadius < 10f || newRadius > 500f)
                {
                    SendReply(player, "Radius must be between 10 and 500 meters.");
                    return;
                }

                config.RecordRadius = newRadius;
                SaveConfig();
                SendReply(player, $"Record radius updated to {newRadius}m. This affects how large an area is saved when creating mazes.");

                // Show visual sphere if in build mode
                if (activeMazeSession != null)
                {
                    player.SendConsoleCommand("ddraw.sphere", 30f, "1 0 0 1", activeMazeSession.MazeCenter.ToVector3(), config.RecordRadius, 30f);
                    SendReply(player, "Visual sphere updated for 30 seconds to show new radius.");
                }
            }
            else
            {
                string message = $"<color=yellow><b>Maze Radius Configuration</b></color>\n\n" +
                               $"<color=#2596be>Current Record Radius:</color> {config.RecordRadius}m\n" +
                               $"<color=#2596be>Current Entry Dome Radius:</color> {config.EntryDomeRadius}m\n\n" +
                               $"<color=orange>Record Radius</color> - Determines how large an area is saved when creating mazes\n" +
                               $"<color=orange>Entry Dome Radius</color> - Visual dome size and event area during maze events\n\n" +
                               $"<color=#2596be>/maze radius <number></color> - Set new record radius (10-500m)\n" +
                               $"<color=red>Note:</color> Record radius should match your maze size. Entry dome should be larger.";
                SendReply(player, message);
            }
        }

        private void ScanExistingEntitiesCommand(BasePlayer player)
        {
            if (activeMazeSession == null)
            {
                SendReply(player, "No active maze session. Use /maze new first.");
                return;
            }

            Vector3 center = activeMazeSession.MazeCenter.ToVector3();
            int existingCount = activeMazeSession.MazeObjects.Count;
            int newEntities = ScanExistingEntities(center);
            int totalEntities = activeMazeSession.MazeObjects.Count;

            SendReply(player, $"Scan complete! Found {newEntities} new entities. Total entities in maze: {totalEntities} (was {existingCount})");

            // Show visual sphere
            player.SendConsoleCommand("ddraw.sphere", 30f, "0 1 0 1", center, config.RecordRadius, 30f);
            SendReply(player, "Green sphere shows current scan radius for 30 seconds.");
        }

        private void ShowMazeInfo(BasePlayer player, string name)
        {
            if (!savedMazes.ContainsKey(name))
            {
                SendReply(player, $"Maze '{name}' not found.");
                return;
            }

            MazeArena maze = savedMazes[name];
            string message = $"<color=yellow><b>Maze Info: {name}</b></color>\n\n" +
                           $"<color=#2596be>Owner ID:</color> {maze.OwnerID}\n" +
                           $"<color=#2596be>Center Position:</color> {maze.MazeCenter.x:F1}, {maze.MazeCenter.y:F1}, {maze.MazeCenter.z:F1}\n" +
                           $"<color=#2596be>Total Objects:</color> {maze.MazeObjects.Count}\n" +
                           $"<color=#2596be>Spawn Points:</color> {maze.SpawnPoints.Count}\n" +
                           $"<color=#2596be>Door Spawns:</color> {maze.DoorSpawns.Count}\n" +
                           $"<color=#2596be>Status:</color> {(maze.Ended ? "Ended" : "Ready")}\n\n";

            if (maze.MazeObjects.Count > 0)
            {
                var prefabGroups = maze.MazeObjects.GroupBy(obj => obj.Prefab).OrderByDescending(g => g.Count()).Take(5);
                message += "<color=orange>Top 5 Entity Types:</color>\n";
                foreach (var group in prefabGroups)
                {
                    string shortName = group.Key.Split('/').LastOrDefault()?.Replace(".prefab", "") ?? group.Key;
                    message += $"- {shortName}: {group.Count()}\n";
                }
            }

            SendReply(player, message);
        }

        private void ListSavedMazes(BasePlayer player)
        {
            if (savedMazes.Count == 0)
            {
                SendReply(player, "No saved mazes found.");
                return;
            }

            string message = $"<color=yellow><b>Saved Mazes ({savedMazes.Count})</b></color>\n\n";
            foreach (var kvp in savedMazes.OrderBy(m => m.Key))
            {
                var maze = kvp.Value;
                message += $"<color=#2596be>{kvp.Key}</color> - {maze.MazeObjects.Count} objects, {maze.SpawnPoints.Count} spawns, {maze.DoorSpawns.Count} doors\n";
            }

            SendReply(player, message);
        }

        private void ShowSetup(BasePlayer player)
        {
            string message = "<color=yellow><b>Maze Commands</b></color>\n\n" +
                             "<color=#2596be>/maze new</color> - Start a new maze session. Build your maze, then use /maze save to record it.\n" +
                             "<color=#2596be>/maze save <name></color> - Save the current maze session (only spawns within the session radius are recorded).\n" +
                             "<color=#2596be>/maze updatecentre <name></color> - Update the center of a saved maze.\n" +
                             "<color=#2596be>/maze adddoor <name></color> - Add a door spawn (using your position relative to the maze center) to a saved maze.\n" +
                             "<color=#2596be>/maze removedoors <name></color> - Remove all door spawns for the specified maze.\n" +
                             "<color=#2596be>/maze removebuild</color> - (Build Mode) Remove the build/wall object nearest your position.\n" +
                             "<color=#2596be>/maze test <name></color> - Test the saved maze by spawning its entities and door spawns.\n" +
                             "<color=#2596be>/maze fs <name></color> - Spawn the saved maze and zone effects; event runs for the configured time.\n" +
                             "<color=#2596be>/maze start <name></color> - Start the maze event (hook available as StartMazeEventHook).\n" +
                             "<color=#2596be>/maze setspawn <name></color> - Add your current position as a spawn point for the maze.\n" +
                             "<color=#2596be>/maze remove <name></color> - Remove a saved maze from data.\n" +
                             "<color=#2596be>/maze end</color> - Force-end the event (remove zone entities, then the doors are removed after 2 minutes).\n" +
                             "<color=#2596be>/maze setup</color> - Show this help message.\n" +
                             "<color=#2596be>/maze radius</color> - Show/set record radius configuration.\n" +
                             "<color=#2596be>/maze scan</color> - Manually scan for existing entities in active session.\n" +
                             "<color=#2596be>/maze info <n></color> - Show detailed information about a saved maze.\n" +
                             "<color=#2596be>/maze list</color> - List all saved mazes.\n" +
                             "<color=#2596be>/maze resetspawn</color> - (Admin) Reset stuck entity spawning state.\n" +
                             "<color=#2596be>/maze cancel</color> - (Admin) Cancel all current maze operations.\n" +
                             "<color=#2596be>/maze force <name></color> - (Admin) Force start maze bypassing all checks.\n" +
                             "Additionally, use <color=#2596be>/tpc</color> to cancel a pending teleport.";
            SendReply(player, message);
        }
        #endregion

        #region Teleportation & Player Handling
        private void RemoveBlockedItems(BasePlayer player)
        {
            List<ItemContainer> containers = new List<ItemContainer>
            {
                player.inventory.containerMain,
                player.inventory.containerBelt,
                player.inventory.containerWear
            };
            foreach (var container in containers)
            {
                if (container == null)
                    continue;
                for (int i = container.itemList.Count - 1; i >= 0; i--)
                {
                    var item = container.itemList[i];
                    if (config.ItemsToRemove.Any(x => string.Equals(x, item.info.shortname, StringComparison.OrdinalIgnoreCase)))
                    {
                        item.RemoveFromContainer();
                        item.Remove();
                    }
                }
            }
        }

        public void InitiateSleep(BasePlayer player)
        {
            if (player == null || player.IsSleeping())
                return;
            Interface.CallHook("OnPlayerSleep", player);
            player.SetPlayerFlag(BasePlayer.PlayerFlags.Sleeping, true);
            player.sleepStartTime = Time.time;
            BasePlayer.sleepingPlayerList.Add(player);
            player.CancelInvoke("InventoryUpdate");
            player.CancelInvoke("TeamUpdate");
            if (!player._limitedNetworking)
            {
                player.EnablePlayerCollider();
                player.RemovePlayerRigidbody();
            }
        }
        #endregion

        #region Helper Methods
        private void RemoveTeleportItems(BasePlayer player)
        {
            if (config.ItemsToRemove == null || config.ItemsToRemove.Count == 0)
                return;
            foreach (var container in new List<ItemContainer> { player.inventory.containerMain, player.inventory.containerBelt })
            {
                if (container == null) continue;
                foreach (var item in container.itemList.ToArray())
                {
                    if (config.ItemsToRemove.Any(x => string.Equals(x, item.info.shortname, StringComparison.OrdinalIgnoreCase)))
                    {
                        item.Remove();
                    }
                }
            }
        }
        #endregion



        private bool isEditing = false;

        void OnItemDropped(Item item, BaseEntity entity)
        {
            if (activeMazeSession != null && !eventEnding && item.info.shortname == "lmg.m249")
            {
                if (Vector3.Distance(entity.transform.position, activeMazeSession.MazeCenter.ToVector3()) <= config.EntryDomeRadius)
                {
                    trackedM249ItemUids.Add(item.uid);
                }
            }
        }

        void OnItemPickup(Item item, BasePlayer player)
        {
            if (activeMazeSession != null && !eventEnding && item.info.shortname == "lmg.m249")
            {
                if (trackedM249ItemUids.Contains(item.uid))
                {
                    trackedM249ItemUids.Remove(item.uid);
                }
            }
        }
    }
}
