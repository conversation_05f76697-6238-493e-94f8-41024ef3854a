using Oxide.Core;
using Oxide.Core.Plugins;
using Oxide.Game.Rust.Cui;
using Oxide.Core.Libraries.Covalence;
using System;
using System.Linq;
using System.Collections.Generic;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Voting System", "Skelee", "1.8.0")]
    [Description("A plugin that allows players to vote on events via UI, for automated Events, now integrated with AutomaticRoamBubble")]
    public class AwakenVotingSystem : RustPlugin
    {
        [PluginReference] private Plugin? AutomaticRoamBubble, AwakenMaze, AwakenSulfurEvent, AwakenControlEvent, MonumentFinder;

        private Dictionary<string, int> eventVotes = new Dictionary<string, int>();
        private Dictionary<string, int> biomeVotes = new Dictionary<string, int>();
        private Dictionary<string, int> monumentVotes = new Dictionary<string, int>();
        private Dictionary<string, int> mazeVotes = new Dictionary<string, int>();
        private Dictionary<string, int> controlVotes = new Dictionary<string, int>();
        private bool votingActive = false;
        private bool eventRunning = false;
        private bool biomeVotingActive = false;
        private bool monumentVotingActive = false;
        private bool mazeVotingActive = false;
        private bool controlVotingActive = false;
        private Timer autoVoteTimer;
        private string pendingEvent = null;
        private bool isVoteActive = false;
        private DateTime? voteEndTime = null;
        private Timer? uiUpdateTimer = null;
        private List<Dictionary<string, object>> availableMonuments = new List<Dictionary<string, object>>();
        private int currentMonumentPage = 0;
        private int currentMazePage = 0;

        private const string AdminPermission = "votingsystem.admin";
        private const string AutoStartPermission = "votingsystem.autostart";

        private class ConfigData
        {
            public Dictionary<string, string> Events { get; set; } = new Dictionary<string, string>
            {
                { "Control", "https://cdn.awakenrust.com/oasis_control.png" },
                { "Maze", "https://cdn.awakenrust.com/oasis_maze.png" },
                { "Roams", "https://cdn.awakenrust.com/oasis_roams.png" },
                { "Sulfur", "https://cdn.awakenrust.com/oasis_sulfur.png" }
            };

            public List<List<string>> MazeMaps { get; set; } = new List<List<string>>
            {
                new List<string> { "Maze1", "Maze2", "Maze3", "Maze4" },
                new List<string> { "Maze5", "Maze6", "Maze7", "Maze8" },
                new List<string> { "Maze9", "Maze10", "Maze11", "Maze12" }
            };

            public Dictionary<string, string> Biomes { get; set; } = new Dictionary<string, string>
            {
                { "Grass", "https://cdn.awakenrust.com/oasis_grass.png" },
                { "Desert", "https://cdn.awakenrust.com/oasis_desert.png" },
                { "Snow", "https://cdn.awakenrust.com/oasis_snow.png" }
            };

            public Dictionary<string, string> ControlEvents { get; set; } = new Dictionary<string, string>
            {
                { "Control Point", "https://cdn.awakenrust.com/oasis_control_point.png" },
                { "King of the Hill", "https://cdn.awakenrust.com/oasis_king_of_hill.png" }
            };

            public int VoteIntervalMinutes { get; set; } = 10; // Interval between votes in minutes
            public int VoteDurationMinutes { get; set; } = 5; // Duration of each vote in minutes
            public int EventStartDelaySeconds { get; set; } = 30; // Delay before starting event after voting ends

            public Dictionary<string, string> PopularMonuments { get; set; } = new Dictionary<string, string>
            {
                { "launch_site_1", "Launch Site" },
                { "military_tunnel_1", "Military Tunnels" },
                { "airfield_1", "Airfield" },
                { "powerplant_1", "Power Plant" },
                { "water_treatment_plant_1", "Water Treatment Plant" },
                { "trainyard_1", "Train Yard" },
                { "sphere_tank", "The Dome" },
                { "satellite_dish", "Satellite Dish" },
                { "entrance_bunker_a", "Sewer Branch" },
                { "entrance_bunker_b", "Sewer Branch" },
                { "entrance_bunker_c", "Sewer Branch" },
                { "entrance_bunker_d", "Sewer Branch" },
                { "harbor_1", "Harbor" },
                { "harbor_2", "Harbor" },
                { "compound", "Outpost" },
                { "bandit_town", "Bandit Camp" },
                { "oilrig_1", "Large Oil Rig" },
                { "oilrig_2", "Small Oil Rig" },
                { "cargo_ship", "Cargo Ship" },
                { "underground_tunnel", "Underground Tunnel" },
                { "arctic_base_a", "Arctic Research Base" }
            };
        }

        private ConfigData config;

        protected override void LoadDefaultConfig()
        {
            config = new ConfigData();
            SaveConfig();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            config = Config.ReadObject<ConfigData>();
        }

        protected override void SaveConfig()
        {
            Config.WriteObject(config);
        }

        void Init()
        {
            permission.RegisterPermission(AdminPermission, this);
            permission.RegisterPermission(AutoStartPermission, this);
            LoadImages();
        }

        void OnServerInitialized()
        {
            // Auto-start voting cycle when server initializes
            timer.Once(60f, () => // Wait 1 minute after server start
            {
                if (!eventRunning && autoVoteTimer == null)
                {
                    StartAutomaticVotingCycle();
                    PrintWarning("AwakenVotingSystem: Automatic voting cycle started.");
                }
            });
        }

        #region Image Loading
        private void LoadImages()
        {
            // Image loading validation (silent) - using direct URL method like roam bubble plugin
        }

        [Command("reloadimages")]
        private void ReloadImagesCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            LoadImages();
            player.Reply("Reloading images from config...");
        }

        [Command("checkimages")]
        private void CheckImagesCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("Event Images:");
            foreach (var evt in config.Events)
            {
                player.Reply($"  {evt.Key}: {evt.Value}");
            }

            player.Reply("Biome Images:");
            foreach (var biome in config.Biomes)
            {
                player.Reply($"  {biome.Key}: {biome.Value}");
            }

            player.Reply("Control Event Images:");
            foreach (var control in config.ControlEvents)
            {
                player.Reply($"  {control.Key}: {control.Value}");
            }

            player.Reply("✅ All images use direct URL method - no ImageLibrary needed");
        }

        [Command("checkeventstatus")]
        private void CheckEventStatusCommand(IPlayer player, string command, string[] args)
        {
            if (!player.IsAdmin) return;

            player.Reply("=== Event Status Check ===");

            // Check internal voting system status
            player.Reply($"Voting Active: {votingActive}");
            player.Reply($"Biome Voting Active: {biomeVotingActive}");
            player.Reply($"Monument Voting Active: {monumentVotingActive}");
            player.Reply($"Maze Voting Active: {mazeVotingActive}");
            player.Reply($"Control Voting Active: {controlVotingActive}");
            player.Reply($"Event Running (internal): {eventRunning}");

            player.Reply("--- External Event Plugins ---");

            // Check AutomaticRoamBubble
            if (AutomaticRoamBubble != null && AutomaticRoamBubble.IsLoaded)
            {
                var isRoamActive = AutomaticRoamBubble.Call("IsRoamEventActive");
                player.Reply($"Roam Event Active: {isRoamActive ?? "null"}");
            }
            else
            {
                player.Reply("Roam Event: Plugin not loaded");
            }

            // Check AwakenMaze
            if (AwakenMaze != null && AwakenMaze.IsLoaded)
            {
                var isMazeActive = AwakenMaze.Call("IsMazeEventActive");
                player.Reply($"Maze Event Active: {isMazeActive ?? "null"}");
            }
            else
            {
                player.Reply("Maze Event: Plugin not loaded");
            }

            // Check AwakenSulfurEvent
            if (AwakenSulfurEvent != null && AwakenSulfurEvent.IsLoaded)
            {
                var isSulfurActive = AwakenSulfurEvent.Call("IsSulfurEventActive");
                player.Reply($"Sulfur Event Active: {isSulfurActive ?? "null"}");
            }
            else
            {
                player.Reply("Sulfur Event: Plugin not loaded");
            }

            // Check AwakenControlEvent
            if (AwakenControlEvent != null && AwakenControlEvent.IsLoaded)
            {
                var isControlActive = AwakenControlEvent.Call("IsControlEventActive");
                player.Reply($"Control Event Active: {isControlActive ?? "null"}");
            }
            else
            {
                player.Reply("Control Event: Plugin not loaded");
            }

            // Overall status
            bool anyEventRunning = IsAnyEventCurrentlyRunning();
            player.Reply($"--- Overall Status ---");
            player.Reply($"Any Event Currently Running: {anyEventRunning}");
            player.Reply($"Can Start New Vote: {!votingActive && !biomeVotingActive && !monumentVotingActive && !mazeVotingActive && !controlVotingActive && !eventRunning && !anyEventRunning}");
        }
        #endregion

        void Unload()
        {
            autoVoteTimer?.Destroy();
            uiUpdateTimer?.Destroy();
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "VotingUI");
                CuiHelper.DestroyUi(player, "BiomeVotingUI");
                CuiHelper.DestroyUi(player, "MazeVotingUI");
                CuiHelper.DestroyUi(player, "ControlVotingUI");
            }
        }

        [ChatCommand("startautovotes")]
        private void StartAutoVotesCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, AutoStartPermission))
            {
                player.ChatMessage("You do not have permission to use this command.");
                return;
            }

            if (autoVoteTimer != null && !autoVoteTimer.Destroyed)
            {
                player.ChatMessage("Automatic voting is already active.");
                return;
            }

            StartAutomaticVotingCycle();
            player.ChatMessage("Automatic voting cycle has been started.");
        }

        private void StartAutomaticVotingCycle()
        {
            autoVoteTimer = timer.Every(config.VoteIntervalMinutes * 60f, () =>
            {
                if (!votingActive && !biomeVotingActive && !monumentVotingActive && !mazeVotingActive && !controlVotingActive && !eventRunning && !IsAnyEventCurrentlyRunning())
                {
                    StartEventVote();
                }
            });
        }

        private bool IsAnyEventCurrentlyRunning()
        {
            // Check AutomaticRoamBubble for active roams
            if (AutomaticRoamBubble != null && AutomaticRoamBubble.IsLoaded)
            {
                var isRoamActive = AutomaticRoamBubble.Call("IsRoamEventActive");
                if (isRoamActive is bool && (bool)isRoamActive)
                {
                    PrintWarning("AwakenVotingSystem: Roam event is currently active - skipping vote");
                    return true;
                }
            }

            // Check AwakenMaze for active maze events
            if (AwakenMaze != null && AwakenMaze.IsLoaded)
            {
                var isMazeActive = AwakenMaze.Call("IsMazeEventActive");
                if (isMazeActive is bool && (bool)isMazeActive)
                {
                    PrintWarning("AwakenVotingSystem: Maze event is currently active - skipping vote");
                    return true;
                }
            }

            // Check AwakenSulfurEvent for active sulfur events
            if (AwakenSulfurEvent != null && AwakenSulfurEvent.IsLoaded)
            {
                var isSulfurActive = AwakenSulfurEvent.Call("IsSulfurEventActive");
                if (isSulfurActive is bool && (bool)isSulfurActive)
                {
                    PrintWarning("AwakenVotingSystem: Sulfur event is currently active - skipping vote");
                    return true;
                }
            }

            // Check AwakenControlEvent for active control events
            if (AwakenControlEvent != null && AwakenControlEvent.IsLoaded)
            {
                var isControlActive = AwakenControlEvent.Call("IsControlEventActive");
                if (isControlActive is bool && (bool)isControlActive)
                {
                    PrintWarning("AwakenVotingSystem: Control event is currently active - skipping vote");
                    return true;
                }
            }

            return false; // No events are currently running
        }

        private void StartEventVote()
        {
            if (eventRunning || IsAnyEventCurrentlyRunning()) return;

            eventVotes.Clear();
            foreach (var evt in config.Events.Keys)
            {
                eventVotes[evt] = 0;
            }
            votingActive = true;
            biomeVotingActive = false;
            monumentVotingActive = false;
            mazeVotingActive = false;
            controlVotingActive = false;
            pendingEvent = null;
            isVoteActive = true;
            voteEndTime = DateTime.Now.AddMinutes(config.VoteDurationMinutes);

            // Start UI update timer to refresh countdown every 5 seconds (reduced frequency)
            uiUpdateTimer?.Destroy();
            uiUpdateTimer = timer.Every(5f, UpdateTimerForAllPlayers); // Changed from 1s to 5s

            Broadcast("VOTE NOW! A vote has started for the next event! Use /vote to participate.");
            timer.Once(config.VoteDurationMinutes * 60f, EndEventVote);
        }

        [ChatCommand("startvote")]
        private void StartVoteCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, AdminPermission))
            {
                player.ChatMessage("You do not have permission to use this command.");
                return;
            }

            if (votingActive || biomeVotingActive || monumentVotingActive || mazeVotingActive || controlVotingActive || eventRunning || IsAnyEventCurrentlyRunning())
            {
                player.ChatMessage("A vote or event is already in progress.");
                return;
            }

            StartEventVote();
            Broadcast("ADMIN VOTE! An admin has started a vote for the next event! Use /vote to participate.");
        }

        [ChatCommand("stopvote")]
        private void StopVoteCommand(BasePlayer player, string command, string[] args)
        {
            if (!permission.UserHasPermission(player.UserIDString, AdminPermission))
            {
                player.ChatMessage("You do not have permission to use this command.");
                return;
            }

            if (!votingActive && !biomeVotingActive && !monumentVotingActive && !mazeVotingActive && !controlVotingActive && !eventRunning)
            {
                player.ChatMessage("There is no active vote or event to stop.");
                return;
            }

            votingActive = false;
            biomeVotingActive = false;
            monumentVotingActive = false;
            mazeVotingActive = false;
            controlVotingActive = false;
            eventRunning = false;
            eventVotes.Clear();
            biomeVotes.Clear();
            monumentVotes.Clear();
            mazeVotes.Clear();
            controlVotes.Clear();
            pendingEvent = null;

            foreach (var activePlayer in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(activePlayer, "VotingUI");
                CuiHelper.DestroyUi(activePlayer, "BiomeVotingUI");
                CuiHelper.DestroyUi(activePlayer, "MonumentVotingUI");
                CuiHelper.DestroyUi(activePlayer, "MazeVotingUI");
                CuiHelper.DestroyUi(activePlayer, "ControlVotingUI");
            }

            if (autoVoteTimer != null && !autoVoteTimer.Destroyed)
            {
                autoVoteTimer.Destroy();
                autoVoteTimer = null;
            }

            Broadcast("The vote has been stopped by an admin.");
        }

        [ChatCommand("vote")]
        private void OpenVoteUI(BasePlayer player, string command, string[] args)
        {
            if (votingActive)
            {
                CreateEventVotingUI(player);
            }
            else if (biomeVotingActive)
            {
                CreateBiomeVotingUI(player);
            }
            else if (monumentVotingActive)
            {
                CreateMonumentVotingUI(player, currentMonumentPage);
            }
            else if (mazeVotingActive)
            {
                CreateMazeVotingUI(player, currentMazePage);
            }
            else if (controlVotingActive)
            {
                CreateControlVotingUI(player);
            }
            else
            {
                player.ChatMessage("There is no active vote right now.");
            }
        }

        private void CreateEventVotingUI(BasePlayer player)
        {
            CuiElementContainer container = new CuiElementContainer();
            string panelName = "VotingUI";

            // Main background panel - wider and positioned like the image
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.15 0.35", AnchorMax = "0.85 0.65" },
                CursorEnabled = true
            }, "Overlay", panelName);

            // Header background - dark gray/black bar
            container.Add(new CuiPanel
            {
                Image = { Color = "0.05 0.05 0.05 1" },
                RectTransform = { AnchorMin = "0 0.8", AnchorMax = "1 1" }
            }, panelName, "HeaderPanel");

            // Timer display (top left)
            string timeRemaining = GetFormattedTimeRemaining();
            container.Add(new CuiLabel
            {
                Text = { Text = timeRemaining, FontSize = 16, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.02 0", AnchorMax = "0.15 1" }
            }, "HeaderPanel", "TimerLabel");

            // Clock icon next to timer
            container.Add(new CuiLabel
            {
                Text = { Text = "⏰", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.12 0", AnchorMax = "0.18 1" }
            }, "HeaderPanel");

            // Main title - centered
            container.Add(new CuiLabel
            {
                Text = { Text = "VOTE FOR THE NEXT EVENT", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.2 0", AnchorMax = "0.8 1" }
            }, "HeaderPanel");

            // Close button (X) - top right
            container.Add(new CuiButton
            {
                Button = { Close = panelName, Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.92 0.1", AnchorMax = "0.98 0.9" },
                Text = { Text = "✕", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "HeaderPanel");

            // Event cards container
            float cardWidth = 0.22f;
            float cardSpacing = 0.025f;
            float startX = 0.04f;

            int index = 0;
            foreach (var evt in config.Events)
            {
                string eventImage = evt.Value;
                string eventName = evt.Key;
                float cardX = startX + (index * (cardWidth + cardSpacing));

                // Event card background
                string cardPanel = $"EventCard_{eventName}";
                container.Add(new CuiPanel
                {
                    Image = { Color = "0.15 0.15 0.15 1" },
                    RectTransform = { AnchorMin = $"{cardX} 0.1", AnchorMax = $"{cardX + cardWidth} 0.75" }
                }, panelName, cardPanel);

                // Event image - square aspect ratio
                container.Add(new CuiElement
                {
                    Parent = cardPanel,
                    Name = CuiHelper.GetGuid(),
                    Components =
                    {
                        new CuiRawImageComponent { Url = eventImage },
                        new CuiRectTransformComponent { AnchorMin = "0.1 0.25", AnchorMax = "0.9 0.85" }
                    }
                });

                // Event name label at bottom
                container.Add(new CuiLabel
                {
                    Text = { Text = eventName.ToUpper(), FontSize = 16, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    RectTransform = { AnchorMin = "0 0.05", AnchorMax = "1 0.2" }
                }, cardPanel);

                // Clickable button overlay
                container.Add(new CuiButton
                {
                    Button = { Color = "0 0 0 0", Command = $"vote_event {eventName}" },
                    RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                    Text = { Text = "", FontSize = 1, Align = TextAnchor.MiddleCenter, Color = "0 0 0 0" }
                }, cardPanel);

                index++;
            }

            CuiHelper.AddUi(player, container);
        }

        private void CreateBiomeVotingUI(BasePlayer player)
        {
            CuiElementContainer container = new CuiElementContainer();
            string panelName = "BiomeVotingUI";

            // Main background panel - matching monument/maze UI style
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.15 0.25", AnchorMax = "0.85 0.75" },
                CursorEnabled = true
            }, "Overlay", panelName);

            // Header background
            container.Add(new CuiPanel
            {
                Image = { Color = "0.05 0.05 0.05 1" },
                RectTransform = { AnchorMin = "0 0.85", AnchorMax = "1 1" }
            }, panelName, "HeaderPanel");

            // Title
            container.Add(new CuiLabel
            {
                Text = { Text = "VOTE FOR ROAM BIOME", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.2 0", AnchorMax = "0.8 1" }
            }, "HeaderPanel");

            // Close button
            container.Add(new CuiButton
            {
                Button = { Close = panelName, Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.92 0.1", AnchorMax = "0.98 0.9" },
                Text = { Text = "✕", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "HeaderPanel");

            // Biome cards
            float cardWidth = 0.28f;
            float cardSpacing = 0.04f;
            float startX = 0.04f;

            int index = 0;
            foreach (var biome in config.Biomes)
            {
                string biomeImage = biome.Value;
                string biomeName = biome.Key;
                float cardX = startX + (index * (cardWidth + cardSpacing));

                // Biome card background
                string cardPanel = $"BiomeCard_{biomeName}";
                container.Add(new CuiPanel
                {
                    Image = { Color = "0.15 0.15 0.15 1" },
                    RectTransform = { AnchorMin = $"{cardX} 0.2", AnchorMax = $"{cardX + cardWidth} 0.75" }
                }, panelName, cardPanel);

                // Biome image - square aspect ratio
                container.Add(new CuiElement
                {
                    Parent = cardPanel,
                    Name = CuiHelper.GetGuid(),
                    Components =
                    {
                        new CuiRawImageComponent { Url = biomeImage },
                        new CuiRectTransformComponent { AnchorMin = "0.15 0.25", AnchorMax = "0.85 0.85" }
                    }
                });

                // Biome name label
                container.Add(new CuiLabel
                {
                    Text = { Text = biomeName.ToUpper(), FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    RectTransform = { AnchorMin = "0 0.05", AnchorMax = "1 0.2" }
                }, cardPanel);

                // Clickable button overlay
                container.Add(new CuiButton
                {
                    Button = { Color = "0 0 0 0", Command = $"vote_biome {biomeName}" },
                    RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                    Text = { Text = "", FontSize = 1, Align = TextAnchor.MiddleCenter, Color = "0 0 0 0" }
                }, cardPanel);

                index++;
            }

            CuiHelper.AddUi(player, container);
        }

        private void CreateControlVotingUI(BasePlayer player)
        {
            CuiElementContainer container = new CuiElementContainer();
            string panelName = "ControlVotingUI";

            // Main background panel - matching monument/maze UI style
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.15 0.25", AnchorMax = "0.85 0.75" },
                CursorEnabled = true
            }, "Overlay", panelName);

            // Header background
            container.Add(new CuiPanel
            {
                Image = { Color = "0.05 0.05 0.05 1" },
                RectTransform = { AnchorMin = "0 0.85", AnchorMax = "1 1" }
            }, panelName, "HeaderPanel");

            // Title
            container.Add(new CuiLabel
            {
                Text = { Text = "VOTE FOR CONTROL EVENT TYPE", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.2 0", AnchorMax = "0.8 1" }
            }, "HeaderPanel");

            // Close button
            container.Add(new CuiButton
            {
                Button = { Close = panelName, Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.92 0.1", AnchorMax = "0.98 0.9" },
                Text = { Text = "✕", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "HeaderPanel");

            // Control event cards
            float cardWidth = 0.4f;
            float cardSpacing = 0.1f;
            float startX = 0.1f;

            int index = 0;
            foreach (var controlEvent in config.ControlEvents)
            {
                string controlImage = controlEvent.Value;
                string controlName = controlEvent.Key;
                float cardX = startX + (index * (cardWidth + cardSpacing));

                // Control event card background
                string cardPanel = $"ControlCard_{controlName.Replace(" ", "")}";
                container.Add(new CuiPanel
                {
                    Image = { Color = "0.15 0.15 0.15 1" },
                    RectTransform = { AnchorMin = $"{cardX} 0.2", AnchorMax = $"{cardX + cardWidth} 0.75" }
                }, panelName, cardPanel);

                // Control event image
                container.Add(new CuiElement
                {
                    Parent = cardPanel,
                    Name = CuiHelper.GetGuid(),
                    Components =
                    {
                        new CuiRawImageComponent { Url = controlImage },
                        new CuiRectTransformComponent { AnchorMin = "0.1 0.2", AnchorMax = "0.9 0.9" }
                    }
                });

                // Control event name label
                container.Add(new CuiLabel
                {
                    Text = { Text = controlName.ToUpper(), FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    RectTransform = { AnchorMin = "0 0.05", AnchorMax = "1 0.2" }
                }, cardPanel);

                // Clickable button overlay
                container.Add(new CuiButton
                {
                    Button = { Color = "0 0 0 0", Command = $"vote_control {controlName}" },
                    RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                    Text = { Text = "", FontSize = 1, Align = TextAnchor.MiddleCenter, Color = "0 0 0 0" }
                }, cardPanel);

                index++;
            }

            CuiHelper.AddUi(player, container);
        }

        private void CreateMonumentVotingUI(BasePlayer player, int pageIndex)
        {
            CuiElementContainer container = new CuiElementContainer();
            string panelName = "MonumentVotingUI";

            // Main background panel
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.15 0.25", AnchorMax = "0.85 0.75" },
                CursorEnabled = true
            }, "Overlay", panelName);

            // Header background
            container.Add(new CuiPanel
            {
                Image = { Color = "0.05 0.05 0.05 1" },
                RectTransform = { AnchorMin = "0 0.85", AnchorMax = "1 1" }
            }, panelName, "HeaderPanel");

            // Title
            container.Add(new CuiLabel
            {
                Text = { Text = "VOTE FOR SULFUR EVENT MONUMENT", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.2 0", AnchorMax = "0.8 1" }
            }, "HeaderPanel");

            // Close button
            container.Add(new CuiButton
            {
                Button = { Close = panelName, Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.92 0.1", AnchorMax = "0.98 0.9" },
                Text = { Text = "✕", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "HeaderPanel");

            // Calculate monuments for this page (4 per page)
            int monumentsPerPage = 4;
            int startIndex = pageIndex * monumentsPerPage;
            int endIndex = Math.Min(startIndex + monumentsPerPage, availableMonuments.Count);

            // Monument cards
            float cardWidth = 0.22f;
            float cardSpacing = 0.025f;
            float startX = 0.04f;

            for (int i = startIndex; i < endIndex; i++)
            {
                var monument = availableMonuments[i];
                string monumentShortName = monument.ContainsKey("ShortName") ? (string)monument["ShortName"] : "Unknown";

                // Get the display name from config, fallback to shortname if not found
                string monumentDisplayName = monumentShortName;
                if (config.PopularMonuments.ContainsKey(monumentShortName))
                {
                    monumentDisplayName = config.PopularMonuments[monumentShortName];
                }

                int cardIndex = i - startIndex;
                float cardX = startX + (cardIndex * (cardWidth + cardSpacing));

                // Monument card background
                string cardPanel = $"MonumentCard_{monumentShortName}";
                container.Add(new CuiPanel
                {
                    Image = { Color = "0.15 0.15 0.15 1" },
                    RectTransform = { AnchorMin = $"{cardX} 0.2", AnchorMax = $"{cardX + cardWidth} 0.75" }
                }, panelName, cardPanel);

                // Monument name label (display friendly name)
                container.Add(new CuiLabel
                {
                    Text = { Text = monumentDisplayName.ToUpper(), FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    RectTransform = { AnchorMin = "0 0.05", AnchorMax = "1 0.2" }
                }, cardPanel);

                // Clickable button overlay (use shortname for voting)
                container.Add(new CuiButton
                {
                    Button = { Color = "0 0 0 0", Command = $"vote_monument {monumentShortName}" },
                    RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                    Text = { Text = "", FontSize = 1, Align = TextAnchor.MiddleCenter, Color = "0 0 0 0" }
                }, cardPanel);
            }

            // Navigation buttons
            if (pageIndex > 0)
            {
                container.Add(new CuiButton
                {
                    Button = { Color = "0.5 0.5 0.5 1", Command = $"nav_monument_page {pageIndex - 1}" },
                    RectTransform = { AnchorMin = "0.1 0.05", AnchorMax = "0.2 0.15" },
                    Text = { Text = "◀ PREV", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, panelName);
            }

            if (endIndex < availableMonuments.Count)
            {
                container.Add(new CuiButton
                {
                    Button = { Color = "0.5 0.5 0.5 1", Command = $"nav_monument_page {pageIndex + 1}" },
                    RectTransform = { AnchorMin = "0.8 0.05", AnchorMax = "0.9 0.15" },
                    Text = { Text = "NEXT ▶", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, panelName);
            }

            // Page indicator
            int totalPages = (int)Math.Ceiling((double)availableMonuments.Count / monumentsPerPage);
            container.Add(new CuiLabel
            {
                Text = { Text = $"Page {pageIndex + 1} of {totalPages}", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.8 0.8 0.8 1" },
                RectTransform = { AnchorMin = "0.4 0.05", AnchorMax = "0.6 0.15" }
            }, panelName);

            CuiHelper.AddUi(player, container);
        }

        [ConsoleCommand("vote_event")]
        private void VoteEventCommand(ConsoleSystem.Arg arg)
        {
            if (!votingActive || arg.Connection == null)
                return;

            string eventName = arg.Args.Length > 0 ? arg.Args[0] : string.Empty;
            if (string.IsNullOrEmpty(eventName) || !eventVotes.ContainsKey(eventName))
                return;

            BasePlayer player = arg.Connection.player as BasePlayer;
            eventVotes[eventName]++;
            player.ChatMessage($"You voted for {eventName}!");
            CuiHelper.DestroyUi(player, "VotingUI");
        }

        [ConsoleCommand("vote_biome")]
        private void VoteBiomeCommand(ConsoleSystem.Arg arg)
        {
            if (!biomeVotingActive || arg.Connection == null)
                return;

            string biomeName = arg.Args.Length > 0 ? arg.Args[0] : string.Empty;
            if (string.IsNullOrEmpty(biomeName) || !biomeVotes.ContainsKey(biomeName))
                return;

            BasePlayer player = arg.Connection.player as BasePlayer;
            biomeVotes[biomeName]++;
            player.ChatMessage($"You voted for {biomeName} biome!");
            CuiHelper.DestroyUi(player, "BiomeVotingUI");
        }

        [ConsoleCommand("vote_monument")]
        private void VoteMonumentCommand(ConsoleSystem.Arg arg)
        {
            if (!monumentVotingActive || arg.Connection == null)
                return;

            string monumentShortName = arg.Args.Length > 0 ? arg.Args[0] : string.Empty;
            if (string.IsNullOrEmpty(monumentShortName))
                return;

            BasePlayer player = arg.Connection.player as BasePlayer;

            // Initialize vote count if not exists
            if (!monumentVotes.ContainsKey(monumentShortName))
                monumentVotes[monumentShortName] = 0;

            monumentVotes[monumentShortName]++;

            // Get display name for chat message
            string displayName = monumentShortName;
            if (config.PopularMonuments.ContainsKey(monumentShortName))
            {
                displayName = config.PopularMonuments[monumentShortName];
            }

            player.ChatMessage($"You voted for {displayName}!");
            CuiHelper.DestroyUi(player, "MonumentVotingUI");
        }

        [ConsoleCommand("vote_maze")]
        private void VoteMazeCommand(ConsoleSystem.Arg arg)
        {
            if (!mazeVotingActive || arg.Connection == null)
                return;

            string mazeName = arg.Args.Length > 0 ? arg.Args[0] : string.Empty;
            if (string.IsNullOrEmpty(mazeName))
                return;

            BasePlayer player = arg.Connection.player as BasePlayer;

            // Initialize vote count if not exists
            if (!mazeVotes.ContainsKey(mazeName))
                mazeVotes[mazeName] = 0;

            mazeVotes[mazeName]++;
            player.ChatMessage($"You voted for {mazeName}!");
            CuiHelper.DestroyUi(player, "MazeVotingUI");
        }

        [ConsoleCommand("vote_control")]
        private void VoteControlCommand(ConsoleSystem.Arg arg)
        {
            if (!controlVotingActive || arg.Connection == null)
                return;

            string controlEventName = arg.Args.Length > 0 ? string.Join(" ", arg.Args) : string.Empty;
            if (string.IsNullOrEmpty(controlEventName))
                return;

            BasePlayer player = arg.Connection.player as BasePlayer;

            // Initialize vote count if not exists
            if (!controlVotes.ContainsKey(controlEventName))
                controlVotes[controlEventName] = 0;

            controlVotes[controlEventName]++;
            player.ChatMessage($"You voted for {controlEventName}!");
            CuiHelper.DestroyUi(player, "ControlVotingUI");
        }

        [ConsoleCommand("nav_monument_page")]
        private void NavigateMonumentPageCommand(ConsoleSystem.Arg arg)
        {
            if (!monumentVotingActive || arg.Connection == null)
                return;

            if (arg.Args.Length > 0)
            {
                int pageIndex = Convert.ToInt32(arg.Args[0]);
                currentMonumentPage = pageIndex;
                BasePlayer player = arg.Connection.player as BasePlayer;
                CuiHelper.DestroyUi(player, "MonumentVotingUI");
                CreateMonumentVotingUI(player, pageIndex);
            }
        }

        private void EndEventVote()
        {
            if (!votingActive)
                return;

            votingActive = false;
            isVoteActive = false;
            voteEndTime = null;
            uiUpdateTimer?.Destroy();
            uiUpdateTimer = null;

            // Close voting UI for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "VotingUI");
            }

            string winner = GetWinningEvent();
            Broadcast($"Event voting has ended! The winning event is {winner}!");

            pendingEvent = winner;

            if (winner == "Roams")
            {
                StartBiomeVote();
            }
            else if (winner == "Sulfur")
            {
                StartMonumentVote();
            }
            else if (winner == "Maze")
            {
                StartMazeVote();
            }
            else if (winner == "Control")
            {
                StartControlVote();
            }
            else
            {
                EndEvent(); // For unknown events, end immediately
            }
        }

        private void StartBiomeVote()
        {
            biomeVotes.Clear();
            foreach (var biome in config.Biomes.Keys)
            {
                biomeVotes[biome] = 0;
            }
            biomeVotingActive = true;
            Broadcast("BIOME VOTE! A second vote has started for the Roam biome! Use /vote to choose Grass, Desert, or Snow.");
            timer.Once(config.VoteDurationMinutes * 60f, EndBiomeVote);
        }

        private void EndBiomeVote()
        {
            if (!biomeVotingActive)
                return;

            biomeVotingActive = false;

            // Close biome voting UI for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "BiomeVotingUI");
            }

            string winningBiome = GetWinningBiome();
            Broadcast($"Biome voting has ended! The winning biome is {winningBiome}!");
            Broadcast($"Starting {pendingEvent} event in {config.EventStartDelaySeconds} seconds...");
            timer.Once(config.EventStartDelaySeconds, () => {
                ExecuteEvent(pendingEvent, winningBiome);
                pendingEvent = null;
            });
        }

        private void StartMonumentVote()
        {
            if (MonumentFinder == null || !MonumentFinder.IsLoaded)
            {
                Broadcast("Monument finder not available! Starting sulfur event at a random location.");
                timer.Once(config.EventStartDelaySeconds, () => ExecuteEvent(pendingEvent, null));
                return;
            }

            // Get all monuments from MonumentFinder
            var allMonuments = MonumentFinder.Call("API_FindMonuments", "") as List<Dictionary<string, object>>;
            if (allMonuments == null || allMonuments.Count == 0)
            {
                Broadcast("No monuments found! Starting sulfur event at a random location.");
                timer.Once(config.EventStartDelaySeconds, () => ExecuteEvent(pendingEvent, null));
                return;
            }

            // Filter for popular monuments only using config (using shortnames as keys)
            var popularMonumentShortNames = new HashSet<string>(config.PopularMonuments.Keys);

            // Filter monuments to only include popular ones
            availableMonuments = allMonuments.Where(monument =>
            {
                if (monument.ContainsKey("ShortName"))
                {
                    string monumentShortName = (string)monument["ShortName"];
                    return popularMonumentShortNames.Contains(monumentShortName);
                }
                return false;
            }).ToList();

            if (availableMonuments.Count == 0)
            {
                Broadcast("No popular monuments found! Starting sulfur event at a random location.");
                timer.Once(config.EventStartDelaySeconds, () => ExecuteEvent(pendingEvent, null));
                return;
            }

            monumentVotes.Clear();
            currentMonumentPage = 0;
            monumentVotingActive = true;

            Broadcast("MONUMENT VOTE! A second vote has started for the Sulfur event monument! Use /vote to choose a popular location.");
            timer.Once(config.VoteDurationMinutes * 60f, EndMonumentVote);
        }

        private void EndMonumentVote()
        {
            if (!monumentVotingActive)
                return;

            monumentVotingActive = false;

            // Close monument voting UI for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "MonumentVotingUI");
            }

            string winningMonumentShortName = GetWinningMonument();
            if (!string.IsNullOrEmpty(winningMonumentShortName))
            {
                // Get display name for broadcast
                string displayName = winningMonumentShortName;
                if (config.PopularMonuments.ContainsKey(winningMonumentShortName))
                {
                    displayName = config.PopularMonuments[winningMonumentShortName];
                }

                Broadcast($"Monument voting has ended! The winning monument is {displayName}!");
                Broadcast($"Starting {pendingEvent} event in {config.EventStartDelaySeconds} seconds...");
                timer.Once(config.EventStartDelaySeconds, () => {
                    ExecuteEvent(pendingEvent, winningMonumentShortName);
                    pendingEvent = null;
                });
            }
            else
            {
                Broadcast("No votes received! Starting sulfur event at a random location.");
                Broadcast($"Starting {pendingEvent} event in {config.EventStartDelaySeconds} seconds...");
                timer.Once(config.EventStartDelaySeconds, () => {
                    ExecuteEvent(pendingEvent, null);
                    pendingEvent = null;
                });
            }
        }

        private void StartMazeVote()
        {
            mazeVotes.Clear();
            currentMazePage = 0;
            mazeVotingActive = true;

            Broadcast("MAZE VOTE! A second vote has started for the Maze map! Use /vote to choose a map.");
            timer.Once(config.VoteDurationMinutes * 60f, EndMazeVote);
        }

        private void StartControlVote()
        {
            controlVotes.Clear();
            foreach (var controlEvent in config.ControlEvents.Keys)
            {
                controlVotes[controlEvent] = 0;
            }
            controlVotingActive = true;

            Broadcast("CONTROL VOTE! A second vote has started for the Control event type! Use /vote to choose Control Point or King of the Hill.");
            timer.Once(config.VoteDurationMinutes * 60f, EndControlVote);
        }

        private void EndMazeVote()
        {
            if (!mazeVotingActive)
                return;

            mazeVotingActive = false;

            // Close maze voting UI for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "MazeVotingUI");
            }

            string winningMaze = GetWinningMaze();
            if (!string.IsNullOrEmpty(winningMaze))
            {
                Broadcast($"Maze voting has ended! The winning map is {winningMaze}!");
                Broadcast($"Starting {pendingEvent} event in {config.EventStartDelaySeconds} seconds...");
                timer.Once(config.EventStartDelaySeconds, () => {
                    ExecuteEvent(pendingEvent, winningMaze);
                    pendingEvent = null;
                });
            }
            else
            {
                Broadcast("No votes received! Starting maze event with a random map.");
                Broadcast($"Starting {pendingEvent} event in {config.EventStartDelaySeconds} seconds...");
                timer.Once(config.EventStartDelaySeconds, () => {
                    ExecuteEvent(pendingEvent, "Maze1");
                    pendingEvent = null;
                });
            }
        }

        private void EndControlVote()
        {
            if (!controlVotingActive)
                return;

            controlVotingActive = false;

            // Close control voting UI for all players
            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "ControlVotingUI");
            }

            string winningControlEvent = GetWinningControlEvent();
            if (!string.IsNullOrEmpty(winningControlEvent))
            {
                Broadcast($"Control voting has ended! The winning event type is {winningControlEvent}!");
                Broadcast($"Starting {pendingEvent} event in {config.EventStartDelaySeconds} seconds...");
                timer.Once(config.EventStartDelaySeconds, () => {
                    ExecuteEvent(pendingEvent, winningControlEvent);
                    pendingEvent = null;
                });
            }
            else
            {
                Broadcast("No votes received! Starting control event with Control Point.");
                Broadcast($"Starting {pendingEvent} event in {config.EventStartDelaySeconds} seconds...");
                timer.Once(config.EventStartDelaySeconds, () => {
                    ExecuteEvent(pendingEvent, "Control Point");
                    pendingEvent = null;
                });
            }
        }

        private string GetWinningEvent()
        {
            int maxVotes = -1;
            string winner = "None";

            foreach (var entry in eventVotes)
            {
                if (entry.Value > maxVotes)
                {
                    maxVotes = entry.Value;
                    winner = entry.Key;
                }
            }

            return winner;
        }

        private string GetWinningControlEvent()
        {
            int maxVotes = -1;
            string winner = "Control Point"; // Default fallback

            foreach (var entry in controlVotes)
            {
                if (entry.Value > maxVotes)
                {
                    maxVotes = entry.Value;
                    winner = entry.Key;
                }
            }

            return winner;
        }

        private string GetWinningBiome()
        {
            int maxVotes = -1;
            string winner = "Grass"; // Default fallback

            foreach (var entry in biomeVotes)
            {
                if (entry.Value > maxVotes)
                {
                    maxVotes = entry.Value;
                    winner = entry.Key;
                }
            }

            return winner;
        }

        private string GetWinningMonument()
        {
            int maxVotes = -1;
            string winner = "";

            foreach (var entry in monumentVotes)
            {
                if (entry.Value > maxVotes)
                {
                    maxVotes = entry.Value;
                    winner = entry.Key;
                }
            }

            return winner;
        }

        private string GetWinningMaze()
        {
            int maxVotes = -1;
            string winner = "";

            foreach (var entry in mazeVotes)
            {
                if (entry.Value > maxVotes)
                {
                    maxVotes = entry.Value;
                    winner = entry.Key;
                }
            }

            return winner;
        }

        private void ExecuteEvent(string eventName, string parameter)
        {
            eventRunning = true;
            PrintWarning($"Executing event: {eventName}" + (parameter != null ? $" with parameter: {parameter}" : ""));

            if (eventName == "Roams")
            {
                StartRoamsEvent(parameter); // parameter is biome
            }
            else if (eventName == "Maze")
            {
                StartMazeEvent(parameter); // parameter is maze map name
            }
            else if (eventName == "Control")
            {
                StartControlEvent(parameter); // parameter is control event type
            }
            else if (eventName == "Sulfur")
            {
                StartSulfurEvent(parameter); // parameter is monument name
            }
            else
            {
                EndEvent(); // For unknown events, end immediately
            }
        }

        private void CreateMazeVotingUI(BasePlayer player, int pageIndex)
        {
            CuiElementContainer container = new CuiElementContainer();
            string panelName = "MazeVotingUI";

            // Main background panel
            container.Add(new CuiPanel
            {
                Image = { Color = "0.1 0.1 0.1 0.95" },
                RectTransform = { AnchorMin = "0.15 0.25", AnchorMax = "0.85 0.75" },
                CursorEnabled = true
            }, "Overlay", panelName);

            // Header background
            container.Add(new CuiPanel
            {
                Image = { Color = "0.05 0.05 0.05 1" },
                RectTransform = { AnchorMin = "0 0.85", AnchorMax = "1 1" }
            }, panelName, "HeaderPanel");

            // Title
            container.Add(new CuiLabel
            {
                Text = { Text = "VOTE FOR MAZE MAP", FontSize = 20, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.2 0", AnchorMax = "0.8 1" }
            }, "HeaderPanel");

            // Close button
            container.Add(new CuiButton
            {
                Button = { Close = panelName, Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.92 0.1", AnchorMax = "0.98 0.9" },
                Text = { Text = "✕", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
            }, "HeaderPanel");

            // Maze options (4 per page)
            string[] mazeOptions = { "test1", "Maze2", "Maze3", "Maze4", "Maze5", "Maze6", "Maze7", "Maze8" };
            int mazesPerPage = 4;
            int startIndex = pageIndex * mazesPerPage;
            int endIndex = Math.Min(startIndex + mazesPerPage, mazeOptions.Length);

            // Maze cards
            float cardWidth = 0.22f;
            float cardSpacing = 0.025f;
            float startX = 0.04f;

            for (int i = startIndex; i < endIndex; i++)
            {
                string mazeName = mazeOptions[i];
                int cardIndex = i - startIndex;
                float cardX = startX + (cardIndex * (cardWidth + cardSpacing));

                // Maze card background
                string cardPanel = $"MazeCard_{mazeName}";
                container.Add(new CuiPanel
                {
                    Image = { Color = "0.15 0.15 0.15 1" },
                    RectTransform = { AnchorMin = $"{cardX} 0.2", AnchorMax = $"{cardX + cardWidth} 0.75" }
                }, panelName, cardPanel);

                // Maze name label
                container.Add(new CuiLabel
                {
                    Text = { Text = mazeName.ToUpper(), FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    RectTransform = { AnchorMin = "0 0.05", AnchorMax = "1 0.2" }
                }, cardPanel);

                // Clickable button overlay
                container.Add(new CuiButton
                {
                    Button = { Color = "0 0 0 0", Command = $"vote_maze {mazeName}" },
                    RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1" },
                    Text = { Text = "", FontSize = 1, Align = TextAnchor.MiddleCenter, Color = "0 0 0 0" }
                }, cardPanel);
            }

            // Navigation buttons
            if (pageIndex > 0)
            {
                container.Add(new CuiButton
                {
                    Button = { Color = "0.5 0.5 0.5 1", Command = $"nav_maze_page {pageIndex - 1}" },
                    RectTransform = { AnchorMin = "0.1 0.05", AnchorMax = "0.2 0.15" },
                    Text = { Text = "◀ PREV", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, panelName);
            }

            if (endIndex < mazeOptions.Length)
            {
                container.Add(new CuiButton
                {
                    Button = { Color = "0.5 0.5 0.5 1", Command = $"nav_maze_page {pageIndex + 1}" },
                    RectTransform = { AnchorMin = "0.8 0.05", AnchorMax = "0.9 0.15" },
                    Text = { Text = "NEXT ▶", FontSize = 14, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" }
                }, panelName);
            }

            // Page indicator
            int totalPages = (int)Math.Ceiling((double)mazeOptions.Length / mazesPerPage);
            container.Add(new CuiLabel
            {
                Text = { Text = $"Page {pageIndex + 1} of {totalPages}", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "0.8 0.8 0.8 1" },
                RectTransform = { AnchorMin = "0.4 0.05", AnchorMax = "0.6 0.15" }
            }, panelName);

            CuiHelper.AddUi(player, container);
        }

        [ConsoleCommand("nav_maze_page")]
        private void NavigateMazePageCommand(ConsoleSystem.Arg arg)
        {
            if (!mazeVotingActive || arg.Connection == null)
                return;

            if (arg.Args.Length > 0)
            {
                int pageIndex = Convert.ToInt32(arg.Args[0]);
                currentMazePage = pageIndex;
                BasePlayer player = arg.Connection.player as BasePlayer;
                CuiHelper.DestroyUi(player, "MazeVotingUI");
                CreateMazeVotingUI(player, pageIndex);
            }
        }

        private void StartRoamsEvent(string biome)
        {
            if (AutomaticRoamBubble == null || !AutomaticRoamBubble.IsLoaded)
            {
                PrintWarning("AutomaticRoamBubble plugin not found or not loaded!");
                EndEvent();
                return;
            }

            // Call the StartRoamEvent hook method with the selected biome
            AutomaticRoamBubble.Call("StartRoamEvent", biome.ToLower());

            // After starting, CanStartRoamEvent should return false (because there's now an active roam)
            // If it still returns true, it means the roam failed to start
            object canStart = AutomaticRoamBubble.Call("CanStartRoamEvent");
            if (canStart is bool && (bool)canStart == true)
            {
                PrintWarning($"Failed to start roam event in {biome} biome via AutomaticRoamBubble!");
                EndEvent();
                return;
            }

            // Use VoteDurationMinutes for the event duration
            int durationSeconds = config.VoteDurationMinutes * 60;
            timer.Once(durationSeconds, EndEvent);
        }

        private void StartMazeEvent(string maze)
        {
            if (AwakenMaze == null || !AwakenMaze.IsLoaded)
            {
                PrintWarning("AwakenMaze plugin not found or not loaded!");
                EndEvent();
                return;
            }

            // Call the maze plugin with the selected map
            object result = AwakenMaze.Call("StartMazeEvent", maze ?? "Maze1");
            if (result == null)
            {
                PrintWarning($"Failed to start maze event with map {maze} via AwakenMaze!");
                EndEvent();
                return;
            }

            Broadcast($"A maze event has started with map {maze ?? "Maze1"}! Use /maze to join the event.");
            int durationSeconds = config.VoteDurationMinutes * 60;
            timer.Once(durationSeconds, EndEvent);
        }

        private void StartControlEvent(string controlEventType = "Control Point")
        {
            if (AwakenControlEvent == null || !AwakenControlEvent.IsLoaded)
            {
                PrintWarning("AwakenControlEvent plugin not found or not loaded!");
                EndEvent();
                return;
            }

            // Convert control event type to parameter for the plugin
            string eventParameter = controlEventType.ToLower().Replace(" ", "");
            object result = AwakenControlEvent.Call("StartEvent", eventParameter);
            if (result == null)
            {
                PrintWarning($"Failed to start {controlEventType} event via AwakenControlEvent!");
                EndEvent();
                return;
            }

            Broadcast($"A {controlEventType} event has started! Use /control to join the battle!");
            int durationSeconds = config.VoteDurationMinutes * 60;
            timer.Once(durationSeconds, EndEvent);
        }

        private void StartSulfurEvent(string monumentShortName)
        {
            if (AwakenSulfurEvent == null || !AwakenSulfurEvent.IsLoaded)
            {
                PrintWarning("AwakenSulfurEvent plugin not found or not loaded!");
                EndEvent();
                return;
            }

            // Check if there are any available arenas
            var availableArenas = AwakenSulfurEvent.Call("API_GetAvailableArenas") as List<string>;
            if (availableArenas == null || availableArenas.Count == 0)
            {
                PrintWarning("No sulfur event arenas are set up! Use /se set command to create arenas first!");
                Broadcast("No sulfur event locations available! Please contact an admin.");
                EndEvent();
                return;
            }

            // Try to find an arena that matches the monument, or use the first available
            string arenaName = availableArenas.FirstOrDefault();
            if (!string.IsNullOrEmpty(monumentShortName))
            {
                // Look for an arena that matches the monument
                foreach (var arena in availableArenas)
                {
                    var arenaInfo = AwakenSulfurEvent.Call("API_GetArenaInfo", arena) as Dictionary<string, object>;
                    if (arenaInfo != null && arenaInfo.ContainsKey("MonumentName"))
                    {
                        string arenaMonument = arenaInfo["MonumentName"] as string;
                        if (arenaMonument == monumentShortName)
                        {
                            arenaName = arena;
                            break;
                        }
                    }
                }
            }

            // Start the sulfur event using the API
            var result = AwakenSulfurEvent.Call("API_StartEventAtArena", arenaName);
            if (result == null || !(bool)result)
            {
                PrintWarning($"Failed to start sulfur event at arena '{arenaName}' via AwakenSulfurEvent API!");
                Broadcast("Failed to start sulfur event! Please contact an admin.");
                EndEvent();
                return;
            }

            // Get display name for broadcast
            string displayName = monumentShortName;
            if (!string.IsNullOrEmpty(monumentShortName) && config.PopularMonuments.ContainsKey(monumentShortName))
            {
                displayName = config.PopularMonuments[monumentShortName];
            }

            string locationText = !string.IsNullOrEmpty(displayName) ? $" at {displayName}" : "";
            Broadcast($"A sulfur event has started{locationText}! Check your map for the location!");
            int durationSeconds = config.VoteDurationMinutes * 60;
            timer.Once(durationSeconds, EndEvent);
        }

        #region Helper Methods
        private string GetFormattedTimeRemaining()
        {
            if (!isVoteActive || voteEndTime == null)
                return "00:00";

            var timeLeft = voteEndTime.Value - DateTime.Now;
            if (timeLeft.TotalSeconds <= 0)
                return "00:00";

            int minutes = (int)timeLeft.TotalMinutes;
            int seconds = timeLeft.Seconds;
            return $"{minutes:00}:{seconds:00}";
        }

        private void UpdateTimerForAllPlayers()
        {
            if (!isVoteActive) return;

            string timeRemaining = GetFormattedTimeRemaining();
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player != null && player.IsConnected)
                {
                    // Update timer label for players who have the UI open
                    CuiHelper.DestroyUi(player, "TimerLabel");

                    var container = new CuiElementContainer();
                    container.Add(new CuiLabel
                    {
                        Text = { Text = timeRemaining, FontSize = 16, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                        RectTransform = { AnchorMin = "0.02 0", AnchorMax = "0.15 1" }
                    }, "HeaderPanel", "TimerLabel");

                    CuiHelper.AddUi(player, container);
                }
            }
        }
        #endregion

        private void EndEvent()
        {
            eventRunning = false;
            votingActive = false;
            biomeVotingActive = false;
            monumentVotingActive = false;
            mazeVotingActive = false;
            controlVotingActive = false;

            foreach (var player in BasePlayer.activePlayerList)
            {
                CuiHelper.DestroyUi(player, "VotingUI");
                CuiHelper.DestroyUi(player, "BiomeVotingUI");
                CuiHelper.DestroyUi(player, "MonumentVotingUI");
                CuiHelper.DestroyUi(player, "MazeVotingUI");
                CuiHelper.DestroyUi(player, "ControlVotingUI");
            }
        }

        private void Broadcast(string message)
        {
            // Make vote-related messages bigger and more prominent
            string formattedMessage = message;

            // Check if this is a vote-related message and format it prominently
            if (message.Contains("vote") || message.Contains("Vote") || message.Contains("VOTE"))
            {
                formattedMessage = $"<size=18><color=#FFD700>{message}</color></size>";
            }
            else if (message.Contains("event") && (message.Contains("started") || message.Contains("Starting")))
            {
                formattedMessage = $"<size=16><color=#00FF00>{message}</color></size>";
            }
            else if (message.Contains("ended") || message.Contains("winning"))
            {
                formattedMessage = $"<size=16><color=#FF6B35>{message}</color></size>";
            }

            foreach (var player in BasePlayer.activePlayerList)
            {
                player.ChatMessage(formattedMessage);
            }
        }
    }
}