using Newtonsoft.Json;
using Oxide.Core.Plugins;
using Rust;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace Oxide.Plugins
{
    [Info("Awaken Action Blocker", "Skelee", "1.1.0")]
    [Description("Blocks player actions based on combat and raid activities.")]

    public class AwakenActionBlocker : CovalencePlugin
    {
        #region Classes
        public class RaidZone : MonoBehaviour
        {
            public List<BasePlayer> Players = new List<BasePlayer>();
            public float TimeLeft = 0;

            private void Awake()
            {
                gameObject.layer = (int)Layer.Reserved1;
                gameObject.name = "Raid Zone";
            }

            private void Update()
            {
                TimeLeft -= Time.deltaTime;
                if (TimeLeft <= 0) DeleteZone();
            }

            private void OnTriggerEnter(Collider other)
            {
                if (other?.ToBaseEntity() is not BasePlayer player || Players.Contains(player)) return;

                Instance.raidBlockPlayers[player.UserIDString] = this;
                Players.Add(player);
                player.ChatMessage("<color=#7000fd>Awaken</color> <color=#606060>»</color> You are <color=red>currently now</color> raid blocked!");
            }

            private void OnTriggerExit(Collider other)
            {
                if (other?.ToBaseEntity() is not BasePlayer player || !Players.Contains(player)) return;
                Instance.raidBlockPlayers.Remove(player.UserIDString);
                Players.Remove(player);
                player.ChatMessage("<color=#7000fd>Awaken</color> <color=#606060>»</color> You are <color=green>no longer</color> raid blocked!");
            }

            public void DeleteZone()
            {
                Players.Where(p => p != null).ToList().ForEach(p =>
                {
                    Instance.raidBlockPlayers.Remove(p.UserIDString);
                    p.ChatMessage("<color=#7000fd>Awaken</color> <color=#606060>»</color> You are <color=green>no longer</color> raid blocked!");
                });
                Instance.raidBlockZones.Remove(this);
                if (gameObject != null) Destroy(gameObject);
            }
        }

        private class CombatBlockInfo
        {
            public Timer? Timer { get; set; } = null;
            public int TimeLeft { get; set; } = 0;
        }

        private class HealComponent : MonoBehaviour
        {
            private BaseCombatEntity Entity => GetComponent<BaseCombatEntity>();
            private bool MaxHealth => Math.Abs(Entity.MaxHealth() - Entity.Health()) < 0.1f;

            private void Awake() => CancelInvoke(nameof(Regen));

            private void OnDestroy() => CancelInvoke(nameof(Regen));

            public void OnChangedState()
            {
                if (config.RaidBlock.BuildingDamage.SpawnHealth > 0)
                    Entity.health = config.RaidBlock.BuildingDamage.SpawnHealth;

                if (config.RaidBlock.BuildingDamage.HealRate > 0 && config.RaidBlock.BuildingDamage.HealAmount > 0)
                {
                    CancelInvoke(nameof(Regen));
                    InvokeRepeating(nameof(Regen), config.RaidBlock.BuildingDamage.HealRate, config.RaidBlock.BuildingDamage.HealRate);
                }
            }

            private void Regen()
            {
                if (MaxHealth || !Entity.IsValid()) { Destroy(this); return; }
                if (Entity.SecondsSinceAttacked < config.RaidBlock.BuildingDamage.AttackHealTime) return;
                Entity.Heal(config.RaidBlock.BuildingDamage.HealAmount);
            }
        }
        #endregion

        #region Defines
        [PluginReference] private Plugin? ActionBlocker;
        [PluginReference] private Plugin? Clans;
        public static AwakenActionBlocker? Instance;
        private readonly List<RaidZone> raidBlockZones = new List<RaidZone>();
        private readonly Dictionary<string, CombatBlockInfo> combatBlockPlayers = new Dictionary<string, CombatBlockInfo>();
        private readonly Dictionary<string, RaidZone> raidBlockPlayers = new Dictionary<string, RaidZone>();
        #endregion

        #region Config
        private static Configuration? config;

        private record BuildingDamage
        {
            [JsonProperty("Use Building Damaged?")]
            public bool UseBuildingDamage { get; init; } = true;

            [JsonProperty("Spawn Health")]
            public int SpawnHealth { get; init; } = 125;

            [JsonProperty("Heal Rate (Secs)")]
            public float HealRate { get; init; } = 1f;

            [JsonProperty("Heal Amount (0 = Disabled)")]
            public float HealAmount { get; init; } = 1f;

            [JsonProperty("After Attack Heal Time (Secs)")]
            public float AttackHealTime { get; init; } = 60;
        }

        private record CombatBlock(
            [property: JsonProperty("Use Combat Block?")] bool UseCombatBlock = true,
            [property: JsonProperty("Combat Block Time (Mins)")] int BlockTime = 3);

        private static readonly CombatBlock DefaultCombatBlock = new CombatBlock();

        private record RaidBlock
        {
            [JsonProperty("Use Raid Block?")]
            public bool UseRaidBlock { get; init; } = true;

            [JsonProperty("Raid Block Time (Secs)")]
            public float BlockTime { get; init; } = 600f;

            [JsonProperty("Raid Block Radius")]
            public float Radius { get; init; } = 100f;

            [JsonProperty("Building Damage Settings")]
            public BuildingDamage BuildingDamage { get; init; } = new BuildingDamage();
        }

        private static readonly RaidBlock DefaultRaidBlock = new RaidBlock();

        private record Configuration
        {
            [JsonProperty("Combat Block Settings")]
            public CombatBlock CombatBlock { get; init; } = DefaultCombatBlock;

            [JsonProperty("Raid Block Settings")]
            public RaidBlock RaidBlock { get; init; } = DefaultRaidBlock;
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>() ?? LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                PrintWarning("Creating new config file.");
                LoadDefaultConfig();
            }
        }

        private Configuration LoadDefaultConfig()
        {
            var defaultConfig = new Configuration();
            Config.WriteObject(defaultConfig);
            return defaultConfig;
        }

        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Hooks
        private void Loaded()
        {
            Instance = this;

            if (!config?.RaidBlock.UseRaidBlock == true)
            {
                Unsubscribe(nameof(OnEntityTakeDamage));
                Unsubscribe(nameof(OnEntityDeath));
                Unsubscribe(nameof(OnEntityBuilt));
                Unsubscribe(nameof(OnStructureUpgrade));
            }

            if (!config?.RaidBlock.BuildingDamage.UseBuildingDamage == true)
            {
                Unsubscribe(nameof(OnEntityBuilt));
                Unsubscribe(nameof(OnStructureUpgrade));
            }

            if (!config?.CombatBlock.UseCombatBlock == true)
                Unsubscribe(nameof(OnPlayerAttack));
        }

        private void Unload()
        {
            raidBlockZones.ToList().ForEach(zone => zone.DeleteZone());
            config = null;
            Instance = null;
        }

        private void OnPlayerDisconnected(BasePlayer? player, string reason)
        {
            if (player == null) return;
            if (raidBlockPlayers.TryGetValue(player.UserIDString, out var raidZone))
            {
                raidZone.Players.Remove(player);
                raidBlockPlayers.Remove(player.UserIDString);
            }

            if (combatBlockPlayers.TryGetValue(player.UserIDString, out var combatBlock) && combatBlock.Timer != null)
            {
                combatBlock.Timer.Destroy();
                combatBlockPlayers.Remove(player.UserIDString);
            }
        }

        private object? OnEntityTakeDamage(StabilityEntity? entity, HitInfo? info)
        {
            return null;
        }

        private void OnEntityDeath(BaseCombatEntity? entity, HitInfo? info)
        {
            if (entity == null) return;
        if (entity is StabilityEntity || entity is BuildingBlock || entity is SimpleBuildingBlock)
            {
                if (info == null || info.damageTypes.GetMajorityDamageType() != DamageType.Explosion || info.WeaponPrefab == null) return;
                if (info.Weapon?.GetItem()?.GetHeldEntity() is BaseProjectile projectile && projectile.primaryMagazine.ammoType.itemid == -1321651331) return;

                if (TryGetRaidBlockZone(entity?.transform.position, out var raidZone))
                    raidZone.TimeLeft = config?.RaidBlock.BlockTime ?? 600f;
                else
                    CreateRaidblockZone(entity);
            }
        }

        private void OnPlayerDeath(BasePlayer? player, HitInfo? info)
        {
            if (player == null) return;
            if (raidBlockPlayers.TryGetValue(player.UserIDString, out var raidZone))
            {
                player.ChatMessage("<color=#7000fd>Awaken</color> <color=#606060>»</color> You are <color=green>no longer</color> raid blocked!");
                raidZone.Players.Remove(player);
                raidBlockPlayers.Remove(player.UserIDString);
            }

            if (combatBlockPlayers.TryGetValue(player.UserIDString, out var combatBlock) && combatBlock.Timer != null)
            {
                player.ChatMessage("<color=#7000fd>Awaken</color> <color=#606060>»</color> You are <color=green>no longer</color> combat blocked.");
                combatBlock.Timer.Destroy();
                combatBlockPlayers.Remove(player.UserIDString);
            }
        }

        private void OnPlayerAttack(BasePlayer? attacker, HitInfo? info)
        {
            if (info == null || info.HitEntity is not BasePlayer victim || !attacker?.userID.IsSteamId() == true || !victim.userID.IsSteamId() == true) return;
            if (attacker.currentTeam != 0 && attacker.currentTeam == victim.currentTeam) return;
            if (Clans?.Call<bool>("SameClan", attacker.userID, victim.userID) == true) return;

            HandleCombatBlock(attacker);
            HandleCombatBlock(victim);
        }

        private void OnEntityBuilt(Planner? plan, GameObject? go)
        {
            if (plan?.GetOwnerPlayer() is not { } player) return;
            CheckEntity(go?.ToBaseEntity());
        }

        private void OnStructureUpgrade(BaseEntity? entity, BasePlayer? player, BuildingGrade.Enum grade)
        {
            if (player != null) CheckEntity(entity);
        }
        #endregion

        #region Functions
        private bool TryGetRaidBlockZone(Vector3? position, out RaidZone? output)
        {
            output = null;
            if (!position.HasValue) return false;
            output = raidBlockZones.FirstOrDefault(zone => Vector3.Distance(position.Value, zone.transform.position) < (config?.RaidBlock.Radius ?? 100f));
            return output != null;
        }

        private void CreateRaidblockZone(BaseCombatEntity? entity)
        {
            var raidZone = new GameObject();
            raidZone.transform.position = entity?.transform.position ?? Vector3.zero;
            raidZone.transform.rotation = Quaternion.Euler(0f, 1f, 0f);

            var rb = raidZone.AddComponent<Rigidbody>();
            rb.isKinematic = true;
            rb.useGravity = false;
            rb.detectCollisions = true;
            rb.collisionDetectionMode = CollisionDetectionMode.Discrete;

            var zoneComp = raidZone.AddComponent<RaidZone>();
            zoneComp.TimeLeft = config?.RaidBlock.BlockTime ?? 600f;

            var col = raidZone.AddComponent<SphereCollider>();
            col.isTrigger = true;
            col.radius = config?.RaidBlock.Radius ?? 100f;

            raidZone.layer = (int)Layer.Reserved1;
            raidBlockZones.Add(zoneComp);
        }

        private void HandleCombatBlock(BasePlayer? player)
        {
            if (player == null) return;
            var blockInfo = combatBlockPlayers.GetValueOrDefault(player.UserIDString);

            if (blockInfo != null)
            {
                blockInfo.Timer?.Destroy();
                blockInfo.TimeLeft = config?.CombatBlock.BlockTime ?? 3;
                blockInfo.Timer = timer.Every(60, () =>
                {
                    if (player == null || !combatBlockPlayers.TryGetValue(player.UserIDString, out var updatedBlock)) return;
                    updatedBlock.TimeLeft--;

                    if (updatedBlock.TimeLeft == 0)
                    {
                        player.ChatMessage("<color=#7000fd>Awaken</color> <color=#606060>»</color> You are <color=green>no longer</color> combat blocked.");
                        updatedBlock.Timer?.Destroy();
                        combatBlockPlayers.Remove(player.UserIDString);
                    }
                    else
                        player.ChatMessage($"<color=#7000fd>Awaken</color> <color=#606060>»</color> Your combat block has <color=orange>{updatedBlock.TimeLeft}</color> min(s) left.");
                });
            }
            else
            {
                combatBlockPlayers[player.UserIDString] = new CombatBlockInfo { TimeLeft = config?.CombatBlock.BlockTime ?? 3 };
                player.ChatMessage($"<color=#7000fd>Awaken</color> <color=#606060>»</color> You are combat blocked for <color=orange>{config?.CombatBlock.BlockTime ?? 3}</color> min(s).");
                HandleCombatBlock(player); // Reinvoke to set up the timer
            }
        }
        
        #region API Methods
        /// <summary>
        /// API method to check if a player is raid blocked
        /// </summary>
        /// <param name="userIdString">Player's UserID as string</param>
        /// <returns>True if player is raid blocked</returns>
        public bool IsRaidBlocked(string userIdString)
        {
            return !string.IsNullOrEmpty(userIdString) && raidBlockPlayers.ContainsKey(userIdString);
        }

        /// <summary>
        /// API method to check if a player is combat blocked
        /// </summary>
        /// <param name="userIdString">Player's UserID as string</param>
        /// <returns>True if player is combat blocked</returns>
        public bool IsCombatBlocked(string userIdString)
        {
            return !string.IsNullOrEmpty(userIdString) && combatBlockPlayers.ContainsKey(userIdString);
        }

        /// <summary>
        /// API method to get remaining raid block time
        /// </summary>
        /// <param name="userIdString">Player's UserID as string</param>
        /// <returns>Remaining time in seconds, or -1 if zone-based (no time limit)</returns>
        public float GetRaidBlockTimeLeft(string userIdString)
        {
            if (string.IsNullOrEmpty(userIdString) || !raidBlockPlayers.ContainsKey(userIdString))
                return 0f;
            
            RaidZone raidZone;
            if (raidBlockPlayers.TryGetValue(userIdString, out raidZone))
            {
                return raidZone.TimeLeft;
            }

            return 0f;
        }

        /// <summary>
        /// API method to get remaining combat block time
        /// </summary>
        /// <param name="userIdString">Player's UserID as string</param>
        /// <returns>Remaining time in seconds, or 0 if not blocked</returns>
        public float GetCombatBlockTimeLeft(string userIdString)
        {
            if (string.IsNullOrEmpty(userIdString) || !combatBlockPlayers.TryGetValue(userIdString, out var combatInfo))
                return 0f;

            // Convert minutes to seconds
            return combatInfo.TimeLeft * 60f;
        }

        /// <summary>
        /// API method to get debug information about a player's block status
        /// </summary>
        /// <param name="userIdString">Player's UserID as string</param>
        /// <returns>Debug information string</returns>
        public string GetDebugInfo(string userIdString)
        {
            if (string.IsNullOrEmpty(userIdString))
                return "Invalid UserID";

            var debugInfo = new List<string>();

            if (raidBlockPlayers.TryGetValue(userIdString, out var raidZone))
            {
                debugInfo.Add($"RaidBlock: Zone-based (Time Left: {raidZone.TimeLeft:F1}s, Players in zone: {raidZone.Players.Count})");
            }

            if (combatBlockPlayers.TryGetValue(userIdString, out var combatInfo))
            {
                float timeLeft = GetCombatBlockTimeLeft(userIdString);
                debugInfo.Add($"CombatBlock: {timeLeft:F1}s left ({combatInfo.TimeLeft}m)");
            }

            if (debugInfo.Count == 0)
                debugInfo.Add("No active blocks");

            return string.Join(", ", debugInfo);
        }
        #endregion

        private void CheckEntity(BaseEntity? entity)
        {
            if (entity == null || !entity.IsValid() || entity.ShortPrefabName.Contains("external.high")) return;
            if (entity is SimpleBuildingBlock or BuildingBlock or Door)
            {
                var go = entity.gameObject;
                var healComponent = go.GetComponent<HealComponent>() ?? go.AddComponent<HealComponent>();
                healComponent.OnChangedState();
            }
        }
        #endregion
    }
}

