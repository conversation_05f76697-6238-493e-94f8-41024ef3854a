// FIXED CODE BY ABTRAL 

using HarmonyLib;
using Newtonsoft.Json;
using Oxide.Core;
using Oxide.Core.Libraries.Covalence;
using Oxide.Game.Rust.Cui;
using Rust;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using System.Net.Http;
using System.Net;

namespace Oxide.Plugins
{
    [Info("Awaken Stats", "Skelee (THE GOAT! LOL :)  ) // Abtral", "1.0.0")]
    [Description("Handles players statistics for Awaken Servers.")]
    public class AwakenStats : CovalencePlugin
    {
        #region Config
        private static Configuration? config;

        public class Configuration
        {
            [JsonProperty(PropertyName = "Server Prefix")]
            public string ServerPrefix { get; set; } = "main";

            [JsonProperty(PropertyName = "API Base URL")]
            public string ApiBaseUrl { get; set; } = "http://localhost:3002";

            [JsonProperty(PropertyName = "Enable Clan Stats")]
            public bool EnableClanStats { get; set; } = false;

            public static Configuration DefaultConfig() => new Configuration();
        }

        protected override void LoadConfig()
        {
            base.LoadConfig();
            try
            {
                config = Config.ReadObject<Configuration>();
                if (config == null) LoadDefaultConfig();
                SaveConfig();
            }
            catch (Exception e)
            {
                LogError($"Failed to load config: {e.Message}");
                LoadDefaultConfig();
            }
        }

        protected override void LoadDefaultConfig() => config = Configuration.DefaultConfig();
        protected override void SaveConfig() => Config.WriteObject(config);
        #endregion

        #region Classes
        public class PlayerStats
        {
            public string Name { get; set; } = string.Empty;
            public int Connections { get; set; }
            public DateTime Connected { get; set; }
            public int Kills { get; set; }
            public int Deaths { get; set; }
            public BodyParts Hits { get; set; } = new BodyParts();
            public int BulletsFired { get; set; }
            public int Suicides { get; set; }
            public int Wounded { get; set; }
            public int ScientistKills { get; set; }
            public int RocketsFired { get; set; }
            public int C4Thrown { get; set; }
            public int SatchelsThrown { get; set; }
            public int TCsDestroyed { get; set; }
            public WeaponStats WeaponStats { get; set; } = new WeaponStats();
            public EventStats EventStats { get; set; } = new EventStats();
        }

        public class BodyParts
        {
            public int Head { get; set; }
            public int Torso { get; set; }
            public int LeftHand { get; set; }
            public int RightHand { get; set; }
            public int LeftLeg { get; set; }
            public int RightLeg { get; set; }
            public int LeftFoot { get; set; }
            public int RightFoot { get; set; }
        }

        public class WeaponStats
        {
            public int AK47 { get; set; }
            public int LR300 { get; set; }
            public int M249 { get; set; }
            public int Snipers { get; set; }
            public int M39 { get; set; }
            public int SAR { get; set; }
            public int MP5 { get; set; }
            public int Thompson { get; set; }
            public int Shotguns { get; set; }
            public int Pistols { get; set; }
            public int Melee { get; set; }
            public int Primitive { get; set; }
        }

        public class EventStats
        {
            public int MazeWins { get; set; }
            public int RoamWins { get; set; }
            public int SulfurWins { get; set; }
            public int ControlWins { get; set; }
            public int KothWins { get; set; }
            public int BedwarsWins { get; set; }
        }

        public class ApiPlayerStats
        {
            public string steamid { get; set; } = string.Empty;
            public string name { get; set; } = string.Empty;
            public int connections { get; set; }
            public int kills { get; set; }
            public int deaths { get; set; }
            public int head_hits { get; set; }
            public int torso_hits { get; set; }
            public int leftarm_hits { get; set; }
            public int rightarm_hits { get; set; }
            public int leftleg_hits { get; set; }
            public int rightleg_hits { get; set; }
            public int leftfoot_hits { get; set; }
            public int rightfoot_hits { get; set; }
            public int bfired { get; set; }
            public int suicides { get; set; }
            public int wounded { get; set; }
            public int scientistkills { get; set; }
            public int rocketsfired { get; set; }
            public int c4thrown { get; set; }
            public int satchelsthrown { get; set; }
            public int tcsdestroyed { get; set; }
            public int ak47 { get; set; }
            public int lr300 { get; set; }
            public int m249 { get; set; }
            public int snipers { get; set; }
            public int m39 { get; set; }
            public int sar { get; set; }
            public int mp5 { get; set; }
            public int thompson { get; set; }
            public int shotguns { get; set; }
            public int pistols { get; set; }
            public int melee { get; set; }
            public int primitive { get; set; }
            public int maze_wins { get; set; }
            public int roam_wins { get; set; }
            public int sulfur_wins { get; set; }
            public int control_wins { get; set; }
            public int koth_wins { get; set; }
            public int bedwars_wins { get; set; }
        }

        public class ClanLeaderboardData
        {
            public string ClanName { get; set; } = string.Empty;
            public int MemberCount { get; set; }
            public int TotalKills { get; set; }
            public int TotalDeaths { get; set; }
            public float KDRatio { get; set; }
        }

        public class ApiClanStats
        {
            public string clanName { get; set; } = string.Empty;
            public string serverPrefix { get; set; } = string.Empty;
            public int memberCount { get; set; }
            public int totalKills { get; set; }
            public int totalDeaths { get; set; }
            public float kdRatio { get; set; }
            public string[] members { get; set; } = new string[0];
        }

        public class ApiResponse<T>
        {
            public bool success { get; set; }
            public T data { get; set; } = default(T)!;
            public string error { get; set; } = string.Empty;
        }
        #endregion

        #region Defines
        private static AwakenStats? Instance;
        private Harmony? _harmony;
        private readonly Dictionary<string, PlayerStats> playerStats = new();
        private static HttpClient? httpClient;
        #endregion

        #region Hooks
        private void Init()
        {
            Instance = this;
            _harmony = new Harmony("com.Skelee.AwakenStats");
            httpClient = new HttpClient();
        }

        private void Loaded()
        {
            try
            {
                Puts("AwakenStats plugin loaded successfully.");
                ApplyHarmonyPatches();
            }
            catch (Exception e)
            {
                LogError($"Failed to initialize plugin: {e.Message}");
            }
        }

        private void Unload()
        {
            try
            {
                _harmony?.UnpatchAll("com.Skelee.AwakenStats");
            }
            catch (Exception ex)
            {
                LogError($"Error during unload: {ex.Message}");
            }
            finally
            {
                httpClient?.Dispose();
                httpClient = null;
                config = null;
                playerStats.Clear();
                Instance = null;
            }
        }

        private void OnNewSave(string filename)
        {
            try
            {
                _ = WipeStatsAsync();
                playerStats.Clear();
                Puts("Player stats cleared for new wipe.");
            }
            catch (Exception ex)
            {
                LogError($"Error during wipe: {ex.Message}");
            }
        }

        private void OnServerInitialized(bool initial)
        {
            ServerMgr.Instance.StartCoroutine(StartupAsync());
            foreach (var player in BasePlayer.activePlayerList)
            {
                if (player != null)
                {
                    OnPlayerConnected(player);
                }
            }
        }

        private void OnPlayerConnected(BasePlayer player)
        {
            if (player == null || string.IsNullOrEmpty(player.UserIDString)) return;

            if (!playerStats.ContainsKey(player.UserIDString))
            {
                playerStats[player.UserIDString] = new PlayerStats { Name = RemoveSpecialCharacters(player.displayName), Connections = 1, Connected = DateTime.Now };
                CheckAndInsertPlayer(player);
            }
            else
            {
                if (playerStats.TryGetValue(player.UserIDString, out var stats) && stats != null)
                {
                    stats.Connections++;
                    stats.Connected = DateTime.Now;
                    stats.Name = RemoveSpecialCharacters(player.displayName);
                }
            }
        }

        private void OnPlayerDisconnected(BasePlayer player, string reason)
        {
            if (player == null || string.IsNullOrEmpty(player.UserIDString)) return;
            _ = SavePlayerStatsAsync(player, true);
        }

        private void OnEntityDeath(BasePlayer player, HitInfo info)
        {
            try
            {
                if (player == null || string.IsNullOrEmpty(player.UserIDString)) return;
                if (!playerStats.TryGetValue(player.UserIDString, out var playerStat) || playerStat == null) return;

                if (player.lastDamage == DamageType.Suicide) playerStat.Suicides++;
                else playerStat.Deaths++;

                if (info == null) return;
                var attacker = info.InitiatorPlayer;
                if (attacker == null || attacker == player || string.IsNullOrEmpty(attacker.UserIDString)) return;
                
                if (!player.HasPlayerFlag(BasePlayer.PlayerFlags.DisplaySash)) return;
                
                if (attacker.currentTeam != 0 && player.currentTeam != 0 && attacker.currentTeam == player.currentTeam) return;

                if (playerStats.TryGetValue(attacker.UserIDString, out var attackerStat) && attackerStat != null)
                {
                    attackerStat.Kills++;
                    var weapon = info.Weapon;
                    if (weapon != null)
                    {
                        var weaponItem = weapon.GetItem();
                        if (weaponItem?.info?.shortname != null)
                        {
                            UpdateWeaponStats(attackerStat, weaponItem.info.shortname);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"OnEntityDeath error: {ex.Message}");
            }
        }

        private void OnEntityDeath(BuildingPrivlidge entity, HitInfo info)
        {
            try
            {
                if (entity == null || info == null) return;
                var attacker = info.InitiatorPlayer;
                if (attacker != null && !string.IsNullOrEmpty(attacker.UserIDString) &&
                    playerStats.TryGetValue(attacker.UserIDString, out var stats) && stats != null)
                {
                    stats.TCsDestroyed++;
                }
            }
            catch (Exception ex)
            {
                LogError($"OnEntityDeath (TC) error: {ex.Message}");
            }
        }

        private void OnEntityDeath(NPCPlayer scientist, HitInfo info)
        {
            try
            {
                if (scientist == null || info == null) return;
                var attacker = info.InitiatorPlayer;
                if (attacker != null && !string.IsNullOrEmpty(attacker.UserIDString) &&
                    playerStats.TryGetValue(attacker.UserIDString, out var stats) && stats != null)
                {
                    stats.ScientistKills++;
                }
            }
            catch (Exception ex)
            {
                LogError($"OnEntityDeath (Scientist) error: {ex.Message}");
            }
        }

        private void OnExplosiveThrown(BasePlayer player, BaseEntity entity)
        {
            try
            {
                if (player == null || string.IsNullOrEmpty(player.UserIDString) || entity == null) return;
                if (!playerStats.TryGetValue(player.UserIDString, out var stats) || stats == null) return;

                var activeItem = player.GetActiveItem();
                if (activeItem?.info?.shortname != null)
                {
                    var itemShortname = activeItem.info.shortname;
                    if (itemShortname == "explosive.timed") stats.C4Thrown++;
                    else if (itemShortname == "explosive.satchel") stats.SatchelsThrown++;
                }
            }
            catch (Exception ex)
            {
                LogError($"OnExplosiveThrown error: {ex.Message}");
            }
        }

        private void OnRocketLaunched(BasePlayer player, BaseEntity entity)
        {
            try
            {
                if (player == null || string.IsNullOrEmpty(player.UserIDString) || entity == null) return;
                if (playerStats.TryGetValue(player.UserIDString, out var stats) && stats != null)
                {
                    stats.RocketsFired++;
                }
            }
            catch (Exception ex)
            {
                LogError($"OnRocketLaunched error: {ex.Message}");
            }
        }

        private object? OnPlayerWound(BasePlayer player, HitInfo hitInfo)
        {
            try
            {
                if (player == null || string.IsNullOrEmpty(player.UserIDString) || hitInfo == null) return null;
                if (playerStats.TryGetValue(player.UserIDString, out var stats) && stats != null)
                {
                    stats.Wounded++;
                }
            }
            catch (Exception ex)
            {
                LogError($"OnPlayerWound error: {ex.Message}");
            }
            return null;
        }
        #endregion

        #region Harmony
        private void ApplyHarmonyPatches()
        {
            try
            {
                var clProjectMethod = AccessTools.Method(typeof(BaseProjectile), "CLProject");
                if (clProjectMethod != null)
                {
                    _harmony?.Patch(
                        clProjectMethod,
                        transpiler: new HarmonyMethod(typeof(OnWeaponFired), nameof(OnWeaponFired.Transpiler)));
                    Puts("Successfully patched BaseProjectile.CLProject for bullet tracking");
                }
                else
                {
                    LogError("Could not find BaseProjectile.CLProject method - bullet tracking disabled");
                }

                var onAttackedMethod = AccessTools.Method(typeof(BasePlayer), "OnAttacked", new Type[] { typeof(HitInfo) });
                if (onAttackedMethod != null)
                {
                    _harmony?.Patch(
                        onAttackedMethod,
                        transpiler: new HarmonyMethod(typeof(OnPlayerAttacked), nameof(OnPlayerAttacked.Transpiler)));
                    Puts("Successfully patched BasePlayer.OnAttacked for hit tracking");
                }
                else
                {
                    LogError("Could not find BasePlayer.OnAttacked method - hit tracking disabled");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error applying Harmony patches: {ex.Message}");
                LogError("Plugin will continue without advanced tracking features");
            }
        }

        private static class Stats
        {
            public static void AddStatsPatch(BasePlayer player, string statType)
            {
                try
                {
                    if (Instance == null || player == null || string.IsNullOrEmpty(player.UserIDString)) return;

                    if (!Instance.playerStats.TryGetValue(player.UserIDString, out var stats) || stats == null) return;

                    if (statType == "WeaponFired") stats.BulletsFired++;
                }
                catch (Exception ex)
                {
                    Instance?.LogError($"AddStatsPatch error: {ex.Message}");
                }
            }
        }

        private static class OnWeaponFired
        {
            public static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                try
                {
                    var list = instructions.ToList();
                    var gestureLine = list.FindIndex(i => i.opcode == OpCodes.Callvirt && i.operand is MethodInfo { Name: "get_InGesture" });
                    if (gestureLine == -1)
                    {
                        Instance?.LogError("OnWeaponFired transpiler: Could not find get_InGesture call");
                        return list;
                    }

                    var methodInfo = AccessTools.Method(typeof(Stats), nameof(Stats.AddStatsPatch));
                    if (methodInfo == null)
                    {
                        Instance?.LogError("OnWeaponFired transpiler: Could not find AddStatsPatch method");
                        return list;
                    }

                    list.InsertRange(gestureLine, new[]
                    {
                        new CodeInstruction(OpCodes.Ldloc_0),
                        new CodeInstruction(OpCodes.Ldstr, "WeaponFired"),
                        new CodeInstruction(OpCodes.Call, methodInfo)
                    });
                    return list;
                }
                catch (Exception ex)
                {
                    Instance?.LogError($"OnWeaponFired transpiler error: {ex.Message}");
                    return instructions.ToList();
                }
            }
        }

        private static class OnPlayerAttacked
        {
            public static IEnumerable<CodeInstruction> Transpiler(IEnumerable<CodeInstruction> instructions)
            {
                var list = instructions.ToList();
                var immortalLine = list.FindIndex(i => i.opcode == OpCodes.Call && i.operand is MethodInfo { Name: "IsImmortalTo" });
                if (immortalLine == -1)
                {
                    Instance?.LogError("OnPlayerAttacked transpiler: Could not find IsImmortalTo call");
                    return list;
                }

                var insertionPoint = Math.Max(0, immortalLine - 5);

                var methodInfo = AccessTools.Method(typeof(OnPlayerAttacked), nameof(HandleHits));
                if (methodInfo == null)
                {
                    Instance?.LogError("OnPlayerAttacked transpiler: Could not find HandleHits method");
                    return list;
                }

                try
                {
                    list.InsertRange(insertionPoint, new[]
                    {
                        new CodeInstruction(OpCodes.Ldarg_0),
                        new CodeInstruction(OpCodes.Ldarg_1),
                        new CodeInstruction(OpCodes.Call, methodInfo)
                    });
                }
                catch (Exception ex)
                {
                    Instance?.LogError($"OnPlayerAttacked transpiler error: {ex.Message}");
                    return list;
                }
                return list;
            }

            private static void HandleHits(BasePlayer player, HitInfo info)
            {
                try
                {
                    if (Instance == null || player == null || info == null) return;

                    var attacker = info.InitiatorPlayer;
                    if (attacker == null || string.IsNullOrEmpty(attacker.UserIDString)) return;

                    if (!Instance.playerStats.TryGetValue(attacker.UserIDString, out var stats) || stats?.Hits == null) return;

                    var boneName = info.boneName?.ToLower();
                    if (string.IsNullOrEmpty(boneName)) return;

                    switch (boneName)
                    {
                        case "head": stats.Hits.Head++; break;
                        case "neck":
                        case "chest":
                        case "spine":
                        case "stomach":
                        case "pelvis":
                        case "hip":
                        case "lowerspine": stats.Hits.Torso++; break;
                        case "l_hip":
                        case "l_knee":
                        case "left knee": stats.Hits.LeftLeg++; break;
                        case "r_hip":
                        case "r_knee":
                        case "right knee": stats.Hits.RightLeg++; break;
                        case "l_foot":
                        case "l_toe":
                        case "left foot": stats.Hits.LeftFoot++; break;
                        case "r_foot":
                        case "r_toe":
                        case "right foot": stats.Hits.RightFoot++; break;
                        case "l_clavicle":
                        case "l_upperarm":
                        case "l_forearm":
                        case "l_hand":
                        case "left arm":
                        case "left forearm":
                        case "left hand": stats.Hits.LeftHand++; break;
                        case "r_clavicle":
                        case "r_upperarm":
                        case "r_forearm":
                        case "r_hand":
                        case "right arm":
                        case "right forearm":
                        case "right hand": stats.Hits.RightHand++; break;
                    }
                }
                catch (Exception ex)
                {
                    Instance?.LogError($"HandleHits error: {ex.Message}");
                }
            }
        }
        #endregion

        #region Functions
        private string RemoveSpecialCharacters(string str)
        {
            if (string.IsNullOrEmpty(str)) return string.Empty;
            var sb = new StringBuilder();
            foreach (var c in str.Where(c => char.IsLetterOrDigit(c) || c == ' '))
                sb.Append(c);
            return sb.ToString().TrimEnd();
        }

        private string CalculateAccuracy(int bulletsFired, BodyParts hits)
        {
            if (hits == null) return "0%";
            int totalHits = hits.Head + hits.Torso + hits.LeftHand + hits.RightHand +
                            hits.LeftLeg + hits.RightLeg + hits.LeftFoot + hits.RightFoot;
            if (bulletsFired == 0) return "0%";
            float accuracy = (totalHits / (float)bulletsFired) * 100;
            return $"{accuracy:F1}%";
        }



        #region Public API Methods for Event Plugins

        /// <summary>
        /// Adds an event win for a specific player by Steam ID
        /// </summary>
        /// <param name="playerId">Player's Steam ID</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control"</param>
        public void AddEventWin(string playerId, string eventType)
        {
            if (string.IsNullOrEmpty(playerId) || string.IsNullOrEmpty(eventType)) return;

            if (!playerStats.TryGetValue(playerId, out var stats) || stats?.EventStats == null) return;

            switch (eventType.ToLower())
            {
                case "maze":
                    stats.EventStats.MazeWins++;
                    break;
                case "roam":
                    stats.EventStats.RoamWins++;
                    break;
                case "sulfur":
                    stats.EventStats.SulfurWins++;
                    break;
                case "control":
                    stats.EventStats.ControlWins++;
                    break;
            }

            Puts($"[API] Added {eventType} win for player {playerId} ({stats.Name})");
        }

        /// <summary>
        /// Adds an event win for a specific player using BasePlayer object
        /// </summary>
        /// <param name="player">BasePlayer object</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control"</param>
        public void AddEventWinForPlayer(BasePlayer player, string eventType)
        {
            if (player == null || string.IsNullOrEmpty(player.UserIDString)) return;
            AddEventWin(player.UserIDString, eventType);
        }

        /// <summary>
        /// Adds event wins for multiple players (e.g., clan members)
        /// </summary>
        /// <param name="playerIds">List of player Steam IDs</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control"</param>
        public void AddEventWinForPlayers(List<string> playerIds, string eventType)
        {
            if (playerIds == null || playerIds.Count == 0 || string.IsNullOrEmpty(eventType)) return;

            foreach (var playerId in playerIds)
            {
                AddEventWin(playerId, eventType);
            }

            Puts($"[API] Added {eventType} wins for {playerIds.Count} players");
        }

        /// <summary>
        /// Adds event wins for multiple players using BasePlayer objects
        /// </summary>
        /// <param name="players">List of BasePlayer objects</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control"</param>
        public void AddEventWinForPlayers(List<BasePlayer> players, string eventType)
        {
            if (players == null || players.Count == 0 || string.IsNullOrEmpty(eventType)) return;

            var playerIds = players.Where(p => p != null && !string.IsNullOrEmpty(p.UserIDString))
                                  .Select(p => p.UserIDString)
                                  .ToList();

            AddEventWinForPlayers(playerIds, eventType);
        }

        /// <summary>
        /// Adds event win for an entire clan by clan tag
        /// </summary>
        /// <param name="clanTag">Clan tag</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control"</param>
        public void AddEventWinForClan(string clanTag, string eventType)
        {
            if (string.IsNullOrEmpty(clanTag) || string.IsNullOrEmpty(eventType)) return;

            var AwakenClans = plugins.Find("awakenClans");
            if (AwakenClans == null)
            {
                LogWarning("[API] AwakenClans plugin not found, cannot add event win for clan");
                return;
            }

            try
            {
                var membersList = AwakenClans.Call("GetClanMembers", clanTag) as List<ulong>;
                if (membersList != null && membersList.Count > 0)
                {
                    var memberIds = membersList.Select(id => id.ToString()).ToList();
                    AddEventWinForPlayers(memberIds, eventType);

                    Puts($"[API] Added {eventType} win for clan {clanTag} ({membersList.Count} members)");
                }
                else
                {
                    LogWarning($"[API] No members found for clan {clanTag}");
                }
            }
            catch (Exception ex)
            {
                LogError($"[API] Error adding event win for clan {clanTag}: {ex.Message}");
            }
        }

        /// <summary>
        /// Adds event win for an entire clan by clan name
        /// </summary>
        /// <param name="clanName">Clan name</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control"</param>
        public void AddEventWinForClanByName(string clanName, string eventType)
        {
            if (string.IsNullOrEmpty(clanName) || string.IsNullOrEmpty(eventType)) return;

            var AwakenClans = plugins.Find("awakenClans");
            if (AwakenClans == null)
            {
                LogWarning("[API] AwakenClans plugin not found, cannot add event win for clan");
                return;
            }

            try
            {
                var clanTagObj = AwakenClans.Call("GetClanTag", clanName);
                if (clanTagObj is string clanTag && !string.IsNullOrEmpty(clanTag))
                {
                    AddEventWinForClan(clanTag, eventType);
                }
                else
                {
                    LogWarning($"[API] Could not find clan tag for clan name: {clanName}");
                }
            }
            catch (Exception ex)
            {
                LogError($"[API] Error adding event win for clan {clanName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets event win count for a specific player and event type
        /// </summary>
        /// <param name="playerId">Player's Steam ID</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control"</param>
        /// <returns>Number of wins for the specified event type</returns>
        public int GetPlayerEventWins(string playerId, string eventType)
        {
            if (string.IsNullOrEmpty(playerId) || string.IsNullOrEmpty(eventType)) return 0;

            if (!playerStats.TryGetValue(playerId, out var stats) || stats?.EventStats == null) return 0;

            return eventType.ToLower() switch
            {
                "maze" => stats.EventStats.MazeWins,
                "roam" => stats.EventStats.RoamWins,
                "sulfur" => stats.EventStats.SulfurWins,
                "control" => stats.EventStats.ControlWins,
                "koth" => stats.EventStats.KothWins,
                "bedwars" => stats.EventStats.BedwarsWins,
                _ => 0
            };
        }

        /// <summary>
        /// Gets total event wins for a player across all event types
        /// </summary>
        /// <param name="playerId">Player's Steam ID</param>
        /// <returns>Total event wins</returns>
        public int GetPlayerTotalEventWins(string playerId)
        {
            if (string.IsNullOrEmpty(playerId)) return 0;

            if (!playerStats.TryGetValue(playerId, out var stats) || stats?.EventStats == null) return 0;

            return stats.EventStats.MazeWins + stats.EventStats.RoamWins +
                   stats.EventStats.SulfurWins + stats.EventStats.ControlWins +
                   stats.EventStats.KothWins + stats.EventStats.BedwarsWins;
        }

        /// <summary>
        /// Gets clan event wins by clan tag
        /// </summary>
        /// <param name="clanTag">Clan tag</param>
        /// <param name="eventType">Event type: "maze", "roam", "sulfur", "control", "koth", "bedwars"</param>
        /// <returns>Total clan wins for the specified event type</returns>
        public int GetClanEventWins(string clanTag, string eventType)
        {
            if (string.IsNullOrEmpty(clanTag) || string.IsNullOrEmpty(eventType)) return 0;

            var AwakenClans = plugins.Find("awakenClans");
            if (AwakenClans == null) return 0;

            try
            {
                var membersList = AwakenClans.Call("GetClanMembers", clanTag) as List<ulong>;
                if (membersList == null || membersList.Count == 0) return 0;

                int totalWins = 0;
                foreach (var memberId in membersList)
                {
                    totalWins += GetPlayerEventWins(memberId.ToString(), eventType);
                }

                return totalWins;
            }
            catch (Exception ex)
            {
                LogError($"[API] Error getting clan event wins for {clanTag}: {ex.Message}");
                return 0;
            }
        }

        #endregion

        private void CheckAndInsertPlayer(BasePlayer player)
        {
            if (player == null || string.IsNullOrEmpty(player.UserIDString)) return;
            _ = CheckAndInsertPlayerAsync(player);
        }

        private async Task CheckAndInsertPlayerAsync(BasePlayer player)
        {
            if (player == null || string.IsNullOrEmpty(player.UserIDString)) return;

            if (config == null || httpClient == null || string.IsNullOrEmpty(config.ApiBaseUrl) || string.IsNullOrEmpty(config.ServerPrefix))
            {
                LogError("HttpClient or config/ApiBaseUrl/ServerPrefix is null, cannot check/insert player");
                return;
            }

            try
            {
                var response = await httpClient.GetAsync($"{config.ApiBaseUrl}/api/stats/{config.ServerPrefix}/{player.UserIDString}");
                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    var playerData = new
                    {
                        steamid = player.UserIDString,
                        name = RemoveSpecialCharacters(player.displayName),
                        serverPrefix = config.ServerPrefix
                    };
                    var json = JsonConvert.SerializeObject(playerData);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    var postResponse = await httpClient.PostAsync($"{config.ApiBaseUrl}/api/stats", content);
                    if (!postResponse.IsSuccessStatusCode)
                    {
                        LogError($"Failed to insert player {player.displayName} to API: {postResponse.StatusCode} - {await postResponse.Content.ReadAsStringAsync()}");
                    }
                }
                else if (!response.IsSuccessStatusCode)
                {
                     LogError($"Error checking player {player.displayName} on API: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error checking/inserting player {player.displayName}: {ex.Message}");
            }
        }

        private void UpdateWeaponStats(PlayerStats stats, string weaponShortname)
        {
            if (stats?.WeaponStats == null || string.IsNullOrEmpty(weaponShortname)) return;

            switch (weaponShortname)
            {
                case "rifle.ak": stats.WeaponStats.AK47++; break;
                case "rifle.lr300": stats.WeaponStats.LR300++; break;
                case "lmg.m249": stats.WeaponStats.M249++; break;
                case "rifle.l96":
                case "rifle.bolt": stats.WeaponStats.Snipers++; break;
                case "rifle.m39": stats.WeaponStats.M39++; break;
                case "rifle.semiauto": stats.WeaponStats.SAR++; break;
                case "smg.mp5": stats.WeaponStats.MP5++; break;
                case "smg.thompson": stats.WeaponStats.Thompson++; break;
                case "shotgun.pump":
                case "shotgun.spas12":
                case "shotgun.doublebarrel":
                case "shotgun.waterpipe": stats.WeaponStats.Shotguns++; break;
                case "pistol.python":
                case "pistol.m92":
                case "pistol.semiauto":
                case "pistol.revolver": stats.WeaponStats.Pistols++; break;
                case "knife.combat":
                case "salvaged.cleaver":
                case "salvaged.sword":
                case "longsword":
                case "mace":
                case "spear.stone":
                case "spear.wooden":
                case "bone.club":
                case "rock": stats.WeaponStats.Melee++; break;
                case "bow.compound":
                case "bow.hunting":
                case "crossbow":
                case "pistol.eoka": stats.WeaponStats.Primitive++; break;
            }
        }

        private async Task SavePlayerStatsAsync(BasePlayer? player, bool disconnect = false)
        {
            if (player == null || string.IsNullOrEmpty(player.UserIDString)) return;
            if (!playerStats.TryGetValue(player.UserIDString, out var stats) || stats == null) return;

            if (config == null || httpClient == null || string.IsNullOrEmpty(config.ApiBaseUrl) || string.IsNullOrEmpty(config.ServerPrefix))
            {
                LogError("HttpClient or config/ApiBaseUrl/ServerPrefix is null, cannot save player stats");
                return;
            }

            try
            {
                stats.Name = RemoveSpecialCharacters(player.displayName);
                
                var payload = new Dictionary<string, object>
                {
                    { "steamid", player.UserIDString },
                    { "name", stats.Name },
                    { "serverPrefix", config.ServerPrefix },
                    { "connections", stats.Connections },
                    { "kills", stats.Kills },
                    { "deaths", stats.Deaths },
                    { "head_hits", stats.Hits.Head },
                    { "torso_hits", stats.Hits.Torso },
                    { "leftarm_hits", stats.Hits.LeftHand },
                    { "rightarm_hits", stats.Hits.RightHand },
                    { "leftleg_hits", stats.Hits.LeftLeg },
                    { "rightleg_hits", stats.Hits.RightLeg },
                    { "leftfoot_hits", stats.Hits.LeftFoot },
                    { "rightfoot_hits", stats.Hits.RightFoot },
                    { "bfired", stats.BulletsFired },
                    { "suicides", stats.Suicides },
                    { "wounded", stats.Wounded },
                    { "scientistkills", stats.ScientistKills },
                    { "rocketsfired", stats.RocketsFired },
                    { "c4thrown", stats.C4Thrown },
                    { "satchelsthrown", stats.SatchelsThrown },
                    { "tcsdestroyed", stats.TCsDestroyed },
                    { "ak47", stats.WeaponStats.AK47 },
                    { "lr300", stats.WeaponStats.LR300 },
                    { "m249", stats.WeaponStats.M249 },
                    { "snipers", stats.WeaponStats.Snipers },
                    { "m39", stats.WeaponStats.M39 },
                    { "sar", stats.WeaponStats.SAR },
                    { "mp5", stats.WeaponStats.MP5 },
                    { "thompson", stats.WeaponStats.Thompson },
                    { "shotguns", stats.WeaponStats.Shotguns },
                    { "pistols", stats.WeaponStats.Pistols },
                    { "melee", stats.WeaponStats.Melee },
                    { "primitive", stats.WeaponStats.Primitive },
                    { "maze_wins", stats.EventStats.MazeWins },
                    { "roam_wins", stats.EventStats.RoamWins },
                    { "sulfur_wins", stats.EventStats.SulfurWins },
                    { "control_wins", stats.EventStats.ControlWins },
                    { "koth_wins", stats.EventStats.KothWins },
                    { "bedwars_wins", stats.EventStats.BedwarsWins }
                };

                if (disconnect)
                {
                    int playtimeIncrement = (int)DateTime.Now.Subtract(stats.Connected).TotalMinutes;
                    if (playtimeIncrement > 0)
                    {
                        payload["playtimeIncrement"] = playtimeIncrement;
                    }
                }

                var json = JsonConvert.SerializeObject(payload);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await httpClient.PutAsync($"{config.ApiBaseUrl}/api/stats/{config.ServerPrefix}/{player.UserIDString}", content);
                if (!response.IsSuccessStatusCode)
                {
                     LogError($"Failed to save stats for {player.displayName} to API: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error saving stats for {player.displayName}: {ex.Message}");
            }
        }

        private IEnumerator StartupAsync()
        {
            playerStats.Clear();
            yield return LoadStatsAsync();
            yield return CoroutineEx.waitForSeconds(3f);

            if (config?.EnableClanStats == true)
            {
                 timer.Every(300f, () =>
                 {
                     _ = Task.Run(async () =>
                     {
                         try
                         {
                             await SyncClanStatsToApiAsync();
                         }
                         catch (Exception ex)
                         {
                             LogError($"Error in periodic clan sync: {ex.Message}");
                         }
                     });
                 });
            }
        }

        private IEnumerator LoadStatsAsync()
        {
            var task = LoadStatsFromApiAsync();
            yield return new WaitUntil(() => task.IsCompleted);
        }

        private async Task LoadStatsFromApiAsync()
        {
            if (config == null || httpClient == null || string.IsNullOrEmpty(config.ApiBaseUrl) || string.IsNullOrEmpty(config.ServerPrefix))
            {
                LogError("HttpClient or config/ApiBaseUrl/ServerPrefix is null, cannot load stats from API");
                return;
            }

            try
            {
                var response = await httpClient.GetAsync($"{config.ApiBaseUrl}/api/stats/{config.ServerPrefix}");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ApiPlayerStats>>>(json);
                    var statsData = apiResponse?.data;

                    if (statsData != null)
                    {
                        foreach (var row in statsData)
                        {
                            try
                            {
                                if (string.IsNullOrEmpty(row.steamid)) continue;

                                playerStats[row.steamid] = new PlayerStats
                                {
                                    Name = row.name ?? string.Empty,
                                    Connections = row.connections,
                                    Connected = DateTime.Now, 
                                    Kills = row.kills,
                                    Deaths = row.deaths,
                                    BulletsFired = row.bfired,
                                    Suicides = row.suicides,
                                    Wounded = row.wounded,
                                    ScientistKills = row.scientistkills,
                                    RocketsFired = row.rocketsfired,
                                    C4Thrown = row.c4thrown,
                                    SatchelsThrown = row.satchelsthrown,
                                    TCsDestroyed = row.tcsdestroyed,
                                    Hits = new BodyParts
                                    {
                                        Head = row.head_hits,
                                        Torso = row.torso_hits,
                                        LeftHand = row.leftarm_hits,
                                        RightHand = row.rightarm_hits,
                                        LeftLeg = row.leftleg_hits,
                                        RightLeg = row.rightleg_hits,
                                        LeftFoot = row.leftfoot_hits,
                                        RightFoot = row.rightfoot_hits
                                    },
                                    WeaponStats = new WeaponStats
                                    {
                                        AK47 = row.ak47,
                                        LR300 = row.lr300,
                                        M249 = row.m249,
                                        Snipers = row.snipers,
                                        M39 = row.m39,
                                        SAR = row.sar,
                                        MP5 = row.mp5,
                                        Thompson = row.thompson,
                                        Shotguns = row.shotguns,
                                        Pistols = row.pistols,
                                        Melee = row.melee,
                                        Primitive = row.primitive
                                    },
                                    EventStats = new EventStats
                                    {
                                        MazeWins = row.maze_wins,
                                        RoamWins = row.roam_wins,
                                        SulfurWins = row.sulfur_wins,
                                        ControlWins = row.control_wins,
                                        KothWins = row.koth_wins,
                                        BedwarsWins = row.bedwars_wins
                                    }
                                };
                            }
                            catch (Exception ex)
                            {
                                LogError($"Error processing stats data for steamid {row.steamid}: {ex.Message}");
                            }
                        }
                         Puts($"Loaded {playerStats.Count} player stats from API.");
                    }
                }
                else
                {
                    Puts($"Failed to load stats from API: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error loading stats from API: {ex.Message}");
            }
        }

        private async Task WipeStatsAsync()
        {
            if (config == null || httpClient == null || string.IsNullOrEmpty(config.ApiBaseUrl) || string.IsNullOrEmpty(config.ServerPrefix))
            {
                LogError("HttpClient or config/ApiBaseUrl/ServerPrefix is null, cannot wipe stats");
                return;
            }
            try
            {
                var response = await httpClient.DeleteAsync($"{config.ApiBaseUrl}/api/stats/{config.ServerPrefix}/wipe");
                if (response.IsSuccessStatusCode)
                {
                     Puts("Stats wiped successfully via API.");
                }
                else
                {
                     LogError($"Failed to wipe stats via API: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error wiping stats: {ex.Message}");
            }
        }

        private async Task<List<ClanLeaderboardData>> GetClanLeaderboardDataAsync()
        {
            try
            {
                if (config?.EnableClanStats != true) return new List<ClanLeaderboardData>();

                var apiData = await GetClanLeaderboardFromApiAsync();
                if (apiData.Count > 0)
                {
                    return apiData;
                }

                return GetLocalClanLeaderboardData();
            }
            catch (Exception ex)
            {
                LogError($"Error getting clan leaderboard data: {ex.Message}");
                return GetLocalClanLeaderboardData(); 
            }
        }

        private async Task<List<ClanLeaderboardData>> GetClanLeaderboardFromApiAsync()
        {
            if (config == null || httpClient == null || string.IsNullOrEmpty(config.ApiBaseUrl) || string.IsNullOrEmpty(config.ServerPrefix))
            {
                return new List<ClanLeaderboardData>();
            }

            try
            {
                var response = await httpClient.GetAsync($"{config.ApiBaseUrl}/api/clans/leaderboard/{config.ServerPrefix}?limit=20");
                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    var apiResponse = JsonConvert.DeserializeObject<ApiResponse<List<ApiClanStats>>>(json);
                    var apiClans = apiResponse?.data;

                    return apiClans?.Select(c => new ClanLeaderboardData
                    {
                        ClanName = c.clanName,
                        MemberCount = c.memberCount,
                        TotalKills = c.totalKills,
                        TotalDeaths = c.totalDeaths,
                        KDRatio = c.kdRatio
                    }).ToList() ?? new List<ClanLeaderboardData>();
                }
                else
                {
                    LogWarning($"Failed to fetch clan leaderboard from API: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                LogError($"Error fetching clan leaderboard from API: {ex.Message}");
            }

            return new List<ClanLeaderboardData>();
        }

        private List<ClanLeaderboardData> GetLocalClanLeaderboardData()
        {
            if (config?.EnableClanStats != true)
            {
                return new List<ClanLeaderboardData>();
            }

            var clanStats = new Dictionary<string, ClanLeaderboardData>();
            var AwakenClans = plugins.Find("awakenClans");

            if (AwakenClans == null)
            {
                LogWarning("AwakenClans plugin not found, cannot generate local clan leaderboard data.");
                return new List<ClanLeaderboardData>();
            }

            try
            {
                var currentStats = playerStats.ToList(); 
                foreach (var playerStatEntry in currentStats)
                {
                    try
                    {
                        if (string.IsNullOrEmpty(playerStatEntry.Key) || !ulong.TryParse(playerStatEntry.Key, out var playerId)) continue;
                        var playerStatValue = playerStatEntry.Value;
                        if (playerStatValue == null) continue;

                        var clanIdObject = AwakenClans.Call("GetClanOf", playerId);
                        if (clanIdObject == null) continue;
                        
                        string? clanTag = clanIdObject as string; 
                        if (string.IsNullOrEmpty(clanTag)) continue; 

                        var overview = AwakenClans.Call("GetClan", clanTag);
                        if (overview == null) continue;

                        string? clanNameFromOverview = overview.GetType().GetProperty("name")?.GetValue(overview, null) as string;
                        
                        string clanDisplayName = string.IsNullOrEmpty(clanNameFromOverview) ? clanTag : clanNameFromOverview;


                        if (!clanStats.ContainsKey(clanDisplayName))
                        {
                            clanStats[clanDisplayName] = new ClanLeaderboardData
                            {
                                ClanName = clanDisplayName,
                                MemberCount = 0, 
                                TotalKills = 0,
                                TotalDeaths = 0
                            };
                        }

                        var clanData = clanStats[clanDisplayName];
                        clanData.TotalKills += playerStatValue.Kills;
                        clanData.TotalDeaths += playerStatValue.Deaths;
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error processing player {playerStatEntry.Key} for clan stats: {ex.Message}");
                    }
                }
                
                foreach (var clanData in clanStats.Values.ToList()) 
                {
                    try
                    {
                        object? clanTagObj = AwakenClans.Call("GetClanTag", clanData.ClanName); 
                        if(clanTagObj != null && clanTagObj is string clanTagString && !string.IsNullOrEmpty(clanTagString))
                        {
                           var membersList = AwakenClans.Call("GetClanMembers", clanTagString) as List<ulong>;
                           if (membersList != null)
                           {
                               clanData.MemberCount = membersList.Count;
                           }
                           else
                           {
                               clanData.MemberCount = clanStats[clanData.ClanName].TotalKills > 0 || clanStats[clanData.ClanName].TotalDeaths > 0 ? 1 : 0;
                           }
                        }
                        else 
                        {
                            clanData.MemberCount = clanStats[clanData.ClanName].TotalKills > 0 || clanStats[clanData.ClanName].TotalDeaths > 0 ? 1 : 0;
                        }
                        clanData.KDRatio = clanData.TotalDeaths > 0 ? (float)clanData.TotalKills / clanData.TotalDeaths : clanData.TotalKills;
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error finalizing clan data for {clanData.ClanName}: {ex.Message}");
                    }
                }


                return clanStats.Values.OrderByDescending(c => c.TotalKills).ToList();
            }
            catch (Exception ex)
            {
                LogError($"Error getting local clan leaderboard data: {ex.Message}");
                return new List<ClanLeaderboardData>();
            }
        }


        private List<ClanLeaderboardData> GetClanLeaderboardData()
        {
            try
            {
                return GetLocalClanLeaderboardData();
            }
            catch (Exception ex)
            {
                LogError($"Error in GetClanLeaderboardData: {ex.Message}");
                return new List<ClanLeaderboardData>();
            }
        }


        private async Task SyncClanStatsToApiAsync()
        {
            if (config == null || httpClient == null || string.IsNullOrEmpty(config.ApiBaseUrl) || string.IsNullOrEmpty(config.ServerPrefix))
            {
                LogWarning("Cannot sync clan stats to API: HttpClient, config, ApiBaseUrl, or ServerPrefix is null.");
                return;
            }
            if (config.EnableClanStats != true) return;

            try
            {
                var localClanData = GetLocalClanLeaderboardData();
                if (localClanData.Count == 0)
                {
                    Puts("No local clan data to sync to API.");
                    return;
                }

                var AwakenClans = plugins.Find("awakenClans");
                if (AwakenClans == null)
                {
                    LogWarning("AwakenClans plugin not found, cannot get members for API sync during SyncClanStatsToApiAsync.");
                    return;
                }


                foreach (var clan in localClanData)
                {
                    try
                    {
                        var apiClanStats = new ApiClanStats
                        {
                            clanName = clan.ClanName,
                            serverPrefix = config.ServerPrefix,
                            memberCount = clan.MemberCount, 
                            totalKills = clan.TotalKills,
                            totalDeaths = clan.TotalDeaths,
                            kdRatio = clan.KDRatio,
                            members = GetClanMemberSteamIds(clan.ClanName, AwakenClans)
                        };

                        var json = JsonConvert.SerializeObject(apiClanStats);
                        var content = new StringContent(json, Encoding.UTF8, "application/json");

                        var response = await httpClient.PostAsync($"{config.ApiBaseUrl}/api/clans", content);
                        if (!response.IsSuccessStatusCode)
                        {
                            LogError($"Failed to sync clan {clan.ClanName} to API: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
                        }
                    }
                    catch (Exception ex)
                    {
                         LogError($"Error syncing individual clan {clan.ClanName} to API: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error syncing clan stats to API: {ex.Message}");
            }
        }

        private string[] GetClanMemberSteamIds(string clanName, Oxide.Core.Plugins.Plugin AwakenClansInstance)
        {
            if (AwakenClansInstance == null || string.IsNullOrEmpty(clanName)) return new string[0];

            try
            {
                var members = new List<string>();

                object clanTagObj = AwakenClansInstance.Call("GetClanTag", clanName);
                if (clanTagObj == null || !(clanTagObj is string clanTag) || string.IsNullOrEmpty(clanTag))
                {
                    LogWarning($"Could not get clan tag for clan name: {clanName}");
                    return new string[0];
                }

                var memberUlongs = AwakenClansInstance.Call("GetClanMembers", clanTag) as List<ulong>;
                if (memberUlongs != null)
                {
                    foreach (var memberId in memberUlongs)
                    {
                        members.Add(memberId.ToString());
                    }
                }
                else
                {
                     LogWarning($"Could not get clan members for clan tag: {clanTag} (name: {clanName})");
                }
                return members.ToArray();
            }
            catch (Exception ex)
            {
                LogError($"Error getting clan members for {clanName}: {ex.Message}");
                return new string[0];
            }
        }
        #endregion

        #region UI
        private void Leaderboard(BasePlayer player)
        {
            CuiHelper.DestroyUi(player, "Leaderboard");

            var sortedList = playerStats.OrderByDescending(key => key.Value.Kills).ToList();
            var element = sortedList.Where(x => x.Key == player.UserIDString).FirstOrDefault();

            if (element.Value == null) 
            {
                if (playerStats.TryGetValue(player.UserIDString, out var selfStats) && selfStats != null)
                {
                    element = new KeyValuePair<string, PlayerStats>(player.UserIDString, selfStats);
                }
                else
                {
                     player.ChatMessage("Your stats are not available yet.");
                     return;
                }
            }


            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                CursorEnabled = true,
                Image = { Color = "1 1 1 0" },
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1", OffsetMin = "0 0", OffsetMax = "0 0" }
            }, "Overlay", "Leaderboard");

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.8", Material = "assets/content/ui/uibackgroundblur.mat" },
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1", OffsetMin = "0 0", OffsetMax = "0 0" }
            }, "Leaderboard", "BlurPanel");

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.4117647 0.6431373 1" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-275.825 247.121", OffsetMax = "275.275 306.419" }
            }, "Leaderboard", "LeaderboardTextPanel");

            container.Add(new CuiElement
            {
                Name = "LeaderboardText",
                Parent = "LeaderboardTextPanel",
                Components = {
                    new CuiTextComponent { Text = "TOP 10 OVERALL PLAYERS", Font = "robotocondensed-bold.ttf", FontSize = 25, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-275.55 -29.649", OffsetMax = "275.55 29.649" }
                }
            });

            container.Add(new CuiButton
            {
                Button = { Color = "1 1 1 0", Command = "destroyui Leaderboard" },
                Text = { Text = "X", Font = "robotocondensed-bold.ttf", FontSize = 30, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "1 0.5", AnchorMax = "1 0.5", OffsetMin = "-62.742 -29.648", OffsetMax = "0 29.649" }
            }, "LeaderboardTextPanel", "CloseButton");

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.7" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-275.825 204.136", OffsetMax = "275.275 247.124" }
            }, "Leaderboard", "LeaderboardTopPanel");

            container.Add(new CuiElement
            {
                Name = "LeaderboardPlaceText",
                Parent = "LeaderboardTopPanel",
                Components = {
                    new CuiTextComponent { Text = "#", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.UpperCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "14.348 -15.905", OffsetMax = "47.604 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardNameText",
                Parent = "LeaderboardTopPanel",
                Components = {
                    new CuiTextComponent { Text = "Name", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.UpperCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "47.609 -15.905", OffsetMax = "178.651 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardKillsText",
                Parent = "LeaderboardTopPanel",
                Components = {
                    new CuiTextComponent { Text = "Kills", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.UpperCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "178.649 -15.905", OffsetMax = "267.685 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardDeathsText",
                Parent = "LeaderboardTopPanel",
                Components = {
                    new CuiTextComponent { Text = "Deaths", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.UpperCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-8.145 -15.905", OffsetMax = "73.14 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardKDText",
                Parent = "LeaderboardTopPanel",
                Components = {
                    new CuiTextComponent { Text = "KD", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.UpperCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "105.587 -15.905", OffsetMax = "145.008 11.829" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-275.825 -304.135", OffsetMax = "275.825 204.135" }
            }, "Leaderboard", "LeaderboardPlayersPanel");

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.4117647 0.6431373 0.7" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-275.825 213.03", OffsetMax = "275.275 254.135" }
            }, "LeaderboardPlayersPanel", "PlayerPanel");

            container.Add(new CuiElement
            {
                Name = "LeaderboardPlace",
                Parent = "PlayerPanel",
                Components = {
                    new CuiTextComponent { Text = (sortedList.IndexOf(element) + 1).ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "10.873 -15.905", OffsetMax = "47.607 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardName",
                Parent = "PlayerPanel",
                Components = {
                    new CuiTextComponent { Text = player.displayName, Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "47.612 -15.905", OffsetMax = "178.654 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardKills",
                Parent = "PlayerPanel",
                Components = {
                    new CuiTextComponent { Text = element.Value.Kills.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "178.652 -15.905", OffsetMax = "267.688 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardDeaths",
                Parent = "PlayerPanel",
                Components = {
                    new CuiTextComponent { Text = element.Value.Deaths.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-7.865 -15.905", OffsetMax = "73.419 11.475" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeaderboardKD",
                Parent = "PlayerPanel",
                Components = {
                    new CuiTextComponent { Text = string.Format("{0}", element.Value.Deaths == 0 ? element.Value.Kills : (float)Math.Round(((float)element.Value.Kills) / element.Value.Deaths, 2)), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "88.012 -16.26", OffsetMax = "162.209 11.474" }
                }
            });

            container.Add(new CuiButton
            {
                Button = { Color = "0.4378783 0.6711068 0.754717 1", Command = "playerstats" },
                Text = { Text = "View Stats", Font = "robotocondensed-regular.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "179.046 -16.259", OffsetMax = "265.554 11.474" }
            }, "PlayerPanel", "ViewStatsButton");

            float minx = -275.825f; float maxx = 275.275f; float miny = 167.1602f; float maxy = 208.2692f;
            for (int i = 0; i < sortedList.Count; i++)
            {
                if (i > 9) break;
                if (i != 0)
                {
                    miny -= 45.6f;
                    maxy -= 45.6f;
                }

                container.Add(new CuiPanel
                {
                    CursorEnabled = false,
                    Image = { Color = "0 0 0 0.7" },
                    RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = $"{minx} {miny}", OffsetMax = $"{maxx} {maxy}" }
                }, "LeaderboardPlayersPanel", "DupePlayerPanel");

                container.Add(new CuiElement
                {
                    Name = "LeaderboardPlace",
                    Parent = "DupePlayerPanel",
                    Components = {
                        new CuiTextComponent { Text = (i+1).ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                        new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "10.873 -15.905", OffsetMax = "47.607 11.475" }
                    }
                });

                string name = sortedList[i].Value.Name;
                container.Add(new CuiElement
                {
                    Name = "LeaderboardName",
                    Parent = "DupePlayerPanel",
                    Components = {
                        new CuiTextComponent { Text = name, Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                        new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "47.612 -15.905", OffsetMax = "178.654 11.475" }
                    }
                });

                container.Add(new CuiElement
                {
                    Name = "LeaderboardKills",
                    Parent = "DupePlayerPanel",
                    Components = {
                        new CuiTextComponent { Text = sortedList[i].Value.Kills.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                        new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "178.652 -15.905", OffsetMax = "267.688 11.475" }
                    }
                });

                container.Add(new CuiElement
                {
                    Name = "LeaderboardDeaths",
                    Parent = "DupePlayerPanel",
                    Components = {
                        new CuiTextComponent { Text = sortedList[i].Value.Deaths.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                        new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-7.865 -15.905", OffsetMax = "73.419 11.475" }
                    }
                });

                container.Add(new CuiElement
                {
                    Name = "LeaderboardKD",
                    Parent = "DupePlayerPanel",
                    Components = {
                        new CuiTextComponent { Text = string.Format("{0}", string.Format("{0}", sortedList[i].Value.Deaths == 0 ? sortedList[i].Value.Kills : (float)Math.Round(((float)sortedList[i].Value.Kills) / sortedList[i].Value.Deaths, 2))), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                        new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "88.012 -16.26", OffsetMax = "162.209 11.474" }
                    }
                });

                container.Add(new CuiButton
                {
                    Button = { Color = "0.4378783 0.6711068 0.754717 1", Command = $"playerstats {sortedList[i].Key} \"{name}\"" },
                    Text = { Text = "View Stats", Font = "robotocondensed-regular.ttf", FontSize = 12, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                    RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "179.046 -16.259", OffsetMax = "265.554 11.474" }
                }, "DupePlayerPanel", "ViewStatsButton");
            }

            CuiHelper.AddUi(player, container);
        }

        private void ShowLeaderboard(BasePlayer player)
        {
            CuiHelper.DestroyUi(player, "StatsLeaderboard");

            var sortedList = playerStats.OrderByDescending(key => key.Value.Kills).ToList();

            var container = CUIClass.CreateOverlay("StatsLeaderboard", "0 0 0 0.8", "0 0", "1 1", true, 0.3f, "assets/content/ui/uibackgroundblur.mat");

            CUIClass.CreatePanel(ref container, "main_panel", "StatsLeaderboard", "0.15 0.15 0.15 0.95", "0.3 0.3", "0.7 0.7", false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");

            CUIClass.CreatePanel(ref container, "title_bar", "main_panel", "0.2 0.2 0.2 1", "0 0.9", "1 1", false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");
            CUIClass.CreateText(ref container, "title_text", "title_bar", "1 1 1 1", "LEADERBOARD", 12, "0.02 0", "0.5 1", TextAnchor.MiddleLeft, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "refresh_text", "title_bar", "0.7 0.7 0.7 1", "This leaderboard refreshes every 5 minutes", 7, "0.5 0", "0.95 1", TextAnchor.MiddleRight, "robotocondensed-regular.ttf");

            CUIClass.CreateButton(ref container, "close_btn", "title_bar", "0.8 0.2 0.2 1", "✘", 10, "0.96 0.1", "0.99 0.9", "stats_close", "", "1 1 1 1", 0.0f, TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            CUIClass.CreateButton(ref container, "players_tab", "main_panel", "0.2 0.5 0.8 1", "PLAYERS", 9, "0.02 0.82", "0.15 0.88", "stats_tab players", "", "1 1 1 1", 0.0f, TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateButton(ref container, "clans_tab", "main_panel", "0.3 0.3 0.3 1", "CLANS", 9, "0.16 0.82", "0.29 0.88", "stats_tab clans", "", "0.7 0.7 0.7 1", 0.0f, TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            CreatePlayersLeaderboard(ref container, sortedList);
            CreatePersonalStatsSection(ref container, player);

            CuiHelper.AddUi(player, container);

            // Automatically show navigation sidebar when stats UI opens
            ShowNavigationSidebar(player);
        }

        private void CreatePlayersLeaderboard(ref CuiElementContainer container, List<KeyValuePair<string, PlayerStats>> sortedList)
        {
            CUIClass.CreatePanel(ref container, "header_row", "main_panel", "0.25 0.25 0.25 1", "0.02 0.75", "0.98 0.81", false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");

            CUIClass.CreateText(ref container, "header_rank", "header_row", "1 1 1 1", "#", 8, "0 0", "0.05 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_username", "header_row", "1 1 1 1", "Username", 8, "0.05 0", "0.25 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_kills", "header_row", "1 1 1 1", "Kills", 8, "0.25 0", "0.32 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_deaths", "header_row", "1 1 1 1", "Deaths", 8, "0.32 0", "0.39 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_kd", "header_row", "1 1 1 1", "K/D", 8, "0.39 0", "0.46 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            CUIClass.CreateText(ref container, "header_maze", "header_row", "0.2 0.8 1 1", "MAZE", 8, "0.46 0", "0.58 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_roam", "header_row", "1 0.4 0.4 1", "ROAM", 8, "0.58 0", "0.70 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_sulfur", "header_row", "1 0.6 0.2 1", "SULFUR", 8, "0.70 0", "0.82 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_control", "header_row", "0.8 0.2 1 1", "CONTROL", 8, "0.82 0", "0.96 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            float rowHeight = 0.035f;
            float startY = 0.71f;

            for (int i = 0; i < Math.Min(sortedList.Count, 14); i++)
            {
                var player = sortedList[i];
                float yMin = startY - (i * rowHeight);
                float yMax = yMin + rowHeight - 0.002f;

                string rowColor = i % 2 == 0 ? "0.2 0.2 0.2 0.8" : "0.15 0.15 0.15 0.8";

                CUIClass.CreatePanel(ref container, $"row_{i}", "main_panel", rowColor, "0.02 " + yMin.ToString("F3"), "0.98 " + yMax.ToString("F3"), false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");

                CUIClass.CreateText(ref container, $"rank_{i}", $"row_{i}", "1 1 1 0.9", (i + 1).ToString(), 7, "0 0", "0.05 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                string playerName = string.IsNullOrEmpty(player.Value.Name) ? "Unknown Player" : player.Value.Name;
                CUIClass.CreateText(ref container, $"name_{i}", $"row_{i}", "1 1 1 0.9", playerName, 7, "0.05 0", "0.25 1", TextAnchor.MiddleLeft, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"kills_{i}", $"row_{i}", "1 1 1 0.9", player.Value.Kills.ToString(), 7, "0.25 0", "0.32 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"deaths_{i}", $"row_{i}", "1 1 1 0.9", player.Value.Deaths.ToString(), 7, "0.32 0", "0.39 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");

                float kd = player.Value.Deaths == 0 ? player.Value.Kills : (float)Math.Round((float)player.Value.Kills / player.Value.Deaths, 2);
                string kdColor = kd >= 2.0f ? "0.2 1 0.2 1" : kd >= 1.0f ? "1 1 0.2 1" : "1 0.4 0.4 1";
                CUIClass.CreateText(ref container, $"kd_{i}", $"row_{i}", kdColor, kd.ToString("F2"), 7, "0.39 0", "0.46 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");

                CUIClass.CreateText(ref container, $"maze_{i}", $"row_{i}", "0.7 0.7 0.7 1", GetPlayerEventWins(player.Key, "maze").ToString(), 7, "0.46 0", "0.58 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"roam_{i}", $"row_{i}", "0.7 0.7 0.7 1", GetPlayerEventWins(player.Key, "roam").ToString(), 7, "0.58 0", "0.70 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"sulfur_{i}", $"row_{i}", "0.7 0.7 0.7 1", GetPlayerEventWins(player.Key, "sulfur").ToString(), 7, "0.70 0", "0.82 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"control_{i}", $"row_{i}", "0.7 0.7 0.7 1", GetPlayerEventWins(player.Key, "control").ToString(), 7, "0.82 0", "0.96 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            }
        }

        private void ShowClansLeaderboard(BasePlayer player)
        {
            if (config?.EnableClanStats != true)
            {
                player.ChatMessage("Clan stats are currently disabled.");
                return;
            }

            CuiHelper.DestroyUi(player, "StatsLeaderboard");

            var container = CUIClass.CreateOverlay("StatsLeaderboard", "0 0 0 0.8", "0 0", "1 1", true, 0.3f, "assets/content/ui/uibackgroundblur.mat");

            CUIClass.CreatePanel(ref container, "main_panel", "StatsLeaderboard", "0.15 0.15 0.15 0.95", "0.3 0.3", "0.7 0.7", false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");

            CUIClass.CreatePanel(ref container, "title_bar", "main_panel", "0.2 0.2 0.2 1", "0 0.9", "1 1", false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");
            CUIClass.CreateText(ref container, "title_text", "title_bar", "1 1 1 1", "LEADERBOARD", 12, "0.02 0", "0.5 1", TextAnchor.MiddleLeft, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "refresh_text", "title_bar", "0.7 0.7 0.7 1", "This leaderboard refreshes every 5 minutes", 7, "0.5 0", "0.95 1", TextAnchor.MiddleRight, "robotocondensed-regular.ttf");

            CUIClass.CreateButton(ref container, "close_btn", "title_bar", "0.8 0.2 0.2 1", "✘", 10, "0.96 0.1", "0.99 0.9", "stats_close", "", "1 1 1 1", 0.0f, TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            CUIClass.CreateButton(ref container, "players_tab", "main_panel", "0.3 0.3 0.3 1", "PLAYERS", 9, "0.02 0.82", "0.15 0.88", "stats_tab players", "", "0.7 0.7 0.7 1", 0.0f, TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateButton(ref container, "clans_tab", "main_panel", "0.2 0.5 0.8 1", "CLANS", 9, "0.16 0.82", "0.29 0.88", "stats_tab clans", "", "1 1 1 1", 0.0f, TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            CreateClansLeaderboard(ref container);
            CreatePersonalStatsSection(ref container, player);

            CuiHelper.AddUi(player, container);

            // Automatically show navigation sidebar when stats UI opens
            ShowNavigationSidebar(player);
        }

        private void CreateClansLeaderboard(ref CuiElementContainer container)
        {
            CUIClass.CreatePanel(ref container, "header_row", "main_panel", "0.25 0.25 0.25 1", "0.02 0.75", "0.98 0.81", false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");

            CUIClass.CreateText(ref container, "header_rank", "header_row", "1 1 1 1", "#", 10, "0 0", "0.05 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_clanname", "header_row", "1 1 1 1", "Clan Name", 10, "0.05 0", "0.25 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_members", "header_row", "1 1 1 1", "Members", 10, "0.25 0", "0.32 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_kills", "header_row", "1 1 1 1", "Kills", 10, "0.32 0", "0.39 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_kd", "header_row", "1 1 1 1", "K/D", 10, "0.39 0", "0.46 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            CUIClass.CreateText(ref container, "header_maze", "header_row", "0.2 0.8 1 1", "MAZE", 10, "0.46 0", "0.58 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_roam", "header_row", "1 0.4 0.4 1", "ROAM", 10, "0.58 0", "0.70 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_sulfur", "header_row", "1 0.6 0.2 1", "SULFUR", 10, "0.70 0", "0.82 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");
            CUIClass.CreateText(ref container, "header_control", "header_row", "0.8 0.2 1 1", "CONTROL", 10, "0.82 0", "0.96 1", TextAnchor.MiddleCenter, "robotocondensed-bold.ttf");

            var clanStats = GetClanLeaderboardData();

            float rowHeight = 0.035f;
            float startY = 0.71f;

            for (int i = 0; i < Math.Min(clanStats.Count, 14); i++)
            {
                var clan = clanStats[i];
                float yMin = startY - (i * rowHeight);
                float yMax = yMin + rowHeight - 0.002f;

                string rowColor = i % 2 == 0 ? "0.2 0.2 0.2 0.8" : "0.15 0.15 0.15 0.8";

                CUIClass.CreatePanel(ref container, $"clan_row_{i}", "main_panel", rowColor, "0.02 " + yMin.ToString("F3"), "0.98 " + yMax.ToString("F3"), false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");

                CUIClass.CreateText(ref container, $"clan_rank_{i}", $"clan_row_{i}", "1 1 1 0.9", (i + 1).ToString(), 9, "0 0", "0.05 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"clan_name_{i}", $"clan_row_{i}", "1 1 1 0.9", clan.ClanName, 9, "0.05 0", "0.25 1", TextAnchor.MiddleLeft, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"clan_members_{i}", $"clan_row_{i}", "1 1 1 0.9", clan.MemberCount.ToString(), 9, "0.25 0", "0.32 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"clan_kills_{i}", $"clan_row_{i}", "1 1 1 0.9", clan.TotalKills.ToString(), 9, "0.32 0", "0.39 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"clan_kd_{i}", $"clan_row_{i}", "0.2 1 0.2 1", clan.KDRatio.ToString("F2"), 9, "0.39 0", "0.46 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");

                CUIClass.CreateText(ref container, $"clan_maze_{i}", $"clan_row_{i}", "0.7 0.7 0.7 1", "0", 9, "0.46 0", "0.58 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"clan_roam_{i}", $"clan_row_{i}", "0.7 0.7 0.7 1", "0", 9, "0.58 0", "0.70 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"clan_sulfur_{i}", $"clan_row_{i}", "0.7 0.7 0.7 1", "0", 9, "0.70 0", "0.82 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
                CUIClass.CreateText(ref container, $"clan_control_{i}", $"clan_row_{i}", "0.7 0.7 0.7 1", "0", 9, "0.82 0", "0.96 1", TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            }
        }

        private void CreatePersonalStatsSection(ref CuiElementContainer container, BasePlayer player)
        {
            if (!playerStats.TryGetValue(player.UserIDString, out var stats) || stats == null)
                return;

            // Personal stats section at the bottom - much more compact
            CUIClass.CreatePanel(ref container, "personal_stats_panel", "main_panel", "0.1 0.3 0.5 0.9", "0.02 0.02", "0.98 0.12", false, 0.0f, 0f, "assets/content/ui/uibackgroundblur.mat");

            // Title - smaller and more compact
            CUIClass.CreateText(ref container, "personal_stats_title", "personal_stats_panel", "1 1 1 1", "YOUR STATS", 8, "0.02 0.75", "0.15 1", TextAnchor.MiddleLeft, "robotocondensed-bold.ttf");

            // Player name - smaller
            string playerName = string.IsNullOrEmpty(stats.Name) ? player.displayName : stats.Name;
            CUIClass.CreateText(ref container, "personal_player_name", "personal_stats_panel", "0.8 0.8 0.8 1", playerName, 7, "0.16 0.75", "0.35 1", TextAnchor.MiddleLeft, "robotocondensed-regular.ttf");

            // Stats in a single compact row
            float kd = stats.Deaths == 0 ? stats.Kills : (float)Math.Round((float)stats.Kills / stats.Deaths, 2);
            string kdColor = kd >= 2.0f ? "0.2 1 0.2 1" : kd >= 1.0f ? "1 1 0.2 1" : "1 0.4 0.4 1";

            // Find player's rank in leaderboard
            var sortedList = playerStats.OrderByDescending(x => x.Value.Kills).ToList();
            int playerRank = sortedList.FindIndex(x => x.Key == player.UserIDString) + 1;
            string rankText = playerRank > 0 ? $"#{playerRank}" : "Unranked";
            string accuracy = CalculateAccuracy(stats.BulletsFired, stats.Hits);

            // Single row layout - all stats horizontally
            float yPos = 0.25f;
            float height = 0.5f;

            // Core stats
            CUIClass.CreateText(ref container, "personal_kills", "personal_stats_panel", "1 1 1 1", $"K: {stats.Kills}", 6, "0.02 " + yPos, "0.08 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_deaths", "personal_stats_panel", "1 1 1 1", $"D: {stats.Deaths}", 6, "0.09 " + yPos, "0.15 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_kd", "personal_stats_panel", kdColor, $"K/D: {kd:F2}", 6, "0.16 " + yPos, "0.24 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_rank", "personal_stats_panel", "1 0.8 0.2 1", rankText, 6, "0.25 " + yPos, "0.31 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_accuracy", "personal_stats_panel", "1 1 1 1", accuracy, 6, "0.32 " + yPos, "0.38 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");

            // Event stats
            CUIClass.CreateText(ref container, "personal_maze", "personal_stats_panel", "0.2 0.8 1 1", $"MAZE: {stats.EventStats.MazeWins}", 6, "0.4 " + yPos, "0.5 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_roam", "personal_stats_panel", "1 0.4 0.4 1", $"ROAM: {stats.EventStats.RoamWins}", 6, "0.51 " + yPos, "0.61 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_sulfur", "personal_stats_panel", "1 0.6 0.2 1", $"SULFUR: {stats.EventStats.SulfurWins}", 6, "0.62 " + yPos, "0.74 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_control", "personal_stats_panel", "0.8 0.2 1 1", $"CONTROL: {stats.EventStats.ControlWins}", 6, "0.75 " + yPos, "0.88 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
            CUIClass.CreateText(ref container, "personal_connections", "personal_stats_panel", "0.7 0.7 0.7 1", $"Conn: {stats.Connections}", 6, "0.89 " + yPos, "0.98 " + (yPos + height), TextAnchor.MiddleCenter, "robotocondensed-regular.ttf");
        }

        private void StatsUI(BasePlayer player, string id, string name)
        {
            CuiHelper.DestroyUi(player, "StatsUI");
            if (!playerStats.TryGetValue(id, out var stats) || stats == null)
            {
                 player.ChatMessage($"Stats for '{name}' (ID: {id}) are not available.");
                 return;
            }

            var container = new CuiElementContainer();
            container.Add(new CuiPanel
            {
                CursorEnabled = true,
                Image = { Color = "1 1 1 0" },
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1", OffsetMin = "0 0", OffsetMax = "0 0" }
            }, "Overlay", "StatsUI");

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6666667", Material = "assets/content/ui/uibackgroundblur.mat" },
                RectTransform = { AnchorMin = "0 0", AnchorMax = "1 1", OffsetMin = "0 0", OffsetMax = "0 0" }
            }, "StatsUI", "BlurPanel");

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.6117647 0.6431373 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 268.11", OffsetMax = "358.451 310.23" }
            }, "StatsUI", "StatsTopPanel");

            container.Add(new CuiElement
            {
                Name = "StatsNameText",
                Parent = "StatsTopPanel",
                Components = {
                    new CuiTextComponent { Text = $"{(string.IsNullOrEmpty(name) ? "YOUR STATS" : $"{name}'s Stats")}", Font = "robotocondensed-bold.ttf", FontSize = 20, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-346.103 -21.06", OffsetMax = "165.035 21.06" }
                }
            });

            container.Add(new CuiButton
            {
                Button = { Color = "1 1 1 0", Command = "destroyui StatsUI" },
                Text = { Text = "X", Font = "robotocondensed-bold.ttf", FontSize = 22, Align = TextAnchor.MiddleCenter, Color = "1 1 1 1" },
                RectTransform = { AnchorMin = "1 0.5", AnchorMax = "1 0.5", OffsetMin = "-55.425 -21.06", OffsetMax = "-4.575 21.06" }
            }, "StatsTopPanel", "CloseButton");

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.6117647 0.6431373 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.553 214.48", OffsetMax = "-127.65 256.6" }
            }, "StatsUI", "CompetitiveTopPanel");

            container.Add(new CuiElement
            {
                Name = "CompetitiveTopText",
                Parent = "CompetitiveTopPanel",
                Components = {
                    new CuiTextComponent { Text = "Competitive", Font = "robotocondensed-bold.ttf", FontSize = 20, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-103.09 -21.06", OffsetMax = "91.264 21.06" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 171.496", OffsetMax = "-127.649 214.484" }
            }, "StatsUI", "ConnectionsPanel");

            container.Add(new CuiElement
            {
                Name = "ConnectionsCountText",
                Parent = "ConnectionsPanel",
                Components = {
                    new CuiTextComponent { Text = "Connections:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "ConnectionsCount",
                Parent = "ConnectionsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Connections.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 128.506", OffsetMax = "-127.649 171.494" }
            }, "StatsUI", "KillsPanel");

            container.Add(new CuiElement
            {
                Name = "KillsText",
                Parent = "KillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Kills:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "Kills",
                Parent = "KillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Kills.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 85.518", OffsetMax = "-127.649 128.506" }
            }, "StatsUI", "DeathsPanel");

            container.Add(new CuiElement
            {
                Name = "DeathsText",
                Parent = "DeathsPanel",
                Components = {
                    new CuiTextComponent { Text = "Deaths:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "Deaths",
                Parent = "DeathsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Deaths.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08627451 0.08627451 0.08627451 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 42.53", OffsetMax = "-127.649 85.518" }
            }, "StatsUI", "BulletsFiredPanel");

            container.Add(new CuiElement
            {
                Name = "BulletsFiredText",
                Parent = "BulletsFiredPanel",
                Components = {
                    new CuiTextComponent { Text = "Bullets Fired:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "11.208 -13.69", OffsetMax = "109.724 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "BulletsFired",
                Parent = "BulletsFiredPanel",
                Components = {
                    new CuiTextComponent { Text = stats.BulletsFired.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "128.179 -13.69", OffsetMax = "214.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 -0.658", OffsetMax = "-127.649 42.53" }
            }, "StatsUI", "AccuracyPanel");

            container.Add(new CuiElement
            {
                Name = "AccuracyText",
                Parent = "AccuracyPanel",
                Components = {
                    new CuiTextComponent { Text = "Accuracy:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "Accuracy",
                Parent = "AccuracyPanel",
                Components = {
                    new CuiTextComponent { Text = CalculateAccuracy(stats.BulletsFired, stats.Hits), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.6117647 0.6431373 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.651 -65.27", OffsetMax = "-127.747 -23.15" }
            }, "StatsUI", "QoLPanel");

            container.Add(new CuiElement
            {
                Name = "QoLTitle",
                Parent = "QoLPanel",
                Components = {
                    new CuiTextComponent { Text = "QoL", Font = "robotocondensed-bold.ttf", FontSize = 20, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-103.09 -21.06", OffsetMax = "91.264 21.06" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 -108.254", OffsetMax = "-127.649 -65.266" }
            }, "StatsUI", "SuicidesPanel");

            container.Add(new CuiElement
            {
                Name = "SuicidesText",
                Parent = "SuicidesPanel",
                Components = {
                    new CuiTextComponent { Text = "Suicides:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "11.208 -13.69", OffsetMax = "109.724 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "Suicides",
                Parent = "SuicidesPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Suicides.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "128.179 -13.69", OffsetMax = "214.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.649 -151.244", OffsetMax = "-127.749 -108.256" }
            }, "StatsUI", "WoundedPanel");

            container.Add(new CuiElement
            {
                Name = "WoundedText",
                Parent = "WoundedPanel",
                Components = {
                    new CuiTextComponent { Text = "Wounded:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "11.208 -13.69", OffsetMax = "109.724 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "Wounded",
                Parent = "WoundedPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Wounded.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "128.179 -13.69", OffsetMax = "214.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.549 -194.234", OffsetMax = "-127.649 -151.246" }
            }, "StatsUI", "ScientistsPanel");

            container.Add(new CuiElement
            {
                Name = "ScientistKillsText",
                Parent = "ScientistsPanel",
                Components = {
                    new CuiTextComponent { Text = "Scientist Kills:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "11.208 -13.69", OffsetMax = "120.9 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "ScientistKills",
                Parent = "ScientistsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.ScientistKills.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "128.179 -13.69", OffsetMax = "214.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08627451 0.08627451 0.08627451 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-356.649 -237.224", OffsetMax = "-127.749 -194.236" }
            }, "StatsUI", "TCsDestroyedPanel");

            container.Add(new CuiElement
            {
                Name = "TCsDestroyedText",
                Parent = "TCsDestroyedPanel",
                Components = {
                    new CuiTextComponent { Text = "TCs Destroyed:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "11.208 -13.69", OffsetMax = "119.503 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "TCsDestroyed",
                Parent = "TCsDestroyedPanel",
                Components = {
                    new CuiTextComponent { Text = stats.TCsDestroyed.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "128.179 -13.69", OffsetMax = "214.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.6117647 0.6431373 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.151 214.48", OffsetMax = "114.753 256.6" }
            }, "StatsUI", "WeaponKillsPanel");

            container.Add(new CuiElement
            {
                Name = "WeaponKillsTitle",
                Parent = "WeaponKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Weapon Kills", Font = "robotocondensed-bold.ttf", FontSize = 20, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-103.09 -21.06", OffsetMax = "91.264 21.06" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 171.496", OffsetMax = "114.751 214.484" }
            }, "StatsUI", "AssultRifleKillsPanel");

            container.Add(new CuiElement
            {
                Name = "AssultRifleKillsText",
                Parent = "AssultRifleKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Assult Rifle:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "AssultRifleKills",
                Parent = "AssultRifleKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.AK47.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.149 128.502", OffsetMax = "114.731 171.49" }
            }, "StatsUI", "LR300KillsPanel");

            container.Add(new CuiElement
            {
                Name = "LR300KillsText",
                Parent = "LR300KillsPanel",
                Components = {
                    new CuiTextComponent { Text = "LR-300:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LR300Kills",
                Parent = "LR300KillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.LR300.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 85.514", OffsetMax = "114.731 128.502" }
            }, "StatsUI", "M249KillsPanel");

            container.Add(new CuiElement
            {
                Name = "M249KillsText",
                Parent = "M249KillsPanel",
                Components = {
                    new CuiTextComponent { Text = "M249:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "M249Kills",
                Parent = "M249KillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.M249.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08627451 0.08627451 0.08627451 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 42.526", OffsetMax = "114.731 85.514" }
            }, "StatsUI", "SniperKillsPanel");

            container.Add(new CuiElement
            {
                Name = "SniperKillsText",
                Parent = "SniperKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Snipers:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "SniperKills",
                Parent = "SniperKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.Snipers.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 -0.662", OffsetMax = "114.731 42.526" }
            }, "StatsUI", "M39KillsPanel");

            container.Add(new CuiElement
            {
                Name = "M39KillsText",
                Parent = "M39KillsPanel",
                Components = {
                    new CuiTextComponent { Text = "M39:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "M39Kills",
                Parent = "M39KillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.M39.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08627451 0.08627451 0.08627451 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 -43.45", OffsetMax = "114.731 -0.662" }
            }, "StatsUI", "SARKillsPanel");

            container.Add(new CuiElement
            {
                Name = "SARKillsText",
                Parent = "SARKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "SAR:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "SARKills",
                Parent = "SARKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.SAR.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 -86.438", OffsetMax = "114.731 -43.45" }
            }, "StatsUI", "MP5KillsPanel");

            container.Add(new CuiElement
            {
                Name = "MP5KillsText",
                Parent = "MP5KillsPanel",
                Components = {
                    new CuiTextComponent { Text = "MP5:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "MP5Kills",
                Parent = "MP5KillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.MP5.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08627451 0.08627451 0.08627451 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.149 -129.424", OffsetMax = "114.751 -86.436" }
            }, "StatsUI", "ThompsonKillsPanel");

            container.Add(new CuiElement
            {
                Name = "ThompsonKillsText",
                Parent = "ThompsonKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Thompson:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "ThompsonKills",
                Parent = "ThompsonKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.Thompson.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 -172.414", OffsetMax = "114.731 -129.426" }
            }, "StatsUI", "ShotgunKillsPanel");

            container.Add(new CuiElement
            {
                Name = "ShotgunKillsText",
                Parent = "ShotgunKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Shotguns:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "ShotgunKills",
                Parent = "ShotgunKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.Shotguns.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08627451 0.08627451 0.08627451 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 -215.404", OffsetMax = "114.731 -172.416" }
            }, "StatsUI", "PistolKillsPanel");

            container.Add(new CuiElement
            {
                Name = "PistolKillsText",
                Parent = "PistolKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Pistols:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "PistolKills",
                Parent = "PistolKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.Pistols.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 -258.394", OffsetMax = "114.751 -215.406" }
            }, "StatsUI", "MeleeKillsPanel");

            container.Add(new CuiElement
            {
                Name = "MeleeKillsText",
                Parent = "MeleeKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Melee:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "MeleeKills",
                Parent = "MeleeKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.Melee.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08627451 0.08627451 0.08627451 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-114.169 -301.384", OffsetMax = "114.731 -258.396" }
            }, "StatsUI", "PimitiveKillsPanel");

            container.Add(new CuiElement
            {
                Name = "PrimitiveKillsText",
                Parent = "PimitiveKillsPanel",
                Components = {
                    new CuiTextComponent { Text = "Primitive:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "11.208 -13.69", OffsetMax = "109.724 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "PrimitiveKills",
                Parent = "PimitiveKillsPanel",
                Components = {
                    new CuiTextComponent { Text = stats.WeaponStats.Primitive.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0 0.5", AnchorMax = "0 0.5", OffsetMin = "128.179 -13.69", OffsetMax = "214.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.6117647 0.6431373 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.549 214.48", OffsetMax = "358.453 256.6" }
            }, "StatsUI", "BodyPartsAccuracyPanel");

            container.Add(new CuiElement
            {
                Name = "BodyPartsAccuracyText",
                Parent = "BodyPartsAccuracyPanel",
                Components = {
                    new CuiTextComponent { Text = "Body Part Hits", Font = "robotocondensed-bold.ttf", FontSize = 20, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-103.09 -21.06", OffsetMax = "91.264 21.06" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.549 171.486", OffsetMax = "358.469 214.474" }
            }, "StatsUI", "HeadAccuracyPanel");

            container.Add(new CuiElement
            {
                Name = "HeadAccuracyText",
                Parent = "HeadAccuracyPanel",
                Components = {
                    new CuiTextComponent { Text = "Head:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "HeadAccuracy",
                Parent = "HeadAccuracyPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.Head.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.55 128.506", OffsetMax = "358.45 171.494" }
            }, "StatsUI", "TorsoPanel");

            container.Add(new CuiElement
            {
                Name = "TorsoKillsText",
                Parent = "TorsoPanel",
                Components = {
                    new CuiTextComponent { Text = "Torso:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "TorsoKills",
                Parent = "TorsoPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.Torso.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.55 85.514", OffsetMax = "358.47 128.502" }
            }, "StatsUI", "LeftHandPanel");

            container.Add(new CuiElement
            {
                Name = "LeftHandText",
                Parent = "LeftHandPanel",
                Components = {
                    new CuiTextComponent { Text = "Left Hand:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeftHand",
                Parent = "LeftHandPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.LeftHand.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.549 42.53", OffsetMax = "358.449 85.518" }
            }, "StatsUI", "RightHandPanel");

            container.Add(new CuiElement
            {
                Name = "RightHandText",
                Parent = "RightHandPanel",
                Components = {
                    new CuiTextComponent { Text = "Right Hand:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "RightHand",
                Parent = "RightHandPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.RightHand.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.55 -0.658", OffsetMax = "358.47 42.53" }
            }, "StatsUI", "LeftLegPanel");

            container.Add(new CuiElement
            {
                Name = "LeftLegText",
                Parent = "LeftLegPanel",
                Components = {
                    new CuiTextComponent { Text = "Left Leg:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeftLeg",
                Parent = "LeftLegPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.LeftLeg.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.549 -43.45", OffsetMax = "358.449 -0.662" }
            }, "StatsUI", "RightLegPanel");

            container.Add(new CuiElement
            {
                Name = "RightLegText",
                Parent = "RightLegPanel",
                Components = {
                    new CuiTextComponent { Text = "Right Leg:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "RightLeg",
                Parent = "RightLegPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.RightLeg.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.529 -86.438", OffsetMax = "358.449 -43.45" }
            }, "StatsUI", "LeftFootPanel");

            container.Add(new CuiElement
            {
                Name = "LeftFootText",
                Parent = "LeftFootPanel",
                Components = {
                    new CuiTextComponent { Text = "Left Foot:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "LeftFoot",
                Parent = "LeftFootPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.LeftFoot.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.57 -129.423", OffsetMax = "358.47 -86.437" }
            }, "StatsUI", "RightFootPanel");

            container.Add(new CuiElement
            {
                Name = "RightFootText",
                Parent = "RightFootPanel",
                Components = {
                    new CuiTextComponent { Text = "Right Foot:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "RightFoot",
                Parent = "RightFootPanel",
                Components = {
                    new CuiTextComponent { Text = stats.Hits.RightFoot.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.1411765 0.6117647 0.6431373 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.57 -191.07", OffsetMax = "358.47 -148.95" }
            }, "StatsUI", "ExplosivesPanel");

            container.Add(new CuiElement
            {
                Name = "ExplosivesText",
                Parent = "ExplosivesPanel",
                Components = {
                    new CuiTextComponent { Text = "Explosives", Font = "robotocondensed-bold.ttf", FontSize = 20, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-103.09 -21.06", OffsetMax = "91.264 21.06" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.57 -234.054", OffsetMax = "358.47 -191.066" }
            }, "StatsUI", "SatchelsThrownPanel");

            container.Add(new CuiElement
            {
                Name = "SatchelsThrownText",
                Parent = "SatchelsThrownPanel",
                Components = {
                    new CuiTextComponent { Text = "Satchels Thrown:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "19.224 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "SatchelsThrown",
                Parent = "SatchelsThrownPanel",
                Components = {
                    new CuiTextComponent { Text = stats.SatchelsThrown.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "29.846 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0.08490568 0.08490568 0.08490568 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.57 -277.044", OffsetMax = "358.47 -234.056" }
            }, "StatsUI", "C4ThrownPanel");

            container.Add(new CuiElement
            {
                Name = "C4ThrownText",
                Parent = "C4ThrownPanel",
                Components = {
                    new CuiTextComponent { Text = "C4 Thrown:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "-4.276 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "C4Thrown",
                Parent = "C4ThrownPanel",
                Components = {
                    new CuiTextComponent { Text = stats.C4Thrown.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "14.179 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            container.Add(new CuiPanel
            {
                CursorEnabled = false,
                Image = { Color = "0 0 0 0.6" },
                RectTransform = { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "129.57 -320.034", OffsetMax = "358.47 -277.046" }
            }, "StatsUI", "RocketsFiredPanel");

            container.Add(new CuiElement
            {
                Name = "RocketsFiredText",
                Parent = "RocketsFiredPanel",
                Components = {
                    new CuiTextComponent { Text = "Rockets Fired:", Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleLeft, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "-102.792 -13.69", OffsetMax = "19.224 13.69" }
                }
            });

            container.Add(new CuiElement
            {
                Name = "RocketsFired",
                Parent = "RocketsFiredPanel",
                Components = {
                    new CuiTextComponent { Text = stats.RocketsFired.ToString(), Font = "robotocondensed-bold.ttf", FontSize = 18, Align = TextAnchor.MiddleRight, Color = "1 1 1 1" },
                    new CuiRectTransformComponent { AnchorMin = "0.5 0.5", AnchorMax = "0.5 0.5", OffsetMin = "29.846 -13.69", OffsetMax = "100.829 13.69" }
                }
            });

            CuiHelper.AddUi(player, container);
        }

        private void ShowNavigationSidebar(BasePlayer player)
        {
            // Check if OasisSidebar plugin is loaded
            var sidebarPlugin = plugins.Find("awakenSidebar");
            if (sidebarPlugin == null)
            {
                return; // Silently fail if sidebar not available
            }

            CuiHelper.DestroyUi(player, "awakenNavigation");

            // Create navigation sidebar using the component
            var container = sidebarPlugin.Call("CreateAwakenNavigation") as CuiElementContainer;

            if (container != null)
            {
                CuiHelper.AddUi(player, container);
            }
        }

        #region Commands
        [Command("stats")] 
        private void StatsCommand(IPlayer player, string command, string[] args)
        {
            var basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;

            if (args.Length == 0)
            {
                ShowLeaderboard(basePlayer);
                return;
            }

            if (args.Length >= 2)
            {
                string idToLookup = args[0];
                string nameToDisplay = args[1]; 
                if (playerStats.TryGetValue(idToLookup, out _))
                {
                    StatsUI(basePlayer, idToLookup, nameToDisplay);
                }
                else
                {
                    player.Reply($"No stats found for ID: {idToLookup}");
                }
            }
            else if (args.Length == 1)
            {
                string targetIdentifier = args[0];
                BasePlayer targetPlayer = BasePlayer.Find(targetIdentifier);
                string? foundSteamId = null;
                string foundDisplayName = targetIdentifier;

                if (targetPlayer != null)
                {
                    foundSteamId = targetPlayer.UserIDString;
                    foundDisplayName = targetPlayer.displayName;
                }
                else if (ulong.TryParse(targetIdentifier, out var steamId))
                {
                     foundSteamId = steamId.ToString();
                }


                if (foundSteamId != null && playerStats.TryGetValue(foundSteamId, out var foundStats))
                {
                    StatsUI(basePlayer, foundSteamId, foundStats.Name);
                    return;
                }
                
                foreach(var entry in playerStats)
                {
                    if(entry.Value.Name.Equals(targetIdentifier, StringComparison.OrdinalIgnoreCase))
                    {
                        StatsUI(basePlayer, entry.Key, entry.Value.Name);
                        return;
                    }
                }
                player.Reply($"Player '{targetIdentifier}' not found or no stats available.");
            }
        }

        [Command("leaderboard")] 
        private void LeaderboardCommand(IPlayer player, string command, string[] args)
        {
            var basePlayer = player.Object as BasePlayer;
            if (basePlayer == null) return;
            ShowLeaderboard(basePlayer);
        }

        [Command("stats_tab")]
        private void StatsTabCommand(IPlayer player, string command, string[] args)
        {
            var basePlayer = player.Object as BasePlayer;
            if (basePlayer == null || args.Length == 0) return;

            if (args[0].Equals("players", StringComparison.OrdinalIgnoreCase))
            {
                ShowLeaderboard(basePlayer);
            }
            else if (args[0].Equals("clans", StringComparison.OrdinalIgnoreCase))
            {
                ShowClansLeaderboard(basePlayer);
            }
        }
        #endregion
        [Command("playerleaderboard")]
        private void LeaderboardCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            var player = iPlayer.Object as BasePlayer;
            if (player == null) return;
            Leaderboard(player);
        }

        [Command("playerstats")]
        private void MyStatsCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            var player = iPlayer.Object as BasePlayer;
            if (player == null) return;
            
            CuiHelper.DestroyUi(player, "Leaderboard");

            if (args.Length == 2)
            {
                string idToLookup = args[0];
                string nameToDisplay = args[1];
                
                if (playerStats.TryGetValue(idToLookup, out var targetStats))
                {
                    StatsUI(player, idToLookup, nameToDisplay); 
                }
                else
                {
                    player.ChatMessage($"Could not find stats for ID {idToLookup}.");
                }
            }
            else if (args.Length == 1) 
            {
                string targetIdentifier = args[0];
                BasePlayer targetPlayer = BasePlayer.Find(targetIdentifier); 
                string? foundSteamId = null;
                string foundDisplayName = targetIdentifier;

                if (targetPlayer != null) 
                {
                    foundSteamId = targetPlayer.UserIDString;
                    foundDisplayName = targetPlayer.displayName;
                }
                else if (ulong.TryParse(targetIdentifier, out var steamId))
                {
                    foundSteamId = steamId.ToString();
                }
                
                if (foundSteamId != null && playerStats.TryGetValue(foundSteamId, out var foundStats))
                {
                    StatsUI(player, foundSteamId, foundStats.Name); 
                }
                else 
                {
                    bool foundByName = false;
                    foreach(var entry in playerStats)
                    {
                        if(entry.Value.Name.Equals(targetIdentifier, StringComparison.OrdinalIgnoreCase))
                        {
                            StatsUI(player, entry.Key, entry.Value.Name);
                            foundByName = true;
                            break;
                        }
                    }
                    if(!foundByName)
                    {
                        player.ChatMessage($"Player '{targetIdentifier}' not found or no stats available.");
                    }
                }
            }
            else
            {
                 StatsUI(player, player.UserIDString, string.Empty); 
            }
        }

        [Command("destroyui")]
        private void DestroyUICMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer || args.Length == 0) return;
            var player = iPlayer.Object as BasePlayer;
            if (player == null) return;
            CuiHelper.DestroyUi(player, args[0]);
        }

        [Command("navigation")]
        private void NavigationCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            var player = iPlayer.Object as BasePlayer;
            if (player == null) return;
            ShowNavigationSidebar(player);
        }

        [Command("stats_close")]
        private void StatsCloseCMD(IPlayer iPlayer, string command, string[] args)
        {
            if (iPlayer.IsServer) return;
            var player = iPlayer.Object as BasePlayer;
            if (player == null) return;

            // Close both the main stats UI and the navigation sidebar
            CuiHelper.DestroyUi(player, "StatsLeaderboard");
            CuiHelper.DestroyUi(player, "awakenNavigation");
        }

        public class CUIClass
        {
            public static CuiElementContainer CreateOverlay(string _name, string _color, string _anchorMin, string _anchorMax, bool _cursorOn = false, float _fade = 0f, string _mat = "")
            {
                var _element = new CuiElementContainer()
                {
                    {
                        new CuiPanel
                        {
                            Image = { Color = _color, Material = _mat, FadeIn = _fade},
                            RectTransform = { AnchorMin = _anchorMin, AnchorMax = _anchorMax },
                            CursorEnabled = _cursorOn
                        },
                        new CuiElement().Parent = "Overlay",
                        _name
                    }
                };
                return _element;
            }

            public static void CreatePanel(ref CuiElementContainer _container, string _name, string _parent, string _color, string _anchorMin, string _anchorMax, bool _cursorOn = false, float _fadeIn = 0f, float _fadeOut = 0f, string _mat2 = "", string _OffsetMin = "", string _OffsetMax = "", bool keyboard = false)
            {
                _container.Add(new CuiPanel
                {
                    Image = { Color = _color, Material = _mat2, FadeIn = _fadeIn },
                    RectTransform = { AnchorMin = _anchorMin, AnchorMax = _anchorMax, OffsetMin = _OffsetMin, OffsetMax = _OffsetMax },
                    FadeOut = _fadeOut,
                    CursorEnabled = _cursorOn,
                    KeyboardEnabled = keyboard
                },
                _parent,
                _name);
            }

            public static void CreateImage(ref CuiElementContainer _container, string _name, string _parent, string _image, string _anchorMin, string _anchorMax, float _fadeIn = 0f, float _fadeOut = 0f, string _OffsetMin = "", string _OffsetMax = "")
            {
                if (_image.StartsWith("http") || _image.StartsWith("www"))
                {
                    _container.Add(new CuiElement
                    {
                        Name = _name,
                        Parent = _parent,
                        FadeOut = _fadeOut,
                        Components =
                        {
                            new CuiRawImageComponent { Url = _image, Sprite = "assets/content/textures/generic/fulltransparent.tga", FadeIn = _fadeIn},
                            new CuiRectTransformComponent { AnchorMin = _anchorMin, AnchorMax = _anchorMax, OffsetMin = _OffsetMin, OffsetMax = _OffsetMax }
                        }

                    });
                }
                else
                {
                    _container.Add(new CuiElement
                    {
                        Name = _name,
                        Parent = _parent,
                        FadeOut = _fadeOut,
                        Components =
                        {
                            new CuiImageComponent { Sprite = _image, FadeIn = _fadeIn},
                            new CuiRectTransformComponent { AnchorMin = _anchorMin, AnchorMax = _anchorMax, OffsetMin = _OffsetMin, OffsetMax = _OffsetMax }
                        }

                    });
                }
            }

            public static void CreateInput(ref CuiElementContainer _container, string _name, string _parent, string _color, int _size, string _anchorMin, string _anchorMax, string _defaultText, string _font = "permanentmarker.ttf", string _command = "command.processinput", TextAnchor _align = TextAnchor.MiddleCenter, int _charsLimit = 200)
            {
                _container.Add(new CuiElement
                {
                    Parent = _parent,
                    Name = _name,

                    Components =
                    {
                        new CuiInputFieldComponent
                        {

                            Text = _defaultText,
                            CharsLimit = _charsLimit,
                            Color = _color,
                            IsPassword = false,
                            Command = _command,
                            Font = _font,
                            FontSize = _size,
                            Align = _align
                        },

                        new CuiRectTransformComponent
                        {
                            AnchorMin = _anchorMin,
                            AnchorMax = _anchorMax

                        }

                    },
                });
            }

            public static void CreateText(ref CuiElementContainer _container, string _name, string _parent, string _color, string _text, int _size, string _anchorMin, string _anchorMax, TextAnchor _align = TextAnchor.MiddleCenter, string _font = "robotocondensed-bold.ttf", float _fadeIn = 0f, float _fadeOut = 0f, string _outlineColor = "0 0 0 0", string _outlineScale = "0 0")
            {
                _container.Add(new CuiElement
                {
                    Parent = _parent,
                    Name = _name,
                    Components =
                    {
                        new CuiTextComponent
                        {
                            Text = _text,
                            FontSize = _size,
                            Font = _font,
                            Align = _align,
                            Color = _color,
                            FadeIn = _fadeIn,
                        },

                        new CuiOutlineComponent
                        {

                            Color = _outlineColor,
                            Distance = _outlineScale

                        },

                        new CuiRectTransformComponent
                        {
                             AnchorMin = _anchorMin,
                             AnchorMax = _anchorMax
                        }
                    },
                    FadeOut = _fadeOut
                });
            }

            public static void CreateButton(ref CuiElementContainer _container, string _name, string _parent, string _color, string _text, int _size, string _anchorMin, string _anchorMax, string _command = "", string _close = "", string _textColor = "0.843 0.816 0.78 1", float _fade = 1f, TextAnchor _align = TextAnchor.MiddleCenter, string _font = "", string _material = "assets/content/ui/uibackgroundblur-ingamemenu.mat")
            {

                _container.Add(new CuiButton
                {
                    Button = { Close = _close, Command = _command, Color = _color, Material = _material, FadeIn = _fade },
                    RectTransform = { AnchorMin = _anchorMin, AnchorMax = _anchorMax },
                    Text = { Text = _text, FontSize = _size, Align = _align, Color = _textColor, Font = _font, FadeIn = _fade }
                },
                _parent,
                _name);
            }
        }
        #endregion
    }
}











